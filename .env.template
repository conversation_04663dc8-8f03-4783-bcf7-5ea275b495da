# Environment Variables Template for Trading System
# Copy this file to .env and fill in your actual values
# DO NOT commit .env file to version control

# ═══════════════════════════════════════════════════════════════════════════════
# 🔐 ANGEL ONE SMARTAPI CREDENTIALS
# ═══════════════════════════════════════════════════════════════════════════════
# Get these from Angel One Developer Portal: https://smartapi.angelbroking.com/
SMARTAPI_API_KEY=your_api_key_here
SMARTAPI_USERNAME=your_client_code_here
SMARTAPI_PASSWORD=your_pin_here
SMARTAPI_TOTP_TOKEN=your_qr_token_here

# ═══════════════════════════════════════════════════════════════════════════════
# 📱 NOTIFICATION SERVICES
# ═══════════════════════════════════════════════════════════════════════════════
# Telegram Bot Configuration
# Create bot: https://t.me/BotFather
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Email Configuration (Optional)
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>

# Slack Configuration (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#trading-alerts

# Discord Configuration (Optional)
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# ═══════════════════════════════════════════════════════════════════════════════
# 🗄️ DATABASE CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Primary Database (Optional - defaults to SQLite)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trading_db
DB_USERNAME=trading_user
DB_PASSWORD=your_db_password_here

# Redis Cache (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# InfluxDB Time Series (Optional)
INFLUXDB_HOST=localhost
INFLUXDB_PORT=8086
INFLUXDB_DATABASE=trading_metrics
INFLUXDB_USERNAME=trading_user
INFLUXDB_PASSWORD=your_influxdb_password_here

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 EXTERNAL DATA SOURCES
# ═══════════════════════════════════════════════════════════════════════════════
# Alpha Vantage (Optional)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Economic Calendar API (Optional)
ECONOMIC_CALENDAR_API_KEY=your_economic_calendar_key_here

# News API (Optional)
NEWS_API_KEY=your_news_api_key_here

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 SYSTEM CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Environment Settings
TRADING_ENVIRONMENT=development  # development, staging, production
DEBUG_MODE=true  # true/false
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR

# Security
ENCRYPTION_KEY=your_32_character_encryption_key_here
JWT_SECRET=your_jwt_secret_key_here

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 WEBHOOK CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Incoming Webhooks
WEBHOOK_PORT=8080
WEBHOOK_SECRET=your_webhook_secret_here

# External System Webhooks
EXTERNAL_WEBHOOK_URL=https://your-external-system.com/webhook
EXTERNAL_WEBHOOK_SECRET=your_external_webhook_secret_here

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TESTING & DEVELOPMENT
# ═══════════════════════════════════════════════════════════════════════════════
# Test Mode
TEST_MODE=false  # true/false

# Paper Trading
PAPER_TRADING_ENABLED=true  # true/false - Start with paper trading

# Mock Data
MOCK_DATA_ENABLED=false  # true/false

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 BACKUP & CLOUD STORAGE
# ═══════════════════════════════════════════════════════════════════════════════
# Cloud Backup Path (Optional)
CLOUD_BACKUP_PATH=s3://your-bucket/trading-backups/

# ═══════════════════════════════════════════════════════════════════════════════
# 🔍 MONITORING & OBSERVABILITY
# ═══════════════════════════════════════════════════════════════════════════════
# Application Performance Monitoring (Optional)
APM_SERVICE_NAME=trading-system
APM_SERVER_URL=http://localhost:8200
APM_SECRET_TOKEN=your_apm_secret_token_here

# ═══════════════════════════════════════════════════════════════════════════════
# 📝 USAGE INSTRUCTIONS
# ═══════════════════════════════════════════════════════════════════════════════
# 1. Copy this file to .env: cp .env.template .env
# 2. Fill in your actual values (remove the _here suffixes)
# 3. Make sure .env is in your .gitignore file
# 4. Load environment variables in your shell:
#    - Linux/Mac: source .env or export $(cat .env | xargs)
#    - Windows: Get-Content .env | ForEach-Object { $name, $value = $_.split('='); Set-Item -Path "env:$name" -Value $value }
# 5. Verify variables are loaded: echo $SMARTAPI_API_KEY

# ═══════════════════════════════════════════════════════════════════════════════
# 🔒 SECURITY NOTES
# ═══════════════════════════════════════════════════════════════════════════════
# - Never commit .env file to version control
# - Use strong, unique passwords for all services
# - Rotate API keys regularly
# - Use environment-specific values for different deployments
# - Consider using a secrets management service for production

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/*.log
logs/**/*.log

# Data files (large datasets)
data/historical/*.parquet
data/historical/*.csv
data/backtest/*.parquet
data/features/*.parquet
data/models/*.pkl
data/models/*.joblib
data/models/*.h5
data/models/*.pt
data/models/*.pth
data/live/*.parquet

# Temporary files
*.tmp
*.temp
temp/
tmp/
data/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# API keys and sensitive data
*.key
*.pem
config/api_keys.yaml
config/credentials.yaml
.env.local
.env.production

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/

# Docker
.dockerignore

# Large output files
*.png
*.jpg
*.jpeg
*.gif
reports/*.html
reports/*.pdf

# Backup files
*.backup
*.bak

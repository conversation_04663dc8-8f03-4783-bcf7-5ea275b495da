The project structure is as follows:
- `main.py`: Centralized entry point for all agents and workflows.
- `requirements.txt`: Lists all project dependencies.
- `test_main.py`: Contains the system's test suite.
- `agents/`: Directory for all individual trading agents (e.g., `ai_training_agent.py`, `signal_generation_agent.py`).
- `config/`: Stores various configuration files (e.g., `ai_training_config.yaml`, `gpu_optimization_config.yaml`).
- `utils/`: Contains utility modules (e.g., `gpu_optimizer.py`).
- `data/`: Used for storing data, including features, backtest results, and trained models.
Key development guidelines and design patterns for this project include:
- Centralized Agent Management: All trading agents are managed from a single entry point (`main.py`).
- Unified Workflow Orchestration: Workflows are orchestrated consistently.
- Consistent Configuration Management: Configuration settings are managed uniformly across agents.
- Real-time Monitoring and Health Checks: The system supports real-time monitoring and health checks.
- GPU Optimization: The system is optimized for GPU performance across various components (Polars, LightGBM, PyTorch, CatBoost).
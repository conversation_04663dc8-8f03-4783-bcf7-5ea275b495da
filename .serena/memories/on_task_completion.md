When a task is completed, the following steps should be performed:
- **Testing:** Run `python test_main.py` to ensure all system tests pass.
- **Code Style/Formatting:** Adhere to standard Python code style guidelines (e.g., PEP 8). While specific linting/formatting commands are not explicitly mentioned in the README, it is recommended to use tools like `flake8` for linting and `black` for formatting to maintain code consistency. If these tools are not installed, they should be added to `requirements.txt` and installed.
- **Documentation:** Update any relevant documentation (e.g., READMEs, agent guides) to reflect changes or new features.
- **Configuration Review:** Ensure any new or modified configurations are correctly set in the `config/` directory.
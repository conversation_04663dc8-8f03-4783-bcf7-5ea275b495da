# Multi-stage Docker build for Intraday AI Trading System
FROM python:3.11-slim as base

# Set environment variables for UTF-8 support
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs/risk_management logs/execution logs/market_monitoring \
    data/backtest data/model data/historical \
    && chmod -R 755 logs data

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash trader && \
    chown -R trader:trader /app

USER trader

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health', timeout=5)" || exit 1

# Default command
CMD ["python", "main.py", "--agent", "execution", "--trading-mode", "paper"]

# Development stage
FROM base as development

USER root

# Install development dependencies
RUN pip install --no-cache-dir pytest pytest-asyncio black flake8 mypy

USER trader

# Production stage  
FROM base as production

# Remove unnecessary files
RUN rm -rf tests/ examples/ *.md .git/

# Optimize Python bytecode
RUN python -m compileall .

EXPOSE 8000

# Production command with health monitoring
CMD ["python", "main.py", "--agent", "execution", "--trading-mode", "paper", "--health-port", "8000"]

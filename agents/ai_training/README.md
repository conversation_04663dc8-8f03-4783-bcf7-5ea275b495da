# Enhanced AI Training System - Modular Architecture

A comprehensive, production-ready AI training system addressing all enhancement areas for trading strategy optimization.

## 🏗️ Modular Architecture

```
agents/ai_training/
├── __init__.py                 # Module exports
├── config.py                   # Configuration management
├── data_handler.py            # Data processing with Polars optimization
├── hyperopt_optimizer.py      # Comprehensive hyperparameter optimization
├── model_trainer.py           # Model creation and training
├── ensemble_creator.py        # Advanced ensemble methods
├── evaluator.py               # Enhanced evaluation metrics
├── model_persistence.py       # Robust model saving/loading
├── meta_learner.py            # Meta-learning with concrete targets
├── main_agent.py              # Main orchestrator
├── example_usage.py           # Usage demonstration
└── README.md                  # This file
```

## ✅ Implemented Enhancements

### 1. **Robust save_models Method** (`model_persistence.py`)
- Handles both batch and online models consistently
- Separate storage for different model types
- Comprehensive metadata saving
- Error handling and recovery

### 2. **Advanced Ensemble Methods** (`ensemble_creator.py`)
- **Stacking**: Meta-learner combines base model predictions
- **Blending**: Holdout-based ensemble training
- **Weighted Voting**: Performance-based weight assignment
- Support for both classification and regression

### 3. **Comprehensive Hyperparameter Optimization** (`hyperopt_optimizer.py`)
- Unified Optuna optimization for all model types
- Model-specific parameter spaces
- Cross-validation based evaluation
- Timeout and trial management

### 4. **Enhanced Evaluation Metrics** (`evaluator.py`)
- **Classification**: F1-score, Precision, Recall, ROC-AUC
- **Regression**: R², RMSE, MAE, MAPE, Explained Variance
- Class-specific metrics for imbalanced datasets
- Model comparison and ranking

### 5. **Concrete Meta-Learning Targets** (`meta_learner.py`)
- Strategy selection based on historical performance ranking
- Optimal holding period from actual trading data
- Risk-reward optimization from real metrics
- Market regime classification from performance patterns

### 6. **Consistent Polars Data Handling** (`data_handler.py`)
- Minimal Polars-to-Pandas conversions
- Efficient data processing with Polars expressions
- Memory-optimized operations
- Consistent data flow throughout pipeline

### 7. **Robust Prediction Feature Preparation** (`data_handler.py`)
- Proper imputation using training statistics
- Missing feature handling with mean/median values
- Feature scaling and selection consistency
- Error handling for edge cases

### 8. **Dynamic Class Handling for SGD** (`model_trainer.py`)
- Automatic class detection from training data
- Proper SGD configuration for multi-class problems
- Class storage for future predictions
- Robust error handling

### 9. **Advanced Preprocessing** (`data_handler.py`)
- Outlier detection and removal using IQR method
- KNN-based imputation for missing values
- Feature selection with statistical tests
- Derived feature creation

## 🚀 Quick Start

```python
import asyncio
from agents.ai_training import EnhancedAITrainingAgent, EnhancedAITrainingConfig

async def main():
    # Configure with all enhancements
    config = EnhancedAITrainingConfig(
        hyperopt_all_models=True,
        ensemble_method="stacking",
        use_advanced_metrics=True,
        feature_selection_enabled=True,
        advanced_imputation=True
    )
    
    # Initialize and train
    agent = EnhancedAITrainingAgent(config)
    results = await agent.train_enhanced_models("data/your_data.parquet")
    
    # Make predictions
    predictions = agent.predict({
        "roi": 0.15,
        "sharpe_ratio": 1.2,
        "max_drawdown": -0.08
    })

asyncio.run(main())
```

## 📊 Prediction Tasks

The system supports multiple prediction objectives:

1. **Profitability Prediction** (Classification)
   - Target: `is_profitable`
   - Metrics: F1-score, Precision, Recall, ROC-AUC

2. **ROI Prediction** (Regression)
   - Target: `roi`
   - Metrics: R², RMSE, MAE

3. **Sharpe Ratio Prediction** (Regression)
   - Target: `sharpe_ratio`
   - Metrics: R², RMSE

4. **Drawdown Prediction** (Regression)
   - Target: `max_drawdown`
   - Metrics: R², MAE

## 🔧 Configuration Options

```python
config = EnhancedAITrainingConfig(
    # Data paths
    data_dir="data/backtest",
    input_file="enhanced_strategy_results.parquet",
    
    # Model selection
    enabled_models=["lightgbm", "xgboost", "catboost", "tabnet", "mlp", "sgd"],
    
    # Ensemble configuration
    ensemble_method="stacking",  # "voting", "stacking", "blending", "weighted"
    stacking_cv_folds=3,
    
    # Optimization
    hyperopt_all_models=True,
    optuna_trials=50,
    optuna_timeout=1800,
    
    # Feature engineering
    feature_selection_enabled=True,
    max_features=50,
    outlier_detection_enabled=True,
    advanced_imputation=True,
    
    # Hardware
    n_jobs=-1,
    use_gpu=True
)
```

## 🎯 Key Benefits

- **Modular Design**: Easy to extend and maintain
- **Production Ready**: Robust error handling and logging
- **Performance Optimized**: Efficient data processing and GPU support
- **Comprehensive**: Covers all aspects of ML pipeline
- **Flexible**: Configurable for different use cases
- **Scalable**: Supports both batch and online learning

## 📈 Performance Features

- **Memory Efficient**: Polars-based data processing
- **GPU Accelerated**: CUDA support for compatible models
- **Parallel Processing**: Multi-core hyperparameter optimization
- **Caching**: Intelligent model and preprocessing object caching
- **Monitoring**: Comprehensive logging and progress tracking

## 🔍 Model Explainability

The system provides insights into model decisions through:
- Feature importance rankings
- Meta-learning model explanations
- Performance comparisons across models
- Ensemble contribution analysis

## 📝 Usage Examples

See `example_usage.py` for comprehensive usage examples demonstrating all features.

## 🛠️ Dependencies

- Core: `numpy`, `pandas`, `polars`, `scikit-learn`
- ML Models: `lightgbm`, `xgboost`, `catboost`, `pytorch-tabnet`
- Optimization: `optuna`
- Deep Learning: `torch` (for TabNet)

## 📋 TODO / Future Enhancements

- [ ] SHAP integration for model explainability
- [ ] Automated feature engineering
- [ ] Model drift detection
- [ ] A/B testing framework
- [ ] Real-time prediction serving
- [ ] Model monitoring dashboard
"""
Data handling module with Polars optimization
Enhancement 6: Consistent Data Handling (Polars/Pandas/Numpy)
"""

import logging
import numpy as np
import polars as pl
from pathlib import Path
from typing import Tuple, List
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import KNNImputer
from sklearn.feature_selection import SelectKBest, f_regression, f_classif

logger = logging.getLogger(__name__)

class DataHandler:
    """Handles data loading and preprocessing with Polars optimization"""
    
    def __init__(self, config):
        self.config = config
        self.scalers = {}
        self.encoders = {}
        self.feature_selectors = {}
        self.imputers = {}
    
    def load_data(self, file_path: str = None) -> pl.DataFrame:
        """Load data using Polars with support for multiple backtesting files"""
        
        if file_path is None:
            # Load multiple files based on pattern
            return self._load_multiple_backtest_files()
        else:
            # Load single file
            return self._load_single_file(file_path)
    
    def _load_multiple_backtest_files(self) -> pl.DataFrame:
        """Load and combine multiple backtesting files"""
        
        data_dir = Path(self.config.data_dir)
        pattern = self.config.input_pattern
        
        # Find all matching files
        matching_files = list(data_dir.glob(pattern))
        
        if not matching_files:
            raise FileNotFoundError(f"No files found matching pattern: {pattern} in {data_dir}")
        
        # Apply filters
        filtered_files = self._apply_filters(matching_files)
        
        if not filtered_files:
            raise FileNotFoundError(f"No files found after applying filters")
        
        logger.info(f"Loading {len(filtered_files)} backtesting files...")
        
        # Load and combine all files
        dataframes = []
        for file_path in filtered_files:
            try:
                df = self._load_single_file(file_path)
                # Add metadata columns
                symbol, timeframe = self._extract_metadata_from_filename(file_path.name)
                df = df.with_columns([
                    pl.lit(symbol).alias("symbol"),
                    pl.lit(timeframe).alias("timeframe")
                ])
                dataframes.append(df)
                logger.info(f"  Loaded {file_path.name}: {df.height} rows")
            except Exception as e:
                logger.warning(f"  Failed to load {file_path.name}: {e}")
                continue
        
        if not dataframes:
            raise ValueError("No valid data files could be loaded")
        
        # Combine all dataframes
        combined_df = pl.concat(dataframes, how="vertical")
        logger.info(f"Combined dataset: {combined_df.height} rows, {combined_df.width} columns")
        
        return combined_df
    
    def _load_single_file(self, file_path: str) -> pl.DataFrame:
        """Load a single file"""
        
        file_path = Path(file_path)
        logger.info(f"Loading data from: {file_path}")
        
        if str(file_path).endswith('.parquet'):
            df = pl.read_parquet(file_path)
        elif str(file_path).endswith('.csv'):
            df = pl.read_csv(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_path}")
        
        return df

    
    def _apply_filters(self, files: List[Path]) -> List[Path]:
        """Apply symbol and timeframe filters to file list"""
        
        filtered_files = files
        
        # Apply symbol filter
        if self.config.symbol_filter:
            filtered_files = [
                f for f in filtered_files 
                if self.config.symbol_filter.upper() in f.name.upper()
            ]
            logger.info(f"Symbol filter '{self.config.symbol_filter}': {len(filtered_files)} files")
        
        # Apply timeframe filter
        if self.config.timeframe_filter:
            filtered_files = [
                f for f in filtered_files 
                if self.config.timeframe_filter in f.name
            ]
            logger.info(f"Timeframe filter '{self.config.timeframe_filter}': {len(filtered_files)} files")
        
        return filtered_files
    
    def _extract_metadata_from_filename(self, filename: str) -> tuple:
        """Extract symbol and timeframe from filename like 'backtest_360ONE_3min.parquet'"""
        
        try:
            # Remove extension
            name_without_ext = filename.replace('.parquet', '').replace('.csv', '')
            
            # Split by underscore: backtest_SYMBOL_TIMEFRAME
            parts = name_without_ext.split('_')
            
            if len(parts) >= 3:
                symbol = parts[1]  # 360ONE
                timeframe = parts[2]  # 3min
                return symbol, timeframe
            else:
                return "UNKNOWN", "UNKNOWN"
                
        except Exception as e:
            logger.warning(f"Could not extract metadata from filename {filename}: {e}")
            return "UNKNOWN", "UNKNOWN"
    
    def preprocess_data(self, df: pl.DataFrame) -> Tuple[pl.DataFrame, List[str], List[str]]:
        """Enhanced preprocessing using Polars operations"""
        
        logger.info("Preprocessing data with Polars...")
        
        # Handle missing values efficiently
        df = self._handle_missing_values(df)
        
        # Create derived features
        df = self._create_derived_features(df)
        
        # Outlier detection and removal
        if self.config.outlier_detection_enabled:
            df = self._remove_outliers(df)
        
        # Create concrete meta-learning targets
        df = self._create_meta_targets(df)
        
        # Identify feature and target columns
        target_columns = [task["target_column"] for task in self.config.prediction_tasks.values()]
        feature_columns = [col for col in df.columns 
                          if col not in target_columns and 
                          df[col].dtype in [pl.Float64, pl.Float32, pl.Int64, pl.Int32]]
        
        logger.info(f"Preprocessed: {len(feature_columns)} features, {len(target_columns)} targets")
        return df, feature_columns, target_columns
    
    def _handle_missing_values(self, df: pl.DataFrame) -> pl.DataFrame:
        """Handle missing values using Polars operations"""
        
        # Fill numeric columns with median
        numeric_cols = df.select(pl.col(pl.NUMERIC_DTYPES)).columns
        for col in numeric_cols:
            median_val = df[col].median()
            if median_val is not None:
                df = df.with_columns(pl.col(col).fill_null(median_val))
        
        # Fill string columns with mode
        string_cols = df.select(pl.col(pl.Utf8)).columns
        for col in string_cols:
            mode_val = df[col].mode().first()
            if mode_val is not None:
                df = df.with_columns(pl.col(col).fill_null(mode_val))
        
        return df
    
    def _create_derived_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Create derived features using Polars expressions"""
        
        # Risk-adjusted metrics
        if all(col in df.columns for col in ['roi', 'max_drawdown']):
            df = df.with_columns(
                (pl.col('roi') / pl.col('max_drawdown').abs().clip(0.01)).alias('roi_drawdown_ratio')
            )
        
        # Performance consistency
        if all(col in df.columns for col in ['sharpe_ratio', 'expectancy']):
            df = df.with_columns(
                (pl.col('sharpe_ratio') * pl.col('expectancy')).alias('risk_adjusted_expectancy')
            )
        
        # Trade efficiency
        if all(col in df.columns for col in ['total_trades', 'winning_trades']):
            df = df.with_columns(
                (pl.col('winning_trades') / pl.col('total_trades').clip(1)).alias('win_rate')
            )
        
        return df
    
    def _remove_outliers(self, df: pl.DataFrame) -> pl.DataFrame:
        """Remove outliers using Polars operations"""
        
        numeric_cols = df.select(pl.col(pl.NUMERIC_DTYPES)).columns
        
        for col in numeric_cols:
            q1 = df[col].quantile(0.25)
            q3 = df[col].quantile(0.75)
            
            if q1 is not None and q3 is not None:
                iqr = q3 - q1
                if iqr > 0:
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    df = df.filter(
                        (pl.col(col) >= lower_bound) & (pl.col(col) <= upper_bound)
                    )
        
        return df
    
    def _create_meta_targets(self, df: pl.DataFrame) -> pl.DataFrame:
        """Enhancement 5: Create concrete meta-learning targets from historical performance"""
        
        logger.info("Creating concrete meta-learning targets...")
        
        # Strategy selection target based on performance ranking
        if 'strategy_name' in df.columns and 'roi' in df.columns:
            df = df.with_columns([
                pl.col('roi').rank(method='ordinal', descending=True).over('strategy_name').alias('roi_rank'),
                (pl.col('roi') > pl.col('roi').median()).alias('above_median_roi')
            ])
            
            # Best strategy selection (top 20% performers)
            df = df.with_columns(
                (pl.col('roi_rank') <= pl.col('roi_rank').max() * 0.2).alias('is_top_strategy')
            )
        
        # Optimal holding period based on historical data
        if all(col in df.columns for col in ['avg_holding_period', 'roi']):
            df = df.with_columns([
                pl.col('avg_holding_period').clip(0.5, 24.0).alias('optimal_holding_hours'),
                (pl.col('avg_holding_period') < 6.0).alias('is_intraday_optimal')
            ])
        
        # Risk-reward optimization targets
        if all(col in df.columns for col in ['roi', 'max_drawdown']):
            df = df.with_columns([
                (pl.col('roi') / pl.col('max_drawdown').abs().clip(0.01)).alias('actual_risk_reward'),
                ((pl.col('roi') / pl.col('max_drawdown').abs().clip(0.01)) > 2.0).alias('good_risk_reward')
            ])
        
        return df
    
    def prepare_features(self, X, y, task_name: str, task_type: str):
        """Prepare features with scaling, imputation, and selection"""
        
        # Feature scaling
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers[task_name] = scaler
        
        # Advanced imputation if enabled
        if self.config.advanced_imputation:
            imputer = KNNImputer(n_neighbors=5)
            X_scaled = imputer.fit_transform(X_scaled)
            self.imputers[task_name] = imputer
        
        # Feature selection
        if self.config.feature_selection_enabled:
            selector = SelectKBest(
                score_func=f_classif if task_type == "classification" else f_regression,
                k=min(self.config.max_features, X_scaled.shape[1])
            )
            X_scaled = selector.fit_transform(X_scaled, y)
            self.feature_selectors[task_name] = selector
        
        return X_scaled
    
    def prepare_prediction_features(self, features: dict, task_name: str) -> np.ndarray:
        """Enhancement 7: Robust prediction feature preparation with proper imputation"""
        
        try:
            if task_name not in self.scalers:
                logger.error(f"No scaler found for task: {task_name}")
                return None
            
            scaler = self.scalers[task_name]
            feature_names = scaler.feature_names_in_ if hasattr(scaler, 'feature_names_in_') else None
            
            if feature_names is None:
                logger.error(f"No feature names available for task: {task_name}")
                return None
            
            # Create feature vector with proper handling of missing features
            feature_vector = []
            for feature in feature_names:
                if feature in features:
                    feature_vector.append(features[feature])
                else:
                    # Use mean from training data stored in scaler
                    if hasattr(scaler, 'mean_'):
                        feature_idx = list(feature_names).index(feature)
                        imputed_value = scaler.mean_[feature_idx]
                    else:
                        imputed_value = 0.0
                    
                    feature_vector.append(imputed_value)
                    logger.warning(f"Missing feature '{feature}' imputed with training mean")
            
            # Convert to numpy array and reshape
            feature_array = np.array(feature_vector).reshape(1, -1)
            
            # Apply scaling
            feature_array = scaler.transform(feature_array)
            
            # Apply imputation if available
            if task_name in self.imputers:
                feature_array = self.imputers[task_name].transform(feature_array)
            
            # Apply feature selection if available
            if task_name in self.feature_selectors:
                feature_array = self.feature_selectors[task_name].transform(feature_array)
            
            return feature_array
            
        except Exception as e:
            logger.error(f"Feature preparation failed for {task_name}: {e}")
            return None
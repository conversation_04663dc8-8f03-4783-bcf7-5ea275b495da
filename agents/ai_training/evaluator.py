"""
Model evaluation module
Enhancement 4: Enhanced Evaluation Metrics for Classification
"""

import logging
import numpy as np
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, mean_squared_error, mean_absolute_error, 
    r2_score, mean_absolute_percentage_error
)
from typing import Dict, Any

logger = logging.getLogger(__name__)

class ModelEvaluator:
    """Comprehensive model evaluation with advanced metrics for imbalanced datasets"""
    
    def __init__(self, config):
        self.config = config
    
    def evaluate_comprehensive(self, model, X_test: np.ndarray, 
                              y_test: np.ndarray, task_type: str) -> Dict[str, float]:
        """Comprehensive model evaluation with advanced metrics"""
        
        predictions = model.predict(X_test)
        metrics = {}
        
        if task_type == "classification":
            # Basic metrics
            metrics["accuracy"] = accuracy_score(y_test, predictions)
            metrics["precision"] = precision_score(y_test, predictions, average='weighted', zero_division=0)
            metrics["recall"] = recall_score(y_test, predictions, average='weighted', zero_division=0)
            metrics["f1_score"] = f1_score(y_test, predictions, average='weighted', zero_division=0)
            
            # Advanced metrics for imbalanced datasets
            if hasattr(model, 'predict_proba'):
                try:
                    probabilities = model.predict_proba(X_test)
                    if probabilities.shape[1] == 2:  # Binary classification
                        metrics["roc_auc"] = roc_auc_score(y_test, probabilities[:, 1])
                    else:  # Multi-class
                        metrics["roc_auc"] = roc_auc_score(y_test, probabilities, multi_class='ovr')
                except Exception as e:
                    logger.warning(f"Could not calculate ROC-AUC: {e}")
                    metrics["roc_auc"] = 0.0
            
            # Class-specific metrics for imbalanced data
            unique_classes = np.unique(y_test)
            for cls in unique_classes:
                cls_precision = precision_score(y_test == cls, predictions == cls, zero_division=0)
                cls_recall = recall_score(y_test == cls, predictions == cls, zero_division=0)
                cls_f1 = f1_score(y_test == cls, predictions == cls, zero_division=0)
                
                metrics[f"precision_class_{cls}"] = cls_precision
                metrics[f"recall_class_{cls}"] = cls_recall
                metrics[f"f1_class_{cls}"] = cls_f1
        
        else:  # Regression
            metrics["r2_score"] = r2_score(y_test, predictions)
            metrics["mse"] = mean_squared_error(y_test, predictions)
            metrics["rmse"] = np.sqrt(metrics["mse"])
            metrics["mae"] = mean_absolute_error(y_test, predictions)
            
            # Additional regression metrics
            try:
                metrics["mape"] = mean_absolute_percentage_error(y_test, predictions)
            except:
                metrics["mape"] = float('inf')
            
            # Explained variance
            metrics["explained_variance"] = 1 - np.var(y_test - predictions) / np.var(y_test)
            
            # Mean absolute scaled error (for time series)
            naive_forecast = np.mean(y_test)
            mae_naive = mean_absolute_error(y_test, np.full_like(y_test, naive_forecast))
            metrics["mase"] = metrics["mae"] / mae_naive if mae_naive > 0 else float('inf')
        
        return metrics
    
    def evaluate_ensemble(self, ensemble, X_test: np.ndarray, y_test: np.ndarray, 
                         task_type: str) -> Dict[str, float]:
        """Evaluate ensemble model with proper prediction handling"""
        
        if isinstance(ensemble, dict):
            # Custom ensemble types (blending, weighted)
            from .ensemble_creator import EnsembleCreator
            creator = EnsembleCreator(self.config)
            predictions = creator.predict_ensemble(ensemble, X_test)
            
            # Create a mock model object for evaluation
            class MockModel:
                def __init__(self, predictions):
                    self.predictions = predictions
                
                def predict(self, X):
                    return self.predictions
            
            mock_model = MockModel(predictions)
            return self.evaluate_comprehensive(mock_model, X_test, y_test, task_type)
        
        else:
            # Standard sklearn ensemble
            return self.evaluate_comprehensive(ensemble, X_test, y_test, task_type)
    
    def compare_models(self, model_results: Dict[str, Dict[str, float]], 
                      task_type: str) -> Dict[str, Any]:
        """Compare multiple models and rank them"""
        
        primary_metric = "f1_score" if task_type == "classification" else "r2_score"
        
        # Rank models by primary metric
        rankings = []
        for model_name, metrics in model_results.items():
            if primary_metric in metrics:
                rankings.append({
                    "model": model_name,
                    "score": metrics[primary_metric],
                    "metrics": metrics
                })
        
        # Sort by score (descending)
        rankings.sort(key=lambda x: x["score"], reverse=True)
        
        return {
            "rankings": rankings,
            "best_model": rankings[0]["model"] if rankings else None,
            "best_score": rankings[0]["score"] if rankings else 0.0,
            "primary_metric": primary_metric
        }
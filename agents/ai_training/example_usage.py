#!/usr/bin/env python3
"""
Example usage of the Enhanced AI Training System
Demonstrates all implemented enhancements
"""

import asyncio
import logging
from pathlib import Path

from .config import EnhancedAITrainingConfig
from .main_agent import EnhancedAITrainingAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Example usage of the enhanced AI training system"""
    
    try:
        # Initialize configuration with all enhancements enabled
        config = EnhancedAITrainingConfig(
            # Data configuration
            data_dir="data/backtest",
            input_file="enhanced_strategy_results.parquet",
            
            # Enable all enhancements
            hyperopt_all_models=True,
            ensemble_method="stacking",  # Advanced ensemble method
            use_advanced_metrics=True,   # Enhanced evaluation metrics
            feature_selection_enabled=True,
            outlier_detection_enabled=True,
            advanced_imputation=True,
            
            # Model configuration
            enabled_models=["lightgbm", "xgboost", "catboost", "tabnet", "mlp", "sgd"],
            optuna_trials=30,  # Reduced for faster demo
            
            # Hardware optimization
            n_jobs=-1,
            use_gpu=True
        )
        
        # Initialize enhanced agent
        agent = EnhancedAITrainingAgent(config)
        
        # Train models with all enhancements
        logger.info("Starting enhanced AI training with all improvements...")
        results = await agent.train_enhanced_models()
        
        # Display results
        print("\n" + "="*80)
        print("ENHANCED AI TRAINING RESULTS")
        print("="*80)
        print(f"Status: {results['status']}")
        
        if results['status'] == 'success':
            print(f"Tasks trained: {results.get('tasks_trained', 0)}")
            print(f"Models per task: {results.get('models_per_task', 0)}")
            print(f"Ensemble method: {results.get('ensemble_method', 'N/A')}")
            print(f"Hyperparameter optimization: {results.get('hyperopt_enabled', False)}")
            
            print("\n[ENHANCEMENTS IMPLEMENTED]")
            print("✅ 1. Robust save_models method with batch/online handling")
            print("✅ 2. Advanced ensemble methods (stacking/blending)")
            print("✅ 3. Comprehensive hyperparameter optimization")
            print("✅ 4. Enhanced evaluation metrics (F1, Precision, Recall, ROC-AUC)")
            print("✅ 5. Concrete meta-learning targets from historical data")
            print("✅ 6. Consistent Polars data handling with minimal conversions")
            print("✅ 7. Robust prediction feature preparation with proper imputation")
            print("✅ 8. Dynamic class handling for SGDClassifier")
            print("✅ 9. Advanced preprocessing with outlier detection and feature selection")
            
            # Demonstrate prediction capabilities
            print("\n[PREDICTION DEMO]")
            sample_features = {
                "roi": 0.15,
                "sharpe_ratio": 1.2,
                "max_drawdown": -0.08,
                "total_trades": 50,
                "winning_trades": 32,
                "expectancy": 0.02
            }
            
            predictions = agent.predict(sample_features)
            print(f"Sample prediction results: {len(predictions)} tasks predicted")
            
            if "meta_predictions" in predictions:
                print("Meta-learning predictions available!")
        
        else:
            print(f"Training failed: {results.get('error', 'Unknown error')}")
        
        print("="*80)
        
    except Exception as e:
        logger.error(f"Example execution failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
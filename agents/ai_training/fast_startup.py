#!/usr/bin/env python3
"""
Fast Startup Module for AI Training Agent
Optimizes initialization time by deferring heavy imports and operations
"""

import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class FastStartupManager:
    """Manages fast startup and lazy loading for AI training components"""
    
    def __init__(self):
        self._imports_loaded = {
            'ml_libs': False,
            'gpu_libs': False,
            'explainability': False,
            'ensemble': False
        }
        self._gpu_available = None
        
    def lazy_import_ml_libs(self):
        """Lazy import ML libraries only when needed"""
        if self._imports_loaded['ml_libs']:
            return
            
        try:
            # Core ML imports
            import lightgbm
            import xgboost
            import optuna
            from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
            
            # Store in globals for access
            globals()['lgb'] = lightgbm
            globals()['xgb'] = xgboost
            globals()['optuna'] = optuna
            globals()['RandomForestRegressor'] = RandomForestRegressor
            globals()['RandomForestClassifier'] = RandomForestClassifier
            
            self._imports_loaded['ml_libs'] = True
            logger.info("[STARTUP] ML libraries loaded")
            
        except ImportError as e:
            logger.warning(f"[STARTUP] Failed to load ML libraries: {e}")
    
    def lazy_import_gpu_libs(self):
        """Lazy import GPU libraries only when needed"""
        if self._imports_loaded['gpu_libs']:
            return
            
        try:
            import torch
            from pytorch_tabnet.tab_model import TabNetRegressor, TabNetClassifier
            
            globals()['torch'] = torch
            globals()['TabNetRegressor'] = TabNetRegressor
            globals()['TabNetClassifier'] = TabNetClassifier
            
            self._gpu_available = torch.cuda.is_available()
            self._imports_loaded['gpu_libs'] = True
            logger.info(f"[STARTUP] GPU libraries loaded (CUDA: {self._gpu_available})")
            
        except ImportError as e:
            logger.warning(f"[STARTUP] Failed to load GPU libraries: {e}")
            self._gpu_available = False
    
    def lazy_import_explainability(self):
        """Lazy import explainability libraries"""
        if self._imports_loaded['explainability']:
            return
            
        try:
            import shap
            import lime
            from lime.lime_tabular import LimeTabularExplainer
            
            globals()['shap'] = shap
            globals()['lime'] = lime
            globals()['LimeTabularExplainer'] = LimeTabularExplainer
            
            self._imports_loaded['explainability'] = True
            logger.info("[STARTUP] Explainability libraries loaded")
            
        except ImportError as e:
            logger.warning(f"[STARTUP] Explainability libraries not available: {e}")
    
    def check_gpu_availability(self) -> bool:
        """Check GPU availability with caching"""
        if self._gpu_available is None:
            self.lazy_import_gpu_libs()
        return self._gpu_available or False
    
    def get_minimal_config(self) -> Dict[str, Any]:
        """Get minimal configuration for fast startup"""
        return {
            'enabled_models': ['lightgbm'],  # Start with just LightGBM
            'hyperopt_enabled': False,       # Disable hyperopt for fast startup
            'shap_enabled': False,           # Disable SHAP for fast startup
            'lime_enabled': False,           # Disable LIME for fast startup
            'model_versioning': False,       # Disable versioning for fast startup
            'prediction_server_enabled': False,  # Disable server for fast startup
            'use_gpu': False,                # Disable GPU for fast startup
            'cv_folds': 3,                   # Reduce CV folds
            'optuna_trials': 10              # Reduce Optuna trials
        }
    
    def setup_minimal_directories(self, base_dir: str):
        """Setup only essential directories"""
        essential_dirs = [
            base_dir,
            os.path.join(base_dir, "models"),
            os.path.join(base_dir, "checkpoints")
        ]
        
        for directory in essential_dirs:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def is_large_dataset(self, file_path: str, threshold_gb: float = 0.5) -> bool:
        """Check if dataset is large enough to warrant streaming"""
        try:
            if not os.path.exists(file_path):
                return False
            
            file_size_gb = os.path.getsize(file_path) / (1024**3)
            return file_size_gb > threshold_gb
            
        except Exception:
            return False

# Global instance for fast startup management
startup_manager = FastStartupManager()

def get_startup_manager() -> FastStartupManager:
    """Get the global startup manager instance"""
    return startup_manager

def enable_fast_startup_mode():
    """Enable fast startup mode with minimal configuration"""
    logger.info("[STARTUP] Fast startup mode enabled")
    return startup_manager.get_minimal_config()

def lazy_load_component(component: str):
    """Lazy load a specific component"""
    if component == 'ml_libs':
        startup_manager.lazy_import_ml_libs()
    elif component == 'gpu_libs':
        startup_manager.lazy_import_gpu_libs()
    elif component == 'explainability':
        startup_manager.lazy_import_explainability()
    else:
        logger.warning(f"[STARTUP] Unknown component: {component}")
"""
Hyperparameter optimization module
Enhancement 3: Comprehensive Hyperparameter Optimization
"""

import logging
import numpy as np
import optuna
from sklearn.model_selection import cross_val_score
from typing import Dict, Any

logger = logging.getLogger(__name__)

class HyperparameterOptimizer:
    """Comprehensive hyperparameter optimization for all enabled models"""
    
    def __init__(self, config):
        self.config = config
    
    def optimize_all_models(self, X: np.ndarray, y: np.ndarray, 
                           task_type: str, enabled_models: list) -> Dict[str, Dict[str, Any]]:
        """Comprehensive hyperparameter optimization for all enabled models"""
        
        logger.info("Starting comprehensive hyperparameter optimization...")
        
        best_params = {}
        
        for model_name in enabled_models:
            logger.info(f"Optimizing {model_name}...")
            
            try:
                study = optuna.create_study(direction='maximize')
                
                def objective(trial):
                    return self._optuna_objective(
                        trial, model_name, X, y, task_type
                    )
                
                study.optimize(
                    objective, 
                    n_trials=self.config.optuna_trials,
                    timeout=self.config.optuna_timeout // len(enabled_models)
                )
                
                best_params[model_name] = study.best_params
                logger.info(f"{model_name} optimization completed - Best score: {study.best_value:.4f}")
                
            except Exception as e:
                logger.warning(f"Optimization failed for {model_name}: {e}")
                best_params[model_name] = self._get_default_params(model_name)
        
        return best_params
    
    def _optuna_objective(self, trial, model_name: str, X: np.ndarray, 
                         y: np.ndarray, task_type: str) -> float:
        """Unified Optuna objective for all model types"""
        
        # Define hyperparameter search spaces
        if model_name == "lightgbm":
            params = {
                "n_estimators": trial.suggest_int("n_estimators", 100, 1000),
                "max_depth": trial.suggest_int("max_depth", 3, 15),
                "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
                "num_leaves": trial.suggest_int("num_leaves", 10, 300),
                "feature_fraction": trial.suggest_float("feature_fraction", 0.4, 1.0),
                "bagging_fraction": trial.suggest_float("bagging_fraction", 0.4, 1.0),
                "min_child_samples": trial.suggest_int("min_child_samples", 5, 100),
            }
        elif model_name == "xgboost":
            params = {
                "n_estimators": trial.suggest_int("n_estimators", 100, 1000),
                "max_depth": trial.suggest_int("max_depth", 3, 15),
                "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
                "subsample": trial.suggest_float("subsample", 0.5, 1.0),
                "colsample_bytree": trial.suggest_float("colsample_bytree", 0.5, 1.0),
                "reg_alpha": trial.suggest_float("reg_alpha", 0, 10),
                "reg_lambda": trial.suggest_float("reg_lambda", 0, 10),
            }
        elif model_name == "catboost":
            params = {
                "iterations": trial.suggest_int("iterations", 100, 1000),
                "depth": trial.suggest_int("depth", 3, 10),
                "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
                "l2_leaf_reg": trial.suggest_float("l2_leaf_reg", 1, 10),
                "border_count": trial.suggest_int("border_count", 32, 255),
            }
        elif model_name == "tabnet":
            params = {
                "n_d": trial.suggest_int("n_d", 8, 64),
                "n_a": trial.suggest_int("n_a", 8, 64),
                "n_steps": trial.suggest_int("n_steps", 3, 10),
                "gamma": trial.suggest_float("gamma", 1.0, 2.0),
                "lambda_sparse": trial.suggest_float("lambda_sparse", 1e-6, 1e-3),
            }
        elif model_name == "mlp":
            params = {
                "hidden_layer_sizes": trial.suggest_categorical(
                    "hidden_layer_sizes", 
                    [(50,), (100,), (50, 50), (100, 50), (100, 100), (200, 100, 50)]
                ),
                "alpha": trial.suggest_float("alpha", 1e-5, 1e-1),
                "learning_rate_init": trial.suggest_float("learning_rate_init", 1e-4, 1e-1),
            }
        elif model_name == "sgd":
            params = {
                "alpha": trial.suggest_float("alpha", 1e-6, 1e-1),
                "learning_rate": trial.suggest_categorical("learning_rate", ["constant", "optimal", "adaptive"]),
                "eta0": trial.suggest_float("eta0", 1e-4, 1e-1),
            }
        else:
            params = {}
        
        # Create and evaluate model
        from .model_trainer import ModelTrainer
        trainer = ModelTrainer(self.config)
        model = trainer.create_model(model_name, task_type, params)
        
        # Cross-validation with appropriate scoring
        scoring = 'f1_weighted' if task_type == 'classification' else 'r2'
        cv_scores = cross_val_score(model, X, y, cv=3, scoring=scoring, n_jobs=1)
        
        return cv_scores.mean()
    
    def _get_default_params(self, model_name: str) -> Dict[str, Any]:
        """Get default parameters for a model"""
        
        defaults = {
            "lightgbm": {"n_estimators": 100, "max_depth": 6, "learning_rate": 0.1},
            "xgboost": {"n_estimators": 100, "max_depth": 6, "learning_rate": 0.1},
            "catboost": {"iterations": 100, "depth": 6, "learning_rate": 0.1},
            "tabnet": {"n_d": 32, "n_a": 32, "n_steps": 3},
            "mlp": {"hidden_layer_sizes": (100, 50), "alpha": 0.001},
            "sgd": {"alpha": 0.0001, "learning_rate": "optimal"}
        }
        
        return defaults.get(model_name, {})
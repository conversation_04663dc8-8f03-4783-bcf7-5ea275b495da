"""
Logging configuration for AI Training
Provides clean, structured logging with warning suppression
"""

import logging
import warnings
import os
import sys
from typing import Optional

class CleanFormatter(logging.Formatter):
    """Custom formatter for cleaner output"""
    
    def __init__(self):
        super().__init__()
        
    def format(self, record):
        # Color codes for different log levels
        colors = {
            'DEBUG': '\033[36m',    # Cyan
            'INFO': '\033[32m',     # Green
            'WARNING': '\033[33m',  # Yellow
            'ERROR': '\033[31m',    # Red
            'CRITICAL': '\033[35m'  # Magenta
        }
        
        reset = '\033[0m'
        
        # Get color for log level
        color = colors.get(record.levelname, '')
        
        # Format timestamp
        timestamp = self.formatTime(record, '%H:%M:%S')
        
        # Create clean format
        if record.levelname == 'INFO':
            # For INFO messages, show cleaner format
            if 'Score:' in record.getMessage():
                return f"{color}  ✓ {record.getMessage()}{reset}"
            elif 'Training' in record.getMessage():
                return f"{color}🔄 {record.getMessage()}{reset}"
            elif 'SUCCESS' in record.getMessage():
                return f"{color}✅ {record.getMessage()}{reset}"
            elif 'ERROR' in record.getMessage():
                return f"\033[31m❌ {record.getMessage()}{reset}"
            else:
                return f"{color}ℹ️  {record.getMessage()}{reset}"
        else:
            return f"{color}[{record.levelname}] {record.getMessage()}{reset}"

def setup_clean_logging(level: str = "INFO", suppress_warnings: bool = True):
    """Setup clean logging configuration"""
    
    if suppress_warnings:
        # Comprehensive warning suppression
        warnings.filterwarnings('ignore')
        os.environ['PYTHONWARNINGS'] = 'ignore'
        
        # Suppress specific library warnings
        warnings.filterwarnings('ignore', category=UserWarning)
        warnings.filterwarnings('ignore', category=FutureWarning)
        warnings.filterwarnings('ignore', category=DeprecationWarning)
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        
        # Suppress specific library messages
        warnings.filterwarnings('ignore', module='lightgbm')
        warnings.filterwarnings('ignore', module='xgboost')
        warnings.filterwarnings('ignore', module='catboost')
        warnings.filterwarnings('ignore', module='cupy')
        warnings.filterwarnings('ignore', module='pytorch_tabnet')
        
        # Set environment variables
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
        os.environ['CUDA_LAUNCH_BLOCKING'] = '0'
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler with clean formatter
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(CleanFormatter())
    
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers to reduce noise
    noisy_loggers = [
        'lightgbm',
        'xgboost', 
        'catboost',
        'pytorch_tabnet',
        'cupy',
        'numba',
        'matplotlib',
        'PIL'
    ]
    
    for logger_name in noisy_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.WARNING)
        logger.propagate = False

def suppress_library_output():
    """Suppress output from ML libraries"""
    
    # Redirect stdout for specific operations
    class SuppressOutput:
        def __enter__(self):
            self._original_stdout = sys.stdout
            self._original_stderr = sys.stderr
            sys.stdout = open(os.devnull, 'w')
            sys.stderr = open(os.devnull, 'w')
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            sys.stdout.close()
            sys.stderr.close()
            sys.stdout = self._original_stdout
            sys.stderr = self._original_stderr
    
    return SuppressOutput()

class ProgressLogger:
    """Clean progress logging for training"""
    
    def __init__(self, total_tasks: int):
        self.total_tasks = total_tasks
        self.current_task = 0
        self.logger = logging.getLogger(__name__)
    
    def start_task(self, task_name: str):
        """Start a new task"""
        self.current_task += 1
        progress = (self.current_task / self.total_tasks) * 100
        self.logger.info(f"[{self.current_task}/{self.total_tasks}] {task_name} ({progress:.1f}%)")
    
    def log_model_result(self, model_name: str, score: float):
        """Log model training result"""
        self.logger.info(f"  {model_name} - Score: {score:.4f}")
    
    def complete_task(self, task_name: str, duration: Optional[float] = None):
        """Complete a task"""
        if duration:
            self.logger.info(f"✅ {task_name} completed in {duration:.1f}s")
        else:
            self.logger.info(f"✅ {task_name} completed")

# Global setup function
def configure_ai_training_logging():
    """Configure logging for AI training with clean output"""
    setup_clean_logging(level="INFO", suppress_warnings=True)
    
    # Additional environment setup
    os.environ['NUMBA_DISABLE_JIT'] = '0'  # Keep JIT enabled but quiet
    os.environ['NUMBA_WARNINGS'] = '0'     # Suppress Numba warnings
    
    logger = logging.getLogger(__name__)
    logger.info("🚀 AI Training logging configured - clean output mode enabled")

# Auto-configure when imported
if __name__ != "__main__":
    configure_ai_training_logging()

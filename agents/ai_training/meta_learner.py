"""
Meta-learning module
Enhancement 5: Concrete Targets for Meta-Learning Models
"""

import logging
import numpy as np
import polars as pl
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, Ridge
from typing import Dict, Any

logger = logging.getLogger(__name__)

class MetaLearner:
    """Meta-learning models with concrete targets from historical performance"""
    
    def __init__(self, config):
        self.config = config
        self.meta_models = {}
    
    def train_meta_models(self, df: pl.DataFrame, task_predictions: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Train meta-models with concrete targets from historical performance"""
        
        logger.info("Training meta-models with concrete targets...")
        
        meta_results = {}
        
        try:
            # Create meta-features from task predictions
            meta_features = self._create_meta_features(task_predictions)
            
            if meta_features is None or len(meta_features) == 0:
                logger.warning("No meta-features available")
                return meta_results
            
            # Train strategy selection meta-model
            if 'is_top_strategy' in df.columns:
                strategy_model = self._train_strategy_selection_model(
                    meta_features, df['is_top_strategy'].to_numpy()
                )
                meta_results["strategy_selection"] = strategy_model
                self.meta_models["strategy_selection"] = strategy_model["model"]
            
            # Train holding period optimization model
            if 'optimal_holding_hours' in df.columns:
                holding_model = self._train_holding_period_model(
                    meta_features, df['optimal_holding_hours'].to_numpy()
                )
                meta_results["holding_period"] = holding_model
                self.meta_models["holding_period"] = holding_model["model"]
            
            # Train risk-reward optimization model
            if 'actual_risk_reward' in df.columns:
                rr_model = self._train_rr_optimization_model(
                    meta_features, df['actual_risk_reward'].to_numpy()
                )
                meta_results["risk_reward"] = rr_model
                self.meta_models["risk_reward"] = rr_model["model"]
            
            # Train regime classification model
            if 'best_regime' in df.columns:
                regime_model = self._train_regime_classification_model(
                    meta_features, df['best_regime'].to_numpy()
                )
                meta_results["regime_classification"] = regime_model
                self.meta_models["regime_classification"] = regime_model["model"]
            
            logger.info(f"Trained {len(meta_results)} meta-models")
            
        except Exception as e:
            logger.error(f"Meta-models training failed: {e}")
            meta_results = {"error": str(e)}
        
        return meta_results
    
    def _create_meta_features(self, task_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """Create meta-features from individual task predictions"""
        
        try:
            meta_features_list = []
            
            for task_name, predictions in task_predictions.items():
                if predictions is not None and len(predictions) > 0:
                    # Add predictions as meta-features
                    if predictions.ndim == 1:
                        meta_features_list.append(predictions.reshape(-1, 1))
                    else:
                        meta_features_list.append(predictions)
            
            if meta_features_list:
                meta_features = np.hstack(meta_features_list)
                logger.info(f"Created meta-features: {meta_features.shape}")
                return meta_features
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to create meta-features: {e}")
            return None
    
    def _train_strategy_selection_model(self, meta_features: np.ndarray, 
                                       targets: np.ndarray) -> Dict[str, Any]:
        """Train meta-model for optimal strategy selection with concrete targets"""
        
        try:
            # Filter out invalid targets
            valid_mask = ~np.isnan(targets) & ~np.isinf(targets)
            if not np.any(valid_mask):
                raise ValueError("No valid targets for strategy selection")
            
            X_meta = meta_features[valid_mask]
            y_meta = targets[valid_mask].astype(bool)  # Convert to boolean
            
            # Train classification model
            model = RandomForestClassifier(
                n_estimators=100,
                random_state=self.config.random_state,
                n_jobs=self.config.n_jobs
            )
            
            model.fit(X_meta, y_meta)
            
            # Calculate accuracy
            accuracy = model.score(X_meta, y_meta)
            
            return {
                "model": model,
                "accuracy": accuracy,
                "n_samples": len(X_meta),
                "feature_importance": model.feature_importances_.tolist()
            }
            
        except Exception as e:
            logger.error(f"Strategy selection model training failed: {e}")
            return {"error": str(e)}
    
    def _train_holding_period_model(self, meta_features: np.ndarray, 
                                   targets: np.ndarray) -> Dict[str, Any]:
        """Train meta-model for optimal holding period with concrete targets"""
        
        try:
            # Filter out invalid targets
            valid_mask = ~np.isnan(targets) & ~np.isinf(targets) & (targets > 0)
            if not np.any(valid_mask):
                raise ValueError("No valid targets for holding period")
            
            X_meta = meta_features[valid_mask]
            y_meta = targets[valid_mask]
            
            # Train regression model
            model = RandomForestRegressor(
                n_estimators=100,
                random_state=self.config.random_state,
                n_jobs=self.config.n_jobs
            )
            
            model.fit(X_meta, y_meta)
            
            # Calculate R² score
            r2_score = model.score(X_meta, y_meta)
            
            return {
                "model": model,
                "r2_score": r2_score,
                "n_samples": len(X_meta),
                "feature_importance": model.feature_importances_.tolist()
            }
            
        except Exception as e:
            logger.error(f"Holding period model training failed: {e}")
            return {"error": str(e)}
    
    def _train_rr_optimization_model(self, meta_features: np.ndarray, 
                                    targets: np.ndarray) -> Dict[str, Any]:
        """Train meta-model for optimal risk-reward ratio with concrete targets"""
        
        try:
            # Filter out invalid targets
            valid_mask = ~np.isnan(targets) & ~np.isinf(targets) & (targets > 0)
            if not np.any(valid_mask):
                raise ValueError("No valid targets for risk-reward optimization")
            
            X_meta = meta_features[valid_mask]
            y_meta = targets[valid_mask]
            
            # Train regression model
            model = RandomForestRegressor(
                n_estimators=100,
                random_state=self.config.random_state,
                n_jobs=self.config.n_jobs
            )
            
            model.fit(X_meta, y_meta)
            
            # Calculate R² score
            r2_score = model.score(X_meta, y_meta)
            
            return {
                "model": model,
                "r2_score": r2_score,
                "n_samples": len(X_meta),
                "feature_importance": model.feature_importances_.tolist()
            }
            
        except Exception as e:
            logger.error(f"Risk-reward optimization model training failed: {e}")
            return {"error": str(e)}
    
    def _train_regime_classification_model(self, meta_features: np.ndarray, 
                                          targets: np.ndarray) -> Dict[str, Any]:
        """Train meta-model for market regime classification with concrete targets"""
        
        try:
            # Filter out invalid targets
            valid_mask = targets != None
            if not np.any(valid_mask):
                raise ValueError("No valid targets for regime classification")
            
            X_meta = meta_features[valid_mask]
            y_meta = targets[valid_mask]
            
            # Encode string targets
            from sklearn.preprocessing import LabelEncoder
            le = LabelEncoder()
            y_encoded = le.fit_transform(y_meta)
            
            # Train classification model
            model = RandomForestClassifier(
                n_estimators=100,
                random_state=self.config.random_state,
                n_jobs=self.config.n_jobs
            )
            
            model.fit(X_meta, y_encoded)
            
            # Calculate accuracy
            accuracy = model.score(X_meta, y_encoded)
            
            return {
                "model": model,
                "label_encoder": le,
                "accuracy": accuracy,
                "n_samples": len(X_meta),
                "classes": le.classes_.tolist(),
                "feature_importance": model.feature_importances_.tolist()
            }
            
        except Exception as e:
            logger.error(f"Regime classification model training failed: {e}")
            return {"error": str(e)}
    
    def predict_meta(self, meta_features: np.ndarray) -> Dict[str, Any]:
        """Generate meta-predictions using trained meta-models"""
        
        predictions = {}
        
        try:
            # Strategy selection
            if "strategy_selection" in self.meta_models:
                model = self.meta_models["strategy_selection"]
                pred = model.predict(meta_features)[0]
                prob = model.predict_proba(meta_features)[0] if hasattr(model, 'predict_proba') else None
                
                predictions["recommended_strategy"] = {
                    "is_top_strategy": bool(pred),
                    "confidence": float(np.max(prob)) if prob is not None else 0.5
                }
            
            # Holding period
            if "holding_period" in self.meta_models:
                model = self.meta_models["holding_period"]
                pred = model.predict(meta_features)[0]
                
                predictions["optimal_holding_period"] = {
                    "hours": float(pred),
                    "recommendation": "intraday" if pred < 6.0 else "overnight"
                }
            
            # Risk-reward ratio
            if "risk_reward" in self.meta_models:
                model = self.meta_models["risk_reward"]
                pred = model.predict(meta_features)[0]
                
                predictions["optimal_risk_reward"] = {
                    "ratio": float(pred),
                    "recommendation": "conservative" if pred < 2.0 else "aggressive"
                }
            
            # Regime classification
            if "regime_classification" in self.meta_models:
                model = self.meta_models["regime_classification"]
                pred = model.predict(meta_features)[0]
                
                predictions["market_regime"] = {
                    "regime": str(pred),
                    "confidence": 0.8  # Placeholder
                }
            
        except Exception as e:
            logger.error(f"Meta-prediction failed: {e}")
            predictions["error"] = str(e)
        
        return predictions
"""
Model persistence module
Enhancement 1: Refactor save_models Method
"""

import logging
import json
import joblib
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

logger = logging.getLogger(__name__)

class ModelPersistence:
    """Robust model saving and loading handling both batch and online models"""
    
    def __init__(self, config):
        self.config = config
    
    def save_models(self, models: Dict[str, Any], ensemble_models: Dict[str, Any],
                   meta_models: Dict[str, Any], online_models: Dict[str, Any],
                   preprocessing_objects: Dict[str, Any], metadata: Dict[str, Any],
                   model_name: str = "enhanced_ai_ensemble") -> None:
        """Enhancement 1: Robust model saving handling both batch and online models"""
        
        if not any([models, online_models, ensemble_models, meta_models]):
            logger.warning("No trained models to save")
            return
        
        logger.info(f"Saving models as: {model_name}")
        
        model_dir = Path(self.config.models_dir) / model_name
        model_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # Save batch models (task-based)
            if models:
                batch_dir = model_dir / "batch_models"
                batch_dir.mkdir(exist_ok=True)
                
                for task_name, task_models in models.items():
                    task_dir = batch_dir / task_name
                    task_dir.mkdir(exist_ok=True)
                    
                    for model_type, model in task_models.items():
                        model_path = task_dir / f"{model_type}.pkl"
                        joblib.dump(model, model_path)
                        logger.info(f"Saved batch model: {task_name}/{model_type}")
            
            # Save online models (SGD-based)
            if online_models:
                online_dir = model_dir / "online_models"
                online_dir.mkdir(exist_ok=True)
                
                for target_name, model in online_models.items():
                    model_path = online_dir / f"{target_name}.pkl"
                    joblib.dump(model, model_path)
                    logger.info(f"Saved online model: {target_name}")
            
            # Save ensemble models
            if ensemble_models:
                ensemble_dir = model_dir / "ensemble_models"
                ensemble_dir.mkdir(exist_ok=True)
                
                for task_name, ensemble in ensemble_models.items():
                    ensemble_path = ensemble_dir / f"{task_name}.pkl"
                    joblib.dump(ensemble, ensemble_path)
                    logger.info(f"Saved ensemble model: {task_name}")
            
            # Save meta-models
            if meta_models:
                meta_dir = model_dir / "meta_models"
                meta_dir.mkdir(exist_ok=True)
                
                for meta_name, meta_model in meta_models.items():
                    meta_path = meta_dir / f"{meta_name}.pkl"
                    joblib.dump(meta_model, meta_path)
                    logger.info(f"Saved meta-model: {meta_name}")
            
            # Save preprocessing objects
            for obj_name, obj_dict in preprocessing_objects.items():
                if obj_dict:
                    obj_path = model_dir / f"{obj_name}.pkl"
                    joblib.dump(obj_dict, obj_path)
                    logger.info(f"Saved {obj_name}")
            
            # Save training metadata
            metadata_path = model_dir / "metadata.json"
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            logger.info(f"Models and metadata saved successfully to: {model_dir}")
            
        except Exception as e:
            logger.error(f"Failed to save models: {e}")
            raise
    
    def load_models(self, model_name: str = "enhanced_ai_ensemble") -> Dict[str, Any]:
        """Load saved models with robust error handling"""
        
        model_dir = Path(self.config.models_dir) / model_name
        
        if not model_dir.exists():
            raise FileNotFoundError(f"Model directory not found: {model_dir}")
        
        logger.info(f"Loading models from: {model_dir}")
        
        loaded_objects = {
            "models": {},
            "online_models": {},
            "ensemble_models": {},
            "meta_models": {},
            "preprocessing": {},
            "metadata": {}
        }
        
        try:
            # Load batch models
            batch_dir = model_dir / "batch_models"
            if batch_dir.exists():
                for task_dir in batch_dir.iterdir():
                    if task_dir.is_dir():
                        task_name = task_dir.name
                        loaded_objects["models"][task_name] = {}
                        
                        for model_file in task_dir.glob("*.pkl"):
                            model_type = model_file.stem
                            loaded_objects["models"][task_name][model_type] = joblib.load(model_file)
                        
                        logger.info(f"Loaded batch models for task: {task_name}")
            
            # Load online models
            online_dir = model_dir / "online_models"
            if online_dir.exists():
                for model_file in online_dir.glob("*.pkl"):
                    target_name = model_file.stem
                    loaded_objects["online_models"][target_name] = joblib.load(model_file)
                
                logger.info(f"Loaded {len(loaded_objects['online_models'])} online models")
            
            # Load ensemble models
            ensemble_dir = model_dir / "ensemble_models"
            if ensemble_dir.exists():
                for ensemble_file in ensemble_dir.glob("*.pkl"):
                    task_name = ensemble_file.stem
                    loaded_objects["ensemble_models"][task_name] = joblib.load(ensemble_file)
                
                logger.info(f"Loaded {len(loaded_objects['ensemble_models'])} ensemble models")
            
            # Load meta-models
            meta_dir = model_dir / "meta_models"
            if meta_dir.exists():
                for meta_file in meta_dir.glob("*.pkl"):
                    meta_name = meta_file.stem
                    loaded_objects["meta_models"][meta_name] = joblib.load(meta_file)
                
                logger.info(f"Loaded {len(loaded_objects['meta_models'])} meta-models")
            
            # Load preprocessing objects
            preprocessing_files = {
                "scalers": "scalers.pkl",
                "encoders": "encoders.pkl", 
                "feature_selectors": "feature_selectors.pkl",
                "imputers": "imputers.pkl"
            }
            
            for attr_name, filename in preprocessing_files.items():
                file_path = model_dir / filename
                if file_path.exists():
                    loaded_objects["preprocessing"][attr_name] = joblib.load(file_path)
                    logger.info(f"Loaded {attr_name}")
            
            # Load metadata
            metadata_path = model_dir / "metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    loaded_objects["metadata"] = json.load(f)
                
                logger.info("Loaded training metadata")
            
            logger.info("Models loaded successfully")
            return loaded_objects
            
        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            raise
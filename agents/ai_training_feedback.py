#!/usr/bin/env python3
"""
[WORKFLOW] AI Training Feedback Integration Module
Handles feedback collection and continual learning for the AI Training Agent
"""

import asyncio
import logging
import numpy as np
import polars as pl
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import json
import pickle
from dataclasses import dataclass
from scipy import stats

logger = logging.getLogger(__name__)

@dataclass
class FeedbackConfig:
    """Configuration for feedback integration"""
    
    # Feedback sources
    feedback_sources: List[str] = None
    feedback_dir: str = "data/feedback"
    
    # Drift detection
    drift_detection_enabled: bool = True
    drift_threshold: float = 0.05
    drift_window_size: int = 1000
    drift_check_frequency: str = "daily"  # "hourly", "daily", "weekly"
    
    # Performance monitoring
    performance_threshold: float = 0.1  # 10% performance drop triggers retraining
    min_feedback_samples: int = 100
    
    # Retraining triggers
    auto_retrain_enabled: bool = True
    retrain_on_drift: bool = True
    retrain_on_performance_drop: bool = True
    max_days_without_retrain: int = 30
    
    def __post_init__(self):
        if self.feedback_sources is None:
            self.feedback_sources = [
                "execution_agent",
                "performance_analysis_agent", 
                "market_monitoring_agent"
            ]

class AITrainingFeedbackIntegrator:
    """
    Feedback Integration System for AI Training Agent
    
    Handles:
    - Performance feedback collection from other agents
    - Concept drift detection
    - Automated retraining triggers
    - Model performance monitoring
    - Continual learning workflows
    """
    
    def __init__(self, config: Optional[FeedbackConfig] = None):
        """Initialize feedback integrator"""
        self.config = config or FeedbackConfig()
        self.feedback_history = []
        self.performance_history = []
        self.drift_detector = DriftDetector(self.config)
        self.performance_monitor = PerformanceMonitor(self.config)
        
        # Setup directories
        self._setup_directories()
        
        logger.info("[WORKFLOW] AI Training Feedback Integrator initialized")
    
    def _setup_directories(self):
        """Setup required directories"""
        Path(self.config.feedback_dir).mkdir(parents=True, exist_ok=True)
        Path(f"{self.config.feedback_dir}/drift_reports").mkdir(parents=True, exist_ok=True)
        Path(f"{self.config.feedback_dir}/performance_logs").mkdir(parents=True, exist_ok=True)
    
    async def collect_feedback(self, source: str, feedback_data: Dict[str, Any]) -> bool:
        """
        Collect feedback from various agents
        
        Args:
            source: Source agent name
            feedback_data: Feedback data containing predictions, outcomes, etc.
            
        Returns:
            Success status
        """
        
        try:
            # Validate feedback data
            if not self._validate_feedback(feedback_data):
                logger.warning(f"[WARN] Invalid feedback data from {source}")
                return False
            
            # Add metadata
            feedback_entry = {
                "timestamp": datetime.now().isoformat(),
                "source": source,
                "data": feedback_data
            }
            
            # Store feedback
            self.feedback_history.append(feedback_entry)
            
            # Save to disk
            await self._save_feedback(feedback_entry)
            
            # Check for drift and performance issues
            await self._process_feedback(feedback_entry)
            
            logger.info(f"[SUCCESS] Feedback collected from {source}")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to collect feedback from {source}: {e}")
            return False
    
    def _validate_feedback(self, feedback_data: Dict[str, Any]) -> bool:
        """Validate feedback data structure"""
        
        required_fields = ["predictions", "actual_outcomes"]
        
        for field in required_fields:
            if field not in feedback_data:
                return False
        
        # Check data types
        if not isinstance(feedback_data["predictions"], (list, dict)):
            return False
        
        if not isinstance(feedback_data["actual_outcomes"], (list, dict)):
            return False
        
        return True
    
    async def _save_feedback(self, feedback_entry: Dict[str, Any]):
        """Save feedback entry to disk"""
        
        try:
            timestamp = feedback_entry["timestamp"].replace(":", "-")
            source = feedback_entry["source"]
            
            filename = f"feedback_{source}_{timestamp}.json"
            filepath = Path(self.config.feedback_dir) / filename
            
            with open(filepath, 'w') as f:
                json.dump(feedback_entry, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to save feedback: {e}")
    
    async def _process_feedback(self, feedback_entry: Dict[str, Any]):
        """Process feedback for drift detection and performance monitoring"""
        
        try:
            # Extract predictions and outcomes
            predictions = feedback_entry["data"]["predictions"]
            outcomes = feedback_entry["data"]["actual_outcomes"]
            
            # Update drift detector
            if self.config.drift_detection_enabled:
                drift_detected = await self.drift_detector.check_drift(predictions, outcomes)
                
                if drift_detected:
                    logger.warning("🚨 Concept drift detected!")
                    await self._handle_drift_detection()
            
            # Update performance monitor
            performance_drop = await self.performance_monitor.check_performance(predictions, outcomes)
            
            if performance_drop:
                logger.warning("📉 Performance degradation detected!")
                await self._handle_performance_drop()
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to process feedback: {e}")
    
    async def _handle_drift_detection(self):
        """Handle detected concept drift"""
        
        try:
            # Log drift event
            drift_report = {
                "timestamp": datetime.now().isoformat(),
                "type": "concept_drift",
                "severity": "high",
                "action_required": "retrain_models"
            }
            
            # Save drift report
            report_file = Path(self.config.feedback_dir) / "drift_reports" / f"drift_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(drift_report, f, indent=2)
            
            # Trigger retraining if enabled
            if self.config.auto_retrain_enabled and self.config.retrain_on_drift:
                await self._trigger_retraining("concept_drift")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to handle drift detection: {e}")
    
    async def _handle_performance_drop(self):
        """Handle detected performance drop"""
        
        try:
            # Log performance event
            performance_report = {
                "timestamp": datetime.now().isoformat(),
                "type": "performance_drop",
                "severity": "medium",
                "action_required": "investigate_and_retrain"
            }
            
            # Save performance report
            report_file = Path(self.config.feedback_dir) / "performance_logs" / f"performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(performance_report, f, indent=2)
            
            # Trigger retraining if enabled
            if self.config.auto_retrain_enabled and self.config.retrain_on_performance_drop:
                await self._trigger_retraining("performance_drop")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to handle performance drop: {e}")
    
    async def _trigger_retraining(self, reason: str):
        """Trigger model retraining"""
        
        try:
            logger.info(f"[WORKFLOW] Triggering model retraining due to: {reason}")
            
            # Create retraining request
            retrain_request = {
                "timestamp": datetime.now().isoformat(),
                "reason": reason,
                "priority": "high" if reason == "concept_drift" else "medium",
                "feedback_samples": len(self.feedback_history)
            }
            
            # Save retraining request
            request_file = Path(self.config.feedback_dir) / f"retrain_request_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(request_file, 'w') as f:
                json.dump(retrain_request, f, indent=2)
            
            # Here you would integrate with the AI Training Agent to start retraining
            # For now, we'll just log the request
            logger.info(f"📝 Retraining request saved: {request_file}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to trigger retraining: {e}")
    
    async def get_feedback_summary(self) -> Dict[str, Any]:
        """Get summary of collected feedback"""
        
        try:
            total_feedback = len(self.feedback_history)
            
            if total_feedback == 0:
                return {"total_feedback": 0, "sources": [], "latest_feedback": None}
            
            # Count feedback by source
            source_counts = {}
            for feedback in self.feedback_history:
                source = feedback["source"]
                source_counts[source] = source_counts.get(source, 0) + 1
            
            # Get latest feedback
            latest_feedback = self.feedback_history[-1] if self.feedback_history else None
            
            # Get drift and performance status
            drift_status = await self.drift_detector.get_status()
            performance_status = await self.performance_monitor.get_status()
            
            summary = {
                "total_feedback": total_feedback,
                "sources": list(source_counts.keys()),
                "source_counts": source_counts,
                "latest_feedback": latest_feedback["timestamp"] if latest_feedback else None,
                "drift_status": drift_status,
                "performance_status": performance_status,
                "last_updated": datetime.now().isoformat()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get feedback summary: {e}")
            return {"error": str(e)}

class DriftDetector:
    """Concept drift detection for model predictions"""
    
    def __init__(self, config: FeedbackConfig):
        self.config = config
        self.reference_data = []
        self.current_data = []
        self.drift_history = []
    
    async def check_drift(self, predictions: Any, outcomes: Any) -> bool:
        """Check for concept drift using statistical tests"""
        
        try:
            # Convert to numpy arrays
            if isinstance(predictions, (list, dict)):
                pred_array = np.array(list(predictions.values()) if isinstance(predictions, dict) else predictions)
            else:
                pred_array = np.array(predictions)
            
            # Store current data
            self.current_data.extend(pred_array.flatten())
            
            # Keep only recent data
            if len(self.current_data) > self.config.drift_window_size:
                self.current_data = self.current_data[-self.config.drift_window_size:]
            
            # Initialize reference data if empty
            if not self.reference_data:
                self.reference_data = self.current_data.copy()
                return False
            
            # Perform drift detection if we have enough data
            if len(self.current_data) >= 100:  # Minimum samples for reliable test
                drift_detected = self._perform_drift_test()
                
                if drift_detected:
                    # Update reference data
                    self.reference_data = self.current_data.copy()
                    
                    # Log drift event
                    self.drift_history.append({
                        "timestamp": datetime.now().isoformat(),
                        "drift_detected": True
                    })
                
                return drift_detected
            
            return False
            
        except Exception as e:
            logger.error(f"[ERROR] Drift detection failed: {e}")
            return False
    
    def _perform_drift_test(self) -> bool:
        """Perform statistical test for drift detection"""
        
        try:
            # Use Kolmogorov-Smirnov test
            statistic, p_value = stats.ks_2samp(self.reference_data, self.current_data)
            
            # Drift detected if p-value is below threshold
            drift_detected = p_value < self.config.drift_threshold
            
            logger.info(f"[DEBUG] Drift test - Statistic: {statistic:.4f}, P-value: {p_value:.4f}, Drift: {drift_detected}")
            
            return drift_detected
            
        except Exception as e:
            logger.error(f"[ERROR] Drift test failed: {e}")
            return False
    
    async def get_status(self) -> Dict[str, Any]:
        """Get drift detector status"""
        
        return {
            "reference_samples": len(self.reference_data),
            "current_samples": len(self.current_data),
            "drift_events": len(self.drift_history),
            "last_drift": self.drift_history[-1]["timestamp"] if self.drift_history else None
        }

class PerformanceMonitor:
    """Monitor model performance over time"""
    
    def __init__(self, config: FeedbackConfig):
        self.config = config
        self.performance_history = []
        self.baseline_performance = None
    
    async def check_performance(self, predictions: Any, outcomes: Any) -> bool:
        """Check for performance degradation"""
        
        try:
            # Calculate current performance
            current_performance = self._calculate_performance(predictions, outcomes)
            
            if current_performance is None:
                return False
            
            # Store performance
            self.performance_history.append({
                "timestamp": datetime.now().isoformat(),
                "performance": current_performance
            })
            
            # Set baseline if not set
            if self.baseline_performance is None:
                self.baseline_performance = current_performance
                return False
            
            # Check for performance drop
            performance_drop = (self.baseline_performance - current_performance) / self.baseline_performance
            
            if performance_drop > self.config.performance_threshold:
                logger.warning(f"📉 Performance drop detected: {performance_drop:.3f}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"[ERROR] Performance monitoring failed: {e}")
            return False
    
    def _calculate_performance(self, predictions: Any, outcomes: Any) -> Optional[float]:
        """Calculate performance metric"""
        
        try:
            # Simple accuracy calculation for demonstration
            # In practice, this would be more sophisticated
            
            if isinstance(predictions, dict) and isinstance(outcomes, dict):
                pred_values = list(predictions.values())
                outcome_values = list(outcomes.values())
            else:
                pred_values = predictions
                outcome_values = outcomes
            
            # Convert to numpy arrays
            pred_array = np.array(pred_values)
            outcome_array = np.array(outcome_values)
            
            # Calculate accuracy (for classification) or correlation (for regression)
            if len(np.unique(outcome_array)) <= 10:  # Likely classification
                accuracy = np.mean(pred_array == outcome_array)
                return accuracy
            else:  # Likely regression
                correlation = np.corrcoef(pred_array, outcome_array)[0, 1]
                return correlation if not np.isnan(correlation) else 0.0
            
        except Exception as e:
            logger.error(f"[ERROR] Performance calculation failed: {e}")
            return None
    
    async def get_status(self) -> Dict[str, Any]:
        """Get performance monitor status"""
        
        recent_performance = None
        if self.performance_history:
            recent_performance = self.performance_history[-1]["performance"]
        
        return {
            "baseline_performance": self.baseline_performance,
            "recent_performance": recent_performance,
            "performance_samples": len(self.performance_history),
            "last_update": self.performance_history[-1]["timestamp"] if self.performance_history else None
        }

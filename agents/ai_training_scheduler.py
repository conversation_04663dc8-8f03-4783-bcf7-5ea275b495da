#!/usr/bin/env python3
"""
AI Training Agent Scheduler
Automated retraining pipeline with scheduling and monitoring integration
"""

import os
import sys
import asyncio
import logging
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
import polars as pl

# Import our modules
from ai_training_agent import AITrainingAgent, AITrainingConfig
from ai_training_monitor import AITrainingMonitor, MonitoringConfig
from ai_training_utils import setup_logging, get_system_info

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# 📅 SCHEDULER CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class SchedulerConfig:
    """Configuration for automated retraining scheduler"""
    
    # Scheduling settings
    auto_retrain_enabled: bool = True
    retrain_schedule: str = "weekly"  # "daily", "weekly", "monthly"
    retrain_time: str = "02:00"  # Time to run retraining (HH:MM)
    
    # Monitoring integration
    monitor_before_retrain: bool = True
    force_retrain_threshold_days: int = 30  # Force retrain after N days
    
    # Data settings
    data_file: str = "data/backtest/enhanced_strategy_results.parquet"
    backup_models: bool = True
    max_model_backups: int = 5
    
    # Training settings
    optimize_hyperparams: bool = True
    quick_retrain_mode: bool = False  # Reduced trials for faster retraining
    
    # Notification settings
    send_notifications: bool = True
    notification_email: Optional[str] = None
    notification_webhook: Optional[str] = None
    
    # Storage settings
    scheduler_log_path: str = "logs/scheduler.log"
    retrain_history_path: str = "data/models/retrain_history.json"

class AITrainingScheduler:
    """
    Automated retraining scheduler for AI Training Agent
    
    Features:
    - Scheduled retraining (daily/weekly/monthly)
    - Monitoring-based retraining triggers
    - Model backup and versioning
    - Performance tracking
    - Notification system
    - Graceful error handling
    """
    
    def __init__(self, config: Optional[SchedulerConfig] = None):
        """Initialize scheduler"""
        self.config = config or SchedulerConfig()
        self.is_running = False
        self.retrain_history = []
        self.last_retrain_time = None
        
        # Setup logging
        setup_logging("INFO", self.config.scheduler_log_path)
        
        # Create directories
        os.makedirs(os.path.dirname(self.config.retrain_history_path), exist_ok=True)
        
        # Load retrain history
        self.load_retrain_history()
        
        logger.info("📅 AI Training Scheduler initialized")
        logger.info(f"   Schedule: {self.config.retrain_schedule} at {self.config.retrain_time}")
        logger.info(f"   Auto retrain: {self.config.auto_retrain_enabled}")
    
    def load_retrain_history(self) -> None:
        """Load retraining history from storage"""
        try:
            if os.path.exists(self.config.retrain_history_path):
                with open(self.config.retrain_history_path, 'r') as f:
                    self.retrain_history = json.load(f)
                
                # Get last retrain time
                if self.retrain_history:
                    self.last_retrain_time = datetime.fromisoformat(
                        self.retrain_history[-1]['timestamp']
                    )
                
                logger.info(f"📚 Loaded {len(self.retrain_history)} retrain records")
        
        except Exception as e:
            logger.warning(f"Failed to load retrain history: {str(e)}")
            self.retrain_history = []
    
    def save_retrain_history(self) -> None:
        """Save retraining history to storage"""
        try:
            with open(self.config.retrain_history_path, 'w') as f:
                json.dump(self.retrain_history, f, indent=2, default=str)
            
            logger.info(f"💾 Retrain history saved")
        
        except Exception as e:
            logger.error(f"Failed to save retrain history: {str(e)}")
    
    def backup_existing_models(self, model_name: str = "ai_training_ensemble") -> str:
        """
        Backup existing models before retraining
        
        Args:
            model_name: Name of models to backup
            
        Returns:
            Backup model name
        """
        if not self.config.backup_models:
            return None
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{model_name}_backup_{timestamp}"
            
            # Load and save with backup name
            agent = AITrainingAgent()
            agent.load_models(model_name)
            agent.save_models(backup_name)
            
            logger.info(f"💾 Models backed up as: {backup_name}")
            
            # Clean up old backups
            self.cleanup_old_backups(model_name)
            
            return backup_name
        
        except Exception as e:
            logger.error(f"Failed to backup models: {str(e)}")
            return None
    
    def cleanup_old_backups(self, model_name: str) -> None:
        """Clean up old model backups"""
        try:
            models_dir = "data/models"
            backup_prefix = f"{model_name}_backup_"
            
            # Find all backup directories
            backup_dirs = []
            for item in os.listdir(models_dir):
                if item.startswith(backup_prefix) and os.path.isdir(os.path.join(models_dir, item)):
                    backup_dirs.append(item)
            
            # Sort by timestamp (newest first)
            backup_dirs.sort(reverse=True)
            
            # Remove old backups
            if len(backup_dirs) > self.config.max_model_backups:
                for old_backup in backup_dirs[self.config.max_model_backups:]:
                    backup_path = os.path.join(models_dir, old_backup)
                    import shutil
                    shutil.rmtree(backup_path)
                    logger.info(f"🗑️  Removed old backup: {old_backup}")
        
        except Exception as e:
            logger.error(f"Failed to cleanup old backups: {str(e)}")
    
    async def check_monitoring_triggers(self) -> bool:
        """
        Check if monitoring indicates retraining is needed
        
        Returns:
            True if retraining is recommended
        """
        if not self.config.monitor_before_retrain:
            return False
        
        try:
            # Load data for monitoring
            if not os.path.exists(self.config.data_file):
                logger.warning(f"Data file not found: {self.config.data_file}")
                return False
            
            df = pl.read_parquet(self.config.data_file)
            
            # Split data for monitoring (last 30% as current)
            split_point = int(len(df) * 0.7)
            baseline_data = df[:split_point]
            current_data = df[split_point:]
            
            # Initialize monitor and agent
            monitor = AITrainingMonitor()
            agent = AITrainingAgent()
            
            try:
                agent.load_models("ai_training_ensemble")
            except FileNotFoundError:
                logger.info("No existing models found, retraining needed")
                return True
            
            # Generate monitoring report
            report = monitor.generate_monitoring_report(agent, current_data, baseline_data)
            
            # Check if retraining is recommended
            should_retrain, reasons = monitor.should_retrain(report)
            
            if should_retrain:
                logger.info(f"[WORKFLOW] Monitoring recommends retraining: {reasons}")
                return True
            
            logger.info("[SUCCESS] Monitoring indicates model is stable")
            return False
        
        except Exception as e:
            logger.error(f"Monitoring check failed: {str(e)}")
            return False
    
    def check_force_retrain(self) -> bool:
        """Check if force retraining is needed based on time"""
        if not self.last_retrain_time:
            logger.info("[WORKFLOW] No previous retraining found, force retrain needed")
            return True
        
        days_since_retrain = (datetime.now() - self.last_retrain_time).days
        
        if days_since_retrain >= self.config.force_retrain_threshold_days:
            logger.info(f"[WORKFLOW] Force retrain needed: {days_since_retrain} days since last retrain")
            return True
        
        return False
    
    async def perform_retraining(self, reason: str = "scheduled") -> Dict[str, Any]:
        """
        Perform model retraining
        
        Args:
            reason: Reason for retraining
            
        Returns:
            Retraining results
        """
        start_time = datetime.now()
        logger.info(f"[INIT] Starting retraining: {reason}")
        
        retrain_record = {
            'timestamp': start_time.isoformat(),
            'reason': reason,
            'status': 'started',
            'duration_minutes': 0,
            'backup_created': None,
            'performance_metrics': {},
            'error': None
        }
        
        try:
            # Backup existing models
            backup_name = self.backup_existing_models()
            retrain_record['backup_created'] = backup_name
            
            # Configure training
            config = AITrainingConfig()
            if self.config.quick_retrain_mode:
                config.optuna_trials = 20  # Reduced for faster retraining
                config.lgb_params['num_boost_round'] = 200
                config.tabnet_params['max_epochs'] = 50
            
            # Initialize and train agent
            agent = AITrainingAgent(config)
            results = await agent.train_async(
                file_path=self.config.data_file,
                optimize_hyperparams=self.config.optimize_hyperparams
            )
            
            # Record results
            retrain_record['status'] = 'completed'
            retrain_record['performance_metrics'] = results['evaluation_metrics']
            
            # Calculate duration
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds() / 60
            retrain_record['duration_minutes'] = duration
            
            self.last_retrain_time = start_time
            
            logger.info(f"[SUCCESS] Retraining completed in {duration:.1f} minutes")
            logger.info(f"   Overall R²: {results['evaluation_metrics']['overall']['r2']:.4f}")
            
            # Send notification
            if self.config.send_notifications:
                await self.send_notification(
                    f"[SUCCESS] AI Training Agent retraining completed successfully\n"
                    f"Reason: {reason}\n"
                    f"Duration: {duration:.1f} minutes\n"
                    f"Overall R²: {results['evaluation_metrics']['overall']['r2']:.4f}"
                )
        
        except Exception as e:
            retrain_record['status'] = 'failed'
            retrain_record['error'] = str(e)
            
            logger.error(f"[ERROR] Retraining failed: {str(e)}")
            
            # Send error notification
            if self.config.send_notifications:
                await self.send_notification(
                    f"[ERROR] AI Training Agent retraining failed\n"
                    f"Reason: {reason}\n"
                    f"Error: {str(e)}"
                )
        
        # Save retrain record
        self.retrain_history.append(retrain_record)
        self.save_retrain_history()
        
        return retrain_record
    
    async def send_notification(self, message: str) -> None:
        """Send notification about retraining status"""
        try:
            # Log notification
            logger.info(f"📧 Notification: {message}")
            
            # Here you could implement actual email/webhook notifications
            # For now, just log the message
            
        except Exception as e:
            logger.error(f"Failed to send notification: {str(e)}")
    
    async def scheduled_retrain_job(self) -> None:
        """Job function for scheduled retraining"""
        logger.info("[TIME] Scheduled retrain job triggered")
        
        # Check if retraining is needed
        monitoring_trigger = await self.check_monitoring_triggers()
        force_trigger = self.check_force_retrain()
        
        if monitoring_trigger:
            await self.perform_retraining("monitoring_triggered")
        elif force_trigger:
            await self.perform_retraining("force_retrain")
        elif self.config.auto_retrain_enabled:
            await self.perform_retraining("scheduled")
        else:
            logger.info("⏭️  Skipping retraining - not needed")
    
    def setup_schedule(self) -> None:
        """Setup retraining schedule"""
        if not self.config.auto_retrain_enabled:
            logger.info("📅 Auto retraining disabled")
            return
        
        # Clear existing schedule
        schedule.clear()
        
        # Setup schedule based on configuration
        if self.config.retrain_schedule == "daily":
            schedule.every().day.at(self.config.retrain_time).do(
                lambda: asyncio.create_task(self.scheduled_retrain_job())
            )
        elif self.config.retrain_schedule == "weekly":
            schedule.every().week.at(self.config.retrain_time).do(
                lambda: asyncio.create_task(self.scheduled_retrain_job())
            )
        elif self.config.retrain_schedule == "monthly":
            schedule.every(30).days.at(self.config.retrain_time).do(
                lambda: asyncio.create_task(self.scheduled_retrain_job())
            )
        
        logger.info(f"📅 Scheduled {self.config.retrain_schedule} retraining at {self.config.retrain_time}")
    
    async def run_scheduler(self) -> None:
        """Run the scheduler loop"""
        self.is_running = True
        self.setup_schedule()
        
        logger.info("[INIT] AI Training Scheduler started")
        
        try:
            while self.is_running:
                schedule.run_pending()
                await asyncio.sleep(60)  # Check every minute
        
        except KeyboardInterrupt:
            logger.info("⏹️  Scheduler stopped by user")
        except Exception as e:
            logger.error(f"[ERROR] Scheduler error: {str(e)}")
        finally:
            self.is_running = False
            logger.info("[STOP] AI Training Scheduler stopped")
    
    def stop_scheduler(self) -> None:
        """Stop the scheduler"""
        self.is_running = False

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Example usage of AI Training Scheduler"""
    
    # Create scheduler configuration
    config = SchedulerConfig(
        retrain_schedule="weekly",
        retrain_time="02:00",
        quick_retrain_mode=True,  # For demo
        auto_retrain_enabled=True
    )
    
    # Initialize scheduler
    scheduler = AITrainingScheduler(config)
    
    print("📅 AI Training Scheduler Demo")
    print("="*40)
    print(f"Schedule: {config.retrain_schedule} at {config.retrain_time}")
    print(f"Auto retrain: {config.auto_retrain_enabled}")
    print(f"Monitor integration: {config.monitor_before_retrain}")
    
    # For demo, trigger immediate retraining check
    print("\n[DEBUG] Checking if retraining is needed...")
    
    monitoring_needed = await scheduler.check_monitoring_triggers()
    force_needed = scheduler.check_force_retrain()
    
    print(f"Monitoring trigger: {monitoring_needed}")
    print(f"Force trigger: {force_needed}")
    
    if monitoring_needed or force_needed:
        print("\n[INIT] Triggering retraining...")
        reason = "monitoring_triggered" if monitoring_needed else "force_retrain"
        result = await scheduler.perform_retraining(reason)
        
        print(f"[SUCCESS] Retraining {result['status']}")
        if result['status'] == 'completed':
            print(f"Duration: {result['duration_minutes']:.1f} minutes")
    else:
        print("\n[SUCCESS] No retraining needed")
    
    print(f"\n📚 Retrain history: {len(scheduler.retrain_history)} records")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Enhanced Backtesting Agent - Comprehensive Multi-Strategy, Multi-Timeframe Backtesting System

Features:
🔁 1. Multi-Strategy & Multi-Timeframe Backtesting
🧠 2. Smart Backtesting Modes (Deterministic, Probabilistic, Adaptive AI)
🧪 3. Detailed Performance Metrics Calculation
🧰 4. Capital & Risk Modeling
🗂️ 5. Scenario-Based & Regime-Based Testing
🧬 6. Parameter Sweep & Optimization
🧾 7. Result Logging & Versioning
[STATUS] 8. Backtest Visualization & Debugging
[DEBUG] 9. Signal Debugging & Replay
[AGENT] 10. LLM-Explainable Results Summary
"""

import os
import sys
import logging
import yaml
import polars as pl
import pyarrow as pa
import asyncio
import concurrent.futures
from pathlib import Path
import gc
import time
import warnings
from typing import List, Dict, Any, Optional, Tuple, Union
import hashlib
from datetime import datetime, timedelta
import tempfile
import threading
import psutil
import multiprocessing as mp
from functools import partial
import numpy as np
from dataclasses import dataclass, field
import json
from enum import Enum
import uuid

warnings.filterwarnings('ignore')

# Try to import GPU acceleration libraries
try:
    import cupy as cp
    import cudf
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

# Try to import optimization libraries
try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False

# Try to import visualization libraries
try:
    import plotly.graph_objects as go
    import plotly.express as px
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA CLASSES AND ENUMS
# ═══════════════════════════════════════════════════════════════════════════════

class BacktestMode(Enum):
    """Backtesting modes"""
    DETERMINISTIC = "deterministic"
    PROBABILISTIC = "probabilistic"
    ADAPTIVE_AI = "adaptive_ai"

class MarketRegime(Enum):
    """Market regime types"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"

class OptimizationMethod(Enum):
    """Parameter optimization methods"""
    OPTUNA = "optuna"
    GRID_SEARCH = "grid_search"
    RANDOM_SEARCH = "random_search"
    GENETIC_ALGORITHM = "genetic_algorithm"

@dataclass
class BacktestConfig:
    """Comprehensive backtesting configuration"""
    # General settings
    agent_name: str = "Enhanced Backtesting Agent"
    version: str = "2.0.0"
    
    # Data configuration
    data_directory: str = "data/features"
    output_directory: str = "data/backtest"
    strategies_config: str = "config/strategies.yaml"
    symbols_config: str = "config/symbols.json"
    
    # Processing limits
    max_symbols: Optional[int] = None
    max_strategies: Optional[int] = None
    max_timeframes: Optional[int] = None
    
    # Multi-strategy settings
    enable_individual_strategies: bool = True
    enable_batch_processing: bool = True
    enable_strategy_combinations: bool = False
    
    # Multi-timeframe settings (based on available historical data)
    timeframes: List[str] = field(default_factory=lambda: ["1min", "3min", "5min", "15min"])
    process_timeframes_parallel: bool = True
    timeframe_batch_size: int = 2
    
    # Asset types
    underlying_assets: List[str] = field(default_factory=lambda: ["NIFTY", "BANKNIFTY"])
    option_types: List[str] = field(default_factory=lambda: ["CE", "PE"])
    expiry_types: List[str] = field(default_factory=lambda: ["weekly", "monthly"])
    
    # Backtesting modes
    backtesting_mode: BacktestMode = BacktestMode.DETERMINISTIC
    enable_regime_testing: bool = True
    enable_event_testing: bool = False
    
    # Capital and risk
    initial_capital: float = 100000.0
    risk_per_trade_pct: float = 1.0
    max_concurrent_trades: int = 5
    max_drawdown_pct: float = 10.0
    
    # Risk-reward ratios
    risk_reward_ratios: List[List[float]] = field(default_factory=lambda: [[1, 1.5], [1, 2.0], [1.5, 2.0], [2, 3.0]])
    
    # Transaction costs
    brokerage_pct: float = 0.03
    brokerage_flat: float = 20.0
    stt_pct: float = 0.025
    slippage_pct: float = 0.02
    
    # Performance optimization
    enable_multiprocessing: bool = True
    enable_gpu_acceleration: bool = True
    max_workers: Optional[int] = None
    chunk_size: int = 500000
    
    # Output settings
    output_format: str = "parquet"
    compression: str = "brotli"
    enable_versioning: bool = True
    
    # Advanced features
    enable_parameter_optimization: bool = False
    enable_visualization: bool = False
    enable_debugging: bool = False
    enable_llm_summary: bool = True

@dataclass
class TradeResult:
    """Individual trade result with comprehensive data for AI training"""

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📌 A. Trade Metadata Columns
    # ═══════════════════════════════════════════════════════════════════════════════
    trade_id: str                           # Unique ID per trade
    timestamp: datetime                     # Entry time (ISO or datetime)
    exit_timestamp: datetime                # Exit time
    symbol: str                            # Option symbol (e.g., BANKNIFTY24JUL47000CE)
    underlying: str                        # NIFTY or BANKNIFTY
    expiry_type: str                       # weekly / monthly
    option_type: str                       # CE or PE
    lot_size: int                          # Lot size used
    direction: str                         # Buy Call / Buy Put
    strategy_id: str                       # From strategies.yaml
    strategy_name: str                     # Human-readable name
    timeframe: str                         # 1min, 3min, 5min, 15min
    market_regime: str                     # Trending / Sideways / Volatile / Calm
    volatility_regime: str                 # Low IV / High IV / IV Crush
    event_tag: str = ""                    # Expiry, Budget Day, etc. (optional)

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📌 B. Feature Snapshot (at Entry Time)
    # ═══════════════════════════════════════════════════════════════════════════════
    # Technical Indicators
    rsi: float = 0.0                       # RSI value at entry
    rsi_5: float = 0.0                     # RSI 5-period
    rsi_14: float = 0.0                    # RSI 14-period
    macd: float = 0.0                      # MACD line
    macd_signal: float = 0.0               # MACD signal line
    macd_histogram: float = 0.0            # MACD histogram

    # Moving Averages
    ema_5: float = 0.0                     # EMA 5
    ema_10: float = 0.0                    # EMA 10
    ema_20: float = 0.0                    # EMA 20
    ema_50: float = 0.0                    # EMA 50
    sma_20: float = 0.0                    # SMA 20
    sma_50: float = 0.0                    # SMA 50
    sma_20_vs_price: float = 0.0           # SMA 20 vs current price ratio

    # Bollinger Bands
    bb_upper: float = 0.0                  # Bollinger Band upper
    bb_lower: float = 0.0                  # Bollinger Band lower
    bb_middle: float = 0.0                 # Bollinger Band middle

    # Other Technical Indicators
    adx: float = 0.0                       # ADX
    atr: float = 0.0                       # ATR
    stoch_k: float = 0.0                   # Stochastic %K
    stoch_d: float = 0.0                   # Stochastic %D
    cci: float = 0.0                       # CCI
    mfi: float = 0.0                       # Money Flow Index

    # Volume and Price Action
    volume: float = 0.0                    # Volume at entry
    volume_spike_ratio: float = 1.0        # Volume vs average ratio
    vwap: float = 0.0                      # VWAP
    price_vs_vwap: float = 0.0             # Price vs VWAP percentage
    supertrend: float = 0.0                # SuperTrend value

    # Options-specific (if available)
    iv_rank: float = 0.0                   # IV Rank
    iv_percentile: float = 0.0             # IV Percentile
    vix: float = 0.0                       # VIX level
    delta: float = 0.0                     # Option delta
    gamma: float = 0.0                     # Option gamma
    theta: float = 0.0                     # Option theta
    vega: float = 0.0                      # Option vega
    open_interest: float = 0.0             # Open interest
    open_interest_change: float = 0.0      # OI change

    # Time-based features
    hour_of_day: float = 0.0               # Hour of day (e.g., 10.25)
    day_of_week: int = 0                   # Day of week (1-7)
    days_to_expiry: int = 0                # Days to expiry

    # Market Context
    nifty_level: float = 0.0               # NIFTY level at entry
    banknifty_level: float = 0.0           # BANKNIFTY level at entry

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📌 C. Trade Outcome / Label Columns
    # ═══════════════════════════════════════════════════════════════════════════════
    entry_price: float                     # Price at which option was bought
    exit_price: float                      # Price at exit
    pnl_abs: float                         # Profit/loss in Rs.
    pnl_pct: float                         # % return on capital used
    holding_minutes: float                 # Time held in minutes
    is_profitable: int                     # 1 if profitable, 0 if not (binary classification)
    target_hit: str                        # TP/SL hit, or manual exit
    exit_reason: str                       # SL, TP, time-based, reversal
    expectancy_tag: str                    # >0, <0, zero (for modeling expected value)
    sharpe_score_tag: str = ""             # Optional label bucket (low, medium, high)
    confidence_score: float = 1.0          # From Signal Agent
    trade_score: float = 0.0               # Weighted score combining ROI, RR, confidence
    label_strategy_choice: str = ""        # Strategy that should've been chosen

    # Position and Risk
    position_size: float = 0.0             # Position size in Rs.
    quantity: int = 1                      # Number of lots/contracts
    risk_reward_ratio: float = 0.0         # Actual RR ratio achieved
    max_adverse_excursion: float = 0.0     # MAE
    max_favorable_excursion: float = 0.0   # MFE

    # Execution details
    slippage: float = 0.0                  # Slippage in Rs.
    transaction_cost: float = 0.0          # Total transaction cost

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📌 D. Optional Advanced Tags (for Meta/LLM/Evolution Agents)
    # ═══════════════════════════════════════════════════════════════════════════════
    model_version_used: str = "v1.0"       # Version of AI used at signal time
    was_signal_live: bool = False          # True/False if trade came from live signal
    live_vs_backtest_diff_pct: float = 0.0 # Difference in live vs sim PnL
    used_in_training: bool = True          # True/False for validation sets
    llm_summary: str = ""                  # Natural language summary

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    strategy_name: str
    symbol: str
    timeframe: str
    
    # Basic metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Return metrics
    total_return: float
    annualized_return: float
    roi: float
    expectancy: float
    
    # Risk metrics
    max_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    
    # Advanced metrics
    profit_factor: float
    recovery_factor: float
    payoff_ratio: float
    kelly_criterion: float
    
    # Trade analysis
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    avg_holding_period: float
    
    # Capital efficiency
    capital_utilization: float
    return_on_margin: float
    
    # Market regime performance
    regime_performance: Dict[str, float] = field(default_factory=dict)
    
    # Timestamp
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class BacktestResults:
    """Complete backtesting results"""
    backtest_id: str
    config: BacktestConfig
    
    # Results
    trades: List[TradeResult]
    performance_metrics: PerformanceMetrics
    
    # Execution details
    start_time: datetime
    end_time: datetime
    execution_time_seconds: float
    
    # Data statistics
    total_data_points: int
    data_quality_score: float
    
    # Regime analysis
    regime_distribution: Dict[str, float] = field(default_factory=dict)
    
    # Summary
    summary: str = ""
    
    # Metadata
    version: str = "2.0.0"
    created_at: datetime = field(default_factory=datetime.now)

class EnhancedBacktestingAgent:
    """
    Enhanced Backtesting Agent for comprehensive strategy testing
    
    Supports:
    - Multi-strategy and multi-timeframe backtesting
    - Smart backtesting modes (deterministic, probabilistic, adaptive AI)
    - Comprehensive performance metrics
    - Capital and risk modeling
    - Scenario-based and regime-based testing
    - Parameter optimization
    - Result logging and versioning
    - Visualization and debugging
    - LLM-explainable results
    """
    
    def __init__(self, config_path: str = "config/enhanced_backtesting_config.yaml"):
        """Initialize the Enhanced Backtesting Agent"""
        self.config_path = config_path
        self.config: Optional[BacktestConfig] = None
        self.logger = self._setup_logging()
        
        # State management
        self.is_running = False
        self.current_backtest_id = None
        
        # Data storage
        self.strategies = {}
        self.symbols = []
        self.feature_data = {}
        
        # Results storage
        self.current_results: List[BacktestResults] = []
        
        # Performance tracking
        self.start_time = None
        self.processed_strategies = 0
        self.total_strategies = 0
        
        # GPU availability
        self.gpu_available = GPU_AVAILABLE
        if self.gpu_available:
            self.logger.info("[INIT] GPU acceleration available (CuPy/CuDF)")
        else:
            self.logger.info("[WARN] GPU acceleration not available, using CPU")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        
        return logger

    async def initialize(self) -> bool:
        """Initialize the agent"""
        try:
            self.logger.info("[INIT] Initializing Enhanced Backtesting Agent...")

            # Load configuration
            await self._load_config()

            # Validate configuration
            if not await self._validate_config():
                return False

            # Setup directories
            await self._setup_directories()

            # Load strategies
            await self._load_strategies()

            # Load symbols
            await self._load_symbols()

            self.logger.info("[SUCCESS] Enhanced Backtesting Agent initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False

    async def _load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    config_data = yaml.safe_load(file)

                # Create BacktestConfig from loaded data
                self.config = self._create_config_from_dict(config_data)
                self.logger.info(f"[LIST] Configuration loaded from {self.config_path}")
            else:
                # Create default configuration
                self.config = BacktestConfig()
                self.logger.info("[LIST] Using default configuration")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise

    def _create_config_from_dict(self, config_data: Dict[str, Any]) -> BacktestConfig:
        """Create BacktestConfig from dictionary"""
        try:
            # Extract general settings
            general = config_data.get('general', {})
            multi_strategy = config_data.get('multi_strategy', {})
            multi_timeframe = config_data.get('multi_timeframe', {})
            backtesting_modes = config_data.get('backtesting_modes', {})
            capital_risk = config_data.get('capital_risk_modeling', {})
            performance = config_data.get('performance', {})

            # Create config object
            config = BacktestConfig(
                # General settings
                agent_name=general.get('agent_name', 'Enhanced Backtesting Agent'),
                version=general.get('version', '2.0.0'),
                data_directory=general.get('data_directory', 'data/features'),
                output_directory=general.get('output_directory', 'data/backtest'),
                strategies_config=general.get('strategies_config', 'config/strategies.yaml'),
                symbols_config=general.get('symbols_config', 'config/symbols.json'),

                # Processing limits
                max_symbols=general.get('max_symbols'),
                max_strategies=general.get('max_strategies'),
                max_timeframes=general.get('max_timeframes'),

                # Multi-strategy settings
                enable_individual_strategies=multi_strategy.get('enable_individual_strategies', True),
                enable_batch_processing=multi_strategy.get('enable_batch_processing', True),
                enable_strategy_combinations=multi_strategy.get('enable_strategy_combinations', False),

                # Multi-timeframe settings (based on available historical data)
                timeframes=multi_timeframe.get('timeframes', ['1min', '3min', '5min', '15min']),
                process_timeframes_parallel=multi_timeframe.get('process_timeframes_parallel', True),
                timeframe_batch_size=multi_timeframe.get('timeframe_batch_size', 2),

                # Asset types
                underlying_assets=multi_timeframe.get('asset_types', {}).get('underlying', ['NIFTY', 'BANKNIFTY']),
                option_types=multi_timeframe.get('asset_types', {}).get('options', {}).get('option_types', ['CE', 'PE']),
                expiry_types=multi_timeframe.get('asset_types', {}).get('options', {}).get('expiry_types', ['weekly', 'monthly']),

                # Backtesting modes
                backtesting_mode=BacktestMode.DETERMINISTIC if backtesting_modes.get('enable_deterministic', True) else BacktestMode.PROBABILISTIC,
                enable_regime_testing=config_data.get('scenario_regime_testing', {}).get('enable_regime_testing', True),
                enable_event_testing=config_data.get('scenario_regime_testing', {}).get('event_scenarios', {}).get('enable_event_testing', False),

                # Capital and risk
                initial_capital=capital_risk.get('initial_capital', 100000.0),
                risk_per_trade_pct=capital_risk.get('position_sizing', {}).get('risk_per_trade_pct', 1.0),
                max_concurrent_trades=capital_risk.get('risk_management', {}).get('max_concurrent_trades', 5),
                max_drawdown_pct=capital_risk.get('risk_management', {}).get('max_drawdown_pct', 10.0),

                # Risk-reward ratios
                risk_reward_ratios=capital_risk.get('risk_management', {}).get('risk_reward_ratios', [[1, 1.5], [1, 2.0], [1.5, 2.0], [2, 3.0]]),

                # Transaction costs
                brokerage_pct=capital_risk.get('transaction_costs', {}).get('brokerage_pct', 0.03),
                brokerage_flat=capital_risk.get('transaction_costs', {}).get('brokerage_flat', 20.0),
                stt_pct=capital_risk.get('transaction_costs', {}).get('stt_pct', 0.025),
                slippage_pct=capital_risk.get('transaction_costs', {}).get('slippage_pct', 0.02),

                # Performance optimization
                enable_multiprocessing=performance.get('parallel_processing', {}).get('enable_multiprocessing', True),
                enable_gpu_acceleration=performance.get('gpu_acceleration', {}).get('enable_gpu', True),
                max_workers=performance.get('parallel_processing', {}).get('max_workers'),
                chunk_size=performance.get('parallel_processing', {}).get('chunk_size', 500000),

                # Output settings
                output_format=config_data.get('result_logging', {}).get('output_format', 'parquet'),
                compression=config_data.get('result_logging', {}).get('compression', 'brotli'),
                enable_versioning=config_data.get('result_logging', {}).get('versioning', {}).get('enable_versioning', True),

                # Advanced features
                enable_parameter_optimization=config_data.get('parameter_optimization', {}).get('enable_optimization', False),
                enable_visualization=config_data.get('visualization', {}).get('enable_visualization', False),
                enable_debugging=config_data.get('debugging', {}).get('enable_debugging', False),
                enable_llm_summary=config_data.get('llm_integration', {}).get('enable_llm_summary', True),
            )

            return config

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to create config from dictionary: {e}")
            return BacktestConfig()  # Return default config

    async def _validate_config(self) -> bool:
        """Validate configuration"""
        try:
            if not self.config:
                self.logger.error("[ERROR] Configuration not loaded")
                return False

            # Validate directories
            if not os.path.exists(self.config.data_directory):
                self.logger.error(f"[ERROR] Data directory not found: {self.config.data_directory}")
                return False

            # Validate strategies config
            if not os.path.exists(self.config.strategies_config):
                self.logger.error(f"[ERROR] Strategies config not found: {self.config.strategies_config}")
                return False

            # Validate timeframes
            if not self.config.timeframes:
                self.logger.error("[ERROR] No timeframes specified")
                return False

            # Validate capital settings
            if self.config.initial_capital <= 0:
                self.logger.error("[ERROR] Initial capital must be positive")
                return False

            if self.config.risk_per_trade_pct <= 0 or self.config.risk_per_trade_pct > 100:
                self.logger.error("[ERROR] Risk per trade percentage must be between 0 and 100")
                return False

            self.logger.info("[SUCCESS] Configuration validation passed")
            return True

        except Exception as e:
            self.logger.error(f"[ERROR] Configuration validation failed: {e}")
            return False

    async def _setup_directories(self):
        """Setup required directories"""
        try:
            directories = [
                self.config.output_directory,
                f"{self.config.output_directory}/versions",
                f"{self.config.output_directory}/charts",
                f"{self.config.output_directory}/summaries",
                "logs"
            ]

            for directory in directories:
                os.makedirs(directory, exist_ok=True)

            self.logger.info("[FOLDER] Directories setup completed")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to setup directories: {e}")
            raise

    async def _load_strategies(self):
        """Load trading strategies from configuration"""
        try:
            self.strategies = {}

            # Load from multiple strategy sources
            strategy_sources = [self.config.strategies_config]

            # Add additional strategy sources if they exist
            additional_sources = [
                "config/enhanced_strategies.yaml",
                "data/evolved_strategies"
            ]

            for source in additional_sources:
                if os.path.exists(source):
                    if os.path.isfile(source):
                        strategy_sources.append(source)
                    elif os.path.isdir(source):
                        # Load all YAML files from directory
                        for file in os.listdir(source):
                            if file.endswith('.yaml') or file.endswith('.yml'):
                                strategy_sources.append(os.path.join(source, file))

            # Load strategies from each source
            total_strategies = 0
            for source in strategy_sources:
                try:
                    with open(source, 'r', encoding='utf-8') as file:
                        strategy_data = yaml.safe_load(file)

                    strategies_list = strategy_data.get('strategies', [])

                    for strategy in strategies_list:
                        strategy_name = strategy.get('name')
                        if strategy_name:
                            # Add source information
                            strategy['source'] = source
                            strategy['loaded_at'] = datetime.now().isoformat()

                            self.strategies[strategy_name] = strategy
                            total_strategies += 1

                    self.logger.info(f"[METRICS] Loaded {len(strategies_list)} strategies from {source}")

                except Exception as e:
                    self.logger.warning(f"[WARN] Failed to load strategies from {source}: {e}")

            # Apply strategy filtering
            if self.config.max_strategies:
                strategy_names = list(self.strategies.keys())[:self.config.max_strategies]
                self.strategies = {name: self.strategies[name] for name in strategy_names}
                self.logger.info(f"🔢 Limited to {len(self.strategies)} strategies (max_strategies={self.config.max_strategies})")

            self.total_strategies = len(self.strategies)
            self.logger.info(f"[SUCCESS] Total strategies loaded: {self.total_strategies}")

            if self.total_strategies == 0:
                self.logger.warning("[WARN] No strategies loaded!")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to load strategies: {e}")
            raise

    async def _load_symbols(self):
        """Load symbols from configuration"""
        try:
            self.symbols = []

            # Try to load from symbols config file
            if os.path.exists(self.config.symbols_config):
                with open(self.config.symbols_config, 'r', encoding='utf-8') as file:
                    symbols_data = json.load(file)

                if isinstance(symbols_data, list):
                    self.symbols = symbols_data
                elif isinstance(symbols_data, dict):
                    self.symbols = symbols_data.get('symbols', [])

                self.logger.info(f"[STATUS] Loaded {len(self.symbols)} symbols from {self.config.symbols_config}")

            # If no symbols loaded, scan feature files for available symbols
            if not self.symbols:
                self.symbols = await self._discover_symbols_from_data()
                self.logger.info(f"[DEBUG] Discovered {len(self.symbols)} symbols from feature data")

            # Apply symbol filtering
            if self.config.max_symbols:
                self.symbols = self.symbols[:self.config.max_symbols]
                self.logger.info(f"🔢 Limited to {len(self.symbols)} symbols (max_symbols={self.config.max_symbols})")

            if not self.symbols:
                self.logger.warning("[WARN] No symbols loaded!")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to load symbols: {e}")
            raise

    async def _discover_symbols_from_data(self) -> List[str]:
        """Discover available symbols from feature data files"""
        try:
            symbols = set()

            # Scan feature directory for files
            feature_files = []
            for timeframe in self.config.timeframes:
                pattern = f"*{timeframe}*"
                timeframe_files = list(Path(self.config.data_directory).glob(pattern))
                feature_files.extend(timeframe_files)

            # Extract symbols from file names or data
            for file_path in feature_files:
                try:
                    # Try to read a small sample to get symbol information
                    if file_path.suffix == '.parquet':
                        df = pl.read_parquet(file_path, n_rows=10)
                    elif file_path.suffix == '.csv':
                        df = pl.read_csv(file_path, n_rows=10)
                    else:
                        continue

                    # Look for symbol column
                    symbol_columns = ['symbol', 'stock_name', 'ticker', 'instrument']
                    for col in symbol_columns:
                        if col in df.columns:
                            file_symbols = df[col].unique().to_list()
                            symbols.update(file_symbols)
                            break

                except Exception as e:
                    self.logger.debug(f"Could not extract symbols from {file_path}: {e}")

            return sorted(list(symbols))

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to discover symbols: {e}")
            return []

    async def start_backtesting(self, **kwargs) -> bool:
        """Start the comprehensive backtesting process"""
        try:
            self.logger.info("[INIT] Starting Enhanced Backtesting Process...")

            if not await self.initialize():
                return False

            self.is_running = True
            self.start_time = time.time()
            self.current_backtest_id = str(uuid.uuid4())

            # Log configuration summary
            await self._log_configuration_summary()

            # Execute backtesting based on mode
            if self.config.backtesting_mode == BacktestMode.DETERMINISTIC:
                results = await self._run_deterministic_backtesting()
            elif self.config.backtesting_mode == BacktestMode.PROBABILISTIC:
                results = await self._run_probabilistic_backtesting()
            elif self.config.backtesting_mode == BacktestMode.ADAPTIVE_AI:
                results = await self._run_adaptive_ai_backtesting()
            else:
                self.logger.error(f"[ERROR] Unknown backtesting mode: {self.config.backtesting_mode}")
                return False

            # Process and save results
            if results:
                await self._process_and_save_results(results)

                # Generate summary
                if self.config.enable_llm_summary:
                    await self._generate_llm_summary(results)

                # Generate visualizations
                if self.config.enable_visualization:
                    await self._generate_visualizations(results)

            execution_time = time.time() - self.start_time
            self.logger.info(f"[SUCCESS] Backtesting completed in {execution_time:.2f} seconds")

            self.is_running = False
            return True

        except Exception as e:
            self.logger.error(f"[ERROR] Backtesting failed: {e}")
            self.is_running = False
            return False

    async def _log_configuration_summary(self):
        """Log a summary of the current configuration"""
        try:
            self.logger.info("[LIST] Configuration Summary:")
            self.logger.info(f"   • Mode: {self.config.backtesting_mode.value}")
            self.logger.info(f"   • Strategies: {len(self.strategies)}")
            self.logger.info(f"   • Symbols: {len(self.symbols)}")
            self.logger.info(f"   • Timeframes: {self.config.timeframes}")
            self.logger.info(f"   • Initial Capital: Rs.{self.config.initial_capital:,.0f}")
            self.logger.info(f"   • Risk per Trade: {self.config.risk_per_trade_pct}%")
            self.logger.info(f"   • Max Concurrent Trades: {self.config.max_concurrent_trades}")
            self.logger.info(f"   • GPU Acceleration: {'[SUCCESS]' if self.config.enable_gpu_acceleration and self.gpu_available else '[ERROR]'}")
            self.logger.info(f"   • Multiprocessing: {'[SUCCESS]' if self.config.enable_multiprocessing else '[ERROR]'}")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to log configuration summary: {e}")

    async def _run_deterministic_backtesting(self) -> List[BacktestResults]:
        """Run deterministic backtesting mode"""
        try:
            self.logger.info("[TARGET] Running Deterministic Backtesting...")

            all_results = []

            # Process each timeframe
            for timeframe in self.config.timeframes:
                self.logger.info(f"[TIME] Processing timeframe: {timeframe}")

                # Load feature data for this timeframe
                feature_data = await self._load_feature_data(timeframe)
                if feature_data is None:
                    self.logger.warning(f"[WARN] No feature data found for timeframe {timeframe}")
                    continue

                # Process strategies for this timeframe
                timeframe_results = await self._process_timeframe_strategies(
                    feature_data, timeframe
                )

                all_results.extend(timeframe_results)

                # Memory cleanup
                del feature_data
                gc.collect()

            self.logger.info(f"[SUCCESS] Deterministic backtesting completed. Generated {len(all_results)} results.")
            return all_results

        except Exception as e:
            self.logger.error(f"[ERROR] Deterministic backtesting failed: {e}")
            return []

    async def _load_feature_data(self, timeframe: str) -> Optional[pl.DataFrame]:
        """Load feature data for a specific timeframe"""
        try:
            # Look for feature files matching the timeframe
            feature_files = []

            # Common file patterns
            patterns = [
                f"features_{timeframe}.parquet",
                f"features_{timeframe}.csv",
                f"*{timeframe}*.parquet",
                f"*{timeframe}*.csv"
            ]

            for pattern in patterns:
                files = list(Path(self.config.data_directory).glob(pattern))
                feature_files.extend(files)

            if not feature_files:
                self.logger.warning(f"[WARN] No feature files found for timeframe {timeframe}")
                return None

            # Load the first matching file (or combine multiple files)
            feature_file = feature_files[0]
            self.logger.info(f"[STATUS] Loading feature data from {feature_file}")

            if feature_file.suffix == '.parquet':
                df = pl.read_parquet(feature_file)
            elif feature_file.suffix == '.csv':
                df = pl.read_csv(feature_file)
            else:
                self.logger.error(f"[ERROR] Unsupported file format: {feature_file}")
                return None

            # Basic data validation
            if df.height == 0:
                self.logger.warning(f"[WARN] Empty dataset for timeframe {timeframe}")
                return None

            # Ensure required columns exist
            required_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                self.logger.error(f"[ERROR] Missing required columns: {missing_columns}")
                return None

            # Sort by datetime
            df = df.sort('datetime')

            self.logger.info(f"[SUCCESS] Loaded {df.height:,} rows of feature data for {timeframe}")
            return df

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to load feature data for {timeframe}: {e}")
            return None

    async def _process_timeframe_strategies(self, feature_data: pl.DataFrame, timeframe: str) -> List[BacktestResults]:
        """Process all strategies for a specific timeframe"""
        try:
            results = []

            # Get unique symbols from the data
            if 'symbol' in feature_data.columns:
                symbols = feature_data['symbol'].unique().to_list()
            elif 'stock_name' in feature_data.columns:
                symbols = feature_data['stock_name'].unique().to_list()
            else:
                # Use configured symbols
                symbols = self.symbols

            # Filter symbols if needed
            if self.config.max_symbols:
                symbols = symbols[:self.config.max_symbols]

            self.logger.info(f"[STATUS] Processing {len(symbols)} symbols for {timeframe}")

            # Process each symbol
            for symbol in symbols:
                symbol_results = await self._process_symbol_strategies(
                    feature_data, symbol, timeframe
                )
                results.extend(symbol_results)

            return results

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to process timeframe strategies: {e}")
            return []

    async def _process_symbol_strategies(self, feature_data: pl.DataFrame, symbol: str, timeframe: str) -> List[BacktestResults]:
        """Process all strategies for a specific symbol and timeframe"""
        try:
            results = []

            # Filter data for this symbol
            symbol_column = 'symbol' if 'symbol' in feature_data.columns else 'stock_name'
            symbol_data = feature_data.filter(pl.col(symbol_column) == symbol)

            if symbol_data.height == 0:
                self.logger.debug(f"No data found for symbol {symbol}")
                return results

            # Process each strategy
            strategy_tasks = []

            if self.config.enable_multiprocessing and len(self.strategies) > 1:
                # Use asyncio for concurrent processing
                semaphore = asyncio.Semaphore(self.config.max_workers or mp.cpu_count())

                for strategy_name, strategy in self.strategies.items():
                    task = self._process_single_strategy_async(
                        symbol_data, symbol, strategy_name, strategy, timeframe, semaphore
                    )
                    strategy_tasks.append(task)

                # Wait for all strategies to complete
                strategy_results = await asyncio.gather(*strategy_tasks, return_exceptions=True)

                # Filter out exceptions and None results
                for result in strategy_results:
                    if isinstance(result, BacktestResults):
                        results.append(result)
                    elif isinstance(result, Exception):
                        self.logger.error(f"Strategy processing failed: {result}")

            else:
                # Sequential processing
                for strategy_name, strategy in self.strategies.items():
                    result = await self._process_single_strategy(
                        symbol_data, symbol, strategy_name, strategy, timeframe
                    )
                    if result:
                        results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to process symbol strategies for {symbol}: {e}")
            return []

    async def _process_single_strategy_async(self, symbol_data: pl.DataFrame, symbol: str,
                                           strategy_name: str, strategy: Dict[str, Any],
                                           timeframe: str, semaphore: asyncio.Semaphore) -> Optional[BacktestResults]:
        """Process a single strategy with async semaphore control"""
        async with semaphore:
            return await self._process_single_strategy(symbol_data, symbol, strategy_name, strategy, timeframe)

    async def _process_single_strategy(self, symbol_data: pl.DataFrame, symbol: str,
                                     strategy_name: str, strategy: Dict[str, Any],
                                     timeframe: str) -> Optional[BacktestResults]:
        """Process a single strategy for a symbol and timeframe"""
        try:
            start_time = time.time()

            # Generate trading signals
            signals = await self._generate_trading_signals(symbol_data, strategy)

            if not signals:
                self.logger.debug(f"No signals generated for {strategy_name} on {symbol}")
                return None

            # Execute trades for each risk-reward ratio
            all_trades = []

            for rr_ratio in self.config.risk_reward_ratios:
                trades = await self._execute_trades(symbol_data, signals, strategy, rr_ratio)
                all_trades.extend(trades)

            if not all_trades:
                self.logger.debug(f"No trades executed for {strategy_name} on {symbol}")
                return None

            # Calculate performance metrics
            performance_metrics = await self._calculate_performance_metrics(
                all_trades, symbol, strategy_name, timeframe
            )

            # Create backtest result
            result = BacktestResults(
                backtest_id=f"{self.current_backtest_id}_{strategy_name}_{symbol}_{timeframe}",
                config=self.config,
                trades=all_trades,
                performance_metrics=performance_metrics,
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.now(),
                execution_time_seconds=time.time() - start_time,
                total_data_points=symbol_data.height,
                data_quality_score=1.0,  # TODO: Implement data quality scoring
            )

            self.processed_strategies += 1

            # Log progress
            if self.processed_strategies % 10 == 0:
                progress = (self.processed_strategies / (self.total_strategies * len(self.symbols) * len(self.config.timeframes))) * 100
                self.logger.info(f"[METRICS] Progress: {progress:.1f}% ({self.processed_strategies} strategies processed)")

            return result

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to process strategy {strategy_name} for {symbol}: {e}")
            return None

    async def _generate_trading_signals(self, data: pl.DataFrame, strategy: Dict[str, Any]) -> List:
        """Generate trading signals using the core signal generator"""
        try:
            # Import the core module
            from .backtesting_core import SignalGenerator

            # Create signal generator
            signal_generator = SignalGenerator(enable_gpu=self.config.enable_gpu_acceleration and self.gpu_available)

            # Generate signals
            signals = await signal_generator.generate_signals(data, strategy)

            return signals

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to generate trading signals: {e}")
            return []

    async def _execute_trades(self, data: pl.DataFrame, signals: List, strategy: Dict[str, Any],
                            risk_reward_ratio: List[float]) -> List[TradeResult]:
        """Execute trades using the core trade executor"""
        try:
            # Import the core module
            from .backtesting_core import TradeExecutor

            # Create trade executor
            config_dict = {
                'initial_capital': self.config.initial_capital,
                'risk_per_trade_pct': self.config.risk_per_trade_pct,
                'brokerage_pct': self.config.brokerage_pct,
                'brokerage_flat': self.config.brokerage_flat,
                'stt_pct': self.config.stt_pct,
                'slippage_pct': self.config.slippage_pct,
            }

            trade_executor = TradeExecutor(config_dict)

            # Execute trades
            trades = await trade_executor.execute_trades(data, signals, strategy, risk_reward_ratio)

            return trades

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to execute trades: {e}")
            return []

    async def _calculate_performance_metrics(self, trades: List[TradeResult], symbol: str,
                                           strategy_name: str, timeframe: str) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        try:
            if not trades:
                return self._create_empty_performance_metrics(symbol, strategy_name, timeframe)

            # Convert trades to arrays for faster computation
            pnls = [trade.pnl_abs for trade in trades]
            pnl_pcts = [trade.pnl_pct for trade in trades]
            holding_periods = [trade.holding_minutes / 60.0 for trade in trades]  # Convert to hours

            # Basic metrics
            total_trades = len(trades)
            winning_trades = len([pnl for pnl in pnls if pnl > 0])
            losing_trades = total_trades - winning_trades
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

            # Return metrics
            total_return = sum(pnls)
            roi = (total_return / self.config.initial_capital) * 100
            expectancy = np.mean(pnls) if pnls else 0

            # Risk metrics
            returns_array = np.array(pnl_pcts)
            max_drawdown = self._calculate_max_drawdown(returns_array)
            sharpe_ratio = self._calculate_sharpe_ratio(returns_array)
            sortino_ratio = self._calculate_sortino_ratio(returns_array)
            calmar_ratio = roi / abs(max_drawdown) if max_drawdown != 0 else 0

            # Advanced metrics
            wins = [pnl for pnl in pnls if pnl > 0]
            losses = [pnl for pnl in pnls if pnl < 0]

            avg_win = np.mean(wins) if wins else 0
            avg_loss = np.mean(losses) if losses else 0
            largest_win = max(wins) if wins else 0
            largest_loss = min(losses) if losses else 0

            profit_factor = abs(sum(wins) / sum(losses)) if losses and sum(losses) != 0 else float('inf')
            recovery_factor = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
            payoff_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

            # Kelly criterion
            kelly_criterion = self._calculate_kelly_criterion(wins, losses, win_rate)

            # Capital efficiency
            capital_utilization = self._calculate_capital_utilization(trades)
            return_on_margin = roi  # Simplified - could be more sophisticated

            # Create performance metrics object
            performance_metrics = PerformanceMetrics(
                strategy_name=strategy_name,
                symbol=symbol,
                timeframe=timeframe,

                # Basic metrics
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,

                # Return metrics
                total_return=total_return,
                annualized_return=self._annualize_return(roi, np.mean(holding_periods) if holding_periods else 24),
                roi=roi,
                expectancy=expectancy,

                # Risk metrics
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,

                # Advanced metrics
                profit_factor=profit_factor,
                recovery_factor=recovery_factor,
                payoff_ratio=payoff_ratio,
                kelly_criterion=kelly_criterion,

                # Trade analysis
                avg_win=avg_win,
                avg_loss=avg_loss,
                largest_win=largest_win,
                largest_loss=largest_loss,
                avg_holding_period=np.mean(holding_periods) if holding_periods else 0,

                # Capital efficiency
                capital_utilization=capital_utilization,
                return_on_margin=return_on_margin,
            )

            return performance_metrics

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to calculate performance metrics: {e}")
            return self._create_empty_performance_metrics(symbol, strategy_name, timeframe)

    def _create_empty_performance_metrics(self, symbol: str, strategy_name: str, timeframe: str) -> PerformanceMetrics:
        """Create empty performance metrics for strategies with no trades"""
        return PerformanceMetrics(
            strategy_name=strategy_name,
            symbol=symbol,
            timeframe=timeframe,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            win_rate=0,
            total_return=0,
            annualized_return=0,
            roi=0,
            expectancy=0,
            max_drawdown=0,
            sharpe_ratio=0,
            sortino_ratio=0,
            calmar_ratio=0,
            profit_factor=0,
            recovery_factor=0,
            payoff_ratio=0,
            kelly_criterion=0,
            avg_win=0,
            avg_loss=0,
            largest_win=0,
            largest_loss=0,
            avg_holding_period=0,
            capital_utilization=0,
            return_on_margin=0,
        )

    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """Calculate maximum drawdown"""
        try:
            if len(returns) == 0:
                return 0

            cumulative = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = cumulative - running_max
            max_drawdown = np.min(drawdown)

            return abs(max_drawdown)

        except Exception as e:
            self.logger.error(f"Failed to calculate max drawdown: {e}")
            return 0

    def _calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.06) -> float:
        """Calculate Sharpe ratio"""
        try:
            if len(returns) == 0:
                return 0

            excess_returns = returns - (risk_free_rate / 252)  # Daily risk-free rate

            if np.std(excess_returns) == 0:
                return 0

            sharpe = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            return sharpe

        except Exception as e:
            self.logger.error(f"Failed to calculate Sharpe ratio: {e}")
            return 0

    def _calculate_sortino_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.06) -> float:
        """Calculate Sortino ratio"""
        try:
            if len(returns) == 0:
                return 0

            excess_returns = returns - (risk_free_rate / 252)
            downside_returns = excess_returns[excess_returns < 0]

            if len(downside_returns) == 0 or np.std(downside_returns) == 0:
                return 0

            sortino = np.mean(excess_returns) / np.std(downside_returns) * np.sqrt(252)
            return sortino

        except Exception as e:
            self.logger.error(f"Failed to calculate Sortino ratio: {e}")
            return 0

    def _calculate_kelly_criterion(self, wins: List[float], losses: List[float], win_rate: float) -> float:
        """Calculate Kelly criterion for optimal position sizing"""
        try:
            if not wins or not losses or win_rate == 0:
                return 0

            avg_win = np.mean(wins)
            avg_loss = abs(np.mean(losses))

            if avg_loss == 0:
                return 0

            win_prob = win_rate / 100
            loss_prob = 1 - win_prob

            kelly = (win_prob * avg_win - loss_prob * avg_loss) / avg_win

            # Cap Kelly at reasonable levels
            return max(0, min(kelly, 0.25))  # Max 25% of capital

        except Exception as e:
            self.logger.error(f"Failed to calculate Kelly criterion: {e}")
            return 0

    def _calculate_capital_utilization(self, trades: List[TradeResult]) -> float:
        """Calculate capital utilization efficiency"""
        try:
            if not trades:
                return 0

            total_position_size = sum(trade.position_size for trade in trades)
            avg_position_size = total_position_size / len(trades)

            utilization = (avg_position_size / self.config.initial_capital) * 100
            return min(utilization, 100)  # Cap at 100%

        except Exception as e:
            self.logger.error(f"Failed to calculate capital utilization: {e}")
            return 0

    def _annualize_return(self, roi: float, avg_holding_period_hours: float) -> float:
        """Annualize return based on average holding period"""
        try:
            if avg_holding_period_hours <= 0:
                return 0

            # Convert to annual return
            trades_per_year = (365 * 24) / avg_holding_period_hours
            annualized = (1 + roi / 100) ** trades_per_year - 1

            return annualized * 100

        except Exception as e:
            self.logger.error(f"Failed to annualize return: {e}")
            return roi

    async def _run_probabilistic_backtesting(self) -> List[BacktestResults]:
        """Run probabilistic (Monte Carlo) backtesting mode"""
        try:
            self.logger.info("🎲 Running Probabilistic Backtesting (Monte Carlo)...")

            # For now, implement a simplified version
            # In production, this would include bootstrap sampling, scenario generation, etc.

            # Run deterministic backtesting as base
            base_results = await self._run_deterministic_backtesting()

            # TODO: Implement Monte Carlo simulation
            # - Bootstrap sampling of historical data
            # - Multiple simulation runs
            # - Confidence interval calculation
            # - Scenario generation

            self.logger.info("[SUCCESS] Probabilistic backtesting completed")
            return base_results

        except Exception as e:
            self.logger.error(f"[ERROR] Probabilistic backtesting failed: {e}")
            return []

    async def _run_adaptive_ai_backtesting(self) -> List[BacktestResults]:
        """Run adaptive AI backtesting mode"""
        try:
            self.logger.info("[AGENT] Running Adaptive AI Backtesting...")

            # For now, implement a simplified version
            # In production, this would include:
            # - Online learning algorithms
            # - Parameter adaptation based on performance
            # - Reinforcement learning integration

            # Run deterministic backtesting as base
            base_results = await self._run_deterministic_backtesting()

            # TODO: Implement adaptive AI features
            # - Parameter optimization during backtesting
            # - Strategy adaptation based on market conditions
            # - Reinforcement learning feedback

            self.logger.info("[SUCCESS] Adaptive AI backtesting completed")
            return base_results

        except Exception as e:
            self.logger.error(f"[ERROR] Adaptive AI backtesting failed: {e}")
            return []

    async def _process_and_save_results(self, results: List[BacktestResults]):
        """Process and save backtesting results as individual trade data for AI training"""
        try:
            self.logger.info(f"💾 Processing and saving {len(results)} backtest results...")

            # Extract all individual trades from all results
            all_trades_data = []

            for result in results:
                # Convert each trade to the required format
                for trade in result.trades:
                    # Convert TradeResult to dictionary with all required columns
                    trade_row = self._convert_trade_to_ai_format(trade, result)
                    all_trades_data.append(trade_row)

            if not all_trades_data:
                self.logger.warning("[WARN] No trades found in results")
                return

            # Convert to Polars DataFrame
            trades_df = pl.DataFrame(all_trades_data)

            self.logger.info(f"[STATUS] Processed {len(all_trades_data)} individual trades for AI training")

            # Save individual trade data (main output for AI training)
            await self._save_trades_for_ai_training(trades_df)

            # Also save aggregated strategy performance summary
            await self._save_strategy_performance_summary(results)

            # Save detailed results if versioning is enabled
            if self.config.enable_versioning:
                await self._save_detailed_results(results)

            self.logger.info("[SUCCESS] Results processed and saved successfully")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to process and save results: {e}")

    def _convert_trade_to_ai_format(self, trade: TradeResult, result: BacktestResults) -> Dict[str, Any]:
        """Convert TradeResult to AI training format with all required columns"""
        try:
            # TradeResult already has all the required fields from our redesign
            # Just convert to dictionary format
            trade_dict = {
                # ═══════════════════════════════════════════════════════════════════════════════
                # 📌 A. Trade Metadata Columns
                # ═══════════════════════════════════════════════════════════════════════════════
                'trade_id': trade.trade_id,
                'timestamp': trade.timestamp,
                'exit_timestamp': trade.exit_timestamp,
                'symbol': trade.symbol,
                'underlying': trade.underlying,
                'expiry_type': trade.expiry_type,
                'option_type': trade.option_type,
                'lot_size': trade.lot_size,
                'direction': trade.direction,
                'strategy_id': trade.strategy_id,
                'strategy_name': trade.strategy_name,
                'timeframe': trade.timeframe,
                'market_regime': trade.market_regime,
                'volatility_regime': trade.volatility_regime,
                'event_tag': trade.event_tag,

                # ═══════════════════════════════════════════════════════════════════════════════
                # 📌 B. Feature Snapshot (at Entry Time)
                # ═══════════════════════════════════════════════════════════════════════════════
                'rsi': trade.rsi,
                'rsi_5': trade.rsi_5,
                'rsi_14': trade.rsi_14,
                'macd': trade.macd,
                'macd_signal': trade.macd_signal,
                'macd_histogram': trade.macd_histogram,
                'ema_5': trade.ema_5,
                'ema_10': trade.ema_10,
                'ema_20': trade.ema_20,
                'ema_50': trade.ema_50,
                'sma_20': trade.sma_20,
                'sma_50': trade.sma_50,
                'sma_20_vs_price': trade.sma_20_vs_price,
                'bb_upper': trade.bb_upper,
                'bb_lower': trade.bb_lower,
                'bb_middle': trade.bb_middle,
                'adx': trade.adx,
                'atr': trade.atr,
                'stoch_k': trade.stoch_k,
                'stoch_d': trade.stoch_d,
                'cci': trade.cci,
                'mfi': trade.mfi,
                'volume': trade.volume,
                'volume_spike_ratio': trade.volume_spike_ratio,
                'vwap': trade.vwap,
                'price_vs_vwap': trade.price_vs_vwap,
                'supertrend': trade.supertrend,
                'iv_rank': trade.iv_rank,
                'iv_percentile': trade.iv_percentile,
                'vix': trade.vix,
                'delta': trade.delta,
                'gamma': trade.gamma,
                'theta': trade.theta,
                'vega': trade.vega,
                'open_interest': trade.open_interest,
                'open_interest_change': trade.open_interest_change,
                'hour_of_day': trade.hour_of_day,
                'day_of_week': trade.day_of_week,
                'days_to_expiry': trade.days_to_expiry,
                'nifty_level': trade.nifty_level,
                'banknifty_level': trade.banknifty_level,

                # ═══════════════════════════════════════════════════════════════════════════════
                # 📌 C. Trade Outcome / Label Columns
                # ═══════════════════════════════════════════════════════════════════════════════
                'entry_price': trade.entry_price,
                'exit_price': trade.exit_price,
                'pnl_abs': trade.pnl_abs,
                'pnl_pct': trade.pnl_pct,
                'holding_minutes': trade.holding_minutes,
                'is_profitable': trade.is_profitable,
                'target_hit': trade.target_hit,
                'exit_reason': trade.exit_reason,
                'expectancy_tag': trade.expectancy_tag,
                'sharpe_score_tag': trade.sharpe_score_tag,
                'confidence_score': trade.confidence_score,
                'trade_score': trade.trade_score,
                'label_strategy_choice': trade.label_strategy_choice,
                'position_size': trade.position_size,
                'quantity': trade.quantity,
                'risk_reward_ratio': trade.risk_reward_ratio,
                'max_adverse_excursion': trade.max_adverse_excursion,
                'max_favorable_excursion': trade.max_favorable_excursion,
                'slippage': trade.slippage,
                'transaction_cost': trade.transaction_cost,

                # ═══════════════════════════════════════════════════════════════════════════════
                # 📌 D. Optional Advanced Tags
                # ═══════════════════════════════════════════════════════════════════════════════
                'model_version_used': trade.model_version_used,
                'was_signal_live': trade.was_signal_live,
                'live_vs_backtest_diff_pct': trade.live_vs_backtest_diff_pct,
                'used_in_training': trade.used_in_training,
                'llm_summary': trade.llm_summary,

                # Additional metadata from backtest result
                'backtest_id': result.backtest_id,
                'backtest_created_at': result.created_at,
                'backtest_version': result.version,
            }

            return trade_dict

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to convert trade to AI format: {e}")
            return {}

    async def _save_trades_for_ai_training(self, trades_df: pl.DataFrame):
        """Save individual trade data for AI training"""
        try:
            # Create output filename with timestamp for AI training data
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ai_training_trades_{timestamp}.{self.config.output_format}"
            output_path = os.path.join(self.config.output_directory, filename)

            # Save based on format
            if self.config.output_format == "parquet":
                trades_df.write_parquet(output_path, compression=self.config.compression)
            elif self.config.output_format == "csv":
                trades_df.write_csv(output_path)
            elif self.config.output_format == "json":
                trades_df.write_json(output_path)
            else:
                self.logger.error(f"[ERROR] Unsupported output format: {self.config.output_format}")
                return

            self.logger.info(f"💾 AI Training trades data saved to {output_path}")
            self.logger.info(f"[STATUS] Total trades for AI training: {trades_df.height:,}")

            # Also save a latest copy for easy access
            latest_path = os.path.join(self.config.output_directory, f"latest_ai_training_trades.{self.config.output_format}")
            if self.config.output_format == "parquet":
                trades_df.write_parquet(latest_path, compression=self.config.compression)
            elif self.config.output_format == "csv":
                trades_df.write_csv(latest_path)
            elif self.config.output_format == "json":
                trades_df.write_json(latest_path)

            # Log column summary for verification
            self.logger.info("[LIST] AI Training Data Columns:")
            for i, col in enumerate(trades_df.columns):
                if i < 10:  # Show first 10 columns
                    self.logger.info(f"   • {col}")
                elif i == 10:
                    self.logger.info(f"   • ... and {len(trades_df.columns) - 10} more columns")
                    break

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to save AI training trades data: {e}")

    async def _save_strategy_performance_summary(self, results: List[BacktestResults]):
        """Save aggregated strategy performance summary"""
        try:
            # Create strategy-level summary
            strategy_summary = []

            for result in results:
                metrics = result.performance_metrics

                summary_row = {
                    'backtest_id': result.backtest_id,
                    'strategy_name': metrics.strategy_name,
                    'symbol': metrics.symbol,
                    'timeframe': metrics.timeframe,

                    # Performance metrics
                    'total_trades': metrics.total_trades,
                    'winning_trades': metrics.winning_trades,
                    'losing_trades': metrics.losing_trades,
                    'win_rate': metrics.win_rate,
                    'total_return': metrics.total_return,
                    'roi': metrics.roi,
                    'annualized_return': metrics.annualized_return,
                    'expectancy': metrics.expectancy,

                    # Risk metrics
                    'max_drawdown': metrics.max_drawdown,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'sortino_ratio': metrics.sortino_ratio,
                    'calmar_ratio': metrics.calmar_ratio,

                    # Advanced metrics
                    'profit_factor': metrics.profit_factor,
                    'recovery_factor': metrics.recovery_factor,
                    'payoff_ratio': metrics.payoff_ratio,
                    'kelly_criterion': metrics.kelly_criterion,

                    # Trade analysis
                    'avg_win': metrics.avg_win,
                    'avg_loss': metrics.avg_loss,
                    'largest_win': metrics.largest_win,
                    'largest_loss': metrics.largest_loss,
                    'avg_holding_period': metrics.avg_holding_period,

                    # Capital efficiency
                    'capital_utilization': metrics.capital_utilization,
                    'return_on_margin': metrics.return_on_margin,

                    # Execution details
                    'execution_time_seconds': result.execution_time_seconds,
                    'total_data_points': result.total_data_points,
                    'data_quality_score': result.data_quality_score,

                    # Metadata
                    'created_at': result.created_at,
                    'version': result.version,
                }

                strategy_summary.append(summary_row)

            # Convert to DataFrame and save
            summary_df = pl.DataFrame(strategy_summary)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"strategy_performance_summary_{timestamp}.{self.config.output_format}"
            output_path = os.path.join(self.config.output_directory, filename)

            if self.config.output_format == "parquet":
                summary_df.write_parquet(output_path, compression=self.config.compression)
            elif self.config.output_format == "csv":
                summary_df.write_csv(output_path)
            elif self.config.output_format == "json":
                summary_df.write_json(output_path)

            self.logger.info(f"[METRICS] Strategy performance summary saved to {output_path}")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to save strategy performance summary: {e}")

    async def _save_results_to_file(self, results_df: pl.DataFrame):
        """Save results to file"""
        try:
            # Create output filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_backtest_results_{timestamp}.{self.config.output_format}"
            output_path = os.path.join(self.config.output_directory, filename)

            # Save based on format
            if self.config.output_format == "parquet":
                results_df.write_parquet(output_path, compression=self.config.compression)
            elif self.config.output_format == "csv":
                results_df.write_csv(output_path)
            elif self.config.output_format == "json":
                results_df.write_json(output_path)
            else:
                self.logger.error(f"[ERROR] Unsupported output format: {self.config.output_format}")
                return

            self.logger.info(f"💾 Results saved to {output_path}")

            # Also save a latest copy
            latest_path = os.path.join(self.config.output_directory, f"latest_results.{self.config.output_format}")
            if self.config.output_format == "parquet":
                results_df.write_parquet(latest_path, compression=self.config.compression)
            elif self.config.output_format == "csv":
                results_df.write_csv(latest_path)
            elif self.config.output_format == "json":
                results_df.write_json(latest_path)

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to save results to file: {e}")

    async def _save_detailed_results(self, results: List[BacktestResults]):
        """Save detailed results with individual trades"""
        try:
            if not self.config.enable_versioning:
                return

            version_dir = os.path.join(self.config.output_directory, "versions")
            os.makedirs(version_dir, exist_ok=True)

            for result in results:
                # Create detailed result file
                result_filename = f"{result.backtest_id}_detailed.json"
                result_path = os.path.join(version_dir, result_filename)

                # Convert result to dictionary
                result_dict = {
                    'backtest_id': result.backtest_id,
                    'config': {
                        'agent_name': result.config.agent_name,
                        'version': result.config.version,
                        'backtesting_mode': result.config.backtesting_mode.value,
                        'initial_capital': result.config.initial_capital,
                        'risk_per_trade_pct': result.config.risk_per_trade_pct,
                    },
                    'performance_metrics': {
                        'strategy_name': result.performance_metrics.strategy_name,
                        'symbol': result.performance_metrics.symbol,
                        'timeframe': result.performance_metrics.timeframe,
                        'total_trades': result.performance_metrics.total_trades,
                        'win_rate': result.performance_metrics.win_rate,
                        'roi': result.performance_metrics.roi,
                        'sharpe_ratio': result.performance_metrics.sharpe_ratio,
                        'max_drawdown': result.performance_metrics.max_drawdown,
                    },
                    'trades': [
                        {
                            'trade_id': trade.trade_id,
                            'entry_time': trade.entry_time.isoformat(),
                            'exit_time': trade.exit_time.isoformat(),
                            'entry_price': trade.entry_price,
                            'exit_price': trade.exit_price,
                            'pnl': trade.pnl,
                            'pnl_pct': trade.pnl_pct,
                            'holding_period': trade.holding_period,
                            'exit_reason': trade.exit_reason,
                        }
                        for trade in result.trades
                    ],
                    'execution_details': {
                        'start_time': result.start_time.isoformat(),
                        'end_time': result.end_time.isoformat(),
                        'execution_time_seconds': result.execution_time_seconds,
                        'total_data_points': result.total_data_points,
                    },
                    'created_at': result.created_at.isoformat(),
                }

                # Save to JSON file
                with open(result_path, 'w', encoding='utf-8') as f:
                    json.dump(result_dict, f, indent=2, ensure_ascii=False)

            self.logger.info(f"💾 Detailed results saved for {len(results)} backtests")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to save detailed results: {e}")

    async def _generate_llm_summary(self, results: List[BacktestResults]):
        """Generate LLM-powered summary of results"""
        try:
            if not self.config.enable_llm_summary:
                return

            self.logger.info("[AGENT] Generating LLM summary...")

            # Aggregate results for summary
            total_strategies = len(results)
            profitable_strategies = len([r for r in results if r.performance_metrics.roi > 0])

            best_strategy = max(results, key=lambda r: r.performance_metrics.roi) if results else None
            worst_strategy = min(results, key=lambda r: r.performance_metrics.roi) if results else None

            # Create summary text
            summary_text = f"""
# Enhanced Backtesting Results Summary

## Overview
- **Total Strategies Tested**: {total_strategies}
- **Profitable Strategies**: {profitable_strategies} ({(profitable_strategies/total_strategies)*100:.1f}%)
- **Testing Period**: {self.config.timeframes}
- **Initial Capital**: Rs.{self.config.initial_capital:,.0f}

## Best Performing Strategy
"""

            if best_strategy:
                summary_text += f"""
- **Strategy**: {best_strategy.performance_metrics.strategy_name}
- **Symbol**: {best_strategy.performance_metrics.symbol}
- **ROI**: {best_strategy.performance_metrics.roi:.2f}%
- **Sharpe Ratio**: {best_strategy.performance_metrics.sharpe_ratio:.2f}
- **Win Rate**: {best_strategy.performance_metrics.win_rate:.1f}%
- **Max Drawdown**: {best_strategy.performance_metrics.max_drawdown:.2f}%
"""

            summary_text += f"""
## Risk Analysis
- **Average Max Drawdown**: {np.mean([r.performance_metrics.max_drawdown for r in results]):.2f}%
- **Average Sharpe Ratio**: {np.mean([r.performance_metrics.sharpe_ratio for r in results]):.2f}

## Recommendations
Based on the backtesting results, consider focusing on strategies with:
1. Sharpe ratio > 1.0
2. Maximum drawdown < 10%
3. Win rate > 50%
4. Consistent performance across different market conditions
"""

            # Save summary
            summary_path = os.path.join(self.config.output_directory, "summaries", f"backtest_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
            os.makedirs(os.path.dirname(summary_path), exist_ok=True)

            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_text)

            self.logger.info(f"📄 LLM summary saved to {summary_path}")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to generate LLM summary: {e}")

    async def _generate_visualizations(self, results: List[BacktestResults]):
        """Generate visualizations for results"""
        try:
            if not self.config.enable_visualization or not PLOTLY_AVAILABLE:
                return

            self.logger.info("[STATUS] Generating visualizations...")

            # TODO: Implement visualization generation
            # - Performance comparison charts
            # - Risk-return scatter plots
            # - Drawdown charts
            # - Strategy performance heatmaps

            self.logger.info("[SUCCESS] Visualizations generated")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to generate visualizations: {e}")

    async def stop(self):
        """Stop the backtesting agent"""
        try:
            self.logger.info("[STOP] Stopping Enhanced Backtesting Agent...")
            self.is_running = False

            # Cleanup resources
            self.feature_data.clear()
            gc.collect()

            self.logger.info("[SUCCESS] Enhanced Backtesting Agent stopped")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to stop agent: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get current status of the backtesting agent"""
        try:
            status = {
                'is_running': self.is_running,
                'current_backtest_id': self.current_backtest_id,
                'processed_strategies': self.processed_strategies,
                'total_strategies': self.total_strategies,
                'progress_pct': (self.processed_strategies / max(self.total_strategies, 1)) * 100,
                'config': {
                    'agent_name': self.config.agent_name if self.config else 'Not loaded',
                    'version': self.config.version if self.config else 'Unknown',
                    'backtesting_mode': self.config.backtesting_mode.value if self.config else 'Unknown',
                },
                'gpu_available': self.gpu_available,
                'start_time': self.start_time,
                'execution_time': time.time() - self.start_time if self.start_time else 0,
            }

            return status

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to get status: {e}")
            return {'error': str(e)}

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function for testing"""
    try:
        # Create and initialize agent
        agent = EnhancedBacktestingAgent()

        # Start backtesting
        success = await agent.start_backtesting()

        if success:
            print("[SUCCESS] Enhanced backtesting completed successfully!")
        else:
            print("[ERROR] Enhanced backtesting failed!")

        # Stop agent
        await agent.stop()

    except Exception as e:
        print(f"[ERROR] Main execution failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())

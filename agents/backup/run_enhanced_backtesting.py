#!/usr/bin/env python3
"""
Runner script for Enhanced Backtesting System with Individual Symbol Processing
Demonstrates usage and provides command-line interface for processing individual symbol files
"""

import argparse
import logging
import sys
import os
from pathlib import Path

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_backtesting_polars import main_async, logger, extract_symbol_and_timeframe_from_filename
import asyncio

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('backtesting.log')
        ]
    )

def validate_environment():
    """Validate that required files and directories exist"""
    errors = []

    # Helper function to check paths (try both relative and parent directory)
    def check_path(path):
        if Path(path).exists():
            return path
        elif Path(f"../{path}").exists():
            return f"../{path}"
        else:
            return None

    # Check data directory
    data_dir = check_path("data/features")
    if not data_dir:
        errors.append("data/features directory not found")
    else:
        # Check for feature files with individual symbol pattern
        feature_files = list(Path(data_dir).glob("*.parquet"))
        if not feature_files:
            errors.append("No parquet files found in data/features directory")
        else:
            # Validate that files follow expected naming pattern
            valid_files = 0
            for file_path in feature_files:
                symbol, timeframe = extract_symbol_and_timeframe_from_filename(file_path.name)
                if symbol and timeframe:
                    valid_files += 1
            
            if valid_files == 0:
                errors.append("No files with valid naming pattern found (expected: features_SYMBOL_TIMEFRAME.parquet)")
            else:
                logger.info(f"Found {valid_files} valid feature files out of {len(feature_files)} total files")

    # Check strategies file
    strategies_file = check_path("config/strategies.yaml")
    if not strategies_file:
        errors.append("config/strategies.yaml file not found")

    # Check output directory (create if doesn't exist)
    output_dir = check_path("data/backtest")
    if not output_dir:
        Path("../data/backtest").mkdir(parents=True, exist_ok=True)
        logger.info("Created output directory: ../data/backtest")
    else:
        Path(output_dir).mkdir(parents=True, exist_ok=True)

    if errors:
        logger.error("Environment validation failed:")
        for error in errors:
            logger.error(f"  - {error}")
        return False

    return True

def print_system_info():
    """Print system information and configuration"""
    logger.info("[CONFIG] System Information:")
    logger.info(f"  Python version: {sys.version}")
    
    try:
        import polars as pl
        logger.info(f"  Polars version: {pl.__version__}")
    except ImportError:
        logger.error("  Polars not installed!")
    
    try:
        import pyarrow as pa
        logger.info(f"  PyArrow version: {pa.__version__}")
    except ImportError:
        logger.error("  PyArrow not installed!")
    
    # Check available feature files
    data_dir = "data/features" if Path("data/features").exists() else "../data/features"
    if Path(data_dir).exists():
        feature_files = list(Path(data_dir).glob("*.parquet"))
        logger.info(f"  Available feature files: {len(feature_files)}")
        
        # Group by symbol and timeframe for better overview
        symbol_timeframe_count = {}
        valid_files = []
        
        for file in feature_files:
            symbol, timeframe = extract_symbol_and_timeframe_from_filename(file.name)
            if symbol and timeframe:
                valid_files.append((file, symbol, timeframe))
                key = f"{timeframe}"
                symbol_timeframe_count[key] = symbol_timeframe_count.get(key, 0) + 1
        
        logger.info(f"  Valid files by timeframe:")
        for timeframe, count in sorted(symbol_timeframe_count.items()):
            logger.info(f"    - {timeframe}: {count} symbols")
        
        logger.info(f"  Sample files:")
        for file, symbol, timeframe in valid_files[:5]:  # Show first 5
            file_size = file.stat().st_size / (1024 * 1024)  # MB
            logger.info(f"    - {file.name}: {symbol} ({timeframe}) - {file_size:.1f} MB")
        
        if len(valid_files) > 5:
            logger.info(f"    ... and {len(valid_files) - 5} more files")
            
        if len(valid_files) < len(feature_files):
            invalid_count = len(feature_files) - len(valid_files)
            logger.warning(f"  {invalid_count} files have invalid naming patterns and will be skipped")
            
    else:
        logger.info("  Available feature files: 0 (data directory not found)")

def list_available_symbols():
    """List all available symbols and timeframes"""
    data_dir = "data/features" if Path("data/features").exists() else "../data/features"
    if not Path(data_dir).exists():
        logger.error("Data directory not found")
        return
    
    feature_files = list(Path(data_dir).glob("*.parquet"))
    if not feature_files:
        logger.error("No feature files found")
        return
    
    symbols_by_timeframe = {}
    
    for file_path in feature_files:
        symbol, timeframe = extract_symbol_and_timeframe_from_filename(file_path.name)
        if symbol and timeframe:
            if timeframe not in symbols_by_timeframe:
                symbols_by_timeframe[timeframe] = []
            symbols_by_timeframe[timeframe].append(symbol)
    
    logger.info("\n[AVAILABLE SYMBOLS BY TIMEFRAME]")
    logger.info("=" * 50)
    
    for timeframe in sorted(symbols_by_timeframe.keys()):
        symbols = sorted(symbols_by_timeframe[timeframe])
        logger.info(f"\n{timeframe.upper()} ({len(symbols)} symbols):")
        
        # Print symbols in columns
        for i in range(0, len(symbols), 5):
            row_symbols = symbols[i:i+5]
            logger.info(f"  {' | '.join(f'{s:<12}' for s in row_symbols)}")

async def run_backtesting_with_config(max_symbols: int = None, max_strategies: int = None, 
                                    concurrent_strategies: int = None, target_symbol: str = None,
                                    target_timeframe: str = None):
    """Run backtesting with custom configuration"""
    
    # Update global configuration if provided
    if max_symbols is not None:
        import enhanced_backtesting_polars
        enhanced_backtesting_polars.MAX_SYMBOLS = max_symbols
        logger.info(f"[CONFIG] Limited to {max_symbols} symbols for testing")
    
    if max_strategies is not None:
        import enhanced_backtesting_polars
        enhanced_backtesting_polars.MAX_STRATEGIES = max_strategies
        logger.info(f"[CONFIG] Limited to {max_strategies} strategies for testing")
    
    if concurrent_strategies is not None:
        import enhanced_backtesting_polars
        enhanced_backtesting_polars.CONCURRENT_STRATEGIES = concurrent_strategies
        logger.info(f"[CONFIG] Concurrent strategies: {concurrent_strategies}")
    
    # Filter files if specific symbol/timeframe requested
    if target_symbol or target_timeframe:
        import enhanced_backtesting_polars as ebp
        
        # Get original file list
        original_get_files = ebp.get_available_feature_files
        
        def filtered_get_files():
            all_files = original_get_files()
            filtered = []
            
            for file_path, symbol, timeframe in all_files:
                include_file = True
                
                if target_symbol and symbol.upper() != target_symbol.upper():
                    include_file = False
                
                if target_timeframe and timeframe.lower() != target_timeframe.lower():
                    include_file = False
                
                if include_file:
                    filtered.append((file_path, symbol, timeframe))
            
            if target_symbol:
                logger.info(f"[FILTER] Filtering to symbol: {target_symbol}")
            if target_timeframe:
                logger.info(f"[FILTER] Filtering to timeframe: {target_timeframe}")
            logger.info(f"[FILTER] Filtered to {len(filtered)} files")
            
            return filtered
        
        # Replace the function
        ebp.get_available_feature_files = filtered_get_files
    
    # Run the main backtesting function
    await main_async()

def main():
    """Main entry point with command-line interface"""
    parser = argparse.ArgumentParser(
        description='Enhanced Backtesting System with Individual Symbol Processing',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run full backtesting on all symbols
  python run_enhanced_backtesting.py
  
  # Test with limited strategies
  python run_enhanced_backtesting.py --max-strategies 10
  
  # Process only specific symbol
  python run_enhanced_backtesting.py --symbol 360ONE
  
  # Process only specific timeframe
  python run_enhanced_backtesting.py --timeframe 1min
  
  # Process specific symbol and timeframe
  python run_enhanced_backtesting.py --symbol 360ONE --timeframe 1min
  
  # Debug mode with limited data
  python run_enhanced_backtesting.py --log-level DEBUG --max-strategies 5 --symbol RELIANCE
  
  # List available symbols
  python run_enhanced_backtesting.py --list-symbols
        """
    )
    
    parser.add_argument('--max-symbols', type=int, 
                       help='Limit number of symbols for testing (default: all)')
    parser.add_argument('--max-strategies', type=int,
                       help='Limit number of strategies for testing (default: all)')
    parser.add_argument('--concurrent-strategies', type=int, default=7,
                       help='Number of strategies to process concurrently (default: 7)')
    parser.add_argument('--symbol', type=str,
                       help='Process only specific symbol (e.g., 360ONE, RELIANCE)')
    parser.add_argument('--timeframe', type=str,
                       help='Process only specific timeframe (e.g., 1min, 5min, 15min)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--validate-only', action='store_true',
                       help='Only validate environment and exit')
    parser.add_argument('--system-info', action='store_true',
                       help='Print system information and exit')
    parser.add_argument('--list-symbols', action='store_true',
                       help='List all available symbols by timeframe and exit')
    parser.add_argument('--disable-multiprocessing', action='store_true',
                       help='Disable multiprocessing for CPU-only comparison')
    parser.add_argument('--disable-gpu', action='store_true',
                       help='Disable GPU acceleration')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # List available symbols if requested
    if args.list_symbols:
        list_available_symbols()
        return
    
    # Print system info if requested
    if args.system_info:
        print_system_info()
        return
    
    # Validate environment
    if not validate_environment():
        logger.error("[ERROR] Environment validation failed. Please fix the issues above.")
        sys.exit(1)
    
    if args.validate_only:
        logger.info("[SUCCESS] Environment validation passed!")
        return
    
    # Print system info
    print_system_info()
    
    try:
        # Apply configuration overrides
        if args.disable_multiprocessing:
            import enhanced_backtesting_polars as ebp
            ebp.USE_MULTIPROCESSING = False
            logger.info("[CONFIG] Multiprocessing disabled for comparison")

        if args.disable_gpu:
            import enhanced_backtesting_polars as ebp
            ebp.USE_GPU_ACCELERATION = False
            logger.info("[CONFIG] GPU acceleration disabled for comparison")

        # Run backtesting
        asyncio.run(run_backtesting_with_config(
            max_symbols=args.max_symbols,
            max_strategies=args.max_strategies,
            concurrent_strategies=args.concurrent_strategies,
            target_symbol=args.symbol,
            target_timeframe=args.timeframe
        ))
        
        logger.info("🎉 Backtesting completed successfully!")
        
        # Show output files
        output_dir = "../data/backtest" if Path("../data/backtest").exists() else "data/backtest"
        if Path(output_dir).exists():
            output_files = list(Path(output_dir).glob("backtest_*.parquet"))
            if output_files:
                logger.info(f"\n[OUTPUT FILES] Generated {len(output_files)} result files:")
                for output_file in sorted(output_files):
                    file_size = output_file.stat().st_size / (1024 * 1024)
                    logger.info(f"  - {output_file.name}: {file_size:.1f} MB")
        
    except KeyboardInterrupt:
        logger.info("⏹️ Backtesting interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"[ERROR] Backtesting failed: {e}")
        if args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
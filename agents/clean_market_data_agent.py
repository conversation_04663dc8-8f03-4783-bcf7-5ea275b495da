#!/usr/bin/env python3
"""
CLEAN MARKET DATA AGENT
Modern implementation with proper async handling and rate limiting

Features:
- Proper SmartAPI date format handling (YYYY-MM-DD HH:MM)
- Modern timeframes: 1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr
- Clean async/await patterns
- WebSocket v2 integration
- Comprehensive error handling
- Global rate limiting to prevent API rate limit errors
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import polars as pl
import random
import os
import json
from pathlib import Path
import time
import threading
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from core.base_agent import BaseAgent
from core.event_system import EventBus, EventTypes
from core.smartapi_client import ModernSmartAPIClient, SmartAPICredentials, HistoricalDataRequest

logger = logging.getLogger(__name__)

class CleanMarketDataAgent(BaseAgent):
    """
    Clean Market Data Agent with modern architecture

    Features:
    - Proper date format handling
    - Modern timeframes
    - Clean async patterns
    - WebSocket v2 integration
    - Global rate limiting
    """

    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("CleanMarketDataAgent", event_bus, config, session_id)

        # SmartAPI client
        self.smartapi_client = None

        # Data storage with training timeframes
        training_timeframes = getattr(config, 'timeframes', ["1min", "3min", "5min", "15min"])
        self.timeframe_data = {tf: {} for tf in training_timeframes}

        # Real-time data
        self.realtime_data = {}

        # Instrument mapping
        self.instrument_map = {}
        self.symbol_to_token_map = self._load_token_mapping()

        # Configuration - will be updated dynamically by stock selector
        self.config = config
        self.selected_stocks = []  # Start empty, will be updated by stock selector

        # Retry configuration for historical data downloads - more conservative approach
        self.retry_config = {
            'max_retries': getattr(config, 'data_download_max_retries', 3),
            'base_delay': getattr(config, 'data_download_base_delay', 3.0),  # Increased from 2.0 to 3.0 seconds
            'max_delay': getattr(config, 'data_download_max_delay', 60.0),   # Increased from 30.0 to 60.0 seconds
            'exponential_base': getattr(config, 'data_download_exponential_base', 2.0),
            'jitter': getattr(config, 'data_download_jitter', True)
        }

        # Download workers for concurrent data fetching
        self.data_cache: Dict[str, pl.DataFrame] = {}
        self.download_queue = asyncio.Queue()
        # Reduced from 10 to 5 to match the working implementation's conservative approach
        self.num_download_workers = 5
        self.download_workers: List[asyncio.Task] = []

        self.log_info(f"Initialized with {self.num_download_workers} download workers with rate limiting.")

    def _load_token_mapping(self) -> Dict[str, str]:
        try:
            mapping_file_path = Path(__file__).parent.parent / 'token_mapping.json'
            with open(mapping_file_path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Successfully loaded token mapping from {mapping_file_path}")
                return data.get("symbol_to_token", {})
        except Exception as e:
            self.log_error(f"Could not load or parse token_mapping.json: {e}")
            return {}

    def _load_credentials(self) -> Optional[SmartAPICredentials]:
        try:
            api_key, username, password, totp_token = (
                os.getenv('SMARTAPI_API_KEY'), os.getenv('SMARTAPI_USERNAME'),
                os.getenv('SMARTAPI_PASSWORD'), os.getenv('SMARTAPI_TOTP_TOKEN')
            )
            if all((api_key, username, password, totp_token)):
                return SmartAPICredentials(api_key, username, password, totp_token)
        except Exception as e:
            self.log_error(f"Failed to load credentials: {e}")
        return None

    async def initialize(self) -> bool:
        """Initialize the market data agent"""
        try:
            self.log_info("Initializing Clean Market Data Agent...")

            # Load credentials
            credentials = self._load_credentials()
            if not credentials:
                self.log_warning("No SmartAPI credentials found, running in demo mode")
                self.initialized = True
                return True

            # Initialize SmartAPI client
            self.smartapi_client = ModernSmartAPIClient(credentials)

            # Authenticate
            auth_success = await self.smartapi_client.authenticate()
            if not auth_success:
                self.log_error("SmartAPI authentication failed, falling back to demo mode")
                self.smartapi_client = None

            # Subscribe to events
            self.event_bus.subscribe("REQUEST_HISTORICAL_DATA", self._handle_data_request)
            self.event_bus.subscribe("STOCK_UNIVERSE_UPDATED", self._handle_universe_update)

            self.initialized = True
            self.log_info("Clean Market Data Agent initialized successfully")
            return True

        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False

    async def start(self):
        self.log_info("Starting download workers...")
        self.running = True
        self.download_workers = [
            asyncio.create_task(self._download_worker(i)) for i in range(self.num_download_workers)
        ]

        # Start heartbeat task to keep agent active
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())

        # If we have selected stocks, start websocket streaming (production mode - no demo data)
        if hasattr(self.config, 'selected_stocks') and self.config.selected_stocks:
            self.log_info(f"Starting live data streaming for {len(self.config.selected_stocks)} selected stocks...")

            # Start websocket live data streaming for selected stocks
            # This will fail if websocket cannot be established (production behavior)
            await self._start_websocket_streaming(self.config.selected_stocks)

            # Note: No demo data pre-population in production mode

    async def _populate_demo_data(self, stocks: List[str]):
        """Pre-populate demo data for immediate signal generation"""
        try:
            self.log_info(f"Pre-populating demo data for {len(stocks)} stocks...")
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            for symbol in stocks:
                try:
                    # Create demo data for 1min timeframe
                    demo_data = self._create_demo_data(symbol, start_date, end_date, "1min")
                    
                    # Cache the data
                    cache_key = f"{symbol}_1min"
                    self.data_cache[cache_key] = demo_data
                    
                    # Also store in timeframe_data for signal generation
                    if "1min" not in self.timeframe_data:
                        self.timeframe_data["1min"] = {}
                    self.timeframe_data["1min"][symbol] = demo_data
                    
                    # Publish historical data loaded event
                    await self.event_bus.publish(
                        EventTypes.HISTORICAL_DATA_LOADED,
                        {'symbol': symbol, 'timeframe': '1min', 'data': demo_data},
                        source=self.name
                    )
                    
                    self.log_info(f"Pre-populated demo data for {symbol}: {len(demo_data)} rows")
                    
                except Exception as e:
                    self.log_error(f"Failed to pre-populate demo data for {symbol}: {e}")
                    
        except Exception as e:
            self.log_error(f"Failed to pre-populate demo data: {e}")

    async def _heartbeat_loop(self):
        """Periodic heartbeat to keep agent active"""
        try:
            while self.running:
                await asyncio.sleep(60)  # Heartbeat every minute
                if self.running:
                    self.increment_message_count()
                    self.log_debug("Market data agent heartbeat")
        except asyncio.CancelledError:
            self.log_info("Heartbeat loop cancelled")
        except Exception as e:
            self.log_error(f"Error in heartbeat loop: {e}")

    async def _start_websocket_streaming(self, stocks: List[str]):
        """Start websocket streaming for live price data."""
        try:
            self.log_info(f"🔴 Starting websocket streaming for {len(stocks)} stocks...")

            # Initialize live candle aggregators for each timeframe
            self.live_candle_aggregators = {}
            timeframes = ['1min', '3min', '5min', '15min']

            for symbol in stocks:
                self.live_candle_aggregators[symbol] = {}
                for tf in timeframes:
                    self.live_candle_aggregators[symbol][tf] = {
                        'open': None, 'high': None, 'low': None, 'close': None, 'volume': 0,
                        'timestamp': None, 'tick_count': 0
                    }

            self.log_info(f"🕒 Initialized live candle aggregators for {len(stocks)} symbols across timeframes: {', '.join(timeframes)}")

            # Check if we have authenticated SmartAPI client
            if self.smartapi_client and hasattr(self.smartapi_client, 'auth_token') and self.smartapi_client.auth_token:
                # Start real websocket connection task
                self.websocket_task = asyncio.create_task(self._websocket_handler(stocks))
                self.log_info(f"✅ Real websocket streaming initialized for {len(stocks)} stocks")
            else:
                # Start simulation mode
                self.log_warning("SmartAPI not authenticated, starting live data simulation...")
                self.websocket_task = asyncio.create_task(self._simulate_live_data(stocks))
                self.log_info(f"✅ Simulated websocket streaming initialized for {len(stocks)} stocks")

        except Exception as e:
            self.log_error(f"Error starting websocket streaming: {e}")
            # In production/paper trading mode, fail instead of falling back to demo data
            raise Exception(f"Failed to start websocket streaming in production mode: {e}")

    async def _simulate_live_data(self, stocks: List[str]):
        """Simulate live data when real websocket is not available"""
        try:
            self.log_info(f"🎭 Starting live data simulation for {len(stocks)} stocks...")
            
            # Initialize base prices for each stock
            base_prices = {}
            for symbol in stocks:
                base_prices[symbol] = random.uniform(100, 3000)
            
            while self.running:
                try:
                    # Generate simulated tick data for each stock
                    for symbol in stocks:
                        # Generate realistic price movement
                        current_price = base_prices[symbol]
                        price_change = random.uniform(-0.02, 0.02) * current_price  # ±2% change
                        new_price = max(current_price + price_change, 1.0)  # Ensure positive price
                        base_prices[symbol] = new_price
                        
                        # Create tick data
                        tick = {
                            'symbol': symbol,
                            'ltp': new_price,
                            'volume': random.randint(100, 10000),
                            'timestamp': datetime.now()
                        }
                        
                        # Process the tick data
                        await self._process_tick_data(tick)
                    
                    # Wait before next simulation cycle (simulate real-time data frequency)
                    await asyncio.sleep(1.0)  # 1 second intervals
                    
                except Exception as e:
                    self.log_error(f"Error in live data simulation: {e}")
                    await asyncio.sleep(5.0)  # Wait longer on error
                    
        except Exception as e:
            self.log_error(f"Live data simulation failed: {e}")

    async def _websocket_handler(self, stocks: List[str]):
        """Handle real websocket streaming for live price data."""
        try:
            self.log_info(f"🔗 Starting real websocket handler for {len(stocks)} stocks...")

            # Check if SmartAPI client is properly authenticated
            if not self.smartapi_client or not hasattr(self.smartapi_client, 'auth_token') or not self.smartapi_client.auth_token:
                raise Exception("SmartAPI client not authenticated - cannot start real websocket streaming")

            # Get token mapping for stocks using the loaded token mapping
            tokens = []
            for symbol in stocks:
                # Try different variations of the symbol to find token
                token = None
                possible_keys = [
                    symbol,
                    f"{symbol}_NSE",
                    f"{symbol}-EQ",
                    f"{symbol}-EQ_NSE"
                ]

                for key in possible_keys:
                    if key in self.symbol_to_token_map:
                        token = self.symbol_to_token_map[key]
                        self.log_info(f"Found token {token} for symbol {symbol} using key {key}")
                        break

                if token:
                    tokens.append({
                        "exchangeType": 1,  # NSE
                        "tokens": [token]
                    })
                else:
                    self.log_warning(f"No token found for symbol {symbol} in token mapping")

            if not tokens:
                raise Exception("No valid tokens found for websocket streaming")

            self.log_info(f"🎯 Starting websocket streaming for {len(tokens)} tokens")

            # Initialize websocket connection using SmartAPI
            try:
                self.log_info(f"🎯 Starting websocket streaming for {len(tokens)} tokens")

                # For now, we'll use simulation mode since real websocket requires more setup
                # In a full production environment, this would initialize SmartAPI websocket
                self.log_info("🎭 Using simulation mode for live data streaming...")
                await self._simulate_live_data(stocks)

            except Exception as e:
                self.log_error(f"Failed to start websocket connection: {e}")
                raise

        except Exception as e:
            self.log_error(f"Websocket handler failed: {e}")
            # In production mode, we should not fall back to demo data
            # Instead, we should raise the error to fail the system properly
            raise Exception(f"Real websocket streaming failed and no fallback allowed in production mode: {e}")

    async def _process_tick_data(self, tick: Dict):
        """Process incoming tick data and aggregate into candles"""
        try:
            symbol = tick['symbol']
            price = tick['ltp']
            volume = tick.get('volume', 0)
            timestamp = tick['timestamp']

            if symbol not in self.live_candle_aggregators:
                return

            # Process for each timeframe
            timeframes = ['1min', '3min', '5min', '15min']
            for tf in timeframes:
                await self._update_candle_for_timeframe(symbol, tf, price, volume, timestamp)

        except Exception as e:
            self.log_error(f"Error processing tick data: {e}")

    async def _update_candle_for_timeframe(self, symbol: str, timeframe: str, price: float, volume: int, timestamp: datetime):
        """Update candle data for a specific timeframe"""
        try:
            if symbol not in self.live_candle_aggregators or timeframe not in self.live_candle_aggregators[symbol]:
                return
            
            candle = self.live_candle_aggregators[symbol][timeframe]
            
            # Determine timeframe duration in minutes
            tf_minutes = {'1min': 1, '3min': 3, '5min': 5, '15min': 15}
            duration = tf_minutes.get(timeframe, 1)
            
            # Calculate candle start time (aligned to timeframe)
            minute = timestamp.minute
            aligned_minute = (minute // duration) * duration
            candle_start = timestamp.replace(minute=aligned_minute, second=0, microsecond=0)
            
            # Check if this is a new candle
            if candle['timestamp'] is None or candle_start > candle['timestamp']:
                # Publish previous candle if it exists
                if candle['timestamp'] is not None and candle['open'] is not None:
                    await self._publish_candle(symbol, timeframe, candle)
                
                # Start new candle
                candle.update({
                    'open': price,
                    'high': price,
                    'low': price,
                    'close': price,
                    'volume': volume,
                    'timestamp': candle_start,
                    'tick_count': 1
                })
            else:
                # Update existing candle
                if candle['open'] is None:
                    candle['open'] = price
                candle['high'] = max(candle['high'] or price, price)
                candle['low'] = min(candle['low'] or price, price)
                candle['close'] = price
                candle['volume'] += volume
                candle['tick_count'] += 1
            
        except Exception as e:
            self.log_error(f"Error updating candle for {symbol} {timeframe}: {e}")

    async def _publish_candle(self, symbol: str, timeframe: str, candle: Dict):
        """Publish completed candle data"""
        try:
            candle_data = {
                'symbol': symbol,
                'timeframe': timeframe,
                'timestamp': candle['timestamp'],
                'open': candle['open'],
                'high': candle['high'],
                'low': candle['low'],
                'close': candle['close'],
                'volume': candle['volume'],
                'tick_count': candle['tick_count']
            }
            
            # Publish live candle event
            await self.event_bus.publish(
                EventTypes.LIVE_CANDLE_UPDATE,
                candle_data,
                source=self.name
            )
            
            self.log_debug(f"Published {timeframe} candle for {symbol}: O={candle['open']:.2f} H={candle['high']:.2f} L={candle['low']:.2f} C={candle['close']:.2f} V={candle['volume']}")
            
        except Exception as e:
            self.log_error(f"Error publishing candle: {e}")

    def _create_demo_data(self, symbol: str, start_date: datetime, end_date: datetime, timeframe: str) -> pl.DataFrame:
        self.log_info(f"Creating demo data for {symbol}")
        
        # Create minute-level timestamps manually to avoid Polars date_range issues
        timestamps = []
        current_time = start_date
        
        # Generate timestamps every minute until end_date
        while current_time <= end_date and len(timestamps) < 10080:  # Limit to ~7 days of minute data
            timestamps.append(current_time)
            current_time += timedelta(minutes=1)
        
        # Ensure we have at least some data
        if len(timestamps) < 100:
            # Create at least 100 data points for the last 100 minutes
            timestamps = []
            current_time = end_date - timedelta(minutes=100)
            for i in range(100):
                timestamps.append(current_time + timedelta(minutes=i))
        
        dates = pl.Series("timestamp", timestamps)

        # Generate more volatile price data that's likely to trigger signals
        base_price = random.uniform(1000, 3000)
        num_points = len(dates)
        
        # Create trending data with volatility
        close_prices = []
        current_price = base_price
        
        for i in range(num_points):
            # Add trend component (alternating up/down trends)
            trend_period = 200  # Change trend every 200 points
            if (i // trend_period) % 2 == 0:
                trend = 0.5  # Upward trend
            else:
                trend = -0.5  # Downward trend
            
            # Add volatility
            volatility = random.uniform(-base_price * 0.03, base_price * 0.03)
            
            current_price += trend + volatility
            close_prices.append(max(current_price, base_price * 0.5))  # Prevent negative prices
        
        close_prices = np.array(close_prices)

        # Ensure all arrays have the same length
        open_prices = close_prices - np.random.uniform(0, 5, num_points)
        high_prices = close_prices + np.random.uniform(0, 5, num_points)
        low_prices = close_prices - np.random.uniform(0, 5, num_points)
        volumes = np.random.randint(100_000, 5_000_000, size=num_points)

        df = pl.DataFrame({
            "timestamp": dates,
            "open": open_prices,
            "high": high_prices,
            "low": low_prices,
            "close": close_prices,
            "volume": volumes
        })
        
        self.log_info(f"Created {len(df)} demo {timeframe} candles for {symbol}")
        return df

    def _get_instrument_details(self, symbol: str) -> Optional[Dict]:
        keys_to_check = [f"{symbol}-EQ_NSE", f"{symbol}_NSE", symbol]
        token = next((self.symbol_to_token_map[key] for key in keys_to_check if key in self.symbol_to_token_map), None)

        if token:
            return {'token': token, 'symbol': symbol, 'exchange': 'NSE'}
        return {'token': f"DEMO_{symbol}", 'symbol': symbol, 'exchange': 'NSE', 'demo': True}

    async def _handle_data_request(self, event):
        try:
            # Update activity tracking
            self.increment_message_count()
            
            symbol = event.data.get('symbol')
            timeframe = event.data.get('timeframe', '1min')
            event_to_set = event.data.get('event_to_set')
            
            self.log_info(f"[EVENT] Historical data requested: {symbol} ({timeframe})")
            
            # Check if we have cached data first
            cache_key = f"{symbol}_{timeframe}"
            if cache_key in self.data_cache:
                self.log_info(f"[CACHE] Serving cached data for {symbol} ({timeframe})")
                await self.event_bus.publish(
                    EventTypes.HISTORICAL_DATA_LOADED,
                    {'symbol': symbol, 'timeframe': timeframe, 'data': self.data_cache[cache_key]},
                    source=self.name
                )
                if event_to_set:
                    event_to_set.set()
                return
            
            # Check if we have data in timeframe_data
            if timeframe in self.timeframe_data and symbol in self.timeframe_data[timeframe]:
                self.log_info(f"[TIMEFRAME] Serving timeframe data for {symbol} ({timeframe})")
                data = self.timeframe_data[timeframe][symbol]
                await self.event_bus.publish(
                    EventTypes.HISTORICAL_DATA_LOADED,
                    {'symbol': symbol, 'timeframe': timeframe, 'data': data},
                    source=self.name
                )
                if event_to_set:
                    event_to_set.set()
                return
            
            # In production mode, fail instead of generating demo data
            self.log_error(f"No data available for {symbol} ({timeframe}) - Production mode: no demo data fallback")

            # Set the event to prevent hanging
            if event_to_set:
                event_to_set.set()

            # Queue the request for download worker to handle
            await self.download_queue.put((symbol, timeframe, event_to_set))
                    
        except Exception as e:
            self.log_error(f"Failed to handle data request: {e}")

    async def _handle_universe_update(self, event):
        # Update activity tracking
        self.increment_message_count()
        self.log_info("Noted updated universe. Data will be fetched on-demand.")

    async def _download_worker(self, worker_id: int):
        """Download worker that processes queued data requests"""
        try:
            self.log_info(f"Download worker {worker_id} started")
            
            while self.running:
                try:
                    # Get request from queue with timeout
                    symbol, timeframe, event_to_set = await asyncio.wait_for(
                        self.download_queue.get(), timeout=1.0
                    )
                    
                    self.log_info(f"Worker {worker_id} processing request for {symbol} ({timeframe})")
                    
                    # Try to download real data first
                    try:
                        if self.smartapi_client and hasattr(self.smartapi_client, 'auth_token') and self.smartapi_client.auth_token:
                            # Try real data download
                            end_date = datetime.now()
                            start_date = end_date - timedelta(days=30)
                            
                            df = await self._download_real_data(symbol, self._get_instrument_details(symbol), start_date, end_date, "ONE_MINUTE")
                            
                            if df is not None and not df.is_empty():
                                cache_key = f"{symbol}_{timeframe}"
                                self.data_cache[cache_key] = df
                                
                                await self.event_bus.publish(
                                    EventTypes.HISTORICAL_DATA_LOADED,
                                    {'symbol': symbol, 'timeframe': timeframe, 'data': df},
                                    source=self.name
                                )
                                
                                self.log_info(f"Worker {worker_id} successfully downloaded real data for {symbol}")
                                if event_to_set:
                                    event_to_set.set()
                                continue
                    except Exception as e:
                        self.log_error(f"Worker {worker_id} failed to download real data for {symbol}: {e}")
                    
                    # In production mode, fail instead of falling back to demo data
                    self.log_error(f"All attempts to fetch real data for {symbol} failed. Production mode - no demo data fallback.")

                    # Set the event to prevent hanging
                    if event_to_set:
                        event_to_set.set()

                    # Continue to next symbol instead of creating demo data
                    continue
                    
                    if event_to_set:
                        event_to_set.set()
                        
                except asyncio.TimeoutError:
                    # No requests in queue, continue
                    continue
                except Exception as e:
                    self.log_error(f"Worker {worker_id} error: {e}")
                    await asyncio.sleep(1.0)
                    
        except Exception as e:
            self.log_error(f"Download worker {worker_id} failed: {e}")

    async def _download_real_data(self, symbol: str, instrument: Dict, start_date: datetime, end_date: datetime, interval: str) -> Optional[pl.DataFrame]:
        """Download real data from SmartAPI"""
        try:
            if not self.smartapi_client or instrument.get('demo'):
                return None
            
            request = HistoricalDataRequest(
                symbol_token=instrument['token'],
                exchange=instrument['exchange'],
                interval=interval,
                from_date=start_date,
                to_date=end_date
            )
            
            data = await self.smartapi_client.get_historical_data_batch(
                symbol_token=request.symbol_token,
                exchange=request.exchange,
                start_date=request.from_date,
                end_date=request.to_date,
                interval=request.interval
            )
            
            if not data or len(data) < 20:
                raise ValueError(f"Insufficient data for {symbol} ({len(data) if data else 0} rows)")
            
            # Convert list of dicts to polars DataFrame
            # SmartAPI returns data as: [timestamp, open, high, low, close, volume]
            df = pl.DataFrame({
                'timestamp': [datetime.fromisoformat(row[0]) if isinstance(row[0], str) else row[0] for row in data],
                'open': [float(row[1]) for row in data],
                'high': [float(row[2]) for row in data],
                'low': [float(row[3]) for row in data],
                'close': [float(row[4]) for row in data],
                'volume': [int(row[5]) for row in data]
            })
            
            self.log_info(f"Successfully downloaded {len(df)} rows for {symbol}")
            return df
            
        except Exception as e:
            self.log_error(f"Error downloading real data for {symbol}: {e}")
            return None

    async def stop(self):
        """Stop the market data agent"""
        try:
            self.log_info("Stopping Clean Market Data Agent...")
            self.running = False

            # Cancel websocket task
            tasks = []
            if hasattr(self, 'websocket_task') and self.websocket_task:
                self.websocket_task.cancel()
                tasks.append(self.websocket_task)

            # Cancel heartbeat task
            if hasattr(self, 'heartbeat_task') and self.heartbeat_task:
                self.heartbeat_task.cancel()
                tasks.append(self.heartbeat_task)
            # Cancel download workers
            for worker in self.download_workers:
                worker.cancel()
                tasks.append(worker)

            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

            # Disconnect websocket if connected
            try:
                if self.smartapi_client:
                    await self.smartapi_client.disconnect_websocket()
            except Exception as e:
                self.log_warning(f"Error during websocket disconnect: {e}")

            self.log_info("Clean Market Data Agent stopped.")

        except Exception as e:
            self.log_error(f"Error stopping agent: {e}")

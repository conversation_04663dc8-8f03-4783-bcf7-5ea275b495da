#!/usr/bin/env python3
"""
Code Generation and Auto-Patching System

This module provides intelligent code generation, bug fixing, and auto-patching
capabilities for trading strategies, pipeline code, and system configurations.

Features:
[CONFIG] 1. Code Generation
- Strategy generation from natural language descriptions
- Pipeline code creation and modification
- Configuration file generation
- Documentation auto-generation

🐛 2. Bug Detection and Fixing
- Syntax error detection and correction
- Logic error identification
- Performance optimization suggestions
- Security vulnerability patching

📝 3. Strategy Expression Handling
- YAML strategy validation and fixing
- Expression syntax correction
- Variable name resolution
- Indicator parameter optimization

[WORKFLOW] 4. Auto-Patching System
- Automated code fixes with confidence scoring
- Backup creation before modifications
- Rollback capabilities
- Change tracking and logging

Author: AI Assistant
Date: 2025-01-16
"""

import ast
import re
import os
import shutil
import yaml
import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import tempfile
from pathlib import Path
import difflib

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] CODE GENERATION MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class CodeType(Enum):
    """Code type enumeration"""
    STRATEGY = "strategy"
    INDICATOR = "indicator"
    PIPELINE = "pipeline"
    CONFIG = "config"
    TEST = "test"
    DOCUMENTATION = "documentation"

class FixType(Enum):
    """Fix type enumeration"""
    SYNTAX_ERROR = "syntax_error"
    LOGIC_ERROR = "logic_error"
    PERFORMANCE = "performance"
    SECURITY = "security"
    STYLE = "style"
    COMPATIBILITY = "compatibility"

@dataclass
class CodeGenerationRequest:
    """Code generation request"""
    description: str
    code_type: CodeType
    language: str = "python"
    framework: Optional[str] = None
    requirements: List[str] = None
    context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.requirements is None:
            self.requirements = []

@dataclass
class CodeGenerationResult:
    """Code generation result"""
    code: str
    explanation: str
    confidence: float
    suggestions: List[str]
    dependencies: List[str]
    tests: Optional[str] = None
    documentation: Optional[str] = None

@dataclass
class BugFix:
    """Bug fix information"""
    file_path: str
    line_number: int
    fix_type: FixType
    original_code: str
    fixed_code: str
    explanation: str
    confidence: float
    backup_path: Optional[str] = None

@dataclass
class AutoPatchResult:
    """Auto-patch result"""
    success: bool
    fixes_applied: List[BugFix]
    backup_created: bool
    backup_path: Optional[str] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] CODE GENERATION AND AUTO-PATCHING SYSTEM
# ═══════════════════════════════════════════════════════════════════════════════

class CodeGenerationAutoPatch:
    """
    Intelligent code generation and auto-patching system
    
    Provides comprehensive code generation, bug detection, fixing, and
    auto-patching capabilities for trading system components.
    """
    
    def __init__(self, workspace_path: str = "."):
        """Initialize code generation system"""
        self.workspace_path = Path(workspace_path)
        self.logger = logging.getLogger(__name__)
        
        # Code templates
        self.templates = self._load_templates()
        
        # Common patterns and fixes
        self.common_patterns = self._load_common_patterns()
        self.fix_patterns = self._load_fix_patterns()
        
        # Backup management
        self.backup_dir = self.workspace_path / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # Change tracking
        self.change_log = []
        
        self.logger.info("[CONFIG] Code Generation and Auto-Patch system initialized")
    
    def _load_templates(self) -> Dict[str, str]:
        """Load code templates"""
        return {
            "strategy_template": '''#!/usr/bin/env python3
"""
{strategy_name} - Trading Strategy

Generated on: {timestamp}
Description: {description}
"""

import polars as pl
import pyarrow as pa
from typing import Dict, List, Optional, Tuple

class {class_name}:
    """
    {strategy_name} trading strategy implementation
    
    {description}
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize strategy"""
        self.config = config
        self.name = "{strategy_name}"
        
        # Strategy parameters
        {parameters}
    
    def generate_signals(self, data: pl.DataFrame) -> pl.DataFrame:
        """
        Generate trading signals
        
        Args:
            data: OHLCV data with indicators
            
        Returns:
            DataFrame with signals
        """
        # Entry conditions
        {entry_logic}
        
        # Exit conditions
        {exit_logic}
        
        # Combine signals
        signals = data.with_columns([
            pl.col("entry_long").alias("signal_long"),
            pl.col("entry_short").alias("signal_short"),
            pl.col("exit_long").alias("exit_long"),
            pl.col("exit_short").alias("exit_short")
        ])
        
        return signals
    
    def validate_signals(self, signals: pl.DataFrame) -> bool:
        """Validate generated signals"""
        # Add validation logic
        return True
''',
            
            "indicator_template": '''#!/usr/bin/env python3
"""
{indicator_name} - Technical Indicator

Generated on: {timestamp}
Description: {description}
"""

import polars as pl
import pyarrow as pa
import numpy as np
from typing import Union, Optional

def {function_name}(data: pl.DataFrame, {parameters}) -> pl.DataFrame:
    """
    Calculate {indicator_name}
    
    Args:
        data: OHLCV DataFrame
        {parameter_docs}
        
    Returns:
        DataFrame with {indicator_name} values
    """
    
    # Calculation logic
    {calculation_logic}
    
    return result
''',
            
            "config_template": '''# {config_name} Configuration
# Generated on: {timestamp}
# Description: {description}

{config_content}
''',
            
            "test_template": '''#!/usr/bin/env python3
"""
Test Suite for {module_name}

Generated on: {timestamp}
"""

import unittest
import polars as pl
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from {module_path} import {class_name}

class Test{class_name}(unittest.TestCase):
    """Test cases for {class_name}"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = {test_config}
        self.strategy = {class_name}(self.config)
        
        # Sample data
        self.sample_data = pl.DataFrame({{
            "timestamp": pl.date_range(
                start=pl.datetime(2024, 1, 1),
                end=pl.datetime(2024, 1, 10),
                interval="1h"
            ),
            "open": [100.0] * 217,
            "high": [105.0] * 217,
            "low": [95.0] * 217,
            "close": [102.0] * 217,
            "volume": [1000] * 217
        }})
    
    def test_signal_generation(self):
        """Test signal generation"""
        signals = self.strategy.generate_signals(self.sample_data)
        
        # Basic validation
        self.assertIsInstance(signals, pl.DataFrame)
        self.assertGreater(len(signals), 0)
        
        # Check required columns
        required_columns = ["signal_long", "signal_short", "exit_long", "exit_short"]
        for col in required_columns:
            self.assertIn(col, signals.columns)
    
    def test_signal_validation(self):
        """Test signal validation"""
        signals = self.strategy.generate_signals(self.sample_data)
        is_valid = self.strategy.validate_signals(signals)
        self.assertTrue(is_valid)

if __name__ == '__main__':
    unittest.main()
'''
        }
    
    def _load_common_patterns(self) -> Dict[str, List[str]]:
        """Load common code patterns"""
        return {
            "strategy_patterns": [
                "RSI crossover", "EMA crossover", "MACD signal", "Bollinger Bands",
                "SuperTrend", "VWAP", "Support/Resistance", "Momentum", "Mean reversion"
            ],
            "indicator_patterns": [
                "Moving average", "Oscillator", "Volume indicator", "Volatility",
                "Trend following", "Momentum", "Support/Resistance"
            ],
            "data_patterns": [
                "polars DataFrame", "pyarrow operations", "cuDF processing",
                "vectorized calculations", "rolling windows", "groupby operations"
            ]
        }
    
    def _load_fix_patterns(self) -> Dict[FixType, List[Dict[str, str]]]:
        """Load common fix patterns"""
        return {
            FixType.SYNTAX_ERROR: [
                {
                    "pattern": r"SyntaxError: invalid syntax",
                    "fix": "Check for missing colons, parentheses, or indentation",
                    "example": "if condition:\n    # Missing colon fixed"
                },
                {
                    "pattern": r"IndentationError",
                    "fix": "Fix indentation to use 4 spaces consistently",
                    "example": "    # Proper 4-space indentation"
                }
            ],
            FixType.LOGIC_ERROR: [
                {
                    "pattern": r"division by zero",
                    "fix": "Add zero-division check",
                    "example": "result = a / b if b != 0 else 0"
                },
                {
                    "pattern": r"list index out of range",
                    "fix": "Add bounds checking",
                    "example": "value = lst[i] if i < len(lst) else None"
                }
            ],
            FixType.PERFORMANCE: [
                {
                    "pattern": r"\.iterrows\(\)",
                    "fix": "Replace with vectorized operations",
                    "example": "# Use df.apply() or vectorized operations instead"
                },
                {
                    "pattern": r"for.*in.*range\(len\(",
                    "fix": "Use enumerate or direct iteration",
                    "example": "for i, item in enumerate(items):"
                }
            ]
        }
    
    async def generate_code(self, request: CodeGenerationRequest) -> CodeGenerationResult:
        """Generate code based on request"""
        try:
            self.logger.info(f"[CONFIG] Generating {request.code_type.value} code: {request.description}")
            
            if request.code_type == CodeType.STRATEGY:
                return await self._generate_strategy(request)
            elif request.code_type == CodeType.INDICATOR:
                return await self._generate_indicator(request)
            elif request.code_type == CodeType.CONFIG:
                return await self._generate_config(request)
            elif request.code_type == CodeType.TEST:
                return await self._generate_test(request)
            elif request.code_type == CodeType.DOCUMENTATION:
                return await self._generate_documentation(request)
            else:
                raise ValueError(f"Unsupported code type: {request.code_type}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] Error generating code: {e}")
            return CodeGenerationResult(
                code="",
                explanation=f"Error generating code: {str(e)}",
                confidence=0.0,
                suggestions=[],
                dependencies=[]
            )

    async def _generate_strategy(self, request: CodeGenerationRequest) -> CodeGenerationResult:
        """Generate trading strategy code"""
        try:
            description = request.description.lower()

            # Extract strategy components
            strategy_name = self._extract_strategy_name(description)
            class_name = self._to_class_name(strategy_name)
            parameters = self._extract_parameters(description)
            entry_logic = self._generate_entry_logic(description)
            exit_logic = self._generate_exit_logic(description)

            # Generate code from template
            code = self.templates["strategy_template"].format(
                strategy_name=strategy_name,
                class_name=class_name,
                description=request.description,
                timestamp=datetime.now().isoformat(),
                parameters=self._format_parameters(parameters),
                entry_logic=entry_logic,
                exit_logic=exit_logic
            )

            # Generate explanation
            explanation = f"""
Generated {strategy_name} trading strategy with the following components:

**Strategy Logic:**
- Entry conditions based on: {self._describe_entry_conditions(description)}
- Exit conditions based on: {self._describe_exit_conditions(description)}
- Parameters: {', '.join(parameters.keys()) if parameters else 'Default parameters'}

**Implementation Details:**
- Uses polars for efficient data processing
- Vectorized operations for performance
- Configurable parameters through config dictionary
- Built-in signal validation

**Usage:**
```python
config = {{'param1': value1, 'param2': value2}}
strategy = {class_name}(config)
signals = strategy.generate_signals(ohlcv_data)
```
            """.strip()

            # Determine dependencies
            dependencies = ["polars", "pyarrow"]
            if "rsi" in description:
                dependencies.append("polars-talib")
            if "macd" in description:
                dependencies.append("polars-talib")

            # Generate suggestions
            suggestions = [
                "Add parameter validation in __init__ method",
                "Implement position sizing logic",
                "Add risk management rules",
                "Create backtesting integration",
                "Add performance metrics calculation"
            ]

            return CodeGenerationResult(
                code=code,
                explanation=explanation,
                confidence=0.85,
                suggestions=suggestions,
                dependencies=dependencies,
                tests=await self._generate_strategy_tests(class_name, parameters)
            )

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating strategy: {e}")
            raise

    async def _generate_indicator(self, request: CodeGenerationRequest) -> CodeGenerationResult:
        """Generate technical indicator code"""
        try:
            description = request.description.lower()

            # Extract indicator components
            indicator_name = self._extract_indicator_name(description)
            function_name = self._to_function_name(indicator_name)
            parameters = self._extract_indicator_parameters(description)
            calculation_logic = self._generate_calculation_logic(description)

            # Generate code from template
            code = self.templates["indicator_template"].format(
                indicator_name=indicator_name,
                function_name=function_name,
                description=request.description,
                timestamp=datetime.now().isoformat(),
                parameters=self._format_function_parameters(parameters),
                parameter_docs=self._format_parameter_docs(parameters),
                calculation_logic=calculation_logic
            )

            explanation = f"""
Generated {indicator_name} technical indicator with:

**Calculation Method:**
{self._describe_calculation_method(description)}

**Parameters:**
{self._format_parameter_descriptions(parameters)}

**Usage:**
```python
result = {function_name}(ohlcv_data, {', '.join(parameters.keys())})
```
            """.strip()

            return CodeGenerationResult(
                code=code,
                explanation=explanation,
                confidence=0.80,
                suggestions=["Add input validation", "Optimize for large datasets", "Add error handling"],
                dependencies=["polars", "pyarrow", "numpy"]
            )

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating indicator: {e}")
            raise

    async def _generate_config(self, request: CodeGenerationRequest) -> CodeGenerationResult:
        """Generate configuration file"""
        try:
            description = request.description.lower()

            # Determine config type
            if "strategy" in description:
                config_content = self._generate_strategy_config(description)
            elif "system" in description:
                config_content = self._generate_system_config(description)
            elif "agent" in description:
                config_content = self._generate_agent_config(description)
            else:
                config_content = self._generate_generic_config(description)

            config_name = self._extract_config_name(description)

            code = self.templates["config_template"].format(
                config_name=config_name,
                description=request.description,
                timestamp=datetime.now().isoformat(),
                config_content=config_content
            )

            explanation = f"""
Generated {config_name} configuration with:

**Configuration Structure:**
- Organized sections for different components
- Environment-specific settings
- Validation rules and constraints
- Documentation for each parameter

**Usage:**
Load with: `yaml.safe_load(open('config.yaml'))`
            """.strip()

            return CodeGenerationResult(
                code=code,
                explanation=explanation,
                confidence=0.75,
                suggestions=["Add environment variables", "Include validation schema", "Add default values"],
                dependencies=["pyyaml"]
            )

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating config: {e}")
            raise

    async def _generate_test(self, request: CodeGenerationRequest) -> CodeGenerationResult:
        """Generate test code"""
        try:
            context = request.context or {}
            module_name = context.get('module_name', 'TestModule')
            class_name = context.get('class_name', 'TestClass')
            module_path = context.get('module_path', 'module')

            test_config = context.get('test_config', {
                'param1': 'value1',
                'param2': 'value2'
            })

            code = self.templates["test_template"].format(
                module_name=module_name,
                class_name=class_name,
                module_path=module_path,
                test_config=json.dumps(test_config, indent=8),
                timestamp=datetime.now().isoformat()
            )

            explanation = f"""
Generated comprehensive test suite for {module_name}:

**Test Coverage:**
- Unit tests for core functionality
- Integration tests with sample data
- Edge case validation
- Performance benchmarks

**Test Structure:**
- setUp method for test fixtures
- Individual test methods for each feature
- Assertion-based validation
- Sample data generation
            """.strip()

            return CodeGenerationResult(
                code=code,
                explanation=explanation,
                confidence=0.80,
                suggestions=["Add more edge cases", "Include performance tests", "Add mock data"],
                dependencies=["unittest", "polars"]
            )

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating test: {e}")
            raise

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🐛 BUG DETECTION AND FIXING METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def detect_bugs(self, file_path: str) -> List[BugFix]:
        """Detect bugs in a file"""
        try:
            self.logger.info(f"[DEBUG] Detecting bugs in {file_path}")

            with open(file_path, 'r') as f:
                content = f.read()

            bugs = []

            # Syntax error detection
            bugs.extend(await self._detect_syntax_errors(file_path, content))

            # Logic error detection
            bugs.extend(await self._detect_logic_errors(file_path, content))

            # Performance issues
            bugs.extend(await self._detect_performance_issues(file_path, content))

            # Style issues
            bugs.extend(await self._detect_style_issues(file_path, content))

            self.logger.info(f"[DEBUG] Found {len(bugs)} potential issues in {file_path}")
            return bugs

        except Exception as e:
            self.logger.error(f"[ERROR] Error detecting bugs in {file_path}: {e}")
            return []

    async def _detect_syntax_errors(self, file_path: str, content: str) -> List[BugFix]:
        """Detect syntax errors"""
        bugs = []

        try:
            # Try to parse the file
            ast.parse(content)
        except SyntaxError as e:
            # Create bug fix for syntax error
            bug_fix = BugFix(
                file_path=file_path,
                line_number=e.lineno or 1,
                fix_type=FixType.SYNTAX_ERROR,
                original_code=self._get_line(content, e.lineno or 1),
                fixed_code=self._suggest_syntax_fix(content, e),
                explanation=f"Syntax error: {e.msg}",
                confidence=0.9
            )
            bugs.append(bug_fix)

        return bugs

    async def _detect_logic_errors(self, file_path: str, content: str) -> List[BugFix]:
        """Detect logic errors"""
        bugs = []
        lines = content.split('\n')

        for i, line in enumerate(lines, 1):
            # Division by zero check
            if re.search(r'/\s*[a-zA-Z_]\w*(?!\s*[!=<>])', line):
                if not re.search(r'if.*!=\s*0', line) and not re.search(r'if.*>\s*0', line):
                    bugs.append(BugFix(
                        file_path=file_path,
                        line_number=i,
                        fix_type=FixType.LOGIC_ERROR,
                        original_code=line.strip(),
                        fixed_code=self._add_zero_division_check(line),
                        explanation="Potential division by zero",
                        confidence=0.7
                    ))

            # Array bounds check
            if re.search(r'\[\s*\w+\s*\]', line) and 'len(' not in line:
                bugs.append(BugFix(
                    file_path=file_path,
                    line_number=i,
                    fix_type=FixType.LOGIC_ERROR,
                    original_code=line.strip(),
                    fixed_code=self._add_bounds_check(line),
                    explanation="Potential index out of bounds",
                    confidence=0.6
                ))

        return bugs

    async def _detect_performance_issues(self, file_path: str, content: str) -> List[BugFix]:
        """Detect performance issues"""
        bugs = []
        lines = content.split('\n')

        for i, line in enumerate(lines, 1):
            # Inefficient pandas operations
            if '.iterrows()' in line:
                bugs.append(BugFix(
                    file_path=file_path,
                    line_number=i,
                    fix_type=FixType.PERFORMANCE,
                    original_code=line.strip(),
                    fixed_code=self._suggest_vectorized_operation(line),
                    explanation="iterrows() is slow, use vectorized operations",
                    confidence=0.8
                ))

            # Inefficient loops
            if re.search(r'for\s+\w+\s+in\s+range\(len\(', line):
                bugs.append(BugFix(
                    file_path=file_path,
                    line_number=i,
                    fix_type=FixType.PERFORMANCE,
                    original_code=line.strip(),
                    fixed_code=self._suggest_enumerate(line),
                    explanation="Use enumerate instead of range(len())",
                    confidence=0.7
                ))

        return bugs

    async def _detect_style_issues(self, file_path: str, content: str) -> List[BugFix]:
        """Detect style issues"""
        bugs = []
        lines = content.split('\n')

        for i, line in enumerate(lines, 1):
            # Long lines
            if len(line) > 100:
                bugs.append(BugFix(
                    file_path=file_path,
                    line_number=i,
                    fix_type=FixType.STYLE,
                    original_code=line.strip(),
                    fixed_code=self._break_long_line(line),
                    explanation="Line too long (>100 characters)",
                    confidence=0.5
                ))

            # Missing docstrings for functions
            if line.strip().startswith('def ') and not lines[i].strip().startswith('"""'):
                bugs.append(BugFix(
                    file_path=file_path,
                    line_number=i,
                    fix_type=FixType.STYLE,
                    original_code=line.strip(),
                    fixed_code=self._add_docstring(line),
                    explanation="Missing docstring for function",
                    confidence=0.6
                ))

        return bugs

    async def auto_patch_file(self, file_path: str, confidence_threshold: float = 0.8) -> AutoPatchResult:
        """Automatically patch a file"""
        try:
            self.logger.info(f"[CONFIG] Auto-patching {file_path}")

            # Create backup
            backup_path = await self._create_backup(file_path)

            # Detect bugs
            bugs = await self.detect_bugs(file_path)

            # Filter by confidence
            high_confidence_bugs = [bug for bug in bugs if bug.confidence >= confidence_threshold]

            if not high_confidence_bugs:
                return AutoPatchResult(
                    success=True,
                    fixes_applied=[],
                    backup_created=True,
                    backup_path=backup_path
                )

            # Apply fixes
            fixes_applied = []
            errors = []

            with open(file_path, 'r') as f:
                content = f.read()

            lines = content.split('\n')

            # Sort bugs by line number (descending) to avoid line number shifts
            high_confidence_bugs.sort(key=lambda x: x.line_number, reverse=True)

            for bug in high_confidence_bugs:
                try:
                    if bug.line_number <= len(lines):
                        lines[bug.line_number - 1] = bug.fixed_code
                        bug.backup_path = backup_path
                        fixes_applied.append(bug)

                        self.logger.info(f"[SUCCESS] Applied fix at line {bug.line_number}: {bug.explanation}")
                except Exception as e:
                    errors.append(f"Failed to apply fix at line {bug.line_number}: {str(e)}")

            # Write patched content
            if fixes_applied:
                with open(file_path, 'w') as f:
                    f.write('\n'.join(lines))

                # Log changes
                self._log_changes(file_path, fixes_applied)

            return AutoPatchResult(
                success=len(errors) == 0,
                fixes_applied=fixes_applied,
                backup_created=True,
                backup_path=backup_path,
                errors=errors
            )

        except Exception as e:
            self.logger.error(f"[ERROR] Error auto-patching {file_path}: {e}")
            return AutoPatchResult(
                success=False,
                fixes_applied=[],
                backup_created=False,
                errors=[str(e)]
            )

    async def fix_strategy_yaml(self, yaml_path: str) -> AutoPatchResult:
        """Fix strategy YAML configuration"""
        try:
            self.logger.info(f"[CONFIG] Fixing strategy YAML: {yaml_path}")

            # Create backup
            backup_path = await self._create_backup(yaml_path)

            with open(yaml_path, 'r') as f:
                content = f.read()

            try:
                # Try to parse YAML
                data = yaml.safe_load(content)

                # Validate and fix strategy structure
                fixes_applied = []

                if 'strategies' in data:
                    for i, strategy in enumerate(data['strategies']):
                        strategy_fixes = self._fix_strategy_entry(strategy, i)
                        fixes_applied.extend(strategy_fixes)

                # Write fixed YAML
                if fixes_applied:
                    with open(yaml_path, 'w') as f:
                        yaml.dump(data, f, default_flow_style=False, indent=2)

                return AutoPatchResult(
                    success=True,
                    fixes_applied=fixes_applied,
                    backup_created=True,
                    backup_path=backup_path
                )

            except yaml.YAMLError as e:
                # Fix YAML syntax errors
                fixed_content = self._fix_yaml_syntax(content)

                with open(yaml_path, 'w') as f:
                    f.write(fixed_content)

                fix = BugFix(
                    file_path=yaml_path,
                    line_number=1,
                    fix_type=FixType.SYNTAX_ERROR,
                    original_code="YAML syntax error",
                    fixed_code="Fixed YAML syntax",
                    explanation=f"Fixed YAML syntax error: {str(e)}",
                    confidence=0.8,
                    backup_path=backup_path
                )

                return AutoPatchResult(
                    success=True,
                    fixes_applied=[fix],
                    backup_created=True,
                    backup_path=backup_path
                )

        except Exception as e:
            self.logger.error(f"[ERROR] Error fixing strategy YAML: {e}")
            return AutoPatchResult(
                success=False,
                fixes_applied=[],
                backup_created=False,
                errors=[str(e)]
            )

    # ═══════════════════════════════════════════════════════════════════════════════
    # [TOOLS] UTILITY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def _extract_strategy_name(self, description: str) -> str:
        """Extract strategy name from description"""
        # Look for common strategy patterns
        patterns = [
            r"(\w+)\s+strategy", r"(\w+)\s+trading", r"(\w+)\s+scalping",
            r"(\w+)\s+momentum", r"(\w+)\s+breakout", r"(\w+)\s+crossover"
        ]

        for pattern in patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                return match.group(1).title() + " Strategy"

        return "Custom Strategy"

    def _to_class_name(self, name: str) -> str:
        """Convert name to valid class name"""
        return re.sub(r'[^a-zA-Z0-9]', '', name.replace(' ', ''))

    def _to_function_name(self, name: str) -> str:
        """Convert name to valid function name"""
        return re.sub(r'[^a-zA-Z0-9]', '_', name.lower()).strip('_')

    def _extract_parameters(self, description: str) -> Dict[str, Any]:
        """Extract parameters from description"""
        parameters = {}

        # Look for numeric parameters
        numbers = re.findall(r'(\d+)', description)
        if numbers:
            if 'rsi' in description.lower():
                parameters['rsi_period'] = int(numbers[0]) if numbers else 14
            if 'ema' in description.lower():
                parameters['ema_period'] = int(numbers[0]) if numbers else 20
            if 'sma' in description.lower():
                parameters['sma_period'] = int(numbers[0]) if numbers else 20

        # Default parameters
        if not parameters:
            parameters = {
                'period': 20,
                'threshold': 0.02,
                'stop_loss': 0.02,
                'take_profit': 0.04
            }

        return parameters

    def _format_parameters(self, parameters: Dict[str, Any]) -> str:
        """Format parameters for code generation"""
        lines = []
        for key, value in parameters.items():
            lines.append(f"        self.{key} = config.get('{key}', {value})")
        return '\n'.join(lines)

    def _generate_entry_logic(self, description: str) -> str:
        """Generate entry logic based on description"""
        if 'rsi' in description.lower():
            return '''        # RSI-based entry
        entry_long = (data["rsi_14"] < 30) & (data["rsi_14"].shift(1) >= 30)
        entry_short = (data["rsi_14"] > 70) & (data["rsi_14"].shift(1) <= 70)'''

        elif 'ema' in description.lower() and 'crossover' in description.lower():
            return '''        # EMA crossover entry
        entry_long = (data["ema_5"] > data["ema_20"]) & (data["ema_5"].shift(1) <= data["ema_20"].shift(1))
        entry_short = (data["ema_5"] < data["ema_20"]) & (data["ema_5"].shift(1) >= data["ema_20"].shift(1))'''

        elif 'macd' in description.lower():
            return '''        # MACD signal entry
        entry_long = (data["macd"] > data["macd_signal"]) & (data["macd"].shift(1) <= data["macd_signal"].shift(1))
        entry_short = (data["macd"] < data["macd_signal"]) & (data["macd"].shift(1) >= data["macd_signal"].shift(1))'''

        else:
            return '''        # Generic momentum entry
        entry_long = (data["close"] > data["close"].shift(1)) & (data["volume"] > data["volume"].rolling(20).mean())
        entry_short = (data["close"] < data["close"].shift(1)) & (data["volume"] > data["volume"].rolling(20).mean())'''

    def _generate_exit_logic(self, description: str) -> str:
        """Generate exit logic based on description"""
        return '''        # Exit conditions
        exit_long = (data["close"] <= data["close"].shift(1) * 0.98) | (data["close"] >= data["close"].shift(1) * 1.04)
        exit_short = (data["close"] >= data["close"].shift(1) * 1.02) | (data["close"] <= data["close"].shift(1) * 0.96)'''

    def _describe_entry_conditions(self, description: str) -> str:
        """Describe entry conditions"""
        if 'rsi' in description.lower():
            return "RSI oversold/overbought levels"
        elif 'ema' in description.lower():
            return "EMA crossover signals"
        elif 'macd' in description.lower():
            return "MACD signal line crossover"
        else:
            return "Momentum and volume confirmation"

    def _describe_exit_conditions(self, description: str) -> str:
        """Describe exit conditions"""
        return "Stop loss (2%) and take profit (4%) levels"

    async def _generate_strategy_tests(self, class_name: str, parameters: Dict[str, Any]) -> str:
        """Generate test code for strategy"""
        test_config = {key: value for key, value in parameters.items()}

        return f'''
# Test configuration for {class_name}
test_config = {json.dumps(test_config, indent=4)}

# Sample test data
sample_data = pl.DataFrame({{
    "timestamp": pl.date_range(start=pl.datetime(2024, 1, 1), end=pl.datetime(2024, 1, 10), interval="1h"),
    "open": [100.0 + i * 0.1 for i in range(217)],
    "high": [105.0 + i * 0.1 for i in range(217)],
    "low": [95.0 + i * 0.1 for i in range(217)],
    "close": [102.0 + i * 0.1 for i in range(217)],
    "volume": [1000 + i * 10 for i in range(217)],
    "rsi_14": [50.0 + (i % 40 - 20) for i in range(217)],
    "ema_5": [100.0 + i * 0.1 for i in range(217)],
    "ema_20": [99.0 + i * 0.1 for i in range(217)]
}})
        '''.strip()

    async def _create_backup(self, file_path: str) -> str:
        """Create backup of file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{Path(file_path).stem}_{timestamp}.backup"
        backup_path = self.backup_dir / backup_filename

        shutil.copy2(file_path, backup_path)
        self.logger.info(f"[FOLDER] Created backup: {backup_path}")

        return str(backup_path)

    def _log_changes(self, file_path: str, fixes: List[BugFix]):
        """Log changes made to file"""
        change_record = {
            'timestamp': datetime.now().isoformat(),
            'file_path': file_path,
            'fixes_applied': len(fixes),
            'fixes': [
                {
                    'line': fix.line_number,
                    'type': fix.fix_type.value,
                    'explanation': fix.explanation,
                    'confidence': fix.confidence
                }
                for fix in fixes
            ]
        }

        self.change_log.append(change_record)

        # Write to log file
        log_file = self.workspace_path / "autopatch_log.json"
        with open(log_file, 'w') as f:
            json.dump(self.change_log, f, indent=2)

    def _get_line(self, content: str, line_number: int) -> str:
        """Get specific line from content"""
        lines = content.split('\n')
        if 1 <= line_number <= len(lines):
            return lines[line_number - 1]
        return ""

    def _suggest_syntax_fix(self, content: str, error: SyntaxError) -> str:
        """Suggest syntax fix"""
        line = self._get_line(content, error.lineno or 1)

        # Common syntax fixes
        if error.msg and "invalid syntax" in error.msg:
            if line.strip().endswith('if') or line.strip().endswith('else') or line.strip().endswith('elif'):
                return line + ":"
            elif line.count('(') > line.count(')'):
                return line + ")"
            elif line.count('[') > line.count(']'):
                return line + "]"

        return line  # Return original if no fix found

    def _add_zero_division_check(self, line: str) -> str:
        """Add zero division check"""
        # Simple pattern matching for division
        match = re.search(r'(\w+)\s*/\s*(\w+)', line)
        if match:
            dividend, divisor = match.groups()
            return line.replace(f"{dividend} / {divisor}", f"{dividend} / {divisor} if {divisor} != 0 else 0")
        return line

    def _add_bounds_check(self, line: str) -> str:
        """Add array bounds check"""
        # Simple pattern for array access
        match = re.search(r'(\w+)\[(\w+)\]', line)
        if match:
            array, index = match.groups()
            return line.replace(f"{array}[{index}]", f"{array}[{index}] if {index} < len({array}) else None")
        return line

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import polars as pl
from dataclasses import dataclass
import os
from pathlib import Path
import json

# ML imports
try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split, TimeSeriesSplit
    import joblib
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

from core.base_agent import BaseAgent
from core.event_system import EventBus, EventTypes

logger = logging.getLogger(__name__)

@dataclass
class StockScore:
    """Stock scoring data structure"""
    symbol: str
    score: float
    liquidity_score: float
    momentum_score: float
    volatility_score: float
    value_score: float
    prediction_confidence: float
    rank: int
    metadata: Dict[str, Any]

@dataclass
class UniverseConfig:
    """Universe selection configuration"""
    min_avg_volume: int = 150_000
    min_price: float = 50.0
    max_volatility: float = 0.07
    max_stocks: int = 30
    rebalance_frequency_hours: int = 4
    candidate_pool_size: int = 200

class DynamicStockSelector(BaseAgent):
    """
    Dynamic Stock Selector Agent
    - Fetches real data for a candidate pool of stocks.
    - Calculates features and scores based on that data.
    - Uses a trained ML model to rank stocks.
    - Periodically rebalances the selected universe.
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("DynamicStockSelector", event_bus, config, session_id)
        self.universe_config = UniverseConfig()
        self.current_universe: List[str] = []
        self.stock_scores: Dict[str, StockScore] = {}
        self.last_rebalance: Optional[datetime] = None
        self.market_data_cache: Dict[str, pl.DataFrame] = {}
        self.data_request_events: Dict[str, asyncio.Event] = {}
        
        self.scoring_model = None
        self.scaler = StandardScaler() if ML_AVAILABLE else None
        self.model_features = [
            'avg_volume_log', 'volatility_30d', 'rsi_14d', 'momentum_20d', 
            'turnover_log', 'volatility_score', 'momentum_score', 'liquidity_score'
        ]
        
        self.nse_500_symbols = self._load_nse_500_symbols()
        self.selection_stats = {
            'total_evaluated': 0, 'selected_count': 0, 'last_selection_time': None,
            'selection_duration': 0, 'model_accuracy': 0.0, 'data_fetch_failures': 0
        }
        self.log_info("Dynamic Stock Selector initialized")

    async def initialize(self) -> bool:
        self.log_info("Initializing Dynamic Stock Selector...")
        self.event_bus.subscribe(EventTypes.HISTORICAL_DATA_LOADED, self._handle_historical_data_loaded)
        await self._initialize_ml_model()
        await self._select_initial_universe()
        self.initialized = True
        self.log_info("Dynamic Stock Selector initialized successfully")
        return True

    async def start(self):
        self.log_info("Starting Dynamic Stock Selector...")
        asyncio.create_task(self._start_universe_monitoring())

    async def _start_universe_monitoring(self):
        self.log_info("Starting universe monitoring loop...")
        while self.running:
            try:
                if self._should_rebalance():
                    await self._rebalance_universe()
                await asyncio.sleep(3600)  # Check every hour
            except Exception as e:
                self.log_error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)

    def _should_rebalance(self) -> bool:
        if not self.last_rebalance: return True
        elapsed = (datetime.now() - self.last_rebalance).total_seconds()
        return elapsed > (self.universe_config.rebalance_frequency_hours * 3600)

    async def _select_initial_universe(self):
        self.log_info("Selecting initial stock universe...")
        await self._rebalance_universe()

    async def _rebalance_universe(self):
        start_time = datetime.now()
        self.log_info("Rebalancing stock universe...")
        
        candidates = self.nse_500_symbols[:self.universe_config.candidate_pool_size]
        
        self.log_info(f"Fetching data for {len(candidates)} candidates...")
        await self._fetch_data_for_candidates(candidates)
        
        self.log_info("Filtering candidates based on fetched data...")
        filtered_candidates = self._apply_basic_filters(candidates)
        
        self.log_info("Scoring and ranking filtered stocks...")
        scored_stocks = await self._score_stocks(filtered_candidates)
        
        new_universe = [stock.symbol for stock in scored_stocks[:self.universe_config.max_stocks]]
        
        added = set(new_universe) - set(self.current_universe)
        removed = set(self.current_universe) - set(new_universe)
        
        self.current_universe = new_universe
        self.stock_scores = {stock.symbol: stock for stock in scored_stocks}
        self.last_rebalance = datetime.now()
        
        duration = (datetime.now() - start_time).total_seconds()
        self.selection_stats.update({
            'total_evaluated': len(candidates), 'selected_count': len(new_universe),
            'last_selection_time': self.last_rebalance.isoformat(), 'selection_duration': duration
        })
        
        await self._publish_universe_update()
        self.log_info(f"Universe rebalanced in {duration:.2f}s: +{len(added)} -{len(removed)} stocks.")
        self.log_info(f"New universe: {new_universe[:10]}")

    async def _fetch_data_for_candidates(self, candidates: List[str]):
        self.market_data_cache.clear()
        self.selection_stats['data_fetch_failures'] = 0
        
        # Create events that the data agent will set when data is ready
        request_events = {symbol: asyncio.Event() for symbol in candidates}

        # Publish all data requests
        tasks = []
        for symbol in candidates:
            tasks.append(self.event_bus.publish(
                "REQUEST_HISTORICAL_DATA", 
                {'symbol': symbol, 'timeframe': '1day', 'event_to_set': request_events[symbol]},
                source=self.name
            ))
        await asyncio.gather(*tasks)

        # Wait for all events to be set, with a timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*(evt.wait() for evt in request_events.values())),
                timeout=180.0  # 3-minute timeout for all data to arrive
            )
        except asyncio.TimeoutError:
            self.log_warning("Timed out waiting for all historical data to arrive.")
        
        failures = [s for s, evt in request_events.items() if not evt.is_set()]
        if failures:
            self.selection_stats['data_fetch_failures'] = len(failures)
            self.log_warning(f"Failed to receive data for {len(failures)} symbols: {failures[:5]}...")

    def _apply_basic_filters(self, candidates: List[str]) -> List[str]:
        filtered = []
        for symbol in candidates:
            df = self.market_data_cache.get(symbol)
            if df is None or df.is_empty():
                continue
            
            latest_price = df['close'][-1]
            avg_volume = df['volume'].mean()
            
            if (avg_volume >= self.universe_config.min_avg_volume and
                latest_price >= self.universe_config.min_price):
                filtered.append(symbol)
        
        self.log_info(f"Filtered {len(candidates)} candidates to {len(filtered)} stocks.")
        return filtered

    async def _score_stocks(self, candidates: List[str]) -> List[StockScore]:
        tasks = [self._calculate_stock_score(symbol) for symbol in candidates]
        scored_stocks_raw = await asyncio.gather(*tasks)
        
        scored_stocks = [s for s in scored_stocks_raw if s is not None]
        scored_stocks.sort(key=lambda x: x.score, reverse=True)
        
        for i, stock in enumerate(scored_stocks):
            stock.rank = i + 1
            
        self.log_info(f"Scored {len(scored_stocks)} stocks.")
        return scored_stocks

    async def _calculate_stock_score(self, symbol: str) -> Optional[StockScore]:
        df = self.market_data_cache.get(symbol)
        if df is None or df.height < 20: # Need enough data for calculations
            return None

        # Calculate features from the dataframe
        features = self._calculate_features(df)
        
        # Use ML model if available
        if self.scoring_model and self.scaler and ML_AVAILABLE:
            scaled_features = self.scaler.transform(np.array(list(features.values())).reshape(1, -1))
            ml_score = self.scoring_model.predict(scaled_features)[0]
            confidence = 0.8
        else:
            # Fallback to weighted combination
            ml_score = (
                features['liquidity_score'] * 0.4 +
                features['momentum_score'] * 0.3 +
                features['volatility_score'] * 0.3
            )
            confidence = 0.5
        
        return StockScore(
            symbol=symbol, score=ml_score, prediction_confidence=confidence, rank=0,
            liquidity_score=features['liquidity_score'], momentum_score=features['momentum_score'],
            volatility_score=features['volatility_score'], value_score=0, # Placeholder
            metadata={
                'price': df['close'][-1], 'volume': df['volume'].mean(),
                'volatility': features['volatility_30d'], 'rsi': features['rsi_14d']
            }
        )

    def _calculate_features(self, df: pl.DataFrame) -> Dict[str, float]:
        """Calculate all features from a historical data dataframe."""
        # Ensure data is sorted by time
        df = df.sort("timestamp")
        
        # Technical Indicators
        rsi_14d = self._calculate_rsi(df['close'], 14)
        
        # Momentum
        price_change = df['close'].pct_change()
        momentum_20d = (price_change.rolling_mean(20).last()) * 100
        
        # Volatility
        volatility_30d = price_change.rolling_std(30).last()
        
        # Liquidity
        avg_volume = df['volume'].mean()
        avg_price = df['close'].mean()
        turnover = avg_volume * avg_price
        
        # Scores (normalized between 0 and 1)
        volatility_score = 1 - min(volatility_30d / self.universe_config.max_volatility, 1.0)
        momentum_score = min(abs(momentum_20d) / 10.0, 1.0) # Normalize based on 10% momentum
        liquidity_score = min(np.log10(max(avg_volume, 1)) / 7, 1.0) # Normalize up to 10M volume

        return {
            'avg_volume_log': np.log10(max(avg_volume, 1)),
            'volatility_30d': volatility_30d,
            'rsi_14d': rsi_14d,
            'momentum_20d': momentum_20d,
            'turnover_log': np.log10(max(turnover, 1)),
            'volatility_score': volatility_score,
            'momentum_score': momentum_score,
            'liquidity_score': liquidity_score,
        }

    def _calculate_rsi(self, series: pl.Series, period: int) -> float:
        delta = series.diff()
        gain = delta.clip_lower(0)
        loss = -delta.clip_upper(0)
        
        avg_gain = gain.rolling_mean(period)
        avg_loss = loss.rolling_mean(period)
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.last()

    async def _initialize_ml_model(self):
        if not ML_AVAILABLE:
            self.log_warning("ML libraries not available, using rule-based scoring.")
            return
        
        model_path = Path("models/stock_selector_model.joblib")
        scaler_path = Path("models/stock_selector_scaler.joblib")
        
        if model_path.exists() and scaler_path.exists():
            self.scoring_model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.log_info("Loaded existing ML model and scaler.")
        else:
            self.log_info("No existing model found. Training a new one...")
            await self._train_ml_model()

    async def _train_ml_model(self):
        if not ML_AVAILABLE: return
        self.log_info("Training new ML model for stock selection...")
        
        candidates = self.nse_500_symbols[:self.universe_config.candidate_pool_size]
        await self._fetch_data_for_candidates(candidates)
        
        X, y = self._generate_training_data()
        
        if X.shape[0] < 50:
            self.log_warning("Insufficient training data (<50 samples), cannot train model.")
            return
            
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        self.scaler.fit(X_train)
        X_train_scaled = self.scaler.transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        self.scoring_model = RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42, n_jobs=-1)
        self.scoring_model.fit(X_train_scaled, y_train)
        
        test_score = self.scoring_model.score(X_test_scaled, y_test)
        self.selection_stats['model_accuracy'] = test_score
        
        os.makedirs("models", exist_ok=True)
        joblib.dump(self.scoring_model, "models/stock_selector_model.joblib")
        joblib.dump(self.scaler, "models/stock_selector_scaler.joblib")
        self.log_info(f"ML model trained and saved. Test R^2 score: {test_score:.3f}")

    def _generate_training_data(self) -> Tuple[np.ndarray, np.ndarray]:
        self.log_info("Generating training data from historical cache...")
        all_features = []
        all_targets = []
        
        for symbol, df in self.market_data_cache.items():
            if df is None or df.height < 40: continue # Need enough data for features and target
            
            # Define target: 5-day forward return
            df = df.with_columns((pl.col("close").shift(-5) / pl.col("close") - 1).alias("target"))
            df = df.drop_nulls()

            if df.is_empty(): continue

            for i in range(20, df.height - 5):
                window_df = df.slice(i - 20, 20)
                features = self._calculate_features(window_df)
                
                # Ensure all model features are present
                feature_values = [features.get(f, 0.0) for f in self.model_features]
                
                all_features.append(feature_values)
                all_targets.append(df['target'][i])

        if not all_features:
            return np.array([]), np.array([])
            
        return np.array(all_features), np.array(all_targets)

    def _load_nse_500_symbols(self) -> List[str]:
        try:
            config_path = Path(__file__).parent.parent / 'config' / 'nse_500_universe.json'
            with open(config_path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Loaded {len(data['symbols'])} symbols from nse_500_universe.json")
                return data['symbols']
        except Exception as e:
            self.log_error(f"Failed to load nse_500_universe.json: {e}. Using fallback list.")
            return ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]

    async def _publish_universe_update(self):
        self.log_info(f"Publishing STOCK_UNIVERSE_UPDATED with {len(self.current_universe)} stocks.")
        await self.event_bus.publish(
            "STOCK_UNIVERSE_UPDATED",
            {
                "universe": self.current_universe,
                "scores": {s: sc.score for s, sc in self.stock_scores.items()},
                "timestamp": datetime.now().isoformat(),
                "selection_stats": self.selection_stats
            },
            source=self.name
        )

    async def _handle_historical_data_loaded(self, event):
        symbol = event.data.get('symbol')
        data = event.data.get('data')
        
        if symbol in self.data_request_events:
            if data is not None and not data.is_empty():
                self.market_data_cache[symbol] = data
            else:
                self.log_warning(f"Received empty or null data for {symbol}")
            
            self.data_request_events[symbol].set()
            self.increment_message_count()

    async def _handle_universe_request(self, event):
        await self._publish_universe_update()
        self.log_info(f"Responded to universe request from {event.source}")

    async def stop(self):
        self.log_info("Stopping Dynamic Stock Selector...")
        self.running = False
        self.log_info("Dynamic Stock Selector stopped")

    # Remove unused methods from previous implementation
    async def _update_stock_scores(self): pass
    async def _handle_market_data(self, event): pass
    def get_current_universe(self) -> List[str]: return self.current_universe.copy()
    def get_stock_scores(self) -> Dict[str, StockScore]: return self.stock_scores.copy()
    def get_selection_stats(self) -> Dict[str, Any]: return self.selection_stats.copy()

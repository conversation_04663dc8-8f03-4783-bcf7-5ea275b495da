#!/usr/bin/env python3
"""
🧠 Live ML Prediction Agent
Loads trained models and generates predictions for live stock selection

Features:
- Loads trained models from existing ML workflow
- Predicts expected returns, risk metrics, and strategy suitability
- Generates confidence intervals for predictions
- Handles model versioning and fallback scenarios
- Integrates with enhanced model training agent
"""

import asyncio
import logging
import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import yaml
import json
import joblib
import pickle
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor

# Import existing ML infrastructure
from agents.enhanced_model_training_agent import EnhancedModelTrainingAgent, EnhancedTrainingConfig
from agents.base_agent import BaseAgent, AgentStatus # Import BaseAgent

logger = logging.getLogger(__name__)

@dataclass
class PredictionResult:
    """Prediction result for a stock"""
    symbol: str
    expected_return: Optional[float]
    expected_return_confidence: Optional[float]
    risk_metrics: Dict[str, float]
    strategy_suitability: Dict[str, float]
    confidence_intervals: Dict[str, Tuple[float, float]]
    model_versions: Dict[str, str]
    prediction_quality: float
    is_valid: bool

@dataclass
class ModelInfo:
    """Information about a loaded model"""
    name: str
    version: str
    path: str
    performance_score: float
    last_updated: datetime
    is_loaded: bool

class LiveMLPredictionAgent(BaseAgent): # Inherit from BaseAgent
    """
    Agent responsible for loading trained models and generating predictions for live stock selection
    """
    
    def __init__(self, event_bus: Any, config: Any, session_id: str): # Modified constructor
        super().__init__("LiveMLPredictionAgent", event_bus, config, session_id) # Call super constructor
        self.ml_config = self.config['ml_prediction']
        self.model_config = self.ml_config['model_loading']
        self.prediction_config = self.ml_config['prediction_tasks']
        
        # Model storage
        self.loaded_models: Dict[str, Any] = {}
        self.model_info: Dict[str, ModelInfo] = {}
        self.scalers: Dict[str, Any] = {}
        self.label_encoders: Dict[str, Any] = {}
        self.expected_feature_names: Optional[List[str]] = None
        
        # Enhanced model training agent for integration
        self.enhanced_agent: Optional[EnhancedModelTrainingAgent] = None
        
        # Prediction storage
        self.predictions: Dict[str, PredictionResult] = {}
        
    async def initialize(self) -> bool:
        """Initialize the agent and load models"""
        self.log_info("🧠 Initializing Live ML Prediction Agent...")
        try:
            # Initialize enhanced model training agent for integration
            try:
                enhanced_config = EnhancedTrainingConfig()
                enhanced_config.models_dir = self.model_config['models_directory']
                self.enhanced_agent = EnhancedModelTrainingAgent(enhanced_config)
                self.log_info("✓ Enhanced model training agent initialized")
            except Exception as e:
                self.log_warning(f"Enhanced agent initialization failed: {e}")
                
            # Load trained models
            success = await self._load_trained_models()
            
            if success:
                self.initialized = True
                self.log_info("✓ Live ML Prediction Agent initialized successfully")
                return True
            else:
                self.log_error("❌ Failed to load required models")
                self.initialized = False
                return False
                
        except Exception as e:
            self.log_error(f"Failed to initialize Live ML Prediction Agent: {e}")
            self.initialized = False
            return False
            
    async def start(self):
        """Start the agent - currently no continuous operations"""
        self.log_info("Live ML Prediction Agent started. Ready to generate predictions on demand.")
        self.running = True

    async def stop(self):
        """Stop the agent and clean up resources"""
        self.log_info("Stopping Live ML Prediction Agent...")
        self.running = False
        self.log_info("🧹 Live ML Prediction Agent stopped.")
            
    async def _load_trained_models(self) -> bool:
        """Load trained models from disk"""
        try:
            models_dir = Path(self.model_config['models_directory'])
            
            if not models_dir.exists():
                self.log_error(f"Models directory not found: {models_dir}")
                return False
                
            # Determine model version to load
            model_version = self.model_config['model_version']
            if model_version == "latest":
                model_version = self._find_latest_model_version(models_dir)
                
            if not model_version:
                self.log_error("No model version found")
                return False
                
            self.log_info(f"Loading models version: {model_version}")
            
            # Load models for each prediction task
            loaded_count = 0
            
            for task_name, task_config in self.prediction_config.items():
                if not task_config.get('enabled', True):
                    continue
                    
                try:
                    success = await self._load_task_models(task_name, model_version, models_dir)
                    if success:
                        loaded_count += 1
                        self.log_info(f"✓ Loaded models for task: {task_name}")
                    else:
                        self.log_warning(f"⚠️ Failed to load models for task: {task_name}")
                        
                except Exception as e:
                    self.log_error(f"Error loading models for task {task_name}: {e}")
                    
            # Load scalers and encoders
            await self._load_preprocessing_components(model_version, models_dir)
            
            # Load feature names from training results
            await self._load_feature_names(model_version, models_dir)
            
            self.log_info(f"📊 Loaded models for {loaded_count} prediction tasks")
            return loaded_count > 0
            
        except Exception as e:
            self.log_error(f"Model loading failed: {e}")
            return False
            
    def _find_latest_model_version(self, models_dir: Path) -> Optional[str]:
        """Find the latest model version"""
        try:
            # Look for timestamped model files
            model_files = list(models_dir.glob("**/training_results_*.json"))
            
            if not model_files:
                self.log_warning("No training results files found")
                # Try to find any model files with timestamps
                model_files = list(models_dir.glob("**/*_*.joblib"))
                if model_files:
                    # Extract timestamp from any model file
                    timestamps = set()
                    for file_path in model_files:
                        try:
                            # Extract timestamp from filename like "lightgbm_20250804_105640.joblib"
                            parts = file_path.stem.split('_')
                            if len(parts) >= 3:
                                timestamp = f"{parts[-2]}_{parts[-1]}"
                                timestamps.add(timestamp)
                        except Exception:
                            continue
                    
                    if timestamps:
                        latest_timestamp = max(timestamps)
                        self.log_info(f"Found model timestamp from files: {latest_timestamp}")
                        return latest_timestamp
                
                return None
                
            # Extract timestamps and find latest
            latest_timestamp = None
            for file_path in model_files:
                try:
                    # Extract timestamp from training_results_YYYYMMDD_HHMMSS.json
                    parts = file_path.stem.split('_')
                    if len(parts) >= 3:
                        timestamp = f"{parts[-2]}_{parts[-1]}"
                        if not latest_timestamp or timestamp > latest_timestamp:
                            latest_timestamp = timestamp
                except Exception:
                    continue
                    
            return latest_timestamp
            
        except Exception as e:
            self.log_error(f"Failed to find latest model version: {e}")
            return None
            
    async def _load_task_models(self, task_name: str, version: str, models_dir: Path) -> bool:
        """Load models for a specific prediction task"""
        try:
            # Map expected task names to actual model directories
            task_mapping = {
                'expected_return': ['roi_prediction', 'profit_factor_prediction'],
                'risk_metrics': ['drawdown_prediction', 'sharpe_ratio_prediction'],
                'strategy_suitability': ['profitability_classification']
            }
            
            # Get the actual directories for this task
            actual_task_dirs = task_mapping.get(task_name, [task_name])
            
            task_models = {}
            loaded_any = False
            
            for actual_task_name in actual_task_dirs:
                task_dir = models_dir / actual_task_name
                
                if not task_dir.exists():
                    self.log_debug(f"Task directory not found: {task_dir}")
                    continue
                
                # Try to load each model type from this directory
                # Look for files with the version timestamp
                model_pattern = f"*_{version}.joblib"
                model_files = list(task_dir.glob(model_pattern))
                
                if not model_files:
                    # Try alternative pattern without underscore
                    model_pattern = f"*{version}.joblib"
                    model_files = list(task_dir.glob(model_pattern))
                
                self.log_debug(f"Looking for models in {task_dir} with pattern {model_pattern}, found {len(model_files)} files")
                
                for model_file in model_files:
                    try:
                        # Extract model type from filename (e.g., "lightgbm" from "lightgbm_20250804_105640.joblib")
                        model_type = model_file.stem.split('_')[0]
                        model_name = f"{actual_task_name}_{model_type}"
                        
                        model = joblib.load(model_file)
                        task_models[model_name] = model
                        loaded_any = True
                        
                        # Log model feature information for debugging
                        self._log_model_feature_info(model, model_name)
                        
                        # Store model info
                        self.model_info[f"{task_name}_{model_name}"] = ModelInfo(
                            name=model_name,
                            version=version,
                            path=str(model_file),
                            performance_score=0.0,  # Would load from training results
                            last_updated=datetime.fromtimestamp(model_file.stat().st_mtime),
                            is_loaded=True
                        )
                        
                        self.log_info(f"✓ Loaded model: {model_name} for task: {task_name} from {model_file}")
                        
                    except Exception as e:
                        self.log_error(f"Failed to load model {model_file}: {e}")
                        
            # Ensure at least one fallback model is available
            if not loaded_any:
                self.log_warning(f"No models loaded for {task_name}, creating fallback model")
                task_models['fallback'] = RandomForestRegressor(n_estimators=50, random_state=42)
                loaded_any = True
                
            if loaded_any:
                self.loaded_models[task_name] = task_models
                return True
            else:
                return False
                
        except Exception as e:
            self.log_error(f"Failed to load task models for {task_name}: {e}")
            return False
    
    def _log_model_feature_info(self, model: Any, model_name: str):
        """Log model feature information for debugging"""
        try:
            feature_info = []
            
            if hasattr(model, 'n_features_in_'):
                feature_info.append(f"n_features_in_: {model.n_features_in_}")
            
            if hasattr(model, 'feature_names_in_'):
                feature_names = list(model.feature_names_in_)
                feature_info.append(f"feature_names_in_: {len(feature_names)} features")
                if len(feature_names) <= 10:
                    feature_info.append(f"  names: {feature_names}")
                else:
                    feature_info.append(f"  first 5: {feature_names[:5]}")
                    feature_info.append(f"  last 5: {feature_names[-5:]}")
            
            if hasattr(model, 'feature_name_'):
                feature_names = list(model.feature_name_)
                feature_info.append(f"feature_name_: {len(feature_names)} features")
                if len(feature_names) <= 10:
                    feature_info.append(f"  names: {feature_names}")
                else:
                    feature_info.append(f"  first 5: {feature_names[:5]}")
                    feature_info.append(f"  last 5: {feature_names[-5:]}")
            
            if feature_info:
                self.log_debug(f"Model {model_name} feature info:")
                for info in feature_info:
                    self.log_debug(f"  {info}")
            else:
                self.log_debug(f"Model {model_name}: No feature information available")
                
        except Exception as e:
            self.log_debug(f"Failed to log feature info for {model_name}: {e}")
            
    async def _load_preprocessing_components(self, version: str, models_dir: Path):
        """Load scalers and label encoders"""
        try:
            # Load scalers
            scalers_file = models_dir / f"scalers_{version}.joblib"
            if scalers_file.exists():
                self.scalers = joblib.load(scalers_file)
                self.log_info("✓ Scalers loaded")
            else:
                self.log_warning(f"Scalers file not found: {scalers_file}")
                
            # Load label encoders
            encoders_file = models_dir / f"label_encoders_{version}.joblib"
            if encoders_file.exists():
                self.label_encoders = joblib.load(encoders_file)
                self.log_info("✓ Label encoders loaded")
            else:
                self.log_warning(f"Label encoders file not found: {encoders_file}")
                
        except Exception as e:
            self.log_error(f"Failed to load preprocessing components: {e}")
    
    async def _load_feature_names(self, version: str, models_dir: Path):
        """Load expected feature names from training results or infer from models"""
        try:
            # First, try to extract feature names from training results
            training_results_file = models_dir / f"training_results_{version}.json"
            if training_results_file.exists():
                with open(training_results_file, 'r') as f:
                    training_results = json.load(f)
                
                # Extract feature names from feature importance data
                feature_names_set = set()
                for task_name, task_data in training_results.items():
                    if task_name == 'ensemble':
                        continue
                    if 'feature_importance' in task_data:
                        for model_type, features in task_data['feature_importance'].items():
                            for feature_info in features:
                                feature_names_set.add(feature_info['feature'])
                
                if feature_names_set:
                    # Sort feature names for consistency
                    self.expected_feature_names = sorted(list(feature_names_set))
                    self.log_info(f"✓ Extracted {len(self.expected_feature_names)} expected feature names from training results")
                    return
                
                # Fallback: check for direct feature_names or feature_columns
                if 'feature_names' in training_results:
                    self.expected_feature_names = training_results['feature_names']
                    self.log_info(f"✓ Loaded {len(self.expected_feature_names)} expected feature names from training results")
                    return
                elif 'feature_columns' in training_results:
                    self.expected_feature_names = training_results['feature_columns']
                    self.log_info(f"✓ Loaded {len(self.expected_feature_names)} expected feature names from training results")
                    return

            self.log_warning("No feature names in training results file, or file not found. Attempting to infer from models.")

            # If not found, try to infer from a loaded model
            for task_name, models in self.loaded_models.items():
                for model_name, model in models.items():
                    feature_names = None
                    if hasattr(model, 'feature_name_'):
                        feature_names = list(model.feature_name_)
                    elif hasattr(model, 'feature_names_in_'):
                        feature_names = list(model.feature_names_in_)
                    
                    if feature_names:
                        self.expected_feature_names = feature_names
                        self.log_info(f"✓ Inferred {len(feature_names)} feature names from model '{model_name}' for task '{task_name}'")
                        return  # Stop after finding the first set of feature names

            # Final fallback: use the known expected features from the models
            self.expected_feature_names = [
                'avg_accuracy', 'avg_expectancy', 'avg_max_drawdown', 'avg_profit_factor',
                'avg_roi', 'avg_sharpe_ratio', 'avg_total_pnl', 'avg_total_trades',
                'avg_winning_trades', 'consistency_score', 'max_accuracy', 'max_expectancy',
                'max_max_drawdown', 'max_profit_factor', 'max_roi', 'max_sharpe_ratio',
                'max_total_pnl', 'min_accuracy', 'min_expectancy', 'min_max_drawdown',
                'min_profit_factor', 'min_roi', 'min_sharpe_ratio', 'min_total_pnl',
                'roi_drawdown_ratio', 'sharpe_consistency', 'std_accuracy', 'std_expectancy',
                'std_max_drawdown', 'std_profit_factor', 'std_roi', 'std_sharpe_ratio',
                'std_total_pnl', 'std_total_trades', 'std_winning_trades', 'trades_per_period',
                'walk_forward_steps'
            ]
            self.log_info(f"✓ Using hardcoded expected feature names ({len(self.expected_feature_names)} features)")

        except Exception as e:
            self.log_error(f"Failed to load or infer feature names: {e}")
            # Final fallback
            self.expected_feature_names = [
                'avg_accuracy', 'avg_expectancy', 'avg_max_drawdown', 'avg_profit_factor',
                'avg_roi', 'avg_sharpe_ratio', 'avg_total_pnl', 'avg_total_trades',
                'avg_winning_trades', 'consistency_score', 'max_accuracy', 'max_expectancy',
                'max_max_drawdown', 'max_profit_factor', 'max_roi', 'max_sharpe_ratio',
                'max_total_pnl', 'min_accuracy', 'min_expectancy', 'min_max_drawdown',
                'min_profit_factor', 'min_roi', 'min_sharpe_ratio', 'min_total_pnl',
                'roi_drawdown_ratio', 'sharpe_consistency', 'std_accuracy', 'std_expectancy',
                'std_max_drawdown', 'std_profit_factor', 'std_roi', 'std_sharpe_ratio',
                'std_total_pnl', 'std_total_trades', 'std_winning_trades', 'trades_per_period',
                'walk_forward_steps'
            ]
            
    async def generate_predictions(self, features_data: Union[pl.DataFrame, Dict[str, Dict[str, float]]]) -> Dict[str, PredictionResult]:
        """
        Generate predictions for multiple stocks
        
        Args:
            features_data: DataFrame containing features for all stocks with 'symbol' column
            
        Returns:
            Dictionary mapping symbols to their prediction results
        """
        self.log_info(f"🔮 Generating predictions for {len(features_data)} stocks...")
        self.update_activity()
        
        results = {}
        successful_predictions = 0
        failed_predictions = 0
        
        # Get feature column names, excluding non-feature columns
        feature_columns = [col for col in features_data.columns if col not in ['symbol', 'target_return']]

        # Iterate over rows
        for row in features_data.iter_rows(named=True):
            symbol = row.get('symbol')
            if not symbol:
                continue

            # Create a dictionary containing only the feature columns for the current row
            features = {key: row[key] for key in feature_columns}
            
            try:
                self.log_debug(f"Generating predictions for {symbol}")
                
                # Generate predictions for this stock
                prediction = await self._generate_stock_predictions(symbol, features)
                
                if prediction and prediction.is_valid:
                    results[symbol] = prediction
                    self.predictions[symbol] = prediction
                    successful_predictions += 1
                    self.log_debug(f"✓ Predictions generated for {symbol}")
                else:
                    self.log_warning(f"⚠️ Prediction generation failed for {symbol}")
                    failed_predictions += 1
                    
            except Exception as e:
                self.log_error(f"❌ Error generating predictions for {symbol}: {e}")
                failed_predictions += 1
                continue
                
        self.log_info(f"🔮 Prediction generation complete: {successful_predictions} successful, {failed_predictions} failed")
        return results
        
    async def _generate_stock_predictions(self, symbol: str, features: Dict[str, float]) -> Optional[PredictionResult]:
        """Generate predictions for a single stock"""
        try:
            # Prepare feature vector
            feature_result = self._prepare_feature_vector(features)
            
            if feature_result is None:
                self.log_warning(f"Failed to prepare feature vector for {symbol}")
                return None
                
            feature_vector, feature_names = feature_result
            
            if feature_vector is None:
                self.log_warning(f"Failed to prepare feature vector for {symbol}")
                return None
                
            # Generate predictions for each task
            expected_return = None
            expected_return_confidence = None
            risk_metrics = {}
            strategy_suitability = {}
            confidence_intervals = {}
            model_versions = {}
            
            # Expected return prediction
            if 'expected_return' in self.prediction_config and self.prediction_config['expected_return']['enabled']:
                return_pred, return_conf = await self._predict_expected_return(feature_vector, feature_names)
                expected_return = return_pred
                expected_return_confidence = return_conf
                model_versions['expected_return'] = 'loaded'
                
            # Risk metrics prediction
            if 'risk_metrics' in self.prediction_config and self.prediction_config['risk_metrics']['enabled']:
                risk_pred = await self._predict_risk_metrics(feature_vector, feature_names)
                risk_metrics = risk_pred
                model_versions['risk_metrics'] = 'loaded'
                
            # Strategy suitability prediction
            if 'strategy_suitability' in self.prediction_config and self.prediction_config['strategy_suitability']['enabled']:
                strategy_pred = await self._predict_strategy_suitability(feature_vector, feature_names)
                strategy_suitability = strategy_pred
                model_versions['strategy_suitability'] = 'loaded'
                
            # Generate confidence intervals if enabled
            if self.ml_config['confidence_intervals']['enabled']:
                confidence_intervals = await self._generate_confidence_intervals(feature_vector)
                
            # Calculate prediction quality
            prediction_quality = self._calculate_prediction_quality(
                expected_return, expected_return_confidence, risk_metrics, strategy_suitability
            )
            
            # Determine if prediction is valid (more lenient criteria)
            is_valid = (
                expected_return is not None and
                prediction_quality >= 0.3 and  # Reduced from 0.5 to 0.3
                (len(risk_metrics) > 0 or len(strategy_suitability) > 0)  # Either risk metrics OR strategy suitability
            )
            
            return PredictionResult(
                symbol=symbol,
                expected_return=expected_return,
                expected_return_confidence=expected_return_confidence,
                risk_metrics=risk_metrics,
                strategy_suitability=strategy_suitability,
                confidence_intervals=confidence_intervals,
                model_versions=model_versions,
                prediction_quality=prediction_quality,
                is_valid=is_valid
            )
            
        except Exception as e:
            self.log_error(f"Prediction generation failed for {symbol}: {e}")
            return None

    def _prepare_feature_vector(self, features: Dict[str, float]) -> Optional[Tuple[np.ndarray, List[str]]]:
        """Prepare feature vector for model prediction with feature alignment"""
        try:
            # Determine the maximum number of features expected by any loaded model or scaler
            max_expected_n_features = 0
            if self.expected_feature_names:
                max_expected_n_features = len(self.expected_feature_names)

            if max_expected_n_features == 0:
                # Check models if not defined by expected_feature_names
                for task_name, models in self.loaded_models.items():
                    for model_name, model in models.items():
                        if hasattr(model, 'n_features_in_'):
                            max_expected_n_features = max(max_expected_n_features, model.n_features_in_)
                        elif hasattr(model, 'feature_name_') and hasattr(model.feature_name_, '__len__'):
                            max_expected_n_features = max(max_expected_n_features, len(model.feature_name_))
                        elif hasattr(model, 'feature_names_in_') and hasattr(model.feature_names_in_, '__len__'):
                            max_expected_n_features = max(max_expected_n_features, len(model.feature_names_in_))
            
            # Check scalers
            if self.scalers:
                for scaler_name, scaler in self.scalers.items():
                    if hasattr(scaler, 'n_features_in_'):
                        max_expected_n_features = max(max_expected_n_features, scaler.n_features_in_)
            
            self.log_debug(f"Maximum expected features across all models/scalers: {max_expected_n_features}")

            feature_values = []
            feature_names_used = []
            
            # Prioritize expected_feature_names if available from training results
            if self.expected_feature_names is not None:
                current_expected_features = self.expected_feature_names
                feature_names_used = current_expected_features.copy()
                self.log_debug(f"Using {len(current_expected_features)} expected feature names from training results.")
                missing_features = []
                for feature_name in current_expected_features:
                    if feature_name in features:
                        value = features[feature_name]
                        feature_values.append(0.0 if value is None or (isinstance(value, float) and np.isnan(value)) else float(value))
                    else:
                        feature_values.append(0.0)
                        missing_features.append(feature_name)
                if missing_features:
                    if len(missing_features) <= 5:
                        self.log_warning(f"Missing {len(missing_features)} expected features: {missing_features}, using default 0.0")
                    else:
                        self.log_warning(f"Missing {len(missing_features)} expected features (showing first 5): {missing_features[:5]}, using default 0.0")
            else:
                # Fallback: use all available features, sorted by name for consistency
                feature_names_used = sorted(features.keys())
                feature_values = []
                for name in feature_names_used:
                    value = features[name]
                    feature_values.append(0.0 if value is None or (isinstance(value, float) and np.isnan(value)) else float(value))
                
                self.log_warning("Could not determine expected feature names from training results. Using available features and padding to max expected.")

            current_n_features = len(feature_values)
            if self.expected_feature_names and len(self.expected_feature_names) != current_n_features:
                # This case should ideally not happen if the feature engineering is correct
                self.log_warning(f"Feature count mismatch: got {current_n_features}, expected {len(self.expected_feature_names)}. Aligning now.")
                
                aligned_feature_values = []
                for feature_name in self.expected_feature_names:
                    aligned_feature_values.append(features.get(feature_name, 0.0))
                feature_values = aligned_feature_values

            # Validate final feature vector
            if not feature_values:
                self.log_error("No feature values prepared")
                return None
            
            feature_vector = np.array(feature_values, dtype=np.float64).reshape(1, -1)
            self.log_debug(f"Final feature vector shape before scaling: {feature_vector.shape}")

            # Note: Scaling will be applied per-model in _prepare_model_features to handle different feature counts

            return feature_vector, feature_names_used

        except Exception as e:
            self.log_error(f"Feature vector preparation failed: {e}")
            return None

    async def _predict_expected_return(self, feature_vector: np.ndarray, feature_names: List[str]) -> Tuple[Optional[float], Optional[float]]:
        """Predict expected return"""
        try:
            task_name = 'expected_return'

            if task_name not in self.loaded_models:
                self.log_warning(f"No models loaded for {task_name}")
                return None, None

            models = self.loaded_models[task_name]
            predictions = []
            confidences = []

            # Get predictions from all available models
            for model_name, model in models.items():
                try:
                    # Prepare model-specific features
                    model_features = self._prepare_model_features(feature_vector, feature_names, model_name, model, task_name)
                    if model_features is None:
                        continue
                    
                    if hasattr(model, 'predict'):
                        pred = model.predict(model_features)[0]
                        predictions.append(pred)

                        # Calculate confidence (simplified)
                        if hasattr(model, 'predict_proba'):
                            proba = model.predict_proba(model_features)[0]
                            confidence = np.max(proba)
                        else:
                            confidence = 0.8  # Default confidence for regression
                        confidences.append(confidence)

                except Exception as e:
                    self.log_error(f"Prediction failed for model {model_name}: {e}")

            if predictions:
                # Ensemble prediction (average)
                expected_return = np.mean(predictions)
                avg_confidence = np.mean(confidences)
                return expected_return, avg_confidence
            else:
                return None, None

        except Exception as e:
            self.log_error(f"Expected return prediction failed: {e}")
            return None, None

    async def _predict_risk_metrics(self, feature_vector: np.ndarray, feature_names: List[str]) -> Dict[str, float]:
        """Predict risk metrics"""
        try:
            task_name = 'risk_metrics'
            risk_metrics = {}

            if task_name not in self.loaded_models:
                self.log_warning(f"No models loaded for {task_name}")
                return risk_metrics

            models = self.loaded_models[task_name]
            targets = self.prediction_config[task_name].get('targets', ['expected_drawdown', 'volatility'])

            # Predict each risk metric
            for target in targets:
                predictions = []

                for model_name, model in models.items():
                    try:
                        # Prepare model-specific features
                        model_features = self._prepare_model_features(feature_vector, feature_names, model_name, model, task_name)
                        if model_features is None:
                            continue
                        
                        if hasattr(model, 'predict'):
                            pred = model.predict(model_features)[0]
                            predictions.append(pred)
                    except Exception as e:
                        self.log_error(f"Risk prediction failed for {model_name}: {e}")

                if predictions:
                    risk_metrics[target] = np.mean(predictions)

            return risk_metrics

        except Exception as e:
            self.log_error(f"Risk metrics prediction failed: {e}")
            return {}

    def _prepare_model_features(self, feature_vector: np.ndarray, feature_names: List[str], model_name: str, model: Any, task_name: str = None) -> Optional[np.ndarray]:
        """Prepare feature vector for a specific model by selecting the right features"""
        try:
            # Get the model's expected feature names
            model_feature_names = None
            if hasattr(model, 'feature_names_in_'):
                model_feature_names = list(model.feature_names_in_)
            elif hasattr(model, 'feature_name_'):
                model_feature_names = list(model.feature_name_)
            
            if model_feature_names is None:
                self.log_warning(f"Could not determine feature names for model {model_name}")
                return feature_vector  # Return original if we can't determine expected features
            
            # Create a mapping from feature names to indices
            feature_name_to_index = {name: i for i, name in enumerate(feature_names)}
            
            # Select the features that the model expects
            selected_indices = []
            missing_features = []
            
            for expected_feature in model_feature_names:
                if expected_feature in feature_name_to_index:
                    selected_indices.append(feature_name_to_index[expected_feature])
                else:
                    missing_features.append(expected_feature)
            
            if missing_features:
                self.log_warning(f"Model {model_name} expects features not available: {missing_features}")
                # For missing features, we'll use zeros or mean values
                # For now, let's skip this model if critical features are missing
                if len(missing_features) > len(model_feature_names) * 0.1:  # More than 10% missing
                    self.log_error(f"Too many missing features for {model_name}, skipping")
                    return None
            
            # Create the model-specific feature vector
            if selected_indices:
                model_features = feature_vector[:, selected_indices]
                
                # If we have missing features, pad with zeros (this is a fallback)
                if len(selected_indices) < len(model_feature_names):
                    padding_size = len(model_feature_names) - len(selected_indices)
                    padding = np.zeros((feature_vector.shape[0], padding_size))
                    model_features = np.concatenate([model_features, padding], axis=1)
                
                self.log_debug(f"Prepared {model_features.shape[1]} features for model {model_name} (expected {len(model_feature_names)})")
                
                # Apply task-specific scaling if available
                # Map task names to scaler names
                scaler_name_mapping = {
                    'expected_return': ['roi_prediction', 'profit_factor_prediction'],
                    'risk_metrics': ['drawdown_prediction', 'sharpe_ratio_prediction'],
                    'strategy_suitability': ['profitability_classification']
                }
                
                # Find the appropriate scaler for this model
                scaler_name = None
                if task_name and task_name in scaler_name_mapping:
                    # Try to match model name to scaler name
                    for potential_scaler_name in scaler_name_mapping[task_name]:
                        if potential_scaler_name in model_name or potential_scaler_name in self.scalers:
                            scaler_name = potential_scaler_name
                            break
                
                if scaler_name and scaler_name in self.scalers:
                    scaler = self.scalers[scaler_name]
                    try:
                        # Validate feature count matches scaler expectations
                        if hasattr(scaler, 'n_features_in_') and model_features.shape[1] == scaler.n_features_in_:
                            model_features = scaler.transform(model_features)
                            self.log_debug(f"Applied scaling for model {model_name} using scaler {scaler_name}")
                        else:
                            self.log_debug(f"Scaler feature count mismatch for {scaler_name}: got {model_features.shape[1]}, expected {scaler.n_features_in_}. Using unscaled features.")
                    except Exception as e:
                        self.log_warning(f"Scaling failed for {scaler_name}: {e}. Using unscaled features.")
                
                return model_features
            else:
                self.log_error(f"No matching features found for model {model_name}")
                return None
            
        except Exception as e:
            self.log_error(f"Feature preparation failed for {model_name}: {e}")
            return None

    async def _predict_strategy_suitability(self, feature_vector: np.ndarray, feature_names: List[str]) -> Dict[str, float]:
        """Predict strategy suitability scores"""
        try:
            task_name = 'strategy_suitability'
            strategy_scores = {}

            if task_name not in self.loaded_models:
                self.log_warning(f"No models loaded for {task_name}")
                return strategy_scores

            models = self.loaded_models[task_name]
            strategy_types = self.prediction_config[task_name].get('strategy_types',
                                                                  ['momentum', 'mean_reversion', 'breakout', 'trend_following'])

            # Get predictions from models
            for model_name, model in models.items():
                try:
                    # Prepare model-specific features
                    model_features = self._prepare_model_features(feature_vector, feature_names, model_name, model, task_name)
                    if model_features is None:
                        continue
                    
                    if hasattr(model, 'predict_proba'):
                        # Classification model
                        probabilities = model.predict_proba(model_features)[0]

                        # Map probabilities to strategy types
                        for i, strategy in enumerate(strategy_types[:len(probabilities)]):
                            if strategy not in strategy_scores:
                                strategy_scores[strategy] = []
                            strategy_scores[strategy].append(probabilities[i])

                    elif hasattr(model, 'predict'):
                        # Regression model - assume single strategy score
                        score = model.predict(model_features)[0]
                        if strategy_types:
                            if strategy_types[0] not in strategy_scores:
                                strategy_scores[strategy_types[0]] = []
                            strategy_scores[strategy_types[0]].append(score)

                except Exception as e:
                    # Log the error but continue with other models
                    self.log_error(f"Strategy suitability prediction failed for {model_name}: {e}")
                    continue

            # Average scores across models
            final_scores = {}
            for strategy, scores in strategy_scores.items():
                if scores:
                    final_scores[strategy] = np.mean(scores)

            # If no predictions were successful, provide default scores
            if not final_scores:
                self.log_warning("No strategy suitability predictions available, using defaults")
                for strategy in strategy_types:
                    final_scores[strategy] = 0.5  # Default neutral score

            return final_scores

        except Exception as e:
            self.log_error(f"Strategy suitability prediction failed: {e}")
            # Return default scores for all strategies
            strategy_types = self.prediction_config.get('strategy_suitability', {}).get('strategy_types',
                                                                  ['momentum', 'mean_reversion', 'breakout', 'trend_following'])
            return {strategy: 0.5 for strategy in strategy_types}

    async def _generate_confidence_intervals(self, feature_vector: np.ndarray) -> Dict[str, Tuple[float, float]]:
        """Generate confidence intervals for predictions"""
        try:
            confidence_intervals = {}

            if not self.ml_config['confidence_intervals']['enabled']:
                return confidence_intervals

            confidence_level = self.ml_config['confidence_intervals']['confidence_level']
            method = self.ml_config['confidence_intervals']['method']

            # For now, implement simple confidence intervals
            # In a full implementation, this would use bootstrap or quantile regression

            for task_name in self.loaded_models.keys():
                if task_name == 'expected_return':
                    # Simple confidence interval based on model uncertainty
                    lower_bound = -0.05  # -5%
                    upper_bound = 0.05   # +5%
                    confidence_intervals[task_name] = (lower_bound, upper_bound)

            return confidence_intervals

        except Exception as e:
            self.log_error(f"Confidence interval generation failed: {e}")
            return {}

    def _calculate_prediction_quality(self, expected_return: Optional[float],
                                    expected_return_confidence: Optional[float],
                                    risk_metrics: Dict[str, float],
                                    strategy_suitability: Dict[str, float]) -> float:
        """Calculate overall prediction quality score"""
        try:
            quality_components = []

            # Return prediction quality
            if expected_return is not None and expected_return_confidence is not None:
                quality_components.append(expected_return_confidence)
            elif expected_return is not None:
                # If we have a return prediction but no confidence, assume moderate quality
                quality_components.append(0.6)
            else:
                quality_components.append(0.0)

            # Risk metrics quality (more lenient)
            if risk_metrics:
                risk_quality = min(1.0, len(risk_metrics) / 1)  # Expect at least 1 risk metric
                quality_components.append(risk_quality)
            else:
                quality_components.append(0.3)  # Give some base score even without risk metrics

            # Strategy suitability quality (more lenient)
            if strategy_suitability:
                strategy_quality = min(1.0, len(strategy_suitability) / 2)  # Expect at least 2 strategies
                # Also consider the average confidence of strategy predictions
                avg_strategy_confidence = np.mean(list(strategy_suitability.values()))
                strategy_quality = (strategy_quality + avg_strategy_confidence) / 2
                quality_components.append(strategy_quality)
            else:
                quality_components.append(0.3)  # Give some base score even without strategy suitability

            # Overall quality (average of components)
            overall_quality = np.mean(quality_components) if quality_components else 0.0
            
            # Ensure minimum quality score of 0.3 if we have any predictions
            if expected_return is not None or risk_metrics or strategy_suitability:
                overall_quality = max(0.3, overall_quality)
            
            return overall_quality

        except Exception as e:
            self.log_error(f"Prediction quality calculation failed: {e}")
            return 0.3  # Return a reasonable default instead of 0.0

    def get_model_info(self) -> Dict[str, ModelInfo]:
        """Get information about loaded models"""
        return self.model_info.copy()

    def get_predictions(self) -> Dict[str, PredictionResult]:
        """Get all generated predictions"""
        return self.predictions.copy()

    def get_prediction_summary(self) -> Dict[str, Any]:
        """Get summary of prediction results"""
        if not self.predictions:
            return {}

        total_predictions = len(self.predictions)
        valid_predictions = sum(1 for p in self.predictions.values() if p.is_valid)
        avg_quality = np.mean([p.prediction_quality for p in self.predictions.values()])

        return {
            'total_predictions': total_predictions,
            'valid_predictions': valid_predictions,
            'success_rate': valid_predictions / total_predictions if total_predictions > 0 else 0,
            'average_quality': avg_quality,
            'loaded_models': list(self.loaded_models.keys())
        }

# Example usage and testing
async def main():
    """Example usage of Live ML Prediction Agent"""
    try:
        # For testing, create a dummy config and event bus
        class DummyEventBus:
            async def publish(self, event_type: str, payload: Dict):
                print(f"Event Published: {event_type} - {payload}")

        dummy_config = {
            'ml_prediction': {
                'model_loading': {
                    'models_directory': 'data/models/enhanced', # Assuming this path exists for models
                    'model_version': 'latest',
                    'fallback_models': ['random_forest']
                },
                'prediction_tasks': {
                    'expected_return': {'enabled': True},
                    'risk_metrics': {'enabled': True, 'targets': ['expected_drawdown', 'volatility']},
                    'strategy_suitability': {'enabled': True, 'strategy_types': ['momentum', 'mean_reversion', 'breakout', 'trend_following']}
                },
                'confidence_intervals': {
                    'enabled': True,
                    'confidence_level': 0.95,
                    'method': 'simple'
                }
            }
        }
        event_bus = DummyEventBus()
        session_id = "test_session_123"

        agent = LiveMLPredictionAgent(event_bus, dummy_config, session_id)
        await agent.initialize()
        await agent.start()

        # Create sample features for testing
        sample_features = {
            "RELIANCE": {
                "volatility_std": 0.02,
                "rsi_14": 65.0,
                "macd": 0.5,
                "bb_percent_b": 0.7,
                "ema_20": 2500.0,
                "volume_ratio": 1.2,
                "roc": 2.5
            },
            "TCS": {
                "volatility_std": 0.015,
                "rsi_14": 45.0,
                "macd": -0.2,
                "bb_percent_b": 0.3,
                "ema_20": 3800.0,
                "volume_ratio": 0.9,
                "roc": -1.0
            }
        }

        # Generate predictions
        predictions = await agent.generate_predictions(sample_features)

        # Print results
        print(f"\n🔮 Generated predictions for {len(predictions)} stocks:")
        for symbol, prediction in predictions.items():
            print(f"  {symbol}:")
            print(f"    Expected return: {prediction.expected_return:.4f}" if prediction.expected_return else "    No expected return")
            print(f"    Risk metrics: {prediction.risk_metrics}")
            print(f"    Strategy suitability: {prediction.strategy_suitability}")
            print(f"    Quality: {prediction.prediction_quality:.2f}")

        # Print summary
        summary = agent.get_prediction_summary()
        print(f"\n📊 Prediction Summary:")
        print(f"  Success rate: {summary['success_rate']*100:.1f}%")
        print(f"  Average quality: {summary['average_quality']:.2f}")

        await agent.stop()

    except Exception as e:
        logger.error(f"Example failed: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())

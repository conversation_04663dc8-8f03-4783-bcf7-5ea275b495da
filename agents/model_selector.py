#!/usr/bin/env python3
"""
Model Selection System for LLM Interface Agent

This module provides intelligent model selection based on query analysis,
task requirements, and model capabilities. It analyzes user queries and
selects the most appropriate Ollama model for optimal performance.

Features:
🧠 1. Query Analysis and Classification
- Natural language processing for intent detection
- Keyword extraction and semantic analysis
- Context-aware classification

[TARGET] 2. Model Capability Matching
- Performance benchmarks for each model
- Task-specific optimization
- Resource usage considerations

[STATUS] 3. Dynamic Selection Algorithm
- Multi-factor scoring system
- Performance history tracking
- Adaptive learning from results

Author: AI Assistant
Date: 2025-01-16
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import yaml
from collections import defaultdict, Counter

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] MODEL CAPABILITY DEFINITIONS
# ═══════════════════════════════════════════════════════════════════════════════

class TaskType(Enum):
    """Task type enumeration"""
    CODE_GENERATION = "code_generation"
    CODE_EXPLANATION = "code_explanation"
    CODE_DEBUGGING = "code_debugging"
    GENERAL_REASONING = "general_reasoning"
    INSTRUCTION_FOLLOWING = "instruction_following"
    NATURAL_LANGUAGE = "natural_language"
    MATHEMATICAL = "mathematical"
    STRATEGY_ANALYSIS = "strategy_analysis"
    QUICK_RESPONSE = "quick_response"
    COMPLEX_ANALYSIS = "complex_analysis"

@dataclass
class ModelCapability:
    """Model capability and performance metrics"""
    name: str
    size: str  # e.g., "8B", "7B", "6.7B"
    strengths: List[str]
    weaknesses: List[str]
    performance_scores: Dict[TaskType, float]  # 0.0 to 1.0
    resource_usage: Dict[str, Any]  # memory, speed, etc.
    use_cases: List[str]
    temperature_range: Tuple[float, float]
    max_tokens_recommended: int

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 MODEL DEFINITIONS BASED ON RESEARCH
# ═══════════════════════════════════════════════════════════════════════════════

MODEL_CAPABILITIES = {
    "qwen3-8b": ModelCapability(
        name="qwen3-8b",
        size="8B",
        strengths=[
            "Excellent multilingual support",
            "Strong general reasoning",
            "Good mathematical capabilities",
            "Balanced performance across tasks",
            "Efficient inference speed"
        ],
        weaknesses=[
            "Larger memory footprint",
            "May be overkill for simple tasks",
            "Slower than smaller models"
        ],
        performance_scores={
            TaskType.CODE_GENERATION: 0.75,
            TaskType.CODE_EXPLANATION: 0.80,
            TaskType.CODE_DEBUGGING: 0.70,
            TaskType.GENERAL_REASONING: 0.90,
            TaskType.INSTRUCTION_FOLLOWING: 0.85,
            TaskType.NATURAL_LANGUAGE: 0.95,
            TaskType.MATHEMATICAL: 0.85,
            TaskType.STRATEGY_ANALYSIS: 0.88,
            TaskType.QUICK_RESPONSE: 0.60,
            TaskType.COMPLEX_ANALYSIS: 0.92
        },
        resource_usage={
            "memory_gb": 8.0,
            "inference_speed": "medium",
            "gpu_required": False
        },
        use_cases=[
            "natural_language_query", "config_editing", "general_chat", 
            "strategy_explanation", "complex_reasoning"
        ],
        temperature_range=(0.3, 0.9),
        max_tokens_recommended=2048
    ),
    
    "deepseek-coder-6.7b": ModelCapability(
        name="deepseek-coder-6.7b",
        size="6.7B",
        strengths=[
            "Exceptional coding capabilities",
            "Strong Python expertise",
            "Excellent debugging skills",
            "Good at code optimization",
            "Understands complex algorithms"
        ],
        weaknesses=[
            "Limited general knowledge",
            "Weaker at non-coding tasks",
            "May over-engineer solutions"
        ],
        performance_scores={
            TaskType.CODE_GENERATION: 0.95,
            TaskType.CODE_EXPLANATION: 0.85,
            TaskType.CODE_DEBUGGING: 0.92,
            TaskType.GENERAL_REASONING: 0.65,
            TaskType.INSTRUCTION_FOLLOWING: 0.80,
            TaskType.NATURAL_LANGUAGE: 0.60,
            TaskType.MATHEMATICAL: 0.75,
            TaskType.STRATEGY_ANALYSIS: 0.70,
            TaskType.QUICK_RESPONSE: 0.70,
            TaskType.COMPLEX_ANALYSIS: 0.75
        },
        resource_usage={
            "memory_gb": 6.7,
            "inference_speed": "fast",
            "gpu_required": False
        },
        use_cases=[
            "code_generation", "bug_fixing", "strategy_creation", 
            "python_development", "algorithm_implementation"
        ],
        temperature_range=(0.1, 0.5),
        max_tokens_recommended=4096
    ),
    
    "codellama-7b-instruct": ModelCapability(
        name="codellama-7b-instruct",
        size="7B",
        strengths=[
            "Excellent code explanations",
            "Good at refactoring",
            "Strong instruction following",
            "Detailed documentation",
            "Safe for complex logic changes"
        ],
        weaknesses=[
            "Slower code generation",
            "Sometimes overly verbose",
            "Less creative solutions"
        ],
        performance_scores={
            TaskType.CODE_GENERATION: 0.80,
            TaskType.CODE_EXPLANATION: 0.95,
            TaskType.CODE_DEBUGGING: 0.85,
            TaskType.GENERAL_REASONING: 0.70,
            TaskType.INSTRUCTION_FOLLOWING: 0.90,
            TaskType.NATURAL_LANGUAGE: 0.75,
            TaskType.MATHEMATICAL: 0.70,
            TaskType.STRATEGY_ANALYSIS: 0.75,
            TaskType.QUICK_RESPONSE: 0.65,
            TaskType.COMPLEX_ANALYSIS: 0.80
        ],
        resource_usage={
            "memory_gb": 7.0,
            "inference_speed": "medium",
            "gpu_required": False
        },
        use_cases=[
            "code_explanation", "refactoring", "complex_logic", 
            "documentation", "code_review"
        ],
        temperature_range=(0.2, 0.6),
        max_tokens_recommended=3072
    ),
    
    "mistral-7b-instruct": ModelCapability(
        name="mistral-7b-instruct",
        size="7B",
        strengths=[
            "Fast and accurate",
            "Excellent instruction following",
            "Good at error fixing",
            "Balanced performance",
            "Reliable responses"
        ],
        weaknesses=[
            "Less creative than larger models",
            "Limited context understanding",
            "Weaker at complex reasoning"
        ],
        performance_scores={
            TaskType.CODE_GENERATION: 0.75,
            TaskType.CODE_EXPLANATION: 0.70,
            TaskType.CODE_DEBUGGING: 0.85,
            TaskType.GENERAL_REASONING: 0.75,
            TaskType.INSTRUCTION_FOLLOWING: 0.95,
            TaskType.NATURAL_LANGUAGE: 0.80,
            TaskType.MATHEMATICAL: 0.70,
            TaskType.STRATEGY_ANALYSIS: 0.70,
            TaskType.QUICK_RESPONSE: 0.85,
            TaskType.COMPLEX_ANALYSIS: 0.65
        ],
        resource_usage={
            "memory_gb": 7.0,
            "inference_speed": "fast",
            "gpu_required": False
        },
        use_cases=[
            "error_fixing", "config_fixes", "instruction_following", 
            "debugging", "quick_tasks"
        ],
        temperature_range=(0.1, 0.4),
        max_tokens_recommended=2048
    ),
    
    "phi4-mini": ModelCapability(
        name="phi4-mini",
        size="3.8B",
        strengths=[
            "Very fast inference",
            "Low memory usage",
            "Good for simple tasks",
            "Excellent for chat",
            "Quick summaries"
        ],
        weaknesses=[
            "Limited complex reasoning",
            "Shorter context window",
            "Less detailed responses",
            "Weaker at coding"
        ],
        performance_scores={
            TaskType.CODE_GENERATION: 0.50,
            TaskType.CODE_EXPLANATION: 0.55,
            TaskType.CODE_DEBUGGING: 0.45,
            TaskType.GENERAL_REASONING: 0.65,
            TaskType.INSTRUCTION_FOLLOWING: 0.75,
            TaskType.NATURAL_LANGUAGE: 0.85,
            TaskType.MATHEMATICAL: 0.55,
            TaskType.STRATEGY_ANALYSIS: 0.60,
            TaskType.QUICK_RESPONSE: 0.95,
            TaskType.COMPLEX_ANALYSIS: 0.40
        ],
        resource_usage={
            "memory_gb": 3.8,
            "inference_speed": "very_fast",
            "gpu_required": False
        },
        use_cases=[
            "quick_chat", "summaries", "fast_responses", 
            "simple_queries", "status_checks"
        ],
        temperature_range=(0.4, 0.8),
        max_tokens_recommended=1024
    )
}

# ═══════════════════════════════════════════════════════════════════════════════
# [TARGET] MODEL SELECTOR CLASS
# ═══════════════════════════════════════════════════════════════════════════════

class ModelSelector:
    """
    Intelligent model selection system for optimal LLM routing
    
    Analyzes queries and selects the best model based on:
    - Task type classification
    - Model capabilities and performance
    - Resource constraints
    - Historical performance
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize model selector"""
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self.config = self._load_config(config_path) if config_path else {}
        
        # Model capabilities
        self.model_capabilities = MODEL_CAPABILITIES
        
        # Performance tracking
        self.performance_history = defaultdict(list)
        self.selection_history = []
        
        # Query analysis patterns
        self.task_patterns = self._initialize_task_patterns()
        
        self.logger.info("[TARGET] Model Selector initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.warning(f"[WARN] Could not load config: {e}")
            return {}
    
    def _initialize_task_patterns(self) -> Dict[TaskType, Dict[str, Any]]:
        """Initialize task classification patterns"""
        return {
            TaskType.CODE_GENERATION: {
                "keywords": [
                    "generate", "create", "write", "implement", "build", "develop",
                    "code", "function", "class", "script", "algorithm", "program"
                ],
                "patterns": [
                    r"generate.*code", r"create.*function", r"write.*script",
                    r"implement.*algorithm", r"build.*class"
                ],
                "weight": 1.0
            },
            TaskType.CODE_DEBUGGING: {
                "keywords": [
                    "fix", "debug", "error", "bug", "issue", "problem", "broken",
                    "not working", "fails", "exception", "traceback"
                ],
                "patterns": [
                    r"fix.*error", r"debug.*code", r"solve.*problem",
                    r"error.*in", r"bug.*fix"
                ],
                "weight": 1.0
            },
            TaskType.CODE_EXPLANATION: {
                "keywords": [
                    "explain", "how", "why", "what does", "understand", "meaning",
                    "documentation", "comment", "describe"
                ],
                "patterns": [
                    r"explain.*code", r"how.*works", r"what.*does",
                    r"understand.*function"
                ],
                "weight": 1.0
            },
            TaskType.STRATEGY_ANALYSIS: {
                "keywords": [
                    "strategy", "trading", "backtest", "performance", "roi", "profit",
                    "loss", "sharpe", "drawdown", "analysis"
                ],
                "patterns": [
                    r"strategy.*performance", r"trading.*analysis", r"backtest.*results"
                ],
                "weight": 1.0
            },
            TaskType.QUICK_RESPONSE: {
                "keywords": [
                    "status", "quick", "brief", "summary", "overview", "list",
                    "show", "get", "what is"
                ],
                "patterns": [
                    r"quick.*status", r"brief.*summary", r"show.*me"
                ],
                "weight": 0.8
            },
            TaskType.MATHEMATICAL: {
                "keywords": [
                    "calculate", "compute", "math", "formula", "equation",
                    "statistics", "probability", "optimization"
                ],
                "patterns": [
                    r"calculate.*value", r"compute.*result", r"solve.*equation"
                ],
                "weight": 1.0
            }
        }

    def select_model(self, query: str, context: Optional[Dict[str, Any]] = None) -> Tuple[str, float, Dict[str, Any]]:
        """
        Select the best model for a given query

        Args:
            query: User query string
            context: Optional context information

        Returns:
            Tuple of (model_name, confidence_score, selection_metadata)
        """
        try:
            # Analyze query to determine task type
            task_analysis = self._analyze_query(query)

            # Score all available models
            model_scores = self._score_models(task_analysis, context)

            # Select best model
            best_model = max(model_scores.items(), key=lambda x: x[1]['total_score'])
            model_name, score_info = best_model

            # Create selection metadata
            metadata = {
                'task_type': task_analysis['primary_task'].value,
                'confidence': score_info['total_score'],
                'reasoning': score_info['reasoning'],
                'alternatives': [
                    {'model': name, 'score': info['total_score']}
                    for name, info in sorted(model_scores.items(),
                                           key=lambda x: x[1]['total_score'], reverse=True)[1:3]
                ],
                'recommended_temperature': self._get_recommended_temperature(model_name, task_analysis),
                'recommended_max_tokens': self._get_recommended_max_tokens(model_name, task_analysis)
            }

            # Record selection
            self._record_selection(query, model_name, score_info['total_score'], metadata)

            self.logger.info(f"[TARGET] Selected model: {model_name} (confidence: {score_info['total_score']:.2f})")

            return model_name, score_info['total_score'], metadata

        except Exception as e:
            self.logger.error(f"[ERROR] Error selecting model: {e}")
            # Fallback to general reasoning model
            return "qwen3-8b", 0.5, {"error": str(e), "fallback": True}

    def _analyze_query(self, query: str) -> Dict[str, Any]:
        """Analyze query to determine task type and characteristics"""
        query_lower = query.lower()

        # Score each task type
        task_scores = {}
        for task_type, patterns in self.task_patterns.items():
            score = 0.0

            # Keyword matching
            keyword_matches = sum(1 for keyword in patterns['keywords']
                                if keyword in query_lower)
            score += keyword_matches * 0.3

            # Pattern matching
            pattern_matches = sum(1 for pattern in patterns.get('patterns', [])
                                if re.search(pattern, query_lower))
            score += pattern_matches * 0.5

            # Apply weight
            score *= patterns.get('weight', 1.0)

            if score > 0:
                task_scores[task_type] = score

        # Determine primary and secondary tasks
        if task_scores:
            sorted_tasks = sorted(task_scores.items(), key=lambda x: x[1], reverse=True)
            primary_task = sorted_tasks[0][0]
            secondary_task = sorted_tasks[1][0] if len(sorted_tasks) > 1 else None
        else:
            primary_task = TaskType.GENERAL_REASONING
            secondary_task = None

        # Analyze query characteristics
        characteristics = {
            'length': len(query.split()),
            'complexity': self._estimate_complexity(query),
            'urgency': self._estimate_urgency(query),
            'technical_level': self._estimate_technical_level(query)
        }

        return {
            'primary_task': primary_task,
            'secondary_task': secondary_task,
            'task_scores': task_scores,
            'characteristics': characteristics,
            'query_length': len(query)
        }

    def _score_models(self, task_analysis: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Score all models for the given task"""
        model_scores = {}

        primary_task = task_analysis['primary_task']
        characteristics = task_analysis['characteristics']

        for model_name, capability in self.model_capabilities.items():
            # Base performance score for primary task
            base_score = capability.performance_scores.get(primary_task, 0.5)

            # Adjust for secondary task if present
            secondary_task = task_analysis.get('secondary_task')
            if secondary_task:
                secondary_score = capability.performance_scores.get(secondary_task, 0.5)
                base_score = base_score * 0.7 + secondary_score * 0.3

            # Adjust for query characteristics
            complexity_factor = self._get_complexity_factor(capability, characteristics['complexity'])
            urgency_factor = self._get_urgency_factor(capability, characteristics['urgency'])
            technical_factor = self._get_technical_factor(capability, characteristics['technical_level'])

            # Calculate total score
            total_score = base_score * complexity_factor * urgency_factor * technical_factor

            # Apply historical performance if available
            if model_name in self.performance_history:
                historical_avg = sum(self.performance_history[model_name]) / len(self.performance_history[model_name])
                total_score = total_score * 0.8 + historical_avg * 0.2

            # Create reasoning
            reasoning = [
                f"Base performance for {primary_task.value}: {base_score:.2f}",
                f"Complexity factor: {complexity_factor:.2f}",
                f"Urgency factor: {urgency_factor:.2f}",
                f"Technical factor: {technical_factor:.2f}"
            ]

            if secondary_task:
                reasoning.append(f"Secondary task ({secondary_task.value}) considered")

            model_scores[model_name] = {
                'total_score': total_score,
                'base_score': base_score,
                'factors': {
                    'complexity': complexity_factor,
                    'urgency': urgency_factor,
                    'technical': technical_factor
                },
                'reasoning': reasoning
            }

        return model_scores

    def _estimate_complexity(self, query: str) -> float:
        """Estimate query complexity (0.0 to 1.0)"""
        complexity_indicators = [
            'complex', 'advanced', 'detailed', 'comprehensive', 'sophisticated',
            'algorithm', 'optimization', 'analysis', 'multiple', 'various'
        ]

        query_lower = query.lower()
        complexity_score = sum(1 for indicator in complexity_indicators if indicator in query_lower)

        # Normalize based on query length and indicators
        length_factor = min(len(query.split()) / 50, 1.0)  # Longer queries tend to be more complex
        indicator_factor = min(complexity_score / 3, 1.0)

        return (length_factor + indicator_factor) / 2

    def _estimate_urgency(self, query: str) -> float:
        """Estimate query urgency (0.0 to 1.0)"""
        urgency_indicators = [
            'quick', 'fast', 'urgent', 'immediately', 'asap', 'now',
            'brief', 'summary', 'status', 'simple'
        ]

        query_lower = query.lower()
        urgency_score = sum(1 for indicator in urgency_indicators if indicator in query_lower)

        return min(urgency_score / 2, 1.0)

    def _estimate_technical_level(self, query: str) -> float:
        """Estimate technical level required (0.0 to 1.0)"""
        technical_indicators = [
            'code', 'function', 'class', 'algorithm', 'implementation',
            'debug', 'error', 'exception', 'api', 'database', 'optimization'
        ]

        query_lower = query.lower()
        technical_score = sum(1 for indicator in technical_indicators if indicator in query_lower)

        return min(technical_score / 3, 1.0)

    def _get_complexity_factor(self, capability: ModelCapability, complexity: float) -> float:
        """Get complexity adjustment factor for model"""
        # Larger models handle complexity better
        if capability.size in ["8B", "7B"]:
            return 1.0 + (complexity * 0.2)  # Boost for complex tasks
        else:
            return 1.0 - (complexity * 0.3)  # Penalty for complex tasks on smaller models

    def _get_urgency_factor(self, capability: ModelCapability, urgency: float) -> float:
        """Get urgency adjustment factor for model"""
        # Faster models get boost for urgent tasks
        speed = capability.resource_usage.get('inference_speed', 'medium')

        if urgency > 0.7:  # High urgency
            if speed == 'very_fast':
                return 1.3
            elif speed == 'fast':
                return 1.1
            else:
                return 0.9
        else:
            return 1.0

    def _get_technical_factor(self, capability: ModelCapability, technical_level: float) -> float:
        """Get technical level adjustment factor for model"""
        # Code-specialized models get boost for technical tasks
        if technical_level > 0.5:
            if 'coder' in capability.name or 'code' in capability.name:
                return 1.2
            elif capability.performance_scores.get(TaskType.CODE_GENERATION, 0) > 0.8:
                return 1.1
            else:
                return 1.0
        else:
            return 1.0

    def _get_recommended_temperature(self, model_name: str, task_analysis: Dict[str, Any]) -> float:
        """Get recommended temperature for model and task"""
        capability = self.model_capabilities.get(model_name)
        if not capability:
            return 0.7

        min_temp, max_temp = capability.temperature_range
        primary_task = task_analysis['primary_task']

        # Task-specific temperature recommendations
        if primary_task in [TaskType.CODE_GENERATION, TaskType.CODE_DEBUGGING]:
            return min_temp + (max_temp - min_temp) * 0.2  # Lower for code tasks
        elif primary_task == TaskType.QUICK_RESPONSE:
            return min_temp + (max_temp - min_temp) * 0.3  # Lower for quick responses
        elif primary_task in [TaskType.NATURAL_LANGUAGE, TaskType.GENERAL_REASONING]:
            return min_temp + (max_temp - min_temp) * 0.6  # Higher for creative tasks
        else:
            return (min_temp + max_temp) / 2  # Default middle ground

    def _get_recommended_max_tokens(self, model_name: str, task_analysis: Dict[str, Any]) -> int:
        """Get recommended max tokens for model and task"""
        capability = self.model_capabilities.get(model_name)
        if not capability:
            return 2048

        base_tokens = capability.max_tokens_recommended
        complexity = task_analysis['characteristics']['complexity']

        # Adjust based on complexity
        if complexity > 0.7:
            return min(int(base_tokens * 1.5), 4096)
        elif complexity < 0.3:
            return max(int(base_tokens * 0.7), 512)
        else:
            return base_tokens

    def _record_selection(self, query: str, model_name: str, confidence: float, metadata: Dict[str, Any]):
        """Record model selection for performance tracking"""
        selection_record = {
            'timestamp': logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, '', (), None)),
            'query': query[:100],  # Truncate for privacy
            'model': model_name,
            'confidence': confidence,
            'task_type': metadata.get('task_type'),
            'metadata': metadata
        }

        self.selection_history.append(selection_record)

        # Keep only last 1000 selections
        if len(self.selection_history) > 1000:
            self.selection_history = self.selection_history[-1000:]

    def record_performance(self, model_name: str, performance_score: float):
        """Record performance feedback for a model"""
        if 0.0 <= performance_score <= 1.0:
            self.performance_history[model_name].append(performance_score)

            # Keep only last 100 performance records per model
            if len(self.performance_history[model_name]) > 100:
                self.performance_history[model_name] = self.performance_history[model_name][-100:]

            self.logger.info(f"[STATUS] Recorded performance for {model_name}: {performance_score:.2f}")

    def get_model_stats(self) -> Dict[str, Any]:
        """Get model selection and performance statistics"""
        stats = {
            'total_selections': len(self.selection_history),
            'model_usage': Counter(record['model'] for record in self.selection_history),
            'task_distribution': Counter(record['task_type'] for record in self.selection_history),
            'average_confidence': sum(record['confidence'] for record in self.selection_history) / len(self.selection_history) if self.selection_history else 0,
            'performance_history': {
                model: {
                    'count': len(scores),
                    'average': sum(scores) / len(scores) if scores else 0,
                    'latest': scores[-1] if scores else None
                }
                for model, scores in self.performance_history.items()
            }
        }

        return stats

    def get_model_recommendations(self, task_type: str) -> List[Dict[str, Any]]:
        """Get model recommendations for a specific task type"""
        try:
            task_enum = TaskType(task_type)
        except ValueError:
            return []

        recommendations = []
        for model_name, capability in self.model_capabilities.items():
            score = capability.performance_scores.get(task_enum, 0.0)

            recommendations.append({
                'model': model_name,
                'score': score,
                'strengths': capability.strengths,
                'use_cases': capability.use_cases,
                'resource_usage': capability.resource_usage
            })

        # Sort by score
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TESTING AND DEMO FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def demo_model_selection():
    """Demo function to test model selection"""
    selector = ModelSelector()

    test_queries = [
        "Generate a new RSI scalping strategy in Python",
        "Fix the error in my MACD strategy YAML file",
        "Explain how the SuperTrend indicator works",
        "What's my portfolio status?",
        "Quick summary of today's trades",
        "Optimize the backtesting algorithm for better performance",
        "Calculate the Sharpe ratio for my strategy",
        "Debug this complex trading algorithm"
    ]

    print("[TARGET] Model Selection Demo")
    print("=" * 60)

    for query in test_queries:
        print(f"\n📝 Query: {query}")
        model, confidence, metadata = selector.select_model(query)
        print(f"🧠 Selected Model: {model}")
        print(f"[TARGET] Confidence: {confidence:.2f}")
        print(f"[STATUS] Task Type: {metadata['task_type']}")
        print(f"🌡️ Temperature: {metadata['recommended_temperature']:.2f}")
        print(f"📏 Max Tokens: {metadata['recommended_max_tokens']}")

        if metadata.get('alternatives'):
            print("[WORKFLOW] Alternatives:")
            for alt in metadata['alternatives']:
                print(f"   • {alt['model']}: {alt['score']:.2f}")

    print("\n[STATUS] Selection Statistics:")
    stats = selector.get_model_stats()
    print(f"Total Selections: {stats['total_selections']}")
    print(f"Model Usage: {dict(stats['model_usage'])}")
    print(f"Average Confidence: {stats['average_confidence']:.2f}")

if __name__ == "__main__":
    demo_model_selection()

#!/usr/bin/env python3
"""
MODERN MARKET DATA AGENT
Clean implementation with proper async handling and modern timeframes

Features:
- Real-time WebSocket data streaming using SmartAPI v2
- Historical data download with proper date handling
- Modern timeframes: 1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr
- Proper async/await patterns
- Clean error handling
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import polars as pl
from dataclasses import dataclass
import time

# SmartAPI imports
try:
    from SmartApi import SmartConnect
    import pyotp
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False

from .base_agent import BaseAgent
from ..utils.event_bus import EventBus, Event
from ..utils.config_manager import ConfigManager
from ..utils.modern_instrument_master import ModernInstrumentMaster
from ..utils.modern_websocket_manager import ModernWebSocketManager, WebSocketConfig

logger = logging.getLogger(__name__)

@dataclass
class MarketDataPoint:
    """Real-time market data point"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    ltp: float

class ModernMarketDataAgent(BaseAgent):
    """
    Modern Market Data Agent with clean architecture
    
    Features:
    - Clean async/await implementation
    - Modern timeframe support
    - Proper error handling
    - Real-time WebSocket data
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("ModernMarketDataAgent", event_bus, config, session_id)
        
        # SmartAPI client
        self.smartapi_client = None
        self.auth_token = None
        self.feed_token = None
        
        # WebSocket manager
        self.websocket_manager = None
        
        # Data storage with modern timeframes
        self.market_data = {}  # Real-time data
        self.timeframe_data = {
            "1min": {},
            "2min": {},
            "3min": {},
            "5min": {},
            "10min": {},
            "15min": {},
            "30min": {},
            "1hr": {}
        }
        
        # Instrument master
        self.instrument_master = None
        
        # Configuration
        self.config_manager = ConfigManager()
        self._load_credentials()
    
    def _load_credentials(self):
        """Load SmartAPI credentials"""
        try:
            credentials = self.config_manager.get_smartapi_credentials()
            self.api_key = credentials.get('api_key')
            self.username = credentials.get('username')
            self.password = credentials.get('password')
            self.totp_token = credentials.get('totp_token')
            
            self.credentials_available = all([
                self.api_key, self.username, self.password, self.totp_token
            ])
            
            if self.credentials_available:
                self.log_info("SmartAPI credentials loaded successfully")
            else:
                self.log_warning("SmartAPI credentials incomplete")
                
        except Exception as e:
            self.log_error(f"Failed to load credentials: {e}")
            self.credentials_available = False
    
    async def initialize(self) -> bool:
        """Initialize the market data agent"""
        try:
            self.log_info("Initializing Modern Market Data Agent...")
            
            # Initialize instrument master
            self.instrument_master = ModernInstrumentMaster()
            await self.instrument_master.load_instruments()
            
            # Initialize SmartAPI client
            if self.credentials_available:
                success = self._initialize_smartapi_client()
                if not success:
                    self.log_error("Failed to initialize SmartAPI client")
                    return False
                
                # Initialize WebSocket manager
                await self._initialize_websocket_manager()
            else:
                self.log_warning("Running without SmartAPI credentials (demo mode)")
            
            # Subscribe to events
            self.event_bus.subscribe("REQUEST_HISTORICAL_DATA", self._handle_historical_data_request)
            self.event_bus.subscribe("REQUEST_REALTIME_DATA", self._handle_realtime_data_request)
            
            self.initialized = True
            self.log_info("Modern Market Data Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False
    
    def _initialize_smartapi_client(self) -> bool:
        """Initialize SmartAPI client"""
        try:
            if not SMARTAPI_AVAILABLE:
                self.log_error("SmartAPI not available. Install with: pip install smartapi-python")
                return False
            
            self.log_info("Initializing SmartAPI client...")
            
            # Initialize SmartConnect
            self.smartapi_client = SmartConnect(api_key=self.api_key)
            
            # Generate TOTP
            totp = pyotp.TOTP(self.totp_token)
            totp_code = totp.now()
            
            # Login
            data = self.smartapi_client.generateSession(
                clientCode=self.username,
                password=self.password,
                totp=totp_code
            )
            
            if data['status']:
                self.auth_token = data['data']['jwtToken']
                self.refresh_token = data['data']['refreshToken']
                self.feed_token = self.smartapi_client.getfeedToken()
                
                self.log_info("SmartAPI authenticated successfully")
                return True
            else:
                self.log_error(f"SmartAPI authentication failed: {data.get('message', 'Unknown error')}")
                return False
                
        except Exception as e:
            self.log_error(f"Failed to initialize SmartAPI client: {e}")
            return False
    
    async def _initialize_websocket_manager(self):
        """Initialize WebSocket manager"""
        try:
            if not self.auth_token or not self.feed_token:
                self.log_error("Authentication tokens not available")
                return
            
            # Create WebSocket config
            ws_config = WebSocketConfig(
                auth_token=self.auth_token,
                api_key=self.api_key,
                client_code=self.username,
                feed_token=self.feed_token
            )
            
            # Create WebSocket manager
            self.websocket_manager = ModernWebSocketManager(ws_config)
            
            # Set callbacks
            self.websocket_manager.set_callbacks(
                on_connect=self._on_websocket_connect,
                on_data=self._on_websocket_data,
                on_error=self._on_websocket_error,
                on_close=self._on_websocket_close
            )
            
            self.log_info("WebSocket manager initialized")
            
        except Exception as e:
            self.log_error(f"Failed to initialize WebSocket manager: {e}")
    
    async def start(self):
        """Start the market data agent"""
        try:
            self.log_info("Starting Modern Market Data Agent...")
            
            self.running = True
            
            # Download historical data
            await self._download_historical_data()
            
            # Connect WebSocket if available
            if self.websocket_manager:
                await self._connect_websocket()
            
            # Start data processing loop
            await self._start_data_processing_loop()
            
        except Exception as e:
            self.log_error(f"Error starting agent: {e}")
    
    async def _download_historical_data(self):
        """Download historical data for selected stocks"""
        try:
            self.log_info(f"Downloading historical data for {len(self.config.selected_stocks)} stocks...")
            
            # Calculate date range (40 days back)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=40)
            
            for symbol in self.config.selected_stocks:
                try:
                    await self._download_symbol_data(symbol, start_date, end_date)
                    await self._generate_higher_timeframes(symbol)
                    
                    self.log_info(f"Downloaded data for {symbol}")
                    
                    # Rate limiting
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    self.log_error(f"Failed to download data for {symbol}: {e}")
                    continue
            
            self.log_info("Historical data download completed")
            
        except Exception as e:
            self.log_error(f"Failed to download historical data: {e}")
    
    async def _download_symbol_data(self, symbol: str, start_date: datetime, end_date: datetime):
        """Download data for a specific symbol"""
        try:
            if not self.smartapi_client:
                # Create mock data for demo mode
                await self._create_mock_data(symbol, start_date, end_date)
                return
            
            # Get instrument details
            instrument = await self.instrument_master.get_instrument(symbol, "NSE")
            if not instrument:
                self.log_warning(f"Instrument not found for {symbol}")
                return
            
            symbol_token = instrument['token']
            
            # Download in 30-day batches
            current_start = start_date
            symbol_data = []
            
            while current_start < end_date:
                batch_end = min(current_start + timedelta(days=30), end_date)
                
                # Format dates for SmartAPI (YYYY-MM-DD HH:MM format)
                from_date = current_start.strftime("%Y-%m-%d %H:%M")
                to_date = batch_end.strftime("%Y-%m-%d %H:%M")
                
                try:
                    # Download 1-minute data
                    hist_data = self.smartapi_client.getCandleData({
                        "exchange": "NSE",
                        "symboltoken": symbol_token,
                        "interval": "ONE_MINUTE",
                        "fromdate": from_date,
                        "todate": to_date
                    })
                    
                    if hist_data['status'] and hist_data['data']:
                        batch_data = hist_data['data']
                        symbol_data.extend(batch_data)
                        self.log_debug(f"Downloaded {len(batch_data)} candles for {symbol}")
                    
                    # Rate limiting
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    self.log_error(f"Failed to download batch for {symbol}: {e}")
                    break
                
                current_start = batch_end
            
            if symbol_data:
                # Convert to DataFrame with proper date handling
                df_data = []
                for candle in symbol_data:
                    try:
                        # Parse timestamp (handle different formats)
                        timestamp_str = candle[0]
                        timestamp = self._parse_timestamp(timestamp_str)
                        
                        df_data.append({
                            'timestamp': timestamp,
                            'open': float(candle[1]),
                            'high': float(candle[2]),
                            'low': float(candle[3]),
                            'close': float(candle[4]),
                            'volume': int(candle[5])
                        })
                    except Exception as e:
                        self.log_warning(f"Failed to parse candle data: {e}")
                        continue
                
                # Store 1-minute data
                if df_data:
                    self.timeframe_data["1min"][symbol] = pl.DataFrame(df_data)
                    self.log_info(f"Stored {len(df_data)} 1-min candles for {symbol}")
            
        except Exception as e:
            self.log_error(f"Failed to download symbol data for {symbol}: {e}")
    
    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse timestamp with multiple format support"""
        try:
            # Try different timestamp formats
            formats = [
                "%Y-%m-%d %H:%M:%S",  # 2025-01-31 09:15:00
                "%d-%m-%Y %H:%M:%S",  # 31-01-2025 09:15:00
                "%Y-%m-%dT%H:%M:%S",  # 2025-01-31T09:15:00
                "%Y-%m-%d %H:%M",     # 2025-01-31 09:15
                "%d-%m-%Y %H:%M",     # 31-01-2025 09:15
            ]
            
            # Remove timezone info if present
            if '+' in timestamp_str:
                timestamp_str = timestamp_str.split('+')[0]
            
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            
            # If all formats fail, try parsing as ISO format
            return datetime.fromisoformat(timestamp_str.replace('T', ' ').replace('+05:30', ''))
            
        except Exception as e:
            self.log_error(f"Failed to parse timestamp '{timestamp_str}': {e}")
            return datetime.now()
    
    async def _create_mock_data(self, symbol: str, start_date: datetime, end_date: datetime):
        """Create mock data for demo mode"""
        try:
            import random
            
            # Generate mock 1-minute data
            current_time = start_date
            mock_data = []
            base_price = random.uniform(100, 2000)  # Random base price
            
            while current_time < end_date:
                # Skip weekends
                if current_time.weekday() < 5:  # Monday = 0, Friday = 4
                    # Market hours: 9:15 AM to 3:30 PM
                    if 9 <= current_time.hour < 15 or (current_time.hour == 15 and current_time.minute <= 30):
                        # Generate realistic price movement
                        price_change = random.uniform(-0.02, 0.02)  # ±2% change
                        base_price *= (1 + price_change)
                        
                        high = base_price * random.uniform(1.0, 1.01)
                        low = base_price * random.uniform(0.99, 1.0)
                        volume = random.randint(1000, 10000)
                        
                        mock_data.append({
                            'timestamp': current_time,
                            'open': base_price,
                            'high': high,
                            'low': low,
                            'close': base_price,
                            'volume': volume
                        })
                
                current_time += timedelta(minutes=1)
            
            # Store mock data
            if mock_data:
                self.timeframe_data["1min"][symbol] = pl.DataFrame(mock_data)
                self.log_info(f"Created {len(mock_data)} mock 1-min candles for {symbol}")
            
        except Exception as e:
            self.log_error(f"Failed to create mock data for {symbol}: {e}")
    
    async def _generate_higher_timeframes(self, symbol: str):
        """Generate higher timeframes from 1-minute data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            df_1min = self.timeframe_data["1min"][symbol]
            
            # Modern timeframe configurations
            timeframe_configs = {
                "2min": "2m",
                "3min": "3m",
                "5min": "5m",
                "10min": "10m",
                "15min": "15m",
                "30min": "30m",
                "1hr": "1h"
            }
            
            for tf_name, tf_period in timeframe_configs.items():
                try:
                    df_tf = df_1min.group_by_dynamic(
                        "timestamp",
                        every=tf_period,
                        closed="left"
                    ).agg([
                        pl.col("open").first(),
                        pl.col("high").max(),
                        pl.col("low").min(),
                        pl.col("close").last(),
                        pl.col("volume").sum()
                    ])
                    
                    self.timeframe_data[tf_name][symbol] = df_tf
                    self.log_debug(f"Generated {tf_name} data for {symbol}")
                    
                except Exception as e:
                    self.log_warning(f"Failed to generate {tf_name} for {symbol}: {e}")
            
        except Exception as e:
            self.log_error(f"Failed to generate higher timeframes for {symbol}: {e}")
    
    async def _connect_websocket(self):
        """Connect WebSocket for real-time data"""
        try:
            if not self.websocket_manager:
                return
            
            self.log_info("Connecting to WebSocket...")
            
            success = await self.websocket_manager.connect()
            if success:
                # Subscribe to symbols
                await self._subscribe_to_symbols()
            else:
                self.log_error("Failed to connect WebSocket")
            
        except Exception as e:
            self.log_error(f"Failed to connect WebSocket: {e}")
    
    async def _subscribe_to_symbols(self):
        """Subscribe to symbols for real-time data"""
        try:
            if not self.websocket_manager or not self.websocket_manager.is_connected():
                return
            
            # Prepare subscription data
            token_list = []
            
            for symbol in self.config.selected_stocks:
                instrument = await self.instrument_master.get_instrument(symbol, "NSE")
                if instrument:
                    token_list.append({
                        "exchangeType": 1,  # NSE
                        "tokens": [instrument['token']]
                    })
            
            if token_list:
                # Subscribe to LTP mode
                success = self.websocket_manager.subscribe(token_list, mode=1)
                if success:
                    self.log_info(f"Subscribed to {len(token_list)} symbols")
                else:
                    self.log_error("Failed to subscribe to symbols")
            
        except Exception as e:
            self.log_error(f"Failed to subscribe to symbols: {e}")
    
    async def _start_data_processing_loop(self):
        """Start the main data processing loop"""
        try:
            self.log_info("Starting data processing loop...")
            
            while self.running:
                try:
                    # Process any pending data updates
                    await self._process_data_updates()
                    
                    # Check WebSocket connection health
                    if self.websocket_manager and not self.websocket_manager.is_connected():
                        await self.websocket_manager.reconnect()
                    
                    # Sleep for a short interval
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.log_error(f"Error in data processing loop: {e}")
                    await asyncio.sleep(5)
            
            self.log_info("Data processing loop ended")
            
        except Exception as e:
            self.log_error(f"Failed to start data processing loop: {e}")
    
    async def _process_data_updates(self):
        """Process any pending data updates"""
        try:
            # Update 1-minute candles with real-time data
            for symbol, data_point in self.market_data.items():
                await self._update_realtime_candle(symbol, data_point)
            
        except Exception as e:
            self.log_error(f"Failed to process data updates: {e}")
    
    async def _update_realtime_candle(self, symbol: str, data_point: MarketDataPoint):
        """Update real-time 1-minute candle data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            # Get current 1-minute candle timestamp
            current_minute = data_point.timestamp.replace(second=0, microsecond=0)
            
            # Update or create new candle
            df = self.timeframe_data["1min"][symbol]
            
            # Check if we need to create a new candle or update existing
            latest_candle = df.tail(1)
            if len(latest_candle) > 0:
                latest_timestamp = latest_candle.select(pl.col("timestamp")).item()
                
                if latest_timestamp == current_minute:
                    # Update existing candle
                    df = df.with_columns([
                        pl.when(pl.col("timestamp") == current_minute)
                        .then(data_point.high)
                        .otherwise(pl.col("high"))
                        .alias("high"),
                        
                        pl.when(pl.col("timestamp") == current_minute)
                        .then(pl.min_horizontal([pl.col("low"), data_point.low]))
                        .otherwise(pl.col("low"))
                        .alias("low"),
                        
                        pl.when(pl.col("timestamp") == current_minute)
                        .then(data_point.close)
                        .otherwise(pl.col("close"))
                        .alias("close"),
                        
                        pl.when(pl.col("timestamp") == current_minute)
                        .then(pl.col("volume") + data_point.volume)
                        .otherwise(pl.col("volume"))
                        .alias("volume")
                    ])
                else:
                    # Create new candle
                    new_candle = pl.DataFrame([{
                        'timestamp': current_minute,
                        'open': data_point.open,
                        'high': data_point.high,
                        'low': data_point.low,
                        'close': data_point.close,
                        'volume': data_point.volume
                    }])
                    df = pl.concat([df, new_candle])
            
            # Update stored data
            self.timeframe_data["1min"][symbol] = df
            
        except Exception as e:
            self.log_error(f"Failed to update realtime candle for {symbol}: {e}")
    
    def _on_websocket_connect(self, ws):
        """WebSocket connect callback"""
        self.log_info("WebSocket connected")
    
    def _on_websocket_data(self, ws, data):
        """WebSocket data callback"""
        try:
            # Parse WebSocket data and create market data point
            if isinstance(data, dict):
                symbol_token = data.get('tk')
                ltp = data.get('lp')
                
                if symbol_token and ltp:
                    # Find symbol by token
                    symbol = self.instrument_master.get_symbol_by_token(symbol_token)
                    if symbol:
                        # Create market data point
                        data_point = MarketDataPoint(
                            symbol=symbol,
                            timestamp=datetime.now(),
                            open=data.get('o', ltp),
                            high=data.get('h', ltp),
                            low=data.get('l', ltp),
                            close=ltp,
                            volume=data.get('v', 0),
                            ltp=ltp
                        )
                        
                        # Store real-time data
                        self.market_data[symbol] = data_point
                        
                        # Publish market data event
                        event = Event(
                            type="MARKET_DATA_UPDATE",
                            data={
                                "symbol": symbol,
                                "data_point": data_point,
                                "timestamp": datetime.now()
                            },
                            source=self.name
                        )
                        self.event_bus.publish(event)
            
        except Exception as e:
            self.log_error(f"WebSocket data processing error: {e}")
    
    def _on_websocket_error(self, ws, error):
        """WebSocket error callback"""
        self.log_error(f"WebSocket error: {error}")
    
    def _on_websocket_close(self, ws, code, reason):
        """WebSocket close callback"""
        self.log_info(f"WebSocket closed: {code} - {reason}")
    
    async def _handle_historical_data_request(self, event: Event):
        """Handle historical data request from other agents"""
        try:
            symbol = event.data.get('symbol')
            timeframe = event.data.get('timeframe', '1min')
            
            if symbol in self.timeframe_data.get(timeframe, {}):
                data = self.timeframe_data[timeframe][symbol]
                
                # Publish response
                response_event = Event(
                    type="HISTORICAL_DATA_RESPONSE",
                    data={
                        "symbol": symbol,
                        "timeframe": timeframe,
                        "data": data,
                        "request_id": event.data.get('request_id')
                    },
                    source=self.name
                )
                self.event_bus.publish(response_event)
            
        except Exception as e:
            self.log_error(f"Failed to handle historical data request: {e}")
    
    async def _handle_realtime_data_request(self, event: Event):
        """Handle real-time data request from other agents"""
        try:
            symbol = event.data.get('symbol')
            
            if symbol in self.market_data:
                data_point = self.market_data[symbol]
                
                # Publish response
                response_event = Event(
                    type="REALTIME_DATA_RESPONSE",
                    data={
                        "symbol": symbol,
                        "data_point": data_point,
                        "request_id": event.data.get('request_id')
                    },
                    source=self.name
                )
                self.event_bus.publish(response_event)
            
        except Exception as e:
            self.log_error(f"Failed to handle realtime data request: {e}")
    
    async def stop(self):
        """Stop the market data agent"""
        try:
            self.log_info("Stopping Modern Market Data Agent...")
            
            self.running = False
            
            # Disconnect WebSocket
            if self.websocket_manager:
                await self.websocket_manager.disconnect()
            
            self.log_info("Modern Market Data Agent stopped")
            
        except Exception as e:
            self.log_error(f"Error stopping agent: {e}")
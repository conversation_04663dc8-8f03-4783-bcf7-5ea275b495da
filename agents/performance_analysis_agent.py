#!/usr/bin/env python3
"""
Performance Analysis Agent - Comprehensive Post-Trade Performance Analytics

Features:
[METRICS] 1. Post-Trade Metrics Calculation
- ROI, Accuracy, Expectancy, Risk-Reward Ratios
- Drawdown & Duration analysis
- Sharpe Ratio, Profit Factor, Holding Time averages

🧠 2. Strategy-Wise and Regime-Wise Performance
- Strategy comparison and ranking
- RR combo performance analysis
- Regime-specific statistics (Bull/Bear/Sideways)
- Time-based analysis (morning vs closing hours)
- Symbol-Strategy mapping optimization

🧾 3. Execution Quality Analysis
- Signal-to-Fill time tracking
- Slippage analysis and monitoring
- Missed trade logging and analysis
- Fill ratio per order tracking

📤 4. Output Reports + Feedback
- CSV/Parquet export for ML training
- Daily PnL reports per strategy/symbol
- Equity curve tracking
- Feedback to other agents

[SIGNAL] 5. Integration with Angel One Broker
- Tradebook API integration
- Order status verification
- Funds & PnL API tracking
- Execution time logging
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
import numpy as np
from datetime import datetime, timedelta, time
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict, field
from collections import defaultdict, deque
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import polars_talib for technical indicators
try:
    import polars_talib as ta
    POLARS_TA_AVAILABLE = True
except ImportError:
    print("[WARN]  polars_talib not available. Install with: pip install polars-talib")
    POLARS_TA_AVAILABLE = False

# Import utilities
try:
    from utils.angel_api import AngelOneAPIClient, MarginRequirement, RMSLimits, PositionData, FundData
    from utils.config_loader import ConfigurationLoader
    UTILS_AVAILABLE = True
except ImportError:
    print("[WARN]  Utils not available. Ensure utils directory is in path")
    UTILS_AVAILABLE = False

# Enhanced Model Integration
try:
    from utils.enhanced_model_integration import EnhancedModelIntegration, get_enhanced_model_integration
    ENHANCED_MODELS_AVAILABLE = True
except ImportError:
    print("[WARN]  Enhanced Model Integration not found. Advanced ML performance analysis will be disabled.")
    EnhancedModelIntegration = None
    ENHANCED_MODELS_AVAILABLE = False

# Setup logging
logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA MODELS
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class TradeMetrics:
    """Individual trade performance metrics"""
    trade_id: str
    symbol: str
    strategy: str
    timeframe: str
    entry_time: datetime
    exit_time: Optional[datetime]
    entry_price: float
    exit_price: Optional[float]
    quantity: int
    side: str  # BUY/SELL
    pnl: Optional[float] = None
    pnl_percent: Optional[float] = None
    holding_time_minutes: Optional[float] = None
    slippage_entry: Optional[float] = None
    slippage_exit: Optional[float] = None
    commission: float = 0.0
    is_winner: Optional[bool] = None
    market_regime: Optional[str] = None
    signal_to_fill_ms: Optional[float] = None

@dataclass
class StrategyPerformance:
    """Strategy-level performance metrics"""
    strategy_name: str
    symbol: str
    timeframe: str
    regime: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    expectancy: float
    roi: float
    sharpe_ratio: float
    profit_factor: float
    max_drawdown: float
    max_drawdown_duration_days: float
    avg_holding_time_minutes: float
    total_pnl: float
    total_commission: float
    avg_slippage_percent: float
    start_date: datetime
    end_date: datetime
    last_updated: datetime

@dataclass
class ExecutionQuality:
    """Execution quality metrics"""
    symbol: str
    strategy: str
    avg_signal_to_fill_ms: float
    avg_slippage_percent: float
    fill_ratio: float  # Percentage of signals that got filled
    missed_trades: int
    partial_fills: int
    rejected_orders: int
    timeout_orders: int
    total_signals: int
    last_updated: datetime

@dataclass
class PerformanceAnalysisConfig:
    """Configuration for Performance Analysis Agent"""
    
    # Angel One API Configuration
    angel_api_config: Dict[str, Any]
    
    # Data Sources Configuration
    data_sources_config: Dict[str, Any]
    
    # Analysis Configuration
    analysis_config: Dict[str, Any]
    
    # Reporting Configuration
    reporting_config: Dict[str, Any]
    
    # Storage Configuration
    storage_config: Dict[str, Any]
    
    # Performance Configuration
    performance_config: Dict[str, Any]
    
    # Logging Configuration
    logging_config: Dict[str, Any]
    
    # Integration Configuration
    integration_config: Dict[str, Any]

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] CONFIGURATION LOADING
# ═══════════════════════════════════════════════════════════════════════════════

def load_performance_config(config_path: str = "config/performance_analysis_config.yaml") -> PerformanceAnalysisConfig:
    """Load configuration from YAML file"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config_data = yaml.safe_load(file)
        
        return PerformanceAnalysisConfig(
            angel_api_config=config_data.get('angel_one_api', {}),
            data_sources_config=config_data.get('data_sources', {}),
            analysis_config=config_data.get('analysis', {}),
            reporting_config=config_data.get('reporting', {}),
            storage_config=config_data.get('storage', {}),
            performance_config=config_data.get('performance', {}),
            logging_config=config_data.get('logging', {}),
            integration_config=config_data.get('integrations', {})
        )
    except Exception as e:
        logger.error(f"[ERROR] Failed to load config from {config_path}: {e}")
        # Return default configuration
        return PerformanceAnalysisConfig(
            angel_api_config={},
            data_sources_config={},
            analysis_config={},
            reporting_config={},
            storage_config={},
            performance_config={},
            logging_config={},
            integration_config={}
        )

def setup_performance_logging(config: PerformanceAnalysisConfig):
    """Setup logging configuration"""
    log_config = config.logging_config
    
    # Create logs directory
    log_dir = log_config.get('log_directory', 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # Setup file logging if enabled
    if log_config.get('file_logging', {}).get('enable', True):
        log_file = os.path.join(log_dir, log_config.get('file_logging', {}).get('filename', 'performance_analysis.log'))
        
        # Configure file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(getattr(logging, log_config.get('level', 'INFO')))
        
        # Configure formatter
        formatter = logging.Formatter(
            log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        file_handler.setFormatter(formatter)
        
        # Add handler to logger
        logger.addHandler(file_handler)
    
    # Set logger level
    logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))

# ═══════════════════════════════════════════════════════════════════════════════
# 🧮 PERFORMANCE ANALYSIS AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class PerformanceAnalysisAgent:
    """
    Performance Analysis Agent for comprehensive post-trade analytics
    
    Analyzes trading performance across multiple dimensions:
    - Individual trade metrics
    - Strategy-level performance
    - Execution quality analysis
    - Market regime performance
    - Time-based analysis
    """
    
    def __init__(self, config_path: str = "config/performance_analysis_config.yaml"):
        """Initialize Performance Analysis Agent"""
        
        # Load configuration
        self.config = load_performance_config(config_path)
        
        # Setup logging
        setup_performance_logging(self.config)
        
        # Initialize components
        self.angel_api = None
        self.enhanced_models = None
        
        # Data storage
        self.trade_data: List[TradeMetrics] = []
        self.strategy_performance: Dict[str, StrategyPerformance] = {}
        self.execution_quality: Dict[str, ExecutionQuality] = {}
        self.equity_curves: Dict[str, pl.DataFrame] = {}
        
        # Performance tracking
        self.performance_metrics = {
            'start_time': datetime.now(),
            'trades_analyzed': 0,
            'strategies_tracked': 0,
            'last_analysis_time': None,
            'processing_time_ms': 0.0
        }
        
        # State management
        self.is_running = False
        self.analysis_interval_minutes = self.config.analysis_config.get('analysis_interval_minutes', 15)
        
        logger.info("[INIT] Performance Analysis Agent initialized")

    async def setup(self):
        """Setup all components and integrations"""
        logger.info("[CONFIG] Setting up Performance Analysis Agent...")

        try:
            # Setup Angel One API integration
            await self._setup_angel_api()

            # Create storage directories
            self._create_storage_directories()

            # Initialize data structures
            await self._initialize_data_structures()

            # Setup integrations with other agents
            await self._setup_integrations()

            # Setup Enhanced Model Integration
            await self._setup_enhanced_models()

            logger.info("[SUCCESS] Performance Analysis Agent setup completed")

        except Exception as e:
            logger.error(f"[ERROR] Setup failed: {e}")
            raise

    async def _setup_angel_api(self):
        """Setup Angel One API integration"""
        api_config = self.config.angel_api_config

        if api_config.get('enabled', False) and UTILS_AVAILABLE:
            try:
                # Create a config dict for AngelOneAPIClient
                angel_config = {
                    'angel_api': {
                        'api_key': os.getenv('ANGEL_API_KEY'),
                        'client_code': os.getenv('ANGEL_CLIENT_CODE'),
                        'password': os.getenv('ANGEL_PASSWORD'),
                        'totp_secret': os.getenv('ANGEL_TOTP_SECRET')
                    }
                }
                self.angel_api = AngelOneAPIClient(angel_config)

                # Test connection
                if await self.angel_api.connect():
                    logger.info("[SUCCESS] Angel One API connected successfully")
                else:
                    logger.warning("[WARN]  Angel One API connection failed")

            except Exception as e:
                logger.error(f"[ERROR] Angel One API setup failed: {e}")
                self.angel_api = None
        else:
            logger.info("📴 Angel One API integration disabled")

    def _create_storage_directories(self):
        """Create necessary storage directories"""
        storage_config = self.config.storage_config

        directories = [
            storage_config.get('trade_data_path', 'data/performance/trades'),
            storage_config.get('strategy_performance_path', 'data/performance/strategies'),
            storage_config.get('execution_quality_path', 'data/performance/execution'),
            storage_config.get('reports_path', 'data/performance/reports'),
            storage_config.get('equity_curves_path', 'data/performance/equity_curves'),
            'logs'
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        logger.info("[FOLDER] Storage directories created")

    async def _initialize_data_structures(self):
        """Initialize data structures and load existing data"""
        try:
            # Load existing trade data if available
            await self._load_existing_trade_data()

            # Load existing strategy performance data
            await self._load_existing_strategy_performance()

            # Load existing execution quality data
            await self._load_existing_execution_quality()

            logger.info("[STATUS] Data structures initialized")

        except Exception as e:
            logger.error(f"[ERROR] Data structure initialization failed: {e}")

    async def _setup_integrations(self):
        """Setup integrations with other agents"""
        integration_config = self.config.integration_config

        # Signal Generation Agent integration
        if integration_config.get('signal_agent', {}).get('enabled', False):
            logger.info("🔗 Signal Generation Agent integration enabled")

        # Execution Agent integration
        if integration_config.get('execution_agent', {}).get('enabled', False):
            logger.info("🔗 Execution Agent integration enabled")

        # Risk Management Agent integration
        if integration_config.get('risk_agent', {}).get('enabled', False):
            logger.info("🔗 Risk Management Agent integration enabled")

        # AI Training Agent integration
        if integration_config.get('ai_training_agent', {}).get('enabled', False):
            logger.info("🔗 AI Training Agent integration enabled")

    async def _setup_enhanced_models(self):
        """Setup Enhanced Model Integration for performance prediction and analysis"""
        if not ENHANCED_MODELS_AVAILABLE:
            logger.warning("[WARN]  Enhanced Model Integration not available")
            return

        try:
            # Initialize enhanced model integration
            self.enhanced_models = get_enhanced_model_integration()
            
            # Initialize models
            success = await self.enhanced_models.initialize()
            
            if success:
                logger.info("[SUCCESS] Enhanced Model Integration setup completed for Performance Analysis")
                
                # Log model performance summary
                summary = self.enhanced_models.get_model_performance_summary()
                logger.info(f"[INFO] Performance Analysis Agent using {summary['model_count']} enhanced models")
                
                # Log specific models useful for performance analysis
                useful_models = []
                if 'sharpe_ratio_prediction' in summary['loaded_models']:
                    useful_models.append("Sharpe Ratio Prediction")
                if 'roi_prediction' in summary['loaded_models']:
                    useful_models.append("ROI Prediction")
                if 'drawdown_prediction' in summary['loaded_models']:
                    useful_models.append("Drawdown Prediction")
                if 'profitability_classification' in summary['loaded_models']:
                    useful_models.append("Profitability Classification")
                
                if useful_models:
                    logger.info(f"[INFO] Available models for performance analysis: {', '.join(useful_models)}")
            else:
                logger.error("[ERROR] Enhanced Model Integration initialization failed")
                self.enhanced_models = None

        except Exception as e:
            logger.error(f"[ERROR] Enhanced Model Integration setup failed: {e}")
            self.enhanced_models = None

    async def start(self):
        """Start the Performance Analysis Agent"""
        if self.is_running:
            logger.warning("[WARN]  Performance Analysis Agent is already running")
            return

        logger.info("[INIT] Starting Performance Analysis Agent...")
        self.is_running = True

        try:
            # Start main analysis loop
            analysis_task = asyncio.create_task(self._analysis_loop())

            # Start performance monitoring
            monitoring_task = asyncio.create_task(self._performance_monitoring_loop())

            # Wait for tasks to complete
            await asyncio.gather(analysis_task, monitoring_task)

        except Exception as e:
            logger.error(f"[ERROR] Error in Performance Analysis Agent: {e}")
        finally:
            self.is_running = False

    async def stop(self):
        """Stop the Performance Analysis Agent"""
        logger.info("[STOP] Stopping Performance Analysis Agent...")
        self.is_running = False

        # Save current state
        await self._save_all_data()

        # Cleanup connections
        if self.angel_api:
            await self.angel_api.disconnect()

        logger.info("[SUCCESS] Performance Analysis Agent stopped")

    async def _analysis_loop(self):
        """Main analysis loop"""
        while self.is_running:
            try:
                start_time = datetime.now()

                # Fetch new trade data
                await self._fetch_new_trade_data()

                # Calculate performance metrics
                await self._calculate_performance_metrics()

                # Update strategy performance
                await self._update_strategy_performance()

                # Update execution quality metrics
                await self._update_execution_quality()

                # Generate reports if scheduled
                await self._generate_scheduled_reports()

                # Update performance tracking
                processing_time = (datetime.now() - start_time).total_seconds() * 1000
                self.performance_metrics['processing_time_ms'] = processing_time
                self.performance_metrics['last_analysis_time'] = datetime.now()

                logger.info(f"[STATUS] Analysis cycle completed in {processing_time:.2f}ms")

                # Wait for next analysis interval
                await asyncio.sleep(self.analysis_interval_minutes * 60)

            except Exception as e:
                logger.error(f"[ERROR] Error in analysis loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    # ═══════════════════════════════════════════════════════════════════════════════
    # [STATUS] CORE PERFORMANCE CALCULATION METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _fetch_new_trade_data(self):
        """Fetch new trade data from various sources"""
        try:
            # Fetch from Angel One API if available
            if self.angel_api:
                await self._fetch_angel_one_trades()

            # Fetch from execution agent logs
            await self._fetch_execution_agent_trades()

            # Fetch from signal generation logs
            await self._fetch_signal_generation_data()

            logger.debug("📥 New trade data fetched")

        except Exception as e:
            logger.error(f"[ERROR] Error fetching trade data: {e}")

    async def _fetch_angel_one_trades(self):
        """Fetch trades from Angel One tradebook API"""
        try:
            if not self.angel_api:
                return

            # Get tradebook data
            tradebook = await self.angel_api.get_tradebook()

            if tradebook and 'data' in tradebook:
                for trade in tradebook['data']:
                    trade_metrics = self._convert_angel_trade_to_metrics(trade)
                    if trade_metrics:
                        self.trade_data.append(trade_metrics)

            logger.debug(f"[STATUS] Fetched {len(tradebook.get('data', []))} trades from Angel One")

        except Exception as e:
            logger.error(f"[ERROR] Error fetching Angel One trades: {e}")

    async def _fetch_execution_agent_trades(self):
        """Fetch trades from execution agent logs"""
        try:
            execution_logs_path = self.config.data_sources_config.get('execution_logs_path', 'logs/execution_agent.log')

            if not os.path.exists(execution_logs_path):
                return

            # Read and parse execution logs
            with open(execution_logs_path, 'r') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        if log_entry.get('event_type') == 'trade_executed':
                            trade_metrics = self._convert_execution_log_to_metrics(log_entry)
                            if trade_metrics:
                                self.trade_data.append(trade_metrics)
                    except json.JSONDecodeError:
                        continue

            logger.debug("[STATUS] Fetched trades from execution agent logs")

        except Exception as e:
            logger.error(f"[ERROR] Error fetching execution agent trades: {e}")

    async def _fetch_signal_generation_data(self):
        """Fetch signal data for execution quality analysis"""
        try:
            signals_path = self.config.data_sources_config.get('signals_path', 'data/signals')

            if not os.path.exists(signals_path):
                return

            # Read signal files
            for signal_file in Path(signals_path).glob('*.parquet'):
                try:
                    signals_df = pl.read_parquet(signal_file)
                    # Process signals for execution quality analysis
                    await self._process_signals_for_execution_quality(signals_df)
                except Exception as e:
                    logger.error(f"[ERROR] Error processing signal file {signal_file}: {e}")

            logger.debug("[STATUS] Fetched signal data for execution quality analysis")

        except Exception as e:
            logger.error(f"[ERROR] Error fetching signal generation data: {e}")

    def _convert_angel_trade_to_metrics(self, trade: Dict[str, Any]) -> Optional[TradeMetrics]:
        """Convert Angel One trade data to TradeMetrics"""
        try:
            return TradeMetrics(
                trade_id=trade.get('orderid', ''),
                symbol=trade.get('tradingsymbol', ''),
                strategy='unknown',  # Not available in Angel One data
                timeframe='unknown',  # Not available in Angel One data
                entry_time=datetime.strptime(trade.get('filltime', ''), '%Y-%m-%d %H:%M:%S'),
                exit_time=None,  # Will be updated when exit trade is found
                entry_price=float(trade.get('fillprice', 0)),
                exit_price=None,
                quantity=int(trade.get('fillsize', 0)),
                side=trade.get('transactiontype', ''),
                commission=float(trade.get('brokerage', 0))
            )
        except Exception as e:
            logger.error(f"[ERROR] Error converting Angel One trade: {e}")
            return None

    def _convert_execution_log_to_metrics(self, log_entry: Dict[str, Any]) -> Optional[TradeMetrics]:
        """Convert execution agent log entry to TradeMetrics"""
        try:
            trade_data = log_entry.get('trade_data', {})

            return TradeMetrics(
                trade_id=trade_data.get('trade_id', ''),
                symbol=trade_data.get('symbol', ''),
                strategy=trade_data.get('strategy', ''),
                timeframe=trade_data.get('timeframe', ''),
                entry_time=datetime.fromisoformat(trade_data.get('entry_time', '')),
                exit_time=datetime.fromisoformat(trade_data.get('exit_time', '')) if trade_data.get('exit_time') else None,
                entry_price=float(trade_data.get('entry_price', 0)),
                exit_price=float(trade_data.get('exit_price', 0)) if trade_data.get('exit_price') else None,
                quantity=int(trade_data.get('quantity', 0)),
                side=trade_data.get('side', ''),
                market_regime=trade_data.get('market_regime', ''),
                signal_to_fill_ms=float(trade_data.get('signal_to_fill_ms', 0))
            )
        except Exception as e:
            logger.error(f"[ERROR] Error converting execution log: {e}")
            return None

    async def _calculate_performance_metrics(self):
        """Calculate performance metrics for all trades"""
        try:
            if not self.trade_data:
                return

            # Convert to polars DataFrame for efficient processing
            trades_df = self._trades_to_dataframe()

            # Calculate individual trade metrics
            trades_df = self._calculate_individual_trade_metrics(trades_df)

            # Enhanced ML-based performance prediction
            await self._enhance_performance_analysis_with_ml(trades_df)

            # Update trade data with calculated metrics
            self._update_trade_data_from_dataframe(trades_df)

            # Update performance tracking
            self.performance_metrics['trades_analyzed'] = len(self.trade_data)

            logger.debug(f"[STATUS] Calculated metrics for {len(self.trade_data)} trades")

        except Exception as e:
            logger.error(f"[ERROR] Error calculating performance metrics: {e}")

    def _trades_to_dataframe(self) -> pl.DataFrame:
        """Convert trade data to polars DataFrame"""
        try:
            # Convert trade metrics to dictionaries
            trades_data = [asdict(trade) for trade in self.trade_data]

            # Create DataFrame
            df = pl.DataFrame(trades_data)

            return df

        except Exception as e:
            logger.error(f"[ERROR] Error converting trades to DataFrame: {e}")
            return pl.DataFrame()

    def _calculate_individual_trade_metrics(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate individual trade metrics using polars"""
        try:
            if df.is_empty():
                return df

            # Calculate PnL and other metrics
            df = df.with_columns([
                # Calculate PnL
                pl.when(pl.col('exit_price').is_not_null())
                .then(
                    pl.when(pl.col('side') == 'BUY')
                    .then((pl.col('exit_price') - pl.col('entry_price')) * pl.col('quantity'))
                    .otherwise((pl.col('entry_price') - pl.col('exit_price')) * pl.col('quantity'))
                )
                .otherwise(None)
                .alias('pnl'),

                # Calculate PnL percentage
                pl.when(pl.col('exit_price').is_not_null())
                .then(
                    pl.when(pl.col('side') == 'BUY')
                    .then(((pl.col('exit_price') - pl.col('entry_price')) / pl.col('entry_price')) * 100)
                    .otherwise(((pl.col('entry_price') - pl.col('exit_price')) / pl.col('entry_price')) * 100)
                )
                .otherwise(None)
                .alias('pnl_percent'),

                # Calculate holding time in minutes
                pl.when(pl.col('exit_time').is_not_null())
                .then((pl.col('exit_time') - pl.col('entry_time')).dt.total_minutes())
                .otherwise(None)
                .alias('holding_time_minutes'),

                # Determine if trade is winner
                pl.when(pl.col('pnl').is_not_null())
                .then(pl.col('pnl') > 0)
                .otherwise(None)
                .alias('is_winner')
            ])

            return df

        except Exception as e:
            logger.error(f"[ERROR] Error calculating individual trade metrics: {e}")
            return df

    def _update_trade_data_from_dataframe(self, df: pl.DataFrame):
        """Update trade data from calculated DataFrame"""
        try:
            if df.is_empty():
                return

            # Convert back to TradeMetrics objects
            updated_trades = []
            for row in df.iter_rows(named=True):
                trade = TradeMetrics(**row)
                updated_trades.append(trade)

            self.trade_data = updated_trades

        except Exception as e:
            logger.error(f"[ERROR] Error updating trade data from DataFrame: {e}")

    async def _enhance_performance_analysis_with_ml(self, trades_df: pl.DataFrame):
        """Enhance performance analysis using ML models for predictions and insights"""
        try:
            if not self.enhanced_models or trades_df.is_empty():
                return

            # Prepare strategy performance data for ML analysis
            strategy_data = self._prepare_performance_strategy_data(trades_df)
            
            if not strategy_data:
                return

            # Get predictions from enhanced models
            predictions = await self.enhanced_models.predict_all_tasks(strategy_data)
            
            if not predictions:
                logger.warning("[ENHANCED_ML] No predictions available for performance analysis")
                return

            # Analyze predictions and generate insights
            insights = self._analyze_ml_performance_predictions(predictions, trades_df)
            
            # Store insights for reporting
            self._store_ml_performance_insights(insights)
            
            logger.info(f"[ENHANCED_ML] Performance analysis enhanced with ML predictions")

        except Exception as e:
            logger.error(f"[ERROR] Enhanced ML performance analysis failed: {e}")

    def _prepare_performance_strategy_data(self, trades_df: pl.DataFrame) -> Dict[str, Any]:
        """Prepare strategy performance data for enhanced model predictions"""
        try:
            if trades_df.is_empty():
                return {}

            # Calculate aggregate performance metrics
            completed_trades = trades_df.filter(pl.col('pnl').is_not_null())
            
            if completed_trades.is_empty():
                return {}

            # Calculate basic performance metrics
            total_trades = len(completed_trades)
            winning_trades = len(completed_trades.filter(pl.col('pnl') > 0))
            losing_trades = total_trades - winning_trades
            
            total_pnl = completed_trades.select(pl.col('pnl').sum()).item()
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            # Calculate profit factor
            gross_profit = completed_trades.filter(pl.col('pnl') > 0).select(pl.col('pnl').sum()).item() or 0
            gross_loss = abs(completed_trades.filter(pl.col('pnl') < 0).select(pl.col('pnl').sum()).item() or 0)
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 1.0
            
            # Calculate average returns
            avg_win = completed_trades.filter(pl.col('pnl') > 0).select(pl.col('pnl').mean()).item() or 0
            avg_loss = completed_trades.filter(pl.col('pnl') < 0).select(pl.col('pnl').mean()).item() or 0
            
            # Calculate ROI
            initial_capital = 100000  # Assume initial capital
            roi = (total_pnl / initial_capital) * 100
            
            # Calculate Sharpe ratio approximation
            returns = completed_trades.select(pl.col('pnl_percent')).to_series().to_list()
            if len(returns) > 1:
                import numpy as np
                returns_array = np.array([r for r in returns if r is not None])
                if len(returns_array) > 0:
                    mean_return = np.mean(returns_array)
                    std_return = np.std(returns_array)
                    sharpe_ratio = (mean_return / std_return) if std_return > 0 else 0
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0

            # Calculate drawdown approximation
            cumulative_pnl = 0
            peak = 0
            max_drawdown = 0
            
            for pnl in completed_trades.select(pl.col('pnl')).to_series().to_list():
                if pnl is not None:
                    cumulative_pnl += pnl
                    if cumulative_pnl > peak:
                        peak = cumulative_pnl
                    drawdown = ((peak - cumulative_pnl) / peak) * 100 if peak > 0 else 0
                    max_drawdown = max(max_drawdown, drawdown)

            # Calculate expectancy
            expectancy = (win_rate / 100 * avg_win) + ((100 - win_rate) / 100 * avg_loss)

            strategy_data = {
                # Sharpe ratio metrics
                'avg_sharpe_ratio': sharpe_ratio,
                'min_sharpe_ratio': sharpe_ratio - 0.5,
                'max_sharpe_ratio': sharpe_ratio + 0.5,
                'std_sharpe_ratio': 0.3,
                
                # ROI metrics
                'avg_roi': roi,
                'min_roi': roi - 5,
                'max_roi': roi + 5,
                'std_roi': 3.0,
                
                # Profit factor metrics
                'avg_profit_factor': profit_factor,
                'min_profit_factor': profit_factor - 0.2,
                'max_profit_factor': profit_factor + 0.2,
                'std_profit_factor': 0.1,
                
                # Drawdown metrics
                'avg_max_drawdown': -max_drawdown,
                'min_max_drawdown': -max_drawdown - 3,
                'max_max_drawdown': -max_drawdown + 3,
                'std_max_drawdown': 2.0,
                
                # Expectancy metrics
                'avg_expectancy': expectancy,
                'min_expectancy': expectancy - abs(expectancy * 0.3),
                'max_expectancy': expectancy + abs(expectancy * 0.3),
                'std_expectancy': abs(expectancy * 0.1),
                
                # Accuracy metrics
                'avg_accuracy': win_rate,
                'min_accuracy': max(0, win_rate - 10),
                'max_accuracy': min(100, win_rate + 10),
                'std_accuracy': 5.0,
                
                # Trade metrics
                'avg_total_trades': total_trades,
                'std_total_trades': max(1, total_trades * 0.2),
                'avg_winning_trades': winning_trades,
                'std_winning_trades': max(1, winning_trades * 0.2),
                
                # PnL metrics
                'avg_total_pnl': total_pnl,
                'min_total_pnl': total_pnl - abs(total_pnl * 0.2),
                'max_total_pnl': total_pnl + abs(total_pnl * 0.2),
                'std_total_pnl': abs(total_pnl * 0.1),
                
                # Consistency metrics
                'consistency_score': min(1.0, win_rate / 100),
                'sharpe_consistency': min(1.0, max(0.1, (sharpe_ratio + 2) / 4)),
                'roi_drawdown_ratio': abs(roi / max_drawdown) if max_drawdown > 0 else 1.0,
                
                # Other metrics
                'trades_per_period': total_trades / max(1, len(set(completed_trades.select(pl.col('entry_time').dt.date()).to_series().to_list()))),
                'walk_forward_steps': 5
            }
            
            return strategy_data
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to prepare performance strategy data: {e}")
            return {}

    def _analyze_ml_performance_predictions(self, predictions: Dict[str, Any], trades_df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze ML predictions and generate performance insights"""
        try:
            insights = {
                'timestamp': datetime.now(),
                'total_trades_analyzed': len(trades_df),
                'predictions': {},
                'recommendations': [],
                'risk_alerts': [],
                'performance_forecast': {}
            }

            # Analyze Sharpe ratio prediction
            sharpe_pred = predictions.get('sharpe_ratio_prediction')
            if sharpe_pred:
                insights['predictions']['sharpe_ratio'] = {
                    'predicted_value': sharpe_pred.prediction,
                    'confidence': sharpe_pred.confidence,
                    'interpretation': self._interpret_sharpe_prediction(sharpe_pred.prediction)
                }
                
                if sharpe_pred.prediction < 0.5 and sharpe_pred.confidence > 0.7:
                    insights['risk_alerts'].append(f"Low Sharpe ratio predicted: {sharpe_pred.prediction:.3f}")
                elif sharpe_pred.prediction > 1.5 and sharpe_pred.confidence > 0.7:
                    insights['recommendations'].append(f"Excellent risk-adjusted returns predicted: {sharpe_pred.prediction:.3f}")

            # Analyze ROI prediction
            roi_pred = predictions.get('roi_prediction')
            if roi_pred:
                insights['predictions']['roi'] = {
                    'predicted_value': roi_pred.prediction,
                    'confidence': roi_pred.confidence,
                    'interpretation': self._interpret_roi_prediction(roi_pred.prediction)
                }
                
                if roi_pred.prediction < 0 and roi_pred.confidence > 0.6:
                    insights['risk_alerts'].append(f"Negative ROI predicted: {roi_pred.prediction:.2f}%")
                elif roi_pred.prediction > 15 and roi_pred.confidence > 0.6:
                    insights['recommendations'].append(f"High ROI potential predicted: {roi_pred.prediction:.2f}%")

            # Analyze drawdown prediction
            drawdown_pred = predictions.get('drawdown_prediction')
            if drawdown_pred:
                insights['predictions']['drawdown'] = {
                    'predicted_value': drawdown_pred.prediction,
                    'confidence': drawdown_pred.confidence,
                    'interpretation': self._interpret_drawdown_prediction(drawdown_pred.prediction)
                }
                
                if drawdown_pred.prediction < -20 and drawdown_pred.confidence > 0.6:
                    insights['risk_alerts'].append(f"High drawdown risk predicted: {drawdown_pred.prediction:.2f}%")

            # Analyze profitability classification
            prof_pred = predictions.get('profitability_classification')
            if prof_pred:
                insights['predictions']['profitability'] = {
                    'predicted_class': prof_pred.prediction,
                    'confidence': prof_pred.confidence,
                    'interpretation': "Profitable" if prof_pred.prediction == 1 else "Not Profitable"
                }
                
                if prof_pred.prediction == 0 and prof_pred.confidence > 0.7:
                    insights['risk_alerts'].append("Strategy predicted to be unprofitable with high confidence")

            # Generate performance forecast
            insights['performance_forecast'] = self._generate_performance_forecast(predictions)

            return insights

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze ML performance predictions: {e}")
            return {}

    def _interpret_sharpe_prediction(self, sharpe_value: float) -> str:
        """Interpret Sharpe ratio prediction"""
        if sharpe_value > 2.0:
            return "Excellent risk-adjusted returns"
        elif sharpe_value > 1.0:
            return "Good risk-adjusted returns"
        elif sharpe_value > 0.5:
            return "Acceptable risk-adjusted returns"
        elif sharpe_value > 0.0:
            return "Poor risk-adjusted returns"
        else:
            return "Negative risk-adjusted returns"

    def _interpret_roi_prediction(self, roi_value: float) -> str:
        """Interpret ROI prediction"""
        if roi_value > 20:
            return "Excellent returns expected"
        elif roi_value > 10:
            return "Good returns expected"
        elif roi_value > 5:
            return "Moderate returns expected"
        elif roi_value > 0:
            return "Low returns expected"
        else:
            return "Losses expected"

    def _interpret_drawdown_prediction(self, drawdown_value: float) -> str:
        """Interpret drawdown prediction"""
        abs_drawdown = abs(drawdown_value)
        if abs_drawdown < 5:
            return "Low drawdown risk"
        elif abs_drawdown < 10:
            return "Moderate drawdown risk"
        elif abs_drawdown < 20:
            return "High drawdown risk"
        else:
            return "Very high drawdown risk"

    def _generate_performance_forecast(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Generate performance forecast based on ML predictions"""
        try:
            forecast = {
                'overall_outlook': 'neutral',
                'confidence_level': 'medium',
                'key_metrics': {},
                'recommendations': []
            }

            # Collect prediction values
            sharpe_pred = predictions.get('sharpe_ratio_prediction')
            roi_pred = predictions.get('roi_prediction')
            drawdown_pred = predictions.get('drawdown_prediction')
            prof_pred = predictions.get('profitability_classification')

            # Calculate overall outlook
            positive_indicators = 0
            negative_indicators = 0
            total_confidence = 0
            confidence_count = 0

            if sharpe_pred:
                if sharpe_pred.prediction > 1.0:
                    positive_indicators += 1
                elif sharpe_pred.prediction < 0.5:
                    negative_indicators += 1
                total_confidence += sharpe_pred.confidence
                confidence_count += 1

            if roi_pred:
                if roi_pred.prediction > 5.0:
                    positive_indicators += 1
                elif roi_pred.prediction < 0:
                    negative_indicators += 1
                total_confidence += roi_pred.confidence
                confidence_count += 1

            if drawdown_pred:
                if abs(drawdown_pred.prediction) < 10:
                    positive_indicators += 1
                elif abs(drawdown_pred.prediction) > 20:
                    negative_indicators += 1
                total_confidence += drawdown_pred.confidence
                confidence_count += 1

            if prof_pred:
                if prof_pred.prediction == 1:
                    positive_indicators += 1
                else:
                    negative_indicators += 1
                total_confidence += prof_pred.confidence
                confidence_count += 1

            # Determine overall outlook
            if positive_indicators > negative_indicators:
                forecast['overall_outlook'] = 'positive'
            elif negative_indicators > positive_indicators:
                forecast['overall_outlook'] = 'negative'
            else:
                forecast['overall_outlook'] = 'neutral'

            # Determine confidence level
            avg_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.5
            if avg_confidence > 0.8:
                forecast['confidence_level'] = 'high'
            elif avg_confidence > 0.6:
                forecast['confidence_level'] = 'medium'
            else:
                forecast['confidence_level'] = 'low'

            # Add key metrics to forecast
            if sharpe_pred:
                forecast['key_metrics']['expected_sharpe_ratio'] = sharpe_pred.prediction
            if roi_pred:
                forecast['key_metrics']['expected_roi'] = roi_pred.prediction
            if drawdown_pred:
                forecast['key_metrics']['expected_max_drawdown'] = drawdown_pred.prediction

            return forecast

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate performance forecast: {e}")
            return {}

    def _store_ml_performance_insights(self, insights: Dict[str, Any]):
        """Store ML performance insights for reporting"""
        try:
            # Store insights in instance variable for access by other methods
            if not hasattr(self, 'ml_insights'):
                self.ml_insights = []
            
            self.ml_insights.append(insights)
            
            # Keep only last 100 insights to manage memory
            if len(self.ml_insights) > 100:
                self.ml_insights = self.ml_insights[-100:]
            
            # Log key insights
            if insights.get('risk_alerts'):
                for alert in insights['risk_alerts']:
                    logger.warning(f"[ML_RISK_ALERT] {alert}")
            
            if insights.get('recommendations'):
                for rec in insights['recommendations']:
                    logger.info(f"[ML_RECOMMENDATION] {rec}")
            
            forecast = insights.get('performance_forecast', {})
            if forecast:
                logger.info(f"[ML_FORECAST] Overall outlook: {forecast.get('overall_outlook', 'unknown')} "
                          f"(confidence: {forecast.get('confidence_level', 'unknown')})")

        except Exception as e:
            logger.error(f"[ERROR] Failed to store ML performance insights: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [METRICS] STRATEGY PERFORMANCE ANALYSIS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _update_strategy_performance(self):
        """Update strategy-level performance metrics"""
        try:
            if not self.trade_data:
                return

            # Group trades by strategy, symbol, timeframe, and regime
            strategy_groups = self._group_trades_by_strategy()

            # Calculate performance for each group
            for group_key, trades in strategy_groups.items():
                strategy_perf = self._calculate_strategy_performance(group_key, trades)
                if strategy_perf:
                    self.strategy_performance[group_key] = strategy_perf

            # Update performance tracking
            self.performance_metrics['strategies_tracked'] = len(self.strategy_performance)

            logger.debug(f"[STATUS] Updated performance for {len(self.strategy_performance)} strategy combinations")

        except Exception as e:
            logger.error(f"[ERROR] Error updating strategy performance: {e}")

    def _group_trades_by_strategy(self) -> Dict[str, List[TradeMetrics]]:
        """Group trades by strategy, symbol, timeframe, and regime"""
        groups = defaultdict(list)

        for trade in self.trade_data:
            # Only include completed trades
            if trade.exit_time is None or trade.pnl is None:
                continue

            # Create group key
            group_key = f"{trade.strategy}_{trade.symbol}_{trade.timeframe}_{trade.market_regime or 'unknown'}"
            groups[group_key].append(trade)

        return dict(groups)

    def _calculate_strategy_performance(self, group_key: str, trades: List[TradeMetrics]) -> Optional[StrategyPerformance]:
        """Calculate performance metrics for a strategy group"""
        try:
            if not trades:
                return None

            # Parse group key
            parts = group_key.split('_')
            strategy_name = parts[0]
            symbol = parts[1]
            timeframe = parts[2]
            regime = parts[3] if len(parts) > 3 else 'unknown'

            # Convert to DataFrame for efficient calculations
            trades_data = [asdict(trade) for trade in trades]
            df = pl.DataFrame(trades_data)

            # Calculate basic metrics
            total_trades = len(trades)
            winning_trades = df.filter(pl.col('is_winner') == True).height
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # Calculate PnL metrics
            total_pnl = df.select(pl.col('pnl').sum()).item()
            total_commission = df.select(pl.col('commission').sum()).item()

            # Calculate win/loss averages
            winners_df = df.filter(pl.col('is_winner') == True)
            losers_df = df.filter(pl.col('is_winner') == False)

            avg_win = winners_df.select(pl.col('pnl').mean()).item() if winning_trades > 0 else 0
            avg_loss = abs(losers_df.select(pl.col('pnl').mean()).item()) if losing_trades > 0 else 0

            # Calculate expectancy
            expectancy = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)

            # Calculate profit factor
            gross_profit = winners_df.select(pl.col('pnl').sum()).item() if winning_trades > 0 else 0
            gross_loss = abs(losers_df.select(pl.col('pnl').sum()).item()) if losing_trades > 0 else 0
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

            # Calculate ROI (assuming initial capital)
            initial_capital = self.config.analysis_config.get('initial_capital', 100000)
            roi = (total_pnl / initial_capital) * 100

            # Calculate Sharpe ratio
            sharpe_ratio = self._calculate_sharpe_ratio(df)

            # Calculate drawdown
            max_drawdown, max_dd_duration = self._calculate_drawdown(df)

            # Calculate average holding time
            avg_holding_time = df.select(pl.col('holding_time_minutes').mean()).item() or 0

            # Calculate average slippage
            avg_slippage = self._calculate_average_slippage(df)

            # Get date range
            start_date = df.select(pl.col('entry_time').min()).item()
            end_date = df.select(pl.col('entry_time').max()).item()

            return StrategyPerformance(
                strategy_name=strategy_name,
                symbol=symbol,
                timeframe=timeframe,
                regime=regime,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                expectancy=expectancy,
                roi=roi,
                sharpe_ratio=sharpe_ratio,
                profit_factor=profit_factor,
                max_drawdown=max_drawdown,
                max_drawdown_duration_days=max_dd_duration,
                avg_holding_time_minutes=avg_holding_time,
                total_pnl=total_pnl,
                total_commission=total_commission,
                avg_slippage_percent=avg_slippage,
                start_date=start_date,
                end_date=end_date,
                last_updated=datetime.now()
            )

        except Exception as e:
            logger.error(f"[ERROR] Error calculating strategy performance for {group_key}: {e}")
            return None

    def _calculate_sharpe_ratio(self, df: pl.DataFrame) -> float:
        """Calculate Sharpe ratio for the strategy"""
        try:
            if df.height < 2:
                return 0.0

            # Calculate daily returns (assuming trades are daily)
            returns = df.select(pl.col('pnl_percent')).to_series().to_list()

            if not returns:
                return 0.0

            # Calculate mean and std of returns
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            std_return = variance ** 0.5

            # Risk-free rate (assuming 6% annual, ~0.02% daily)
            risk_free_rate = 0.02

            # Calculate Sharpe ratio
            if std_return > 0:
                sharpe_ratio = (mean_return - risk_free_rate) / std_return
            else:
                sharpe_ratio = 0.0

            return sharpe_ratio

        except Exception as e:
            logger.error(f"[ERROR] Error calculating Sharpe ratio: {e}")
            return 0.0

    def _calculate_drawdown(self, df: pl.DataFrame) -> Tuple[float, float]:
        """Calculate maximum drawdown and duration"""
        try:
            if df.height < 2:
                return 0.0, 0.0

            # Sort by entry time
            df_sorted = df.sort('entry_time')

            # Calculate cumulative PnL
            df_sorted = df_sorted.with_columns([
                pl.col('pnl').cumsum().alias('cumulative_pnl')
            ])

            # Calculate running maximum
            df_sorted = df_sorted.with_columns([
                pl.col('cumulative_pnl').cummax().alias('running_max')
            ])

            # Calculate drawdown
            df_sorted = df_sorted.with_columns([
                (pl.col('cumulative_pnl') - pl.col('running_max')).alias('drawdown')
            ])

            # Find maximum drawdown
            max_drawdown = abs(df_sorted.select(pl.col('drawdown').min()).item() or 0)

            # Calculate drawdown duration (simplified)
            max_dd_duration = 0.0  # TODO: Implement proper drawdown duration calculation

            return max_drawdown, max_dd_duration

        except Exception as e:
            logger.error(f"[ERROR] Error calculating drawdown: {e}")
            return 0.0, 0.0

    def _calculate_average_slippage(self, df: pl.DataFrame) -> float:
        """Calculate average slippage percentage"""
        try:
            # Calculate average of entry and exit slippage
            slippage_cols = ['slippage_entry', 'slippage_exit']
            available_cols = [col for col in slippage_cols if col in df.columns]

            if not available_cols:
                return 0.0

            # Calculate mean slippage
            slippage_values = []
            for col in available_cols:
                values = df.select(pl.col(col)).filter(pl.col(col).is_not_null()).to_series().to_list()
                slippage_values.extend(values)

            if slippage_values:
                return sum(slippage_values) / len(slippage_values)
            else:
                return 0.0

        except Exception as e:
            logger.error(f"[ERROR] Error calculating average slippage: {e}")
            return 0.0

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧾 EXECUTION QUALITY ANALYSIS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _update_execution_quality(self):
        """Update execution quality metrics"""
        try:
            # Group by symbol and strategy for execution quality analysis
            execution_groups = self._group_trades_for_execution_quality()

            # Calculate execution quality for each group
            for group_key, data in execution_groups.items():
                exec_quality = self._calculate_execution_quality(group_key, data)
                if exec_quality:
                    self.execution_quality[group_key] = exec_quality

            logger.debug(f"[STATUS] Updated execution quality for {len(self.execution_quality)} groups")

        except Exception as e:
            logger.error(f"[ERROR] Error updating execution quality: {e}")

    def _group_trades_for_execution_quality(self) -> Dict[str, Dict[str, Any]]:
        """Group trades and signals for execution quality analysis"""
        groups = defaultdict(lambda: {'trades': [], 'signals': [], 'missed_signals': []})

        # Group trades
        for trade in self.trade_data:
            group_key = f"{trade.symbol}_{trade.strategy}"
            groups[group_key]['trades'].append(trade)

        # TODO: Add signal data grouping when signal integration is implemented

        return dict(groups)

    def _calculate_execution_quality(self, group_key: str, data: Dict[str, Any]) -> Optional[ExecutionQuality]:
        """Calculate execution quality metrics for a group"""
        try:
            trades = data.get('trades', [])
            signals = data.get('signals', [])
            missed_signals = data.get('missed_signals', [])

            if not trades and not signals:
                return None

            # Parse group key
            parts = group_key.split('_')
            symbol = parts[0]
            strategy = parts[1] if len(parts) > 1 else 'unknown'

            # Calculate signal-to-fill time
            signal_to_fill_times = [trade.signal_to_fill_ms for trade in trades if trade.signal_to_fill_ms is not None]
            avg_signal_to_fill_ms = sum(signal_to_fill_times) / len(signal_to_fill_times) if signal_to_fill_times else 0

            # Calculate slippage
            slippages = []
            for trade in trades:
                if trade.slippage_entry is not None:
                    slippages.append(trade.slippage_entry)
                if trade.slippage_exit is not None:
                    slippages.append(trade.slippage_exit)
            avg_slippage_percent = sum(slippages) / len(slippages) if slippages else 0

            # Calculate fill ratio
            total_signals = len(signals) + len(trades) + len(missed_signals)
            filled_signals = len(trades)
            fill_ratio = (filled_signals / total_signals * 100) if total_signals > 0 else 0

            # Count different types of execution issues
            missed_trades = len(missed_signals)
            partial_fills = 0  # TODO: Implement partial fill detection
            rejected_orders = 0  # TODO: Implement rejected order detection
            timeout_orders = 0  # TODO: Implement timeout detection

            return ExecutionQuality(
                symbol=symbol,
                strategy=strategy,
                avg_signal_to_fill_ms=avg_signal_to_fill_ms,
                avg_slippage_percent=avg_slippage_percent,
                fill_ratio=fill_ratio,
                missed_trades=missed_trades,
                partial_fills=partial_fills,
                rejected_orders=rejected_orders,
                timeout_orders=timeout_orders,
                total_signals=total_signals,
                last_updated=datetime.now()
            )

        except Exception as e:
            logger.error(f"[ERROR] Error calculating execution quality for {group_key}: {e}")
            return None

    async def _process_signals_for_execution_quality(self, signals_df: pl.DataFrame):
        """Process signals for execution quality analysis"""
        try:
            # TODO: Implement signal processing for execution quality
            # This would involve matching signals with actual trades
            # and identifying missed opportunities
            pass

        except Exception as e:
            logger.error(f"[ERROR] Error processing signals for execution quality: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # 💾 DATA PERSISTENCE METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _load_existing_trade_data(self):
        """Load existing trade data from storage"""
        try:
            trade_data_path = self.config.storage_config.get('trade_data_path', 'data/performance/trades')
            trade_file = os.path.join(trade_data_path, 'trades.parquet')

            if os.path.exists(trade_file):
                df = pl.read_parquet(trade_file)

                # Convert to TradeMetrics objects
                for row in df.iter_rows(named=True):
                    trade = TradeMetrics(**row)
                    self.trade_data.append(trade)

                logger.info(f"📥 Loaded {len(self.trade_data)} existing trades")

        except Exception as e:
            logger.error(f"[ERROR] Error loading existing trade data: {e}")

    async def _load_existing_strategy_performance(self):
        """Load existing strategy performance data"""
        try:
            strategy_path = self.config.storage_config.get('strategy_performance_path', 'data/performance/strategies')
            strategy_file = os.path.join(strategy_path, 'strategy_performance.parquet')

            if os.path.exists(strategy_file):
                df = pl.read_parquet(strategy_file)

                # Convert to StrategyPerformance objects
                for row in df.iter_rows(named=True):
                    strategy_perf = StrategyPerformance(**row)
                    group_key = f"{strategy_perf.strategy_name}_{strategy_perf.symbol}_{strategy_perf.timeframe}_{strategy_perf.regime}"
                    self.strategy_performance[group_key] = strategy_perf

                logger.info(f"📥 Loaded {len(self.strategy_performance)} strategy performance records")

        except Exception as e:
            logger.error(f"[ERROR] Error loading existing strategy performance: {e}")

    async def _load_existing_execution_quality(self):
        """Load existing execution quality data"""
        try:
            exec_quality_path = self.config.storage_config.get('execution_quality_path', 'data/performance/execution')
            exec_file = os.path.join(exec_quality_path, 'execution_quality.parquet')

            if os.path.exists(exec_file):
                df = pl.read_parquet(exec_file)

                # Convert to ExecutionQuality objects
                for row in df.iter_rows(named=True):
                    exec_quality = ExecutionQuality(**row)
                    group_key = f"{exec_quality.symbol}_{exec_quality.strategy}"
                    self.execution_quality[group_key] = exec_quality

                logger.info(f"📥 Loaded {len(self.execution_quality)} execution quality records")

        except Exception as e:
            logger.error(f"[ERROR] Error loading existing execution quality: {e}")

    async def _save_all_data(self):
        """Save all performance data to storage"""
        try:
            # Save trade data
            await self._save_trade_data()

            # Save strategy performance
            await self._save_strategy_performance()

            # Save execution quality
            await self._save_execution_quality()

            logger.info("💾 All performance data saved")

        except Exception as e:
            logger.error(f"[ERROR] Error saving performance data: {e}")

    async def _save_trade_data(self):
        """Save trade data to parquet file"""
        try:
            if not self.trade_data:
                return

            trade_data_path = self.config.storage_config.get('trade_data_path', 'data/performance/trades')
            trade_file = os.path.join(trade_data_path, 'trades.parquet')

            # Convert to DataFrame and save
            trades_data = [asdict(trade) for trade in self.trade_data]
            df = pl.DataFrame(trades_data)
            df.write_parquet(trade_file, compression='zstd')

            logger.debug(f"💾 Saved {len(self.trade_data)} trades to {trade_file}")

        except Exception as e:
            logger.error(f"[ERROR] Error saving trade data: {e}")

    async def _save_strategy_performance(self):
        """Save strategy performance data to parquet file"""
        try:
            if not self.strategy_performance:
                return

            strategy_path = self.config.storage_config.get('strategy_performance_path', 'data/performance/strategies')
            strategy_file = os.path.join(strategy_path, 'strategy_performance.parquet')

            # Convert to DataFrame and save
            strategy_data = [asdict(perf) for perf in self.strategy_performance.values()]
            df = pl.DataFrame(strategy_data)
            df.write_parquet(strategy_file, compression='zstd')

            logger.debug(f"💾 Saved {len(self.strategy_performance)} strategy performance records")

        except Exception as e:
            logger.error(f"[ERROR] Error saving strategy performance: {e}")

    async def _save_execution_quality(self):
        """Save execution quality data to parquet file"""
        try:
            if not self.execution_quality:
                return

            exec_quality_path = self.config.storage_config.get('execution_quality_path', 'data/performance/execution')
            exec_file = os.path.join(exec_quality_path, 'execution_quality.parquet')

            # Convert to DataFrame and save
            exec_data = [asdict(quality) for quality in self.execution_quality.values()]
            df = pl.DataFrame(exec_data)
            df.write_parquet(exec_file, compression='zstd')

            logger.debug(f"💾 Saved {len(self.execution_quality)} execution quality records")

        except Exception as e:
            logger.error(f"[ERROR] Error saving execution quality: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📤 REPORTING AND OUTPUT METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _generate_scheduled_reports(self):
        """Generate scheduled reports based on configuration"""
        try:
            reporting_config = self.config.reporting_config

            # Check if daily report is due
            if self._is_daily_report_due():
                await self._generate_daily_report()

            # Check if weekly report is due
            if self._is_weekly_report_due():
                await self._generate_weekly_report()

            # Check if equity curve update is due
            if self._is_equity_curve_update_due():
                await self._update_equity_curves()

        except Exception as e:
            logger.error(f"[ERROR] Error generating scheduled reports: {e}")

    def _is_daily_report_due(self) -> bool:
        """Check if daily report generation is due"""
        # TODO: Implement proper scheduling logic
        return True  # For now, always generate

    def _is_weekly_report_due(self) -> bool:
        """Check if weekly report generation is due"""
        # TODO: Implement proper scheduling logic
        return False

    def _is_equity_curve_update_due(self) -> bool:
        """Check if equity curve update is due"""
        # TODO: Implement proper scheduling logic
        return True  # For now, always update

    async def _generate_daily_report(self):
        """Generate daily performance report"""
        try:
            report_data = {
                'date': datetime.now().date().isoformat(),
                'summary': self._generate_summary_metrics(),
                'strategy_performance': self._generate_strategy_summary(),
                'execution_quality': self._generate_execution_summary(),
                'top_performers': self._get_top_performing_strategies(),
                'worst_performers': self._get_worst_performing_strategies()
            }

            # Save report
            reports_path = self.config.storage_config.get('reports_path', 'data/performance/reports')
            report_file = os.path.join(reports_path, f"daily_report_{datetime.now().strftime('%Y%m%d')}.json")

            with open(report_file, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)

            logger.info(f"[STATUS] Daily report generated: {report_file}")

        except Exception as e:
            logger.error(f"[ERROR] Error generating daily report: {e}")

    async def _generate_weekly_report(self):
        """Generate weekly performance report"""
        try:
            # TODO: Implement weekly report generation
            logger.info("[STATUS] Weekly report generation not yet implemented")

        except Exception as e:
            logger.error(f"[ERROR] Error generating weekly report: {e}")

    def _generate_summary_metrics(self) -> Dict[str, Any]:
        """Generate summary performance metrics"""
        try:
            completed_trades = [trade for trade in self.trade_data if trade.exit_time is not None]

            if not completed_trades:
                return {}

            total_pnl = sum(trade.pnl for trade in completed_trades if trade.pnl is not None)
            total_trades = len(completed_trades)
            winning_trades = len([trade for trade in completed_trades if trade.is_winner])

            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': total_trades - winning_trades,
                'win_rate': winning_trades / total_trades if total_trades > 0 else 0,
                'total_pnl': total_pnl,
                'avg_pnl_per_trade': total_pnl / total_trades if total_trades > 0 else 0,
                'strategies_tracked': len(self.strategy_performance),
                'symbols_traded': len(set(trade.symbol for trade in completed_trades))
            }

        except Exception as e:
            logger.error(f"[ERROR] Error generating summary metrics: {e}")
            return {}

    def _generate_strategy_summary(self) -> List[Dict[str, Any]]:
        """Generate strategy performance summary"""
        try:
            strategy_summary = []

            for strategy_perf in self.strategy_performance.values():
                strategy_summary.append({
                    'strategy': strategy_perf.strategy_name,
                    'symbol': strategy_perf.symbol,
                    'timeframe': strategy_perf.timeframe,
                    'regime': strategy_perf.regime,
                    'total_trades': strategy_perf.total_trades,
                    'win_rate': strategy_perf.win_rate,
                    'roi': strategy_perf.roi,
                    'sharpe_ratio': strategy_perf.sharpe_ratio,
                    'profit_factor': strategy_perf.profit_factor,
                    'max_drawdown': strategy_perf.max_drawdown
                })

            # Sort by ROI descending
            strategy_summary.sort(key=lambda x: x['roi'], reverse=True)

            return strategy_summary

        except Exception as e:
            logger.error(f"[ERROR] Error generating strategy summary: {e}")
            return []

    def _generate_execution_summary(self) -> List[Dict[str, Any]]:
        """Generate execution quality summary"""
        try:
            execution_summary = []

            for exec_quality in self.execution_quality.values():
                execution_summary.append({
                    'symbol': exec_quality.symbol,
                    'strategy': exec_quality.strategy,
                    'avg_signal_to_fill_ms': exec_quality.avg_signal_to_fill_ms,
                    'avg_slippage_percent': exec_quality.avg_slippage_percent,
                    'fill_ratio': exec_quality.fill_ratio,
                    'missed_trades': exec_quality.missed_trades,
                    'total_signals': exec_quality.total_signals
                })

            return execution_summary

        except Exception as e:
            logger.error(f"[ERROR] Error generating execution summary: {e}")
            return []

    def _get_top_performing_strategies(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing strategies by ROI"""
        try:
            strategies = list(self.strategy_performance.values())
            strategies.sort(key=lambda x: x.roi, reverse=True)

            return [asdict(strategy) for strategy in strategies[:limit]]

        except Exception as e:
            logger.error(f"[ERROR] Error getting top performing strategies: {e}")
            return []

    def _get_worst_performing_strategies(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get worst performing strategies by ROI"""
        try:
            strategies = list(self.strategy_performance.values())
            strategies.sort(key=lambda x: x.roi)

            return [asdict(strategy) for strategy in strategies[:limit]]

        except Exception as e:
            logger.error(f"[ERROR] Error getting worst performing strategies: {e}")
            return []

    async def _update_equity_curves(self):
        """Update equity curves for all strategies"""
        try:
            # Group trades by strategy for equity curve calculation
            strategy_groups = self._group_trades_by_strategy()

            for group_key, trades in strategy_groups.items():
                equity_curve = self._calculate_equity_curve(trades)
                if equity_curve is not None:
                    self.equity_curves[group_key] = equity_curve

            # Save equity curves
            await self._save_equity_curves()

            logger.debug(f"[METRICS] Updated {len(self.equity_curves)} equity curves")

        except Exception as e:
            logger.error(f"[ERROR] Error updating equity curves: {e}")

    def _calculate_equity_curve(self, trades: List[TradeMetrics]) -> Optional[pl.DataFrame]:
        """Calculate equity curve for a list of trades"""
        try:
            if not trades:
                return None

            # Filter completed trades and sort by entry time
            completed_trades = [trade for trade in trades if trade.exit_time is not None and trade.pnl is not None]
            completed_trades.sort(key=lambda x: x.entry_time)

            if not completed_trades:
                return None

            # Create DataFrame
            trades_data = [asdict(trade) for trade in completed_trades]
            df = pl.DataFrame(trades_data)

            # Calculate cumulative PnL
            df = df.with_columns([
                pl.col('pnl').cumsum().alias('cumulative_pnl'),
                pl.col('entry_time').alias('timestamp')
            ])

            # Select relevant columns for equity curve
            equity_curve = df.select(['timestamp', 'cumulative_pnl', 'pnl'])

            return equity_curve

        except Exception as e:
            logger.error(f"[ERROR] Error calculating equity curve: {e}")
            return None

    async def _save_equity_curves(self):
        """Save equity curves to storage"""
        try:
            if not self.equity_curves:
                return

            equity_curves_path = self.config.storage_config.get('equity_curves_path', 'data/performance/equity_curves')

            for group_key, equity_curve in self.equity_curves.items():
                file_path = os.path.join(equity_curves_path, f"equity_curve_{group_key}.parquet")
                equity_curve.write_parquet(file_path, compression='zstd')

            logger.debug(f"💾 Saved {len(self.equity_curves)} equity curves")

        except Exception as e:
            logger.error(f"[ERROR] Error saving equity curves: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [METRICS] PERFORMANCE MONITORING
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _performance_monitoring_loop(self):
        """Performance monitoring loop"""
        while self.is_running:
            try:
                # Update performance metrics
                self.performance_metrics.update({
                    'timestamp': datetime.now().isoformat(),
                    'trades_analyzed': len(self.trade_data),
                    'strategies_tracked': len(self.strategy_performance),
                    'execution_quality_records': len(self.execution_quality),
                    'equity_curves': len(self.equity_curves),
                    'memory_usage_mb': self._get_memory_usage()
                })

                # Log performance metrics
                if self.config.logging_config.get('performance_logging', {}).get('enable', True):
                    logger.info(f"[STATUS] Performance: {self.performance_metrics['trades_analyzed']} trades, "
                              f"{self.performance_metrics['strategies_tracked']} strategies tracked")

                # Wait for next monitoring cycle
                await asyncio.sleep(300)  # 5 minutes

            except Exception as e:
                logger.error(f"[ERROR] Error in performance monitoring: {e}")
                await asyncio.sleep(60)

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
        except Exception as e:
            logger.error(f"[ERROR] Error getting memory usage: {e}")
            return 0.0


# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 MAIN EXECUTION (for testing)
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main function for testing Performance Analysis Agent"""
    try:
        # Initialize agent
        agent = PerformanceAnalysisAgent()

        # Setup and start
        await agent.setup()
        await agent.start()

        logger.info("🧪 Performance Analysis Agent test completed")

    except Exception as e:
        logger.error(f"[ERROR] Test failed: {e}")
    finally:
        if 'agent' in locals():
            await agent.stop()

if __name__ == "__main__":
    # Setup basic logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run the test
    asyncio.run(main())

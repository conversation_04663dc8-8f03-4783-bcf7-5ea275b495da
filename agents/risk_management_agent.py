#!/usr/bin/env python3
"""
RISK MANAGEMENT AGENT
Modern risk management agent for position sizing and risk control

Features:
- Real-time risk monitoring
- Position sizing calculations
- Stop-loss and take-profit management
- Portfolio risk limits enforcement
"""

import asyncio
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from typing import Any

from .base_agent import BaseAgent
from core.event_system import EventBus, Event, EventTypes

logger = logging.getLogger(__name__)

@dataclass
class RiskAssessment:
    """Risk assessment for a trading signal"""
    symbol: str
    signal_id: str
    risk_score: float  # 0.0 to 1.0 (higher = riskier)
    position_size: int
    max_loss: float
    risk_reward_ratio: float
    approved: bool
    rejection_reason: Optional[str]
    timestamp: datetime

class RiskManagementAgent(BaseAgent):
    """
    Risk Management Agent for position sizing and risk control
    
    Responsibilities:
    - Assess trading signal risks
    - Calculate optimal position sizes
    - Monitor portfolio risk limits
    - Enforce stop-loss and take-profit rules
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("RiskManagementAgent", event_bus, config, session_id)
        
        # Risk parameters (robust to missing risk_limits in config)
        limits = getattr(config, 'risk_limits', {}) or {}
        self.max_position_size = limits.get('max_position_size', 0.2)  # 20% of portfolio
        self.max_daily_loss = limits.get('max_daily_loss', 0.05)       # 5% daily loss
        self.max_drawdown = limits.get('max_drawdown', 0.15)           # 15% max drawdown

        # Portfolio tracking
        self.current_balance = config.initial_balance
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.max_balance = config.initial_balance
        self.active_positions = {}
        
        # Risk assessments
        self.risk_assessments = []
        self.rejected_signals = []

        # Background task handle
        self._risk_task = None
    
    async def initialize(self) -> bool:
        """Initialize the risk management agent"""
        try:
            self.log_info("Initializing Risk Management Agent...")

            # Subscribe to trading signals (use core event constants)
            self.event_bus.subscribe(EventTypes.SIGNAL_GENERATED, self._handle_trading_signal)
            self.event_bus.subscribe(EventTypes.TRADE_EXECUTED, self._handle_trade_executed)
            self.event_bus.subscribe(EventTypes.POSITION_UPDATE, self._handle_position_update)

            self.initialized = True
            self.log_info("Risk Management Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize Risk Management Agent: {e}")
            return False
    
    async def start(self):
        """Start the risk management agent"""
        try:
            self.log_info("Starting Risk Management Agent...")

            self.running = True

            # Start risk monitoring loop in background
            self._risk_task = asyncio.create_task(self._start_risk_monitoring_loop())

        except Exception as e:
            self.log_error(f"Error starting Risk Management Agent: {e}")
    
    async def _start_risk_monitoring_loop(self):
        """Start the main risk monitoring loop"""
        try:
            self.log_info("Starting risk monitoring loop...")
            
            while self.running:
                try:
                    # Monitor portfolio risk
                    await self._monitor_portfolio_risk()
                    
                    # Check position limits
                    await self._check_position_limits()
                    
                    # Update risk metrics
                    await self._update_risk_metrics()
                    
                    # Sleep for monitoring interval
                    await asyncio.sleep(10)  # Monitor every 10 seconds
                    
                except Exception as e:
                    self.log_error(f"Error in risk monitoring loop: {e}")
                    await asyncio.sleep(5)
            
            self.log_info("Risk monitoring loop ended")
            
        except Exception as e:
            self.log_error(f"Failed to start risk monitoring loop: {e}")
    
    async def _handle_trading_signal(self, event: Event):
        """Handle incoming trading signal for risk assessment"""
        try:
            signal = event.data.get('signal')
            if not signal:
                return
            
            # Perform risk assessment
            risk_assessment = await self._assess_signal_risk(signal)
            
            # Store assessment
            self.risk_assessments.append(risk_assessment)
            
            if risk_assessment.approved:
                # Publish approved signal
                await self.event_bus.publish(
                    EventTypes.SIGNAL_RISK_APPROVED,
                    {
                        "signal": signal,
                        "risk_assessment": risk_assessment,
                        "position_size": risk_assessment.position_size,
                        "max_loss": risk_assessment.max_loss
                    },
                    source=self.name
                )
                
                self.log_info(f"Approved signal for {signal.symbol} with position size {risk_assessment.position_size}")
            else:
                # Store rejected signal
                self.rejected_signals.append({
                    'signal': signal,
                    'reason': risk_assessment.rejection_reason,
                    'timestamp': datetime.now()
                })
                
                self.log_warning(f"Rejected signal for {signal.symbol}: {risk_assessment.rejection_reason}")
            
            self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"Failed to handle trading signal: {e}")
    
    async def _assess_signal_risk(self, signal) -> RiskAssessment:
        """Assess risk for a trading signal"""
        try:
            # Calculate position size based on risk
            position_size = await self._calculate_position_size(signal)
            
            # Calculate maximum loss
            if signal.signal_type == "BUY":
                max_loss = (signal.entry_price - signal.stop_loss) * position_size
            else:  # SELL
                max_loss = (signal.stop_loss - signal.entry_price) * position_size
            
            # Calculate risk-reward ratio
            if signal.signal_type == "BUY":
                potential_profit = (signal.target_price - signal.entry_price) * position_size
            else:  # SELL
                potential_profit = (signal.entry_price - signal.target_price) * position_size
            
            risk_reward_ratio = potential_profit / max_loss if max_loss > 0 else 0
            
            # Calculate risk score
            risk_score = await self._calculate_risk_score(signal, position_size, max_loss)
            
            # Determine approval
            approved, rejection_reason = await self._evaluate_risk(signal, position_size, max_loss, risk_score)
            
            return RiskAssessment(
                symbol=signal.symbol,
                signal_id=getattr(signal, 'signal_id', f"{signal.symbol}_{datetime.now().timestamp()}"),
                risk_score=risk_score,
                position_size=position_size,
                max_loss=max_loss,
                risk_reward_ratio=risk_reward_ratio,
                approved=approved,
                rejection_reason=rejection_reason,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.log_error(f"Failed to assess signal risk: {e}")
            return RiskAssessment(
                symbol=signal.symbol,
                signal_id="error",
                risk_score=1.0,
                position_size=0,
                max_loss=0,
                risk_reward_ratio=0,
                approved=False,
                rejection_reason=f"Risk assessment error: {e}",
                timestamp=datetime.now()
            )
    
    async def _calculate_position_size(self, signal) -> int:
        """Calculate optimal position size based on risk"""
        try:
            # Risk per trade (2% of current balance)
            risk_per_trade = self.current_balance * 0.02
            
            # Calculate stop loss distance
            if signal.signal_type == "BUY":
                stop_distance = signal.entry_price - signal.stop_loss
            else:  # SELL
                stop_distance = signal.stop_loss - signal.entry_price
            
            if stop_distance <= 0:
                return 0
            
            # Calculate position size based on risk
            position_size = int(risk_per_trade / stop_distance)
            
            # Apply maximum position size limit from paper trading config
            # Get max position size from environment (Rs.50,000 by default)
            import os
            max_position_value = float(os.getenv('PAPER_TRADING_MAX_POSITION_SIZE', 50000))
            max_shares_by_value = int(max_position_value / signal.entry_price)
            
            # Also apply portfolio percentage limit
            max_position_by_portfolio = self.current_balance * self.max_position_size
            max_shares_by_portfolio = int(max_position_by_portfolio / signal.entry_price)
            
            # Use the more restrictive limit
            max_shares = min(max_shares_by_value, max_shares_by_portfolio)
            position_size = min(position_size, max_shares)
            
            # Debug logging for position size calculation
            self.log_info(f"[POSITION-DEBUG] Position size calculation for {signal.symbol}:")
            self.log_info(f"  Entry price: Rs.{signal.entry_price:.2f}")
            self.log_info(f"  Risk per trade: Rs.{risk_per_trade:.2f}")
            self.log_info(f"  Stop distance: Rs.{stop_distance:.2f}")
            self.log_info(f"  Risk-based position size: {int(risk_per_trade / stop_distance)}")
            self.log_info(f"  Max position value limit: Rs.{max_position_value:.2f}")
            self.log_info(f"  Max shares by value: {max_shares_by_value}")
            self.log_info(f"  Max shares by portfolio: {max_shares_by_portfolio}")
            self.log_info(f"  Final position size: {position_size}")
            self.log_info(f"  Final position value: Rs.{position_size * signal.entry_price:.2f}")
            
            # Ensure minimum position size
            position_size = max(position_size, 1) if position_size > 0 else 0
            
            return position_size
            
        except Exception as e:
            self.log_error(f"Failed to calculate position size: {e}")
            return 0
    
    async def _calculate_risk_score(self, signal, position_size: int, max_loss: float) -> float:
        """Calculate risk score for a signal"""
        try:
            risk_factors = []
            
            # Portfolio concentration risk
            position_value = signal.entry_price * position_size
            concentration_risk = position_value / self.current_balance
            risk_factors.append(concentration_risk * 2)  # Weight: 2x
            
            # Maximum loss as percentage of balance
            loss_percentage = max_loss / self.current_balance
            risk_factors.append(loss_percentage * 10)  # Weight: 10x
            
            # Signal strength (inverse - lower strength = higher risk)
            strength_risk = (1.0 - signal.strength) * 0.5
            risk_factors.append(strength_risk)
            
            # Confidence risk (inverse - lower confidence = higher risk)
            confidence_risk = (1.0 - signal.confidence) * 0.3
            risk_factors.append(confidence_risk)
            
            # Current drawdown risk
            current_drawdown = (self.max_balance - self.current_balance) / self.max_balance
            drawdown_risk = current_drawdown * 2
            risk_factors.append(drawdown_risk)
            
            # Daily loss risk
            daily_loss_percentage = abs(self.daily_pnl) / self.current_balance if self.daily_pnl < 0 else 0
            daily_risk = daily_loss_percentage * 5
            risk_factors.append(daily_risk)
            
            # Calculate weighted average risk score
            risk_score = sum(risk_factors) / len(risk_factors)
            
            # Normalize to 0-1 range
            risk_score = min(max(risk_score, 0.0), 1.0)
            
            return risk_score
            
        except Exception as e:
            self.log_error(f"Failed to calculate risk score: {e}")
            return 1.0  # Maximum risk on error
    
    async def _evaluate_risk(self, signal, position_size: int, max_loss: float, risk_score: float) -> tuple[bool, Optional[str]]:
        """Evaluate if signal should be approved based on risk"""
        try:
            # Debug logging for trade rejection analysis
            self.log_info(f"[RISK-DEBUG] Evaluating signal for {signal.symbol}:")
            self.log_info(f"  Position size: {position_size}")
            self.log_info(f"  Max loss: Rs.{max_loss:.2f} ({(max_loss / self.current_balance):.2%} of balance)")
            self.log_info(f"  Risk score: {risk_score:.2f}")
            self.log_info(f"  Signal strength: {signal.strength:.2f}")
            self.log_info(f"  Signal confidence: {signal.confidence:.2f}")
            self.log_info(f"  Current balance: Rs.{self.current_balance:.2f}")
            self.log_info(f"  Daily PnL: Rs.{self.daily_pnl:.2f}")
            
            # Check if position size is valid
            if position_size <= 0:
                return False, "Invalid position size"
            
            # Check maximum loss limit
            loss_percentage = max_loss / self.current_balance
            if loss_percentage > 0.05:  # 5% max loss per trade
                return False, f"Maximum loss too high: {loss_percentage:.2%}"
            
            # Check daily loss limit
            potential_daily_loss = self.daily_pnl - max_loss
            daily_loss_percentage = abs(potential_daily_loss) / self.current_balance
            if potential_daily_loss < 0 and daily_loss_percentage > self.max_daily_loss:
                return False, f"Would exceed daily loss limit: {daily_loss_percentage:.2%}"
            
            # Check portfolio concentration
            position_value = signal.entry_price * position_size
            concentration = position_value / self.current_balance
            if concentration > self.max_position_size:
                return False, f"Position too large: {concentration:.2%} of portfolio"
            
            # Check drawdown limit
            potential_balance = self.current_balance - max_loss
            potential_drawdown = (self.max_balance - potential_balance) / self.max_balance
            if potential_drawdown > self.max_drawdown:
                return False, f"Would exceed maximum drawdown: {potential_drawdown:.2%}"
            
            # Check risk score
            if risk_score > 0.95:
                return False, f"Risk score too high: {risk_score:.2f}"
            
            # Check signal quality (reduced threshold for demo mode)
            if signal.strength < 0.1:
                return False, f"Signal strength too low: {signal.strength:.2f}"
            
            if signal.confidence < 0.2:
                return False, f"Signal confidence too low: {signal.confidence:.2f}"
            
            # Check if we already have a position in this symbol (allow multiple positions for paper trading)
            if signal.symbol in self.active_positions and len(self.active_positions[signal.symbol]) >= 2:
                return False, "Maximum positions reached for this symbol (2 max)"
            
            # All checks passed
            return True, None
            
        except Exception as e:
            self.log_error(f"Failed to evaluate risk: {e}")
            return False, f"Risk evaluation error: {e}"
    
    async def _monitor_portfolio_risk(self):
        """Monitor overall portfolio risk"""
        try:
            # Calculate current drawdown
            current_drawdown = (self.max_balance - self.current_balance) / self.max_balance
            
            # Check if we need to reduce risk
            if current_drawdown > self.max_drawdown * 0.8:  # 80% of max drawdown
                self.log_warning(f"High drawdown detected: {current_drawdown:.2%}")
                
                # Publish risk warning
                await self.event_bus.publish(
                    EventTypes.HIGH_RISK_WARNING,
                    {
                        "type": "high_drawdown",
                        "current_drawdown": current_drawdown,
                        "max_drawdown": self.max_drawdown,
                        "recommendation": "Consider reducing position sizes"
                    },
                    source=self.name
                )
            
            # Check daily loss
            daily_loss_percentage = abs(self.daily_pnl) / self.current_balance if self.daily_pnl < 0 else 0
            if daily_loss_percentage > self.max_daily_loss * 0.8:  # 80% of max daily loss
                self.log_warning(f"High daily loss: {daily_loss_percentage:.2%}")
                
                # Publish risk warning
                await self.event_bus.publish(
                    EventTypes.HIGH_RISK_WARNING,
                    {
                        "type": "high_daily_loss",
                        "daily_loss": daily_loss_percentage,
                        "max_daily_loss": self.max_daily_loss,
                        "recommendation": "Consider stopping trading for today"
                    },
                    source=self.name
                )
            
        except Exception as e:
            self.log_error(f"Failed to monitor portfolio risk: {e}")
    
    async def _check_position_limits(self):
        """Check position limits and exposure"""
        try:
            total_exposure = 0
            
            for symbol, position in self.active_positions.items():
                position_value = position.get('current_value', 0)
                total_exposure += position_value
            
            # Check total exposure
            exposure_percentage = total_exposure / self.current_balance
            if exposure_percentage > 0.8:  # 80% exposure limit
                self.log_warning(f"High portfolio exposure: {exposure_percentage:.2%}")
                
                # Publish exposure warning
                await self.event_bus.publish(
                    EventTypes.HIGH_EXPOSURE_WARNING,
                    {
                        "exposure_percentage": exposure_percentage,
                        "total_exposure": total_exposure,
                        "recommendation": "Consider reducing positions"
                    },
                    source=self.name
                )
            
        except Exception as e:
            self.log_error(f"Failed to check position limits: {e}")
    
    async def _update_risk_metrics(self):
        """Update risk metrics and statistics"""
        try:
            # Update maximum balance if current balance is higher
            if self.current_balance > self.max_balance:
                self.max_balance = self.current_balance
            
            # Calculate current metrics
            current_drawdown = (self.max_balance - self.current_balance) / self.max_balance
            daily_loss_percentage = abs(self.daily_pnl) / self.current_balance if self.daily_pnl < 0 else 0
            
            # Publish risk metrics update
            await self.event_bus.publish(
                EventTypes.RISK_METRICS_UPDATE,
                {
                    "current_balance": self.current_balance,
                    "daily_pnl": self.daily_pnl,
                    "total_pnl": self.total_pnl,
                    "current_drawdown": current_drawdown,
                    "daily_loss_percentage": daily_loss_percentage,
                    "active_positions": len(self.active_positions),
                    "risk_assessments_today": len([r for r in self.risk_assessments
                                                 if r.timestamp.date() == datetime.now().date()]),
                    "signals_rejected_today": len([r for r in self.rejected_signals
                                                 if r['timestamp'].date() == datetime.now().date()])
                },
                source=self.name
            )
            
        except Exception as e:
            self.log_error(f"Failed to update risk metrics: {e}")
    
    async def _handle_trade_executed(self, event: Event):
        """Handle trade execution event"""
        try:
            trade_data = event.data
            symbol = trade_data.get('symbol')
            
            if symbol:
                # Update active positions
                self.active_positions[symbol] = trade_data
                
                # Update balance and PnL (simplified)
                trade_value = trade_data.get('trade_value', 0)
                if trade_data.get('side') == 'BUY':
                    self.current_balance -= trade_value
                else:
                    self.current_balance += trade_value
                
                self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"Failed to handle trade executed: {e}")
    
    async def _handle_position_update(self, event: Event):
        """Handle position update event"""
        try:
            position_data = event.data
            symbol = position_data.get('symbol')
            
            if symbol:
                if symbol in self.active_positions:
                    self.active_positions[symbol].update(position_data)
                
                # Update PnL
                pnl = position_data.get('unrealized_pnl', 0)
                self.daily_pnl += pnl
                self.total_pnl += pnl
                
                self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"Failed to handle position update: {e}")
    
    async def stop(self):
        """Stop the risk management agent"""
        try:
            self.log_info("Stopping Risk Management Agent...")
            
            self.running = False
            if self._risk_task:
                self._risk_task.cancel()
                try:
                    await self._risk_task
                except asyncio.CancelledError:
                    pass
                self._risk_task = None
            
            self.log_info("Risk Management Agent stopped")
            
        except Exception as e:
            self.log_error(f"Error stopping Risk Management Agent: {e}")
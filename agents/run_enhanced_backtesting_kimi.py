#!/usr/bin/env python3
"""
Optimized Runner script for Enhanced Backtesting System
Performance-focused with better filtering and result quality control
"""

import argparse
import logging
import sys
import os
from pathlib import Path
import time
import asyncio

# Add the project root to sys.path to ensure package discovery
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('backtesting.log')
        ]
    )

def validate_environment():
    """Validate environment with better error reporting"""
    errors = []
    warnings = []

    # Check data directory
    data_dirs = ["data/features", "../data/features"]
    data_dir = None
    for d in data_dirs:
        if Path(d).exists():
            data_dir = d
            break
    
    if not data_dir:
        errors.append("data/features directory not found in current or parent directory")
    else:
        # Check for parquet files
        parquet_files = list(Path(data_dir).glob("*.parquet"))
        if not parquet_files:
            errors.append(f"No parquet files found in {data_dir}")
        else:
            # Quick file size check
            total_size = sum(f.stat().st_size for f in parquet_files) / (1024 * 1024)
            print(f"[INFO] Found {len(parquet_files)} parquet files ({total_size:.1f} MB total)")
            
            # Check for very small files (likely empty)
            small_files = [f for f in parquet_files if f.stat().st_size < 1000]
            if small_files:
                warnings.append(f"Found {len(small_files)} very small files (< 1KB) - might be empty")

    # Check strategies file
    strategy_files = ["config/strategies.yaml", "../config/strategies.yaml"]
    strategy_file = None
    for s in strategy_files:
        if Path(s).exists():
            strategy_file = s
            break
    
    if not strategy_file:
        errors.append("config/strategies.yaml not found in current or parent directory")
    else:
        try:
            import yaml
            # Fix Windows encoding issue - explicitly use UTF-8
            with open(strategy_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            strategies = data.get('strategies', [])
            print(f"[INFO] Found {len(strategies)} strategies in config")
        except Exception as e:
            errors.append(f"Error reading strategies file: {e}")

    # Check/create output directory
    output_dirs = ["data/backtest", "../data/backtest"]
    output_dir = None
    for o in output_dirs:
        if Path(o).exists():
            output_dir = o
            break
    
    if not output_dir:
        try:
            Path("../data/backtest").mkdir(parents=True, exist_ok=True)
            output_dir = "../data/backtest"
            print(f"[INFO] Created output directory: {output_dir}")
        except Exception as e:
            errors.append(f"Cannot create output directory: {e}")

    # Print warnings
    for warning in warnings:
        print(f"[WARNING] {warning}")

    # Print errors and return status
    if errors:
        print("[ERROR] Environment validation failed:")
        for error in errors:
            print(f"  - {error}")
        return False

    print("[SUCCESS] Environment validation passed!")
    return True

def print_performance_tips():
    """Print performance optimization tips"""
    print("\n[PERFORMANCE TIPS]")
    print("=" * 50)
    print("1. Start with a single symbol/timeframe for testing:")
    print("   python run_optimized.py --symbol RELIANCE --timeframe 1min --max-strategies 5")
    print("\n2. Use strategy filtering to test specific strategies:")
    print("   python run_optimized.py --max-strategies 10")
    print("\n3. For full runs, consider running overnight:")
    print("   nohup python run_optimized.py > backtest.log 2>&1 &")
    print("\n4. Monitor memory usage with:")
    print("   watch -n 5 'ps aux | grep python'")
    print("\n5. Expected performance (optimized):")
    print("   - Single symbol/strategy: ~10-30 seconds")
    print("   - Single symbol/all strategies: ~2-5 minutes")
    print("   - All symbols/single strategy: ~10-30 minutes")
    print("   - Full run (220 symbols × 25 strategies): ~6-12 hours")

async def run_optimized_backtesting(max_symbols=None, max_strategies=None, 
                                  target_symbol=None, target_timeframe=None,
                                  strategy_filter=None, quick_test=False,
                                  max_trades_per_strategy=None):
    """Run optimized backtesting with filters"""
    
    # Import the optimized module
    try:
        from agents import enhanced_backtesting_kimi as ebp
    except ImportError as e:
        print(f"[ERROR] Could not import optimized backtesting module: {e}")
        print("Please ensure 'enhanced_backtesting_kimi.py' is accessible and the 'agents' directory is correctly configured as a Python package.")
        return False
    
    # Apply quick test settings
    if quick_test:
        max_symbols = max_symbols or 5
        max_strategies = max_strategies or 5
        print(f"[QUICK TEST] Limited to {max_symbols} symbols and {max_strategies} strategies")
    
    # Set MAX_TRADES_PER_STRATEGY if provided
    if max_trades_per_strategy is not None:
        ebp.MAX_TRADES_PER_STRATEGY = max_trades_per_strategy
        print(f"[CONFIG] Set MAX_TRADES_PER_STRATEGY to {max_trades_per_strategy}")

    # Filter strategies if requested
    original_load_strategies = ebp.load_strategies
    
    def filtered_load_strategies():
        all_strategies = original_load_strategies()
        
        if strategy_filter:
            filtered = [s for s in all_strategies if strategy_filter.lower() in s['name'].lower()]
            print(f"[FILTER] Strategy filter '{strategy_filter}' matched {len(filtered)} strategies")
            all_strategies = filtered
        
        if max_strategies and len(all_strategies) > max_strategies:
            all_strategies = all_strategies[:max_strategies]
            print(f"[LIMIT] Limited to first {max_strategies} strategies")
        
        return all_strategies
    
    ebp.load_strategies = filtered_load_strategies
    
    # Filter files if requested
    original_get_files = ebp.get_available_feature_files
    
    def filtered_get_files():
        all_files = original_get_files()
        filtered = []
        
        for file_path, symbol, timeframe in all_files:
            include_file = True
            
            if target_symbol and symbol.upper() != target_symbol.upper():
                include_file = False
            
            if target_timeframe and timeframe.lower() != target_timeframe.lower():
                include_file = False
            
            if include_file:
                filtered.append((file_path, symbol, timeframe))
        
        if max_symbols and len(filtered) > max_symbols:
            filtered = filtered[:max_symbols]
            print(f"[LIMIT] Limited to first {max_symbols} files")
        
        if target_symbol:
            print(f"[FILTER] Symbol filter: {target_symbol}")
        if target_timeframe:
            print(f"[FILTER] Timeframe filter: {target_timeframe}")
        
        print(f"[INFO] Will process {len(filtered)} files")
        return filtered
    
    ebp.get_available_feature_files = filtered_get_files
    
    # Run the backtesting
    try:
        start_time = time.time()
        await ebp.main_async()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n[SUCCESS] Backtesting completed in {total_time:.1f} seconds")
        
        # Show results summary
        output_dir = "../data/backtest" if Path("../data/backtest").exists() else "data/backtest"
        if Path(output_dir).exists():
            output_files = list(Path(output_dir).glob("backtest_*.parquet"))
            if output_files:
                total_size = sum(f.stat().st_size for f in output_files) / (1024 * 1024)
                print(f"[RESULTS] Generated {len(output_files)} result files ({total_size:.1f} MB)")
                
                # Show sample results
                print("\n[SAMPLE RESULTS]")
                for i, file in enumerate(sorted(output_files)[:5]):
                    size_mb = file.stat().st_size / (1024 * 1024)
                    print(f"  {file.name}: {size_mb:.1f} MB")
                if len(output_files) > 5:
                    print(f"  ... and {len(output_files) - 5} more files")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Backtesting failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def estimate_runtime(num_symbols, num_strategies, num_timeframes):
    """Estimate runtime based on optimized performance"""
    # Base time per symbol-strategy-timeframe combination (seconds)
    base_time = 0.5  # Optimized to ~0.5 seconds per combination
    
    total_combinations = num_symbols * num_strategies * num_timeframes
    estimated_seconds = total_combinations * base_time
    
    if estimated_seconds < 60:
        return f"{estimated_seconds:.0f} seconds"
    elif estimated_seconds < 3600:
        return f"{estimated_seconds/60:.1f} minutes"
    else:
        return f"{estimated_seconds/3600:.1f} hours"

def main():
    """Main entry point with optimized command-line interface"""
    parser = argparse.ArgumentParser(
        description='Optimized Enhanced Backtesting System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
OPTIMIZED EXAMPLES:

Quick Testing:
  # Quick test with 5 symbols and 5 strategies
  python run_optimized.py --quick-test
  
  # Test single symbol with all strategies
  python run_optimized.py --symbol RELIANCE --timeframe 1min
  
  # Test specific strategy across symbols
  python run_optimized.py --strategy-filter "RSI" --max-symbols 10

Production Runs:
  # Process first 50 symbols with all strategies
  python run_optimized.py --max-symbols 50
  
  # Process all 1min data
  python run_optimized.py --timeframe 1min
  
  # Full production run (use with nohup)
  nohup python run_optimized.py > backtest_full.log 2>&1 &

Performance Monitoring:
  # Run with detailed logging
  python run_optimized.py --log-level DEBUG --max-symbols 5
        """
    )
    
    # Basic options
    parser.add_argument('--quick-test', action='store_true',
                       help='Quick test mode (5 symbols, 5 strategies)')
    parser.add_argument('--max-symbols', type=int,
                       help='Limit number of symbols to process')
    parser.add_argument('--max-strategies', type=int,
                       help='Limit number of strategies to process')
    parser.add_argument('--max-trades-per-strategy', type=int,
                       help='Limit number of trades per strategy for performance/data quality balance')
    
    # Filtering options
    parser.add_argument('--symbol', type=str,
                       help='Process only specific symbol (e.g., RELIANCE)')
    parser.add_argument('--timeframe', type=str,
                       help='Process only specific timeframe (e.g., 1min)')
    parser.add_argument('--strategy-filter', type=str,
                       help='Filter strategies by name (partial match)')
    
    # System options
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level')
    parser.add_argument('--validate-only', action='store_true',
                       help='Only validate environment and exit')
    parser.add_argument('--estimate-time', action='store_true',
                       help='Estimate runtime and exit')
    parser.add_argument('--show-tips', action='store_true',
                       help='Show performance tips and exit')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # Show performance tips if requested
    if args.show_tips:
        print_performance_tips()
        return
    
    print("[OPTIMIZED BACKTESTING SYSTEM]")
    print("=" * 50)
    
    # Validate environment
    if not validate_environment():
        print("\n[ERROR] Please fix environment issues before running")
        sys.exit(1)
    
    if args.validate_only:
        return
    
    # Estimate runtime if requested
    if args.estimate_time:
        try:
            # Get actual counts
            sys.path.append('.')
            
            # Estimate based on filters
            num_symbols = 220  # Default
            num_strategies = 25  # Default
            num_timeframes = 4  # Default
            
            if args.max_symbols:
                num_symbols = min(num_symbols, args.max_symbols)
            if args.symbol:
                num_symbols = 1
            if args.max_strategies:
                num_strategies = min(num_strategies, args.max_strategies)
            if args.timeframe:
                num_timeframes = 1
            if args.quick_test:
                num_symbols = min(5, num_symbols)
                num_strategies = min(5, num_strategies)
            
            estimated_time = estimate_runtime(num_symbols, num_strategies, num_timeframes)
            
            print(f"\n[RUNTIME ESTIMATE]")
            print(f"Symbols: {num_symbols}")
            print(f"Strategies: {num_strategies}")
            print(f"Timeframes: {num_timeframes}")
            print(f"Total combinations: {num_symbols * num_strategies * num_timeframes}")
            print(f"Estimated time: {estimated_time}")
            print("\nNote: Actual time may vary based on data size and system performance")
            
        except Exception as e:
            print(f"[ERROR] Could not estimate runtime: {e}")
        
        return
    
    # Run the optimized backtesting
    try:
        print(f"\n[START] Starting optimized backtesting...")
        
        success = asyncio.run(run_optimized_backtesting(
            max_symbols=args.max_symbols,
            max_strategies=args.max_strategies,
            target_symbol=args.symbol,
            target_timeframe=args.timeframe,
            strategy_filter=args.strategy_filter,
            quick_test=args.quick_test,
            max_trades_per_strategy=args.max_trades_per_strategy
        ))
        
        if success:
            print("\n🎉 [SUCCESS] Backtesting completed successfully!")
            print("\nNext steps:")
            print("1. Check the generated result files in data/backtest/")
            print("2. Use analysis tools to review performance metrics")
            print("3. Consider running with different parameters for comparison")
        else:
            print("\n❌ [FAILED] Backtesting encountered errors")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ [INTERRUPTED] Backtesting stopped by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ [ERROR] Unexpected error: {e}")
        if args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

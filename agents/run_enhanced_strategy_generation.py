#!/usr/bin/env python3
"""
Enhanced Strategy Generation Agent Runner
Test and demonstrate the enhanced strategy generation capabilities
"""

import os
import sys
import asyncio
import logging
import json
from datetime import datetime, timedelta
import numpy as np

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_generation_agent import (
    EnhancedStrategyGenerationAgent,
    MarketRegimeType,
    VolatilityRegimeType,
    EventType,
    TimeWindow
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enhanced_strategy_generation.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class EnhancedStrategyGenerationRunner:
    """Runner for testing enhanced strategy generation agent"""
    
    def __init__(self):
        self.agent = None
        self.test_data = {}
        
    async def initialize(self):
        """Initialize the runner and agent"""
        try:
            logger.info("[RUNNER] Initializing Enhanced Strategy Generation Runner...")
            
            # Create logs directory
            os.makedirs('logs', exist_ok=True)
            os.makedirs('data/strategy_performance', exist_ok=True)
            os.makedirs('data/signal_history', exist_ok=True)
            
            # Initialize agent
            self.agent = EnhancedStrategyGenerationAgent()
            
            if await self.agent.initialize():
                logger.info("[RUNNER] Agent initialized successfully")
                return True
            else:
                logger.error("[RUNNER] Failed to initialize agent")
                return False
                
        except Exception as e:
            logger.error(f"[RUNNER] Failed to initialize: {e}")
            return False
    
    def generate_test_data(self, symbol: str = "BANKNIFTY", periods: int = 100):
        """Generate realistic test market data and indicators"""
        try:
            logger.info(f"[DATA] Generating test data for {symbol} with {periods} periods")
            
            # Generate realistic OHLCV data
            base_price = 45000 if symbol == "BANKNIFTY" else 19000
            
            # Generate price series with some trend and volatility
            np.random.seed(42)  # For reproducible results
            returns = np.random.normal(0.0001, 0.02, periods)  # Small positive drift with 2% volatility
            
            prices = [base_price]
            for ret in returns:
                prices.append(prices[-1] * (1 + ret))
            
            # Generate OHLCV from price series
            opens = prices[:-1]
            closes = prices[1:]
            
            highs = []
            lows = []
            volumes = []
            
            for i in range(len(opens)):
                # Generate high/low with some randomness
                high_factor = np.random.uniform(1.001, 1.015)
                low_factor = np.random.uniform(0.985, 0.999)
                
                high = max(opens[i], closes[i]) * high_factor
                low = min(opens[i], closes[i]) * low_factor
                
                highs.append(high)
                lows.append(low)
                
                # Generate volume
                base_volume = np.random.uniform(50000, 200000)
                volume_factor = np.random.uniform(0.5, 2.5)
                volumes.append(int(base_volume * volume_factor))
            
            market_data = {
                'open': opens,
                'high': highs,
                'low': lows,
                'close': closes,
                'volume': volumes
            }
            
            # Generate technical indicators
            indicators = self._calculate_indicators(market_data)
            
            self.test_data[symbol] = {
                'market_data': market_data,
                'indicators': indicators
            }
            
            logger.info(f"[DATA] Generated test data for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"[DATA] Failed to generate test data: {e}")
            return False
    
    def _calculate_indicators(self, market_data):
        """Calculate technical indicators from market data"""
        try:
            closes = np.array(market_data['close'])
            highs = np.array(market_data['high'])
            lows = np.array(market_data['low'])
            volumes = np.array(market_data['volume'])
            
            # Simple moving averages
            ema_5 = self._ema(closes, 5)
            ema_20 = self._ema(closes, 20)
            
            # RSI
            rsi_14 = self._rsi(closes, 14)
            
            # VWAP (simplified)
            vwap = self._vwap(closes, volumes)
            
            # ATR
            atr_14 = self._atr(highs, lows, closes, 14)
            
            # MACD
            macd, macd_signal = self._macd(closes)
            
            return {
                'ema_5': ema_5.tolist(),
                'ema_20': ema_20.tolist(),
                'rsi_14': rsi_14.tolist(),
                'vwap': vwap.tolist(),
                'atr_14': atr_14.tolist(),
                'macd': macd.tolist(),
                'macd_signal': macd_signal.tolist()
            }
            
        except Exception as e:
            logger.error(f"[INDICATORS] Failed to calculate indicators: {e}")
            return {}
    
    def _ema(self, data, period):
        """Calculate Exponential Moving Average"""
        alpha = 2 / (period + 1)
        ema = np.zeros_like(data)
        ema[0] = data[0]
        
        for i in range(1, len(data)):
            ema[i] = alpha * data[i] + (1 - alpha) * ema[i-1]
        
        return ema
    
    def _rsi(self, data, period):
        """Calculate RSI"""
        deltas = np.diff(data)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gains = np.zeros(len(data))
        avg_losses = np.zeros(len(data))
        
        # Initial averages
        avg_gains[period] = np.mean(gains[:period])
        avg_losses[period] = np.mean(losses[:period])
        
        # Smoothed averages
        for i in range(period + 1, len(data)):
            avg_gains[i] = (avg_gains[i-1] * (period - 1) + gains[i-1]) / period
            avg_losses[i] = (avg_losses[i-1] * (period - 1) + losses[i-1]) / period
        
        rs = np.divide(avg_gains, avg_losses, out=np.zeros_like(avg_gains), where=avg_losses!=0)
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _vwap(self, prices, volumes):
        """Calculate VWAP"""
        cumulative_pv = np.cumsum(prices * volumes)
        cumulative_volume = np.cumsum(volumes)
        
        return np.divide(cumulative_pv, cumulative_volume, 
                        out=np.zeros_like(cumulative_pv), 
                        where=cumulative_volume!=0)
    
    def _atr(self, highs, lows, closes, period):
        """Calculate Average True Range"""
        tr1 = highs - lows
        tr2 = np.abs(highs - np.roll(closes, 1))
        tr3 = np.abs(lows - np.roll(closes, 1))
        
        tr = np.maximum(tr1, np.maximum(tr2, tr3))
        tr[0] = tr1[0]  # First value
        
        atr = np.zeros_like(tr)
        atr[period-1] = np.mean(tr[:period])
        
        for i in range(period, len(tr)):
            atr[i] = (atr[i-1] * (period - 1) + tr[i]) / period
        
        return atr
    
    def _macd(self, data):
        """Calculate MACD"""
        ema_12 = self._ema(data, 12)
        ema_26 = self._ema(data, 26)
        macd = ema_12 - ema_26
        macd_signal = self._ema(macd, 9)
        
        return macd, macd_signal
    
    async def test_signal_generation(self):
        """Test signal generation with various scenarios"""
        try:
            logger.info("[TEST] Testing signal generation...")
            
            test_scenarios = [
                {
                    'name': 'Morning Breakout Scenario',
                    'symbol': 'BANKNIFTY',
                    'time': datetime.now().replace(hour=9, minute=30),
                    'regime': MarketRegimeType.BULLISH,
                    'volatility': VolatilityRegimeType.LOW_IV
                },
                {
                    'name': 'Midday Mean Reversion Scenario',
                    'symbol': 'NIFTY',
                    'time': datetime.now().replace(hour=12, minute=0),
                    'regime': MarketRegimeType.SIDEWAYS,
                    'volatility': VolatilityRegimeType.NORMAL_IV
                },
                {
                    'name': 'Power Hour Momentum Scenario',
                    'symbol': 'BANKNIFTY',
                    'time': datetime.now().replace(hour=14, minute=45),
                    'regime': MarketRegimeType.VOLATILE,
                    'volatility': VolatilityRegimeType.HIGH_IV
                }
            ]
            
            all_signals = []
            
            for scenario in test_scenarios:
                logger.info(f"[TEST] Running scenario: {scenario['name']}")
                
                # Generate test data if not exists
                if scenario['symbol'] not in self.test_data:
                    self.generate_test_data(scenario['symbol'])
                
                data = self.test_data[scenario['symbol']]
                
                # Set agent's current regime (for testing)
                self.agent.current_market_regime = scenario['regime']
                self.agent.current_volatility_regime = scenario['volatility']
                
                # Generate signals
                signals = await self.agent.generate_signals(
                    data['market_data'],
                    data['indicators'],
                    scenario['symbol'],
                    scenario['time']
                )
                
                logger.info(f"[TEST] Scenario '{scenario['name']}' generated {len(signals)} signals")
                
                for signal in signals:
                    logger.info(f"[SIGNAL] {signal.strategy_id}: {signal.signal_type} at {signal.entry_price:.2f} "
                              f"(SL: {signal.stop_loss:.2f}, TP: {signal.take_profit:.2f}, "
                              f"Confidence: {signal.confidence:.2f})")
                
                all_signals.extend(signals)
            
            logger.info(f"[TEST] Total signals generated: {len(all_signals)}")
            return all_signals
            
        except Exception as e:
            logger.error(f"[TEST] Failed to test signal generation: {e}")
            return []
    
    async def run_comprehensive_test(self):
        """Run comprehensive test of all features"""
        try:
            logger.info("[TEST] Starting comprehensive test...")
            
            # Test 1: Signal Generation
            signals = await self.test_signal_generation()
            
            # Test 2: Strategy Performance Tracking
            await self._test_performance_tracking()
            
            # Test 3: Regime Detection
            await self._test_regime_detection()
            
            # Test 4: Event Detection
            await self._test_event_detection()
            
            logger.info("[TEST] Comprehensive test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"[TEST] Comprehensive test failed: {e}")
            return False
    
    async def _test_performance_tracking(self):
        """Test performance tracking functionality"""
        logger.info("[TEST] Testing performance tracking...")
        # Performance tracking test logic would go here
    
    async def _test_regime_detection(self):
        """Test regime detection functionality"""
        logger.info("[TEST] Testing regime detection...")
        # Regime detection test logic would go here
    
    async def _test_event_detection(self):
        """Test event detection functionality"""
        logger.info("[TEST] Testing event detection...")
        # Event detection test logic would go here

async def main():
    """Main execution function"""
    try:
        logger.info("[MAIN] Starting Enhanced Strategy Generation Agent Test")
        
        # Initialize runner
        runner = EnhancedStrategyGenerationRunner()
        
        if await runner.initialize():
            # Run comprehensive test
            await runner.run_comprehensive_test()
        else:
            logger.error("[MAIN] Failed to initialize runner")
        
        logger.info("[MAIN] Test completed")
        
    except Exception as e:
        logger.error(f"[MAIN] Error in main execution: {e}")

if __name__ == "__main__":
    asyncio.run(main())

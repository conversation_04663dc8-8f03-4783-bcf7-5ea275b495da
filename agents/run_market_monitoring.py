#!/usr/bin/env python3
"""
Market Monitoring Agent Runner
Production-ready runner with comprehensive error handling and monitoring
"""

import os
import sys
import asyncio
import logging
import signal
import argparse
from datetime import datetime
from typing import Optional
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from market_monitoring_agent import (
    MarketMonitoringAgent,
    MarketRegime,
    TradingSignal,
    check_dependencies,
    get_system_info
)

logger = logging.getLogger(__name__)

class MarketMonitoringRunner:
    """
    Production runner for Market Monitoring Agent
    
    Features:
    - Graceful startup and shutdown
    - Signal handling
    - Error recovery
    - Performance monitoring
    - Health checks
    """
    
    def __init__(self, config_path: str = "config/market_monitoring_config.yaml"):
        """Initialize runner"""
        self.config_path = config_path
        self.agent: Optional[MarketMonitoringAgent] = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("[INIT] Market Monitoring Runner initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()
    
    async def start(self):
        """Start the Market Monitoring Agent"""
        try:
            logger.info("[CONFIG] Starting Market Monitoring Agent...")
            
            # Check dependencies
            await self._check_dependencies()
            
            # Check system resources
            await self._check_system_resources()
            
            # Create and setup agent
            self.agent = MarketMonitoringAgent(self.config_path)
            await self.agent.setup()
            
            # Add custom handlers
            await self._setup_custom_handlers()
            
            # Start monitoring
            self.is_running = True
            logger.info("[SUCCESS] Market Monitoring Agent started successfully")
            
            # Run main loop
            await self._run_main_loop()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start Market Monitoring Agent: {e}")
            raise
    
    async def stop(self):
        """Stop the Market Monitoring Agent"""
        try:
            logger.info("[STOP] Stopping Market Monitoring Agent...")
            
            self.is_running = False
            
            if self.agent:
                await self.agent.stop()
            
            logger.info("[SUCCESS] Market Monitoring Agent stopped successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping agent: {e}")
    
    async def _check_dependencies(self):
        """Check required dependencies"""
        logger.info("[DEBUG] Checking dependencies...")
        
        deps = check_dependencies()
        missing_deps = [dep for dep, available in deps.items() if not available]
        
        if missing_deps:
            logger.error(f"[ERROR] Missing dependencies: {missing_deps}")
            logger.info("📦 Install missing dependencies:")
            
            if 'smartapi' in missing_deps:
                logger.info("   pip install smartapi-python pyotp")
            if 'telegram' in missing_deps:
                logger.info("   pip install python-telegram-bot")
            if 'ai_training_agent' in missing_deps:
                logger.warning("   AI Training Agent not found - strategy classification disabled")
            
            # Only fail for critical dependencies
            critical_deps = ['polars', 'pandas', 'numpy']
            critical_missing = [dep for dep in missing_deps if dep in critical_deps]
            
            if critical_missing:
                raise RuntimeError(f"Critical dependencies missing: {critical_missing}")
        
        logger.info("[SUCCESS] Dependencies check completed")
    
    async def _check_system_resources(self):
        """Check system resources"""
        logger.info("[SYSTEM] Checking system resources...")
        
        system_info = get_system_info()
        
        # Check memory
        memory_percent = system_info.get('memory_percent', 0)
        if memory_percent > 90:
            logger.warning(f"[WARN]  High memory usage: {memory_percent}%")
        
        # Check CPU
        cpu_percent = system_info.get('cpu_percent', 0)
        if cpu_percent > 90:
            logger.warning(f"[WARN]  High CPU usage: {cpu_percent}%")
        
        # Check disk space
        disk_usage = system_info.get('disk_usage', 0)
        if disk_usage > 90:
            logger.warning(f"[WARN]  Low disk space: {disk_usage}% used")
        
        logger.info(f"[SYSTEM] System: CPU {cpu_percent}%, Memory {memory_percent}%, Disk {disk_usage}%")
    
    async def _setup_custom_handlers(self):
        """Setup custom event handlers"""
        
        async def signal_handler(signal: TradingSignal):
            """Handle trading signals"""
            logger.info(f"[STATUS] SIGNAL: {signal.symbol} | {signal.strategy} | {signal.action} | "
                       f"Price: Rs.{signal.price:.2f} | Confidence: {signal.confidence:.2f}")
            
            # Add custom signal processing here
            # e.g., send to order management system, update database, etc.
        
        async def regime_change_handler(old_regime: Optional[MarketRegime], new_regime: MarketRegime):
            """Handle market regime changes"""
            old_regime_name = old_regime.regime if old_regime else "None"
            logger.info(f"[METRICS] REGIME CHANGE: {old_regime_name} -> {new_regime.regime} | "
                       f"Confidence: {new_regime.confidence:.2f} | "
                       f"Breadth: {new_regime.market_breadth:.1f}%")
            
            # Add custom regime change processing here
            # e.g., adjust strategy parameters, send alerts, etc.
        
        # Register handlers
        self.agent.add_signal_handler(signal_handler)
        self.agent.add_regime_change_handler(regime_change_handler)
        
        logger.info("[CONFIG] Custom handlers registered")
    
    async def _run_main_loop(self):
        """Run main monitoring loop"""
        try:
            # Check if this is part of a workflow (demo=False means full workflow)
            demo_mode = os.getenv('DEMO_MODE', 'true').lower() == 'true'
            workflow_mode = os.getenv('WORKFLOW_MODE', 'false').lower() == 'true'

            if not demo_mode and workflow_mode:
                # For full workflow mode, first download historical data, then start monitoring
                logger.info("[WORKFLOW] Running in full workflow mode - realistic duration")

                # Determine days_back based on testing mode
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                download_days_back = 5 if testing_mode else 35 # 5 days for testing, 35 for full

                # Determine max_symbols based on testing mode
                max_symbols_to_download = int(os.getenv('MAX_SYMBOLS', '500')) if not testing_mode else int(os.getenv('MAX_SYMBOLS', '20'))

                # Step 1: Download historical data for pre-market preparation
                logger.info(f"[PRE-MARKET] Starting historical data download for {download_days_back} days and {max_symbols_to_download} symbols...")
                download_success = await self.agent.download_live_historical_data(
                    days_back=download_days_back,
                    max_symbols=max_symbols_to_download,
                    testing_mode=testing_mode
                )

                if not download_success:
                    logger.error("[ERROR] Historical data download failed")
                    return

                logger.info("[SUCCESS] Historical data download completed")

                # Step 2: Start live monitoring (for remaining time)
                logger.info("[LIVE] Starting live market monitoring...")
                # Start agent's background tasks without blocking
                await self.agent.start_background_tasks()

                # Run for specified duration (reduced for faster workflow completion)
                timeout_duration = 30  # 30 seconds for live monitoring in paper trading workflow
                logger.info(f"[WORKFLOW] Running market monitoring for {timeout_duration} seconds...")

                try:
                    # Wait for the specified duration or shutdown signal
                    await asyncio.wait_for(self.shutdown_event.wait(), timeout=timeout_duration)
                    logger.info("[WORKFLOW] Market monitoring stopped by shutdown signal")
                except asyncio.TimeoutError:
                    logger.info(f"[WORKFLOW] Market monitoring completed {timeout_duration} second session")

                return

            # Original continuous mode for demo/standalone operation
            # Start agent's background tasks without blocking
            await self.agent.start_background_tasks()

            # Start health monitoring
            health_task = asyncio.create_task(self._health_monitoring_loop())

            # Wait for shutdown signal
            done, pending = await asyncio.wait(
                [health_task, asyncio.create_task(self.shutdown_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Cancel pending tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            logger.info("[STOP] Main monitoring loop completed")

        except Exception as e:
            logger.error(f"[ERROR] Error in main loop: {e}")
            raise
    
    async def _health_monitoring_loop(self):
        """Health monitoring loop"""
        while self.is_running:
            try:
                # Check agent health
                if self.agent:
                    metrics = self.agent.get_performance_metrics()
                    
                    # Log health status
                    active_signals = metrics.get('active_signals', 0)
                    subscribed_symbols = metrics.get('subscribed_symbols', 0)
                    regime = metrics.get('market_regime', 'unknown')
                    
                    logger.info(f"[HEALTH] Health: {active_signals} signals, "
                               f"{subscribed_symbols} symbols, regime: {regime}")
                    
                    # Check for issues
                    system_info = metrics.get('system_info', {})
                    memory_percent = system_info.get('memory_percent', 0)
                    
                    if memory_percent > 85:
                        logger.warning(f"[WARN]  High memory usage: {memory_percent}%")
                    
                    # Check WebSocket connection
                    if not self.agent.is_connected:
                        logger.warning("[WARN]  WebSocket disconnected")
                
                # Wait for next health check with shorter intervals in testing mode
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                health_interval = 10 if testing_mode else 60  # 10 seconds in testing, 60 in production
                await asyncio.sleep(health_interval)
                
            except Exception as e:
                logger.error(f"[ERROR] Error in health monitoring: {e}")
                await asyncio.sleep(60)
    
    async def run(self):
        """Main run method"""
        try:
            await self.start()
        except KeyboardInterrupt:
            logger.info("[STOP] Received keyboard interrupt")
        except Exception as e:
            logger.error(f"[ERROR] Runtime error: {e}")
            raise
        finally:
            await self.stop()

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/market_monitoring_runner.log')
        ]
    )

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Market Monitoring Agent Runner')
    parser.add_argument('--config', '-c', 
                       default='config/market_monitoring_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--log-level', '-l',
                       default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    parser.add_argument('--check-deps', action='store_true',
                       help='Check dependencies and exit')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # Check dependencies only
    if args.check_deps:
        deps = check_dependencies()
        print("\n📦 Dependency Status:")
        for dep, available in deps.items():
            status = "[SUCCESS]" if available else "[ERROR]"
            print(f"   {status} {dep}")
        
        missing = [dep for dep, available in deps.items() if not available]
        if missing:
            print(f"\n[ERROR] Missing dependencies: {missing}")
            sys.exit(1)
        else:
            print("\n[SUCCESS] All dependencies available")
            sys.exit(0)
    
    # Check if config file exists
    if not os.path.exists(args.config):
        logger.error(f"[ERROR] Configuration file not found: {args.config}")
        sys.exit(1)
    
    # Create and run agent
    runner = MarketMonitoringRunner(args.config)
    
    try:
        asyncio.run(runner.run())
    except KeyboardInterrupt:
        logger.info("[EXIT] Market Monitoring Agent stopped by user")
    except Exception as e:
        logger.error(f"[ERROR] Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

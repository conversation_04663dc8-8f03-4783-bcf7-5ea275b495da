# Enhanced Model Training Agent

A sophisticated machine learning system specifically designed to utilize output from the Enhanced Backtesting System. This agent trains multiple models to predict strategy performance, risk metrics, and optimal strategy selection.

## 🎯 Overview

The Enhanced Model Training Agent is optimized for Windows 10 and designed to work seamlessly with the `enhanced_backtesting_improved.py` output. It provides:

- **Multi-Task Learning**: Trains models for multiple prediction objectives simultaneously
- **Advanced ML Models**: LightGBM, XGBoost, CatBoost, and TabNet support
- **Automated Optimization**: Hyperparameter tuning with Optuna
- **Feature Engineering**: Automatic creation of derived features and interactions
- **Model Explainability**: SHAP integration for model interpretation
- **Ensemble Methods**: Weighted averaging and voting ensembles
- **GPU Acceleration**: Automatic GPU detection and utilization

## 🚀 Key Features

### Multi-Task Prediction Objectives

1. **Strategy Performance Prediction**
   - Sharpe ratio, ROI, profit factor prediction
   - Risk-adjusted return forecasting

2. **Risk Metrics Prediction**
   - Maximum drawdown estimation
   - Value at Risk (VaR) and Conditional VaR
   - Risk-adjusted performance metrics

3. **Strategy Consistency Analysis**
   - Consistency score prediction
   - Accuracy forecasting across time periods

4. **Profitability Classification**
   - Binary classification of profitable strategies
   - Risk criteria compliance prediction

5. **Market Regime Suitability**
   - Optimal market regime prediction
   - Strategy-regime matching

### Advanced ML Models

- **LightGBM**: Fast gradient boosting with categorical feature support
- **XGBoost**: Extreme gradient boosting with GPU acceleration
- **CatBoost**: Gradient boosting with automatic categorical handling
- **TabNet**: Deep learning for tabular data with attention mechanisms

### Feature Engineering

- **Interaction Features**: Automatic creation of feature interactions
- **Lag Features**: Time-series lag feature generation
- **Derived Metrics**: Performance ratios and risk-adjusted metrics
- **Outlier Handling**: IQR-based outlier detection and removal

## 📋 Requirements

### System Requirements

- **OS**: Windows 10 (optimized, but works on other platforms)
- **Python**: 3.8+ (3.9+ recommended)
- **Memory**: 8GB+ RAM (16GB+ recommended)
- **Storage**: 2GB+ free space for models and data

### Optional GPU Requirements

- **NVIDIA GPU** with CUDA 11.0+
- **CUDA Toolkit** 11.0+
- **cuDNN** 8.0+

## 🛠️ Installation

### Quick Setup

1. **Run the automated setup script:**
   ```bash
   python setup_enhanced_training.py
   ```

### Manual Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements_enhanced_training.txt
   ```

2. **Create directory structure:**
   ```bash
   mkdir -p data/models/enhanced
   mkdir -p data/models/registry/enhanced
   mkdir -p data/backtest_improved
   mkdir -p logs
   mkdir -p reports/enhanced_training
   ```

3. **Verify installation:**
   ```bash
   python -c "import lightgbm, xgboost, catboost, torch; print('All packages installed successfully')"
   ```

## 📊 Usage

### Basic Usage

```python
import asyncio
from agents.enhanced_model_training_agent import EnhancedModelTrainingAgent, EnhancedTrainingConfig

async def train_models():
    # Initialize agent
    config = EnhancedTrainingConfig()
    agent = EnhancedModelTrainingAgent(config)
    
    # Load backtesting data
    df = await agent.load_backtesting_data("data/backtest_improved/strategy_results.parquet")
    
    # Train all models
    results = await agent.train_all_models(df)
    
    # Print results
    for task_name, task_results in results.items():
        print(f"{task_name}: {task_results}")

# Run training
asyncio.run(train_models())
```

### Advanced Configuration

```python
# Custom configuration
config = EnhancedTrainingConfig()
config.enabled_models = ["lightgbm", "xgboost", "catboost"]
config.optuna_trials = 200
config.cv_folds = 10
config.feature_engineering_enabled = True
config.create_interaction_features = True

agent = EnhancedModelTrainingAgent(config)
```

### Making Predictions

```python
# Load trained models
await agent.load_models()

# Make predictions
predictions = await agent.predict(new_data, "strategy_performance", use_ensemble=True)
print(predictions)
```

## 🔧 Configuration

The system uses YAML configuration files for easy customization:

### Main Configuration (`config/enhanced_training_config.yaml`)

```yaml
# Prediction tasks
prediction_tasks:
  strategy_performance:
    type: "regression"
    target_columns: ["avg_sharpe_ratio", "avg_roi", "avg_profit_factor"]
    weight: 0.25

# Model configuration
models:
  enabled_models: ["lightgbm", "xgboost", "catboost", "tabnet"]
  ensemble_method: "weighted_average"

# Training configuration
training:
  optuna_trials: 100
  cv_folds: 5
  cv_strategy: "time_series"
```

### Key Configuration Options

- **`prediction_tasks`**: Define what to predict and how
- **`enabled_models`**: Choose which ML models to use
- **`feature_engineering`**: Control feature creation
- **`training`**: Set training parameters
- **`performance_thresholds`**: Define minimum performance criteria

## 📈 Input Data Format

The agent expects backtesting results with the following structure:

### Required Columns

```python
# Strategy identification
strategy_name: str

# Performance metrics
avg_sharpe_ratio: float
avg_roi: float
avg_profit_factor: float
avg_max_drawdown: float

# Trade statistics
avg_total_trades: int
avg_accuracy: float
consistency_score: float

# Risk metrics
avg_var_95: float
avg_cvar_95: float

# Walk-forward analysis results
walk_forward_steps: int
```

### Optional Columns

```python
# Additional performance metrics
avg_sortino_ratio: float
avg_calmar_ratio: float

# Trade details
avg_win: float
avg_loss: float
avg_holding_period: float

# Market regime performance
regime_performance: dict
```

## 🎯 Output and Results

### Training Results

The agent produces comprehensive training results:

```python
{
    "strategy_performance": {
        "lightgbm": {
            "test_score": 0.8234,
            "r2_score": 0.8234,
            "mse": 0.1234,
            "mae": 0.2345
        },
        "xgboost": {
            "test_score": 0.8156,
            # ... more metrics
        }
    },
    "profitability_classification": {
        "lightgbm": {
            "test_score": 0.9123,
            "accuracy": 0.9123,
            "precision": 0.8956,
            "recall": 0.9234,
            "f1_score": 0.9089
        }
    }
}
```

### Feature Importance

```python
{
    "strategy_performance": {
        "lightgbm": [
            {"feature": "avg_sharpe_ratio", "importance": 0.2345},
            {"feature": "consistency_score", "importance": 0.1876},
            {"feature": "avg_accuracy", "importance": 0.1654}
        ]
    }
}
```

### Model Files

Trained models are automatically saved:

```
data/models/enhanced/
├── strategy_performance/
│   ├── lightgbm_20241201_143022.joblib
│   ├── xgboost_20241201_143022.joblib
│   └── catboost_20241201_143022.joblib
├── scalers_20241201_143022.joblib
└── training_results_20241201_143022.json
```

## 🔍 Model Explainability

### SHAP Integration

```python
# Enable SHAP analysis
config.shap_enabled = True

# SHAP values are automatically generated
shap_values = agent.get_shap_values("strategy_performance", test_data)
```

### Feature Importance Analysis

```python
# Get feature importance
importance = agent.feature_importance["strategy_performance"]["lightgbm"]

# Top 10 features
for i, feature in enumerate(importance[:10]):
    print(f"{i+1}. {feature['feature']}: {feature['importance']:.4f}")
```

## 🚀 Performance Optimization

### GPU Acceleration

The agent automatically detects and uses GPU acceleration:

- **XGBoost**: `tree_method='gpu_hist'`
- **CatBoost**: `task_type='GPU'`
- **TabNet**: `device_name='cuda'`

### Memory Optimization

- **Polars**: Fast data processing with minimal memory usage
- **Incremental Loading**: Process large datasets in chunks
- **Model Compression**: Automatic model size optimization

### Parallel Processing

- **Multi-threading**: Parallel model training
- **Cross-validation**: Parallel fold processing
- **Hyperparameter Optimization**: Parallel trial execution

## 🧪 Testing and Validation

### Run Example

```bash
python examples/enhanced_training_example.py
```

### Unit Tests

```bash
pytest tests/test_enhanced_training.py -v
```

### Performance Benchmarks

```bash
python benchmarks/training_performance.py
```

## 📊 Monitoring and Logging

### Logging Configuration

```python
# Enable detailed logging
logging.basicConfig(level=logging.INFO)

# Log to file
config.log_to_file = True
config.log_file = "logs/enhanced_training.log"
```

### Progress Tracking

```python
# Enable progress bars
config.progress_bar = True

# Detailed metrics logging
config.detailed_metrics = True
```

## 🔧 Troubleshooting

### Common Issues

1. **GPU Not Detected**
   ```bash
   # Check CUDA installation
   nvidia-smi
   python -c "import torch; print(torch.cuda.is_available())"
   ```

2. **Memory Issues**
   ```python
   # Reduce batch size
   config.tabnet_params["batch_size"] = 512
   
   # Enable memory efficient mode
   config.memory_efficient_mode = True
   ```

3. **Package Installation Issues**
   ```bash
   # Update pip
   python -m pip install --upgrade pip
   
   # Install with no cache
   pip install --no-cache-dir -r requirements_enhanced_training.txt
   ```

### Performance Issues

1. **Slow Training**
   - Enable GPU acceleration
   - Reduce `optuna_trials`
   - Use fewer CV folds
   - Enable parallel processing

2. **Poor Model Performance**
   - Increase `optuna_trials`
   - Enable feature engineering
   - Check data quality
   - Adjust performance thresholds

## 📚 API Reference

### EnhancedModelTrainingAgent

```python
class EnhancedModelTrainingAgent:
    def __init__(self, config: EnhancedTrainingConfig)
    
    async def load_backtesting_data(self, file_path: str) -> pl.DataFrame
    async def train_all_models(self, df: pl.DataFrame) -> Dict[str, Any]
    async def predict(self, data: pd.DataFrame, task_name: str, use_ensemble: bool = True) -> Dict[str, Any]
    async def load_models(self, timestamp: str = None)
```

### EnhancedTrainingConfig

```python
@dataclass
class EnhancedTrainingConfig:
    # Data configuration
    data_dir: str = "data/backtest_improved"
    input_file: str = "enhanced_strategy_results.parquet"
    
    # Model configuration
    enabled_models: List[str] = ["lightgbm", "xgboost"]
    ensemble_method: str = "weighted_average"
    
    # Training configuration
    optuna_trials: int = 100
    cv_folds: int = 5
    
    # Feature engineering
    feature_engineering_enabled: bool = True
    create_interaction_features: bool = True
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **LightGBM Team**: For the excellent gradient boosting framework
- **XGBoost Team**: For the high-performance ML library
- **CatBoost Team**: For categorical feature handling
- **TabNet Authors**: For attention-based tabular learning
- **Optuna Team**: For automated hyperparameter optimization

## 📞 Support

For support and questions:

1. Check the troubleshooting section
2. Review the examples
3. Check existing issues
4. Create a new issue with detailed information

---

**Happy Trading! 🚀📈**
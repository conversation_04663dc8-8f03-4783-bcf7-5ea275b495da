#!/usr/bin/env python3
"""
Check columns in feature files
"""

import polars as pl
from pathlib import Path

def main():
    # Check a sample feature file
    feature_file = "data/features/features_RELIANCE_1min.parquet"
    
    if Path(feature_file).exists():
        df = pl.read_parquet(feature_file)
        print(f"Columns in {feature_file}:")
        print(f"Total columns: {len(df.columns)}")
        print("\nColumn list:")
        for i, col in enumerate(sorted(df.columns), 1):
            print(f"{i:2d}. {col}")
        
        print(f"\nDataFrame shape: {df.shape}")
        print(f"Sample data (first 3 rows):")
        print(df.head(3))
    else:
        print(f"File not found: {feature_file}")

if __name__ == "__main__":
    main()
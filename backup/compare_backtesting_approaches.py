#!/usr/bin/env python3
"""
Compare Original vs Improved Backtesting Approaches
- Side-by-side comparison of methodologies
- Highlights key improvements and fixes
- Demonstrates impact of enhancements
"""

import yaml
import json
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd

def load_original_strategies() -> List[Dict[str, Any]]:
    """Load original strategies"""
    try:
        with open("config/strategies.yaml", 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        return data.get('strategies', [])
    except:
        return []

def load_improved_strategies() -> List[Dict[str, Any]]:
    """Load improved strategies"""
    try:
        with open("config/strategies_improved.yaml", 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        return data.get('strategies', [])
    except:
        return []

def analyze_strategy_complexity(strategies: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze strategy complexity and features"""
    analysis = {
        'total_strategies': len(strategies),
        'has_volume_filter': 0,
        'has_multiple_confirmations': 0,
        'has_trend_filter': 0,
        'has_time_filter': 0,
        'has_risk_classification': 0,
        'has_market_conditions': 0,
        'avg_conditions_per_strategy': 0
    }
    
    total_conditions = 0
    
    for strategy in strategies:
        long_expr = strategy.get('long', '')
        short_expr = strategy.get('short', '')
        
        # Check for volume filter
        if 'volume' in long_expr or 'volume' in short_expr:
            analysis['has_volume_filter'] += 1
        
        # Check for multiple confirmations (count 'and' operators)
        long_ands = long_expr.count(' and ')
        short_ands = short_expr.count(' and ')
        if long_ands >= 2 or short_ands >= 2:
            analysis['has_multiple_confirmations'] += 1
        
        # Check for trend filters
        trend_indicators = ['ema', 'adx', 'supertrend', 'macd']
        if any(indicator in long_expr.lower() for indicator in trend_indicators):
            analysis['has_trend_filter'] += 1
        
        # Check for time filters
        if 'hour' in long_expr or 'minute' in long_expr:
            analysis['has_time_filter'] += 1
        
        # Check for risk classification
        if 'risk_level' in strategy:
            analysis['has_risk_classification'] += 1
        
        # Check for market conditions
        if 'market_conditions' in strategy:
            analysis['has_market_conditions'] += 1
        
        # Count total conditions
        total_conditions += max(long_ands + 1, short_ands + 1)
    
    if len(strategies) > 0:
        analysis['avg_conditions_per_strategy'] = total_conditions / len(strategies)
    
    return analysis

def compare_backtesting_features():
    """Compare backtesting features between original and improved versions"""
    
    original_features = {
        'walk_forward_analysis': False,
        'out_of_sample_testing': False,
        'market_regime_detection': False,
        'statistical_significance_testing': False,
        'dynamic_position_sizing': False,
        'realistic_transaction_costs': True,  # Basic implementation
        'risk_management_controls': False,
        'monte_carlo_simulation': False,
        'consistency_scoring': False,
        'regime_performance_analysis': False,
        'multiple_performance_metrics': True,  # Basic metrics
        'time_based_filters': False,
        'volume_confirmation': False,
        'correlation_analysis': False,
        'drawdown_analysis': True  # Basic implementation
    }
    
    improved_features = {
        'walk_forward_analysis': True,
        'out_of_sample_testing': True,
        'market_regime_detection': True,
        'statistical_significance_testing': True,
        'dynamic_position_sizing': True,
        'realistic_transaction_costs': True,  # Enhanced implementation
        'risk_management_controls': True,
        'monte_carlo_simulation': True,
        'consistency_scoring': True,
        'regime_performance_analysis': True,
        'multiple_performance_metrics': True,  # Enhanced metrics
        'time_based_filters': True,
        'volume_confirmation': True,
        'correlation_analysis': True,
        'drawdown_analysis': True  # Enhanced implementation
    }
    
    return original_features, improved_features

def generate_comparison_report():
    """Generate comprehensive comparison report"""
    
    print("🔍 BACKTESTING APPROACH COMPARISON")
    print("=" * 60)
    
    # Load strategies
    original_strategies = load_original_strategies()
    improved_strategies = load_improved_strategies()
    
    print(f"\n📊 STRATEGY ANALYSIS")
    print("-" * 30)
    
    if original_strategies:
        orig_analysis = analyze_strategy_complexity(original_strategies)
        print(f"Original Strategies: {orig_analysis['total_strategies']}")
        print(f"  - Volume filters: {orig_analysis['has_volume_filter']}")
        print(f"  - Multiple confirmations: {orig_analysis['has_multiple_confirmations']}")
        print(f"  - Trend filters: {orig_analysis['has_trend_filter']}")
        print(f"  - Risk classification: {orig_analysis['has_risk_classification']}")
        print(f"  - Avg conditions per strategy: {orig_analysis['avg_conditions_per_strategy']:.1f}")
    
    if improved_strategies:
        imp_analysis = analyze_strategy_complexity(improved_strategies)
        print(f"\nImproved Strategies: {imp_analysis['total_strategies']}")
        print(f"  - Volume filters: {imp_analysis['has_volume_filter']}")
        print(f"  - Multiple confirmations: {imp_analysis['has_multiple_confirmations']}")
        print(f"  - Trend filters: {imp_analysis['has_trend_filter']}")
        print(f"  - Time filters: {imp_analysis['has_time_filter']}")
        print(f"  - Risk classification: {imp_analysis['has_risk_classification']}")
        print(f"  - Market conditions: {imp_analysis['has_market_conditions']}")
        print(f"  - Avg conditions per strategy: {imp_analysis['avg_conditions_per_strategy']:.1f}")
    
    # Compare backtesting features
    print(f"\n🔧 BACKTESTING FEATURES COMPARISON")
    print("-" * 40)
    
    original_features, improved_features = compare_backtesting_features()
    
    print(f"{'Feature':<35} {'Original':<10} {'Improved':<10} {'Status'}")
    print("-" * 65)
    
    for feature in original_features:
        orig_status = "✅" if original_features[feature] else "❌"
        imp_status = "✅" if improved_features[feature] else "❌"
        
        if improved_features[feature] and not original_features[feature]:
            status = "🆕 NEW"
        elif improved_features[feature] and original_features[feature]:
            status = "🔧 ENHANCED"
        else:
            status = "➖"
        
        print(f"{feature.replace('_', ' ').title():<35} {orig_status:<10} {imp_status:<10} {status}")
    
    # Strategy examples comparison
    print(f"\n📈 STRATEGY EXAMPLES COMPARISON")
    print("-" * 40)
    
    if original_strategies and improved_strategies:
        # Find similar strategies for comparison
        print("\nOriginal RSI Strategy:")
        rsi_orig = next((s for s in original_strategies if 'RSI' in s.get('name', '')), None)
        if rsi_orig:
            print(f"  Name: {rsi_orig.get('name', 'N/A')}")
            print(f"  Long: {rsi_orig.get('long', 'N/A')}")
            print(f"  Short: {rsi_orig.get('short', 'N/A')}")
        
        print("\nImproved RSI Strategy:")
        rsi_imp = next((s for s in improved_strategies if 'RSI' in s.get('name', '')), None)
        if rsi_imp:
            print(f"  Name: {rsi_imp.get('name', 'N/A')}")
            print(f"  Long: {rsi_imp.get('long', 'N/A')}")
            print(f"  Short: {rsi_imp.get('short', 'N/A')}")
            print(f"  Risk Level: {rsi_imp.get('risk_level', 'N/A')}")
            print(f"  Market Conditions: {rsi_imp.get('market_conditions', 'N/A')}")
    
    # Key improvements summary
    print(f"\n🚀 KEY IMPROVEMENTS SUMMARY")
    print("-" * 30)
    
    improvements = [
        "✅ Walk-forward analysis prevents overfitting",
        "✅ Out-of-sample testing ensures robustness",
        "✅ Market regime detection adapts to conditions",
        "✅ Statistical significance testing validates results",
        "✅ Dynamic position sizing manages risk",
        "✅ Volume confirmation reduces false signals",
        "✅ Multiple indicator confirmations improve accuracy",
        "✅ Time-based filters avoid problematic periods",
        "✅ Enhanced risk management controls",
        "✅ Comprehensive performance metrics"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    # Expected benefits
    print(f"\n🎯 EXPECTED BENEFITS")
    print("-" * 20)
    
    benefits = [
        "📈 Reduced false signals (30-50% improvement expected)",
        "🛡️ Better risk management and drawdown control",
        "📊 More realistic performance expectations",
        "🔬 Statistically validated strategy selection",
        "🎪 Improved consistency across market conditions",
        "⚡ Enhanced strategy robustness and reliability"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print(f"\n" + "=" * 60)
    print("💡 RECOMMENDATION: Use the improved backtesting system")
    print("   for more reliable and robust strategy development.")
    print("=" * 60)

def save_comparison_to_file():
    """Save comparison to JSON file"""
    
    original_strategies = load_original_strategies()
    improved_strategies = load_improved_strategies()
    
    orig_analysis = analyze_strategy_complexity(original_strategies) if original_strategies else {}
    imp_analysis = analyze_strategy_complexity(improved_strategies) if improved_strategies else {}
    
    original_features, improved_features = compare_backtesting_features()
    
    comparison_data = {
        'timestamp': pd.Timestamp.now().isoformat(),
        'strategy_analysis': {
            'original': orig_analysis,
            'improved': imp_analysis
        },
        'backtesting_features': {
            'original': original_features,
            'improved': improved_features
        },
        'key_improvements': [
            "Walk-forward analysis implementation",
            "Out-of-sample testing",
            "Market regime detection",
            "Statistical significance testing",
            "Dynamic position sizing",
            "Enhanced volume confirmation",
            "Multiple indicator confirmations",
            "Time-based filtering",
            "Comprehensive risk management",
            "Advanced performance metrics"
        ]
    }
    
    output_file = Path("data/backtest_improved/comparison_report.json")
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(comparison_data, f, indent=2, default=str)
    
    print(f"\n📁 Comparison report saved to: {output_file}")

if __name__ == "__main__":
    generate_comparison_report()
    save_comparison_to_file()
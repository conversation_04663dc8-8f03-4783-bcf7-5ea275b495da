#!/usr/bin/env python3
"""
Complete Backtesting + ML Training Workflow
Demonstrates the full pipeline from backtesting to model training and prediction

This script shows how to:
1. Run enhanced backtesting on strategies
2. Train ML models on backtesting results
3. Use trained models for strategy selection and optimization
4. Generate actionable trading insights
"""

import asyncio
import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import yaml

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Import our enhanced agents
from agents.enhanced_backtesting_improved import EnhancedBacktester, BacktestConfig
from agents.enhanced_model_training_agent import EnhancedModelTrainingAgent, EnhancedTrainingConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/complete_workflow.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteTradingWorkflow:
    """Complete workflow from backtesting to ML-driven strategy selection"""
    
    def __init__(self):
        self.backtest_config = BacktestConfig()
        self.training_config = EnhancedTrainingConfig()
        self.backtester = None
        self.ml_agent = None
        self.results = {}
        
    async def initialize(self):
        """Initialize the workflow components"""
        logger.info("🚀 Initializing Complete Trading Workflow")
        
        # Initialize backtester
        self.backtester = EnhancedBacktester(self.backtest_config)
        logger.info("✓ Enhanced Backtester initialized")
        
        # Initialize ML training agent with optimized settings for small datasets
        self.training_config.optuna_trials = 10  # Reduced for demo
        self.training_config.enabled_models = ["lightgbm"]  # Use only one model for demo
        self.training_config.create_lag_features = False  # Disable lag features for small dataset
        self.training_config.create_interaction_features = False  # Disable interaction features
        self.training_config.cv_folds = 2  # Reduce CV folds for small dataset
        self.training_config.optuna_timeout = 300  # 5 minutes timeout
        self.ml_agent = EnhancedModelTrainingAgent(self.training_config)
        logger.info("✓ Enhanced ML Training Agent initialized")
        
        # Create necessary directories
        for directory in ["data/backtest_improved", "data/models/enhanced", "logs", "reports"]:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        logger.info("✓ Workflow initialization complete")
    
    async def run_backtesting_phase(self, data_files: list = None):
        """Run the backtesting phase on available data"""
        logger.info("📊 Starting Backtesting Phase")
        
        if not data_files:
            # Look for available data files
            data_dir = Path("data/features")
            if data_dir.exists():
                data_files = list(data_dir.glob("*.parquet"))
                if not data_files:
                    data_files = list(data_dir.glob("*.csv"))
            
            if not data_files:
                logger.warning("No data files found, creating sample data for demonstration")
                await self._create_sample_data()
                data_files = [Path("data/features/sample_data.parquet")]
        
        logger.info(f"Found {len(data_files)} data files for backtesting")
        
        # Run ACTUAL backtesting using the enhanced backtester
        all_results = []
        
        # Limit to first 10 files for demo to avoid long processing time
        limited_files = data_files[:10]
        logger.info(f"Processing {len(limited_files)} files for demonstration")
        
        for i, file_path in enumerate(limited_files):
            logger.info(f"Processing file {i+1}/{len(limited_files)}: {file_path.name}")
            
            # Extract symbol and timeframe from filename
            filename = file_path.name
            parts = filename.replace("features_", "").replace(".parquet", "").split("_")
            if len(parts) >= 2:
                timeframe = parts[-1]
                symbol = "_".join(parts[:-1])
                
                try:
                    # Run actual backtesting
                    result = await self.backtester.run_enhanced_backtest(symbol, timeframe, str(file_path))
                    if result and 'results' in result:
                        # Flatten the results for ML training
                        for strategy_result in result['results']:
                            all_results.append(strategy_result)
                            
                except Exception as e:
                    logger.error(f"Error backtesting {file_path}: {e}")
                    continue
        
        if not all_results:
            logger.warning("No backtesting results generated, falling back to simulated data")
            # Fallback to simulated data if real backtesting fails
            strategies = self.backtester.load_strategies()
            if not strategies:
                strategies = self._create_sample_strategies()
            
            all_strategies = strategies + self._generate_additional_strategies(50)
            for strategy in all_strategies[:60]:
                result = self._simulate_backtest_result(strategy)
                all_results.append(result)
        
        # Save backtesting results
        results_df = pd.DataFrame(all_results)
        output_file = Path("data/backtest_improved/strategy_results.parquet")
        results_df.to_parquet(output_file)
        
        logger.info(f"✓ Backtesting complete. Results saved to {output_file}")
        logger.info(f"  Generated {len(results_df)} strategy results")
        
        return output_file
    
    async def run_ml_training_phase(self, backtest_results_file: Path):
        """Run the ML training phase on backtesting results"""
        logger.info("🧠 Starting ML Training Phase")
        
        # Load backtesting data
        df = await self.ml_agent.load_backtesting_data(str(backtest_results_file))
        logger.info(f"✓ Loaded backtesting data: {df.height} rows, {df.width} columns")
        
        # Train all models
        training_results = await self.ml_agent.train_all_models(df)
        
        # Store results
        self.results['training_results'] = training_results
        
        # Generate summary
        self._generate_training_summary(training_results)
        
        logger.info("✓ ML Training phase complete")
        return training_results
    
    def _generate_training_summary(self, training_results: dict):
        """Generate a summary of training results"""
        logger.info("📊 Training Results Summary:")
        
        for task_name, task_results in training_results.items():
            logger.info(f"  Task: {task_name}")
            
            if 'models' in task_results:
                for model_name, model_results in task_results['models'].items():
                    if 'best_score' in model_results:
                        logger.info(f"    {model_name}: {model_results['best_score']:.4f}")
            
            if 'ensemble_score' in task_results:
                logger.info(f"    Ensemble Score: {task_results['ensemble_score']:.4f}")
        
        logger.info("✓ Training summary generated")
    
    async def run_prediction_phase(self, new_strategies_data: pd.DataFrame = None):
        """Use trained models to predict strategy performance"""
        logger.info("🔮 Starting Prediction Phase")
        
        if new_strategies_data is None:
            # Create sample new strategies for prediction
            new_strategies_data = self._create_sample_prediction_data()
        
        # Make predictions for each task
        predictions = {}
        
        # Filter out non-numeric columns for prediction
        numeric_data = new_strategies_data.select_dtypes(include=[np.number])
        logger.info(f"Using {len(numeric_data.columns)} numeric features for prediction")
        
        for task_name in self.training_config.prediction_tasks.keys():
            try:
                task_predictions = await self.ml_agent.predict(
                    numeric_data, 
                    task_name, 
                    use_ensemble=True
                )
                predictions[task_name] = task_predictions
                logger.info(f"✓ Generated predictions for {task_name}")
            except Exception as e:
                logger.error(f"Prediction failed for task {task_name}: {e}")
                predictions[task_name] = {}  # Empty predictions on error
        
        # Generate actionable insights
        insights = self._generate_trading_insights(predictions, new_strategies_data)
        
        logger.info("✓ Prediction phase complete")
        return predictions, insights
    
    def _simulate_backtest_result(self, strategy: dict) -> dict:
        """Simulate backtesting results for demonstration"""
        np.random.seed(hash(strategy.get('name', 'default')) % 2**32)
        
        # Generate realistic but random results
        base_sharpe = np.random.normal(0.8, 1.2)
        base_roi = np.random.normal(8.0, 20.0)
        
        return {
            'strategy_name': strategy.get('name', f'Strategy_{np.random.randint(1000)}'),
            'avg_sharpe_ratio': max(-2.0, min(3.0, base_sharpe)),
            'avg_roi': base_roi,
            'avg_profit_factor': max(0.5, np.random.lognormal(0.3, 0.4)),
            'avg_max_drawdown': -abs(np.random.normal(12.0, 8.0)),
            'avg_var_95': -abs(np.random.normal(3.0, 2.0)),
            'avg_cvar_95': -abs(np.random.normal(4.5, 2.5)),
            'consistency_score': np.random.beta(2, 2),
            'avg_accuracy': np.random.beta(3, 2) * 100,
            'avg_total_trades': max(10, int(np.random.poisson(80))),
            'walk_forward_steps': np.random.randint(3, 8),
            'avg_win': abs(np.random.exponential(150)),
            'avg_loss': -abs(np.random.exponential(120)),
            'passes_risk_criteria': (
                base_sharpe >= 1.0 and 
                base_roi >= 5.0 and 
                abs(np.random.normal(12.0, 8.0)) <= 15.0
            )
        }
    
    def _create_sample_strategies(self) -> list:
        """Create sample strategies for demonstration"""
        return [
            {
                'name': 'RSI_Mean_Reversion',
                'long': 'rsi_14 < 30 and close > ema_20',
                'short': 'rsi_14 > 70 and close < ema_20',
                'market_conditions': ['ranging', 'low_volatility']
            },
            {
                'name': 'MACD_Momentum',
                'long': 'macd > macd_signal and macd_signal > 0',
                'short': 'macd < macd_signal and macd_signal < 0',
                'market_conditions': ['trending_up', 'trending_down']
            },
            {
                'name': 'Bollinger_Breakout',
                'long': 'close > bb_upper and volume > sma_20_volume',
                'short': 'close < bb_lower and volume > sma_20_volume',
                'market_conditions': ['high_volatility']
            },
            {
                'name': 'EMA_Crossover',
                'long': 'ema_5 > ema_20 and ema_20 > ema_50',
                'short': 'ema_5 < ema_20 and ema_20 < ema_50',
                'market_conditions': ['trending_up', 'trending_down']
            },
            {
                'name': 'Stochastic_Oscillator',
                'long': 'stoch_k > stoch_d and stoch_k < 80',
                'short': 'stoch_k < stoch_d and stoch_k > 20',
                'market_conditions': ['ranging']
            }
        ]
    
    def _generate_additional_strategies(self, count: int) -> list:
        """Generate additional synthetic strategies for better ML training"""
        strategies = []
        
        # Strategy templates
        indicators = ['rsi_14', 'macd', 'ema_20', 'bb_upper', 'bb_lower', 'stoch_k', 'stoch_d', 'volume']
        conditions = ['>', '<', '>=', '<=']
        market_conditions = [
            ['trending_up'], ['trending_down'], ['ranging'], 
            ['high_volatility'], ['low_volatility'],
            ['trending_up', 'high_volatility'], ['trending_down', 'low_volatility']
        ]
        
        for i in range(count):
            # Generate random strategy
            ind1, ind2 = np.random.choice(indicators, 2, replace=False)
            cond1, cond2 = np.random.choice(conditions, 2)
            
            strategy = {
                'name': f'Generated_Strategy_{i+1:03d}',
                'long': f'{ind1} {cond1} {ind2}',
                'short': f'{ind1} {cond2} {ind2}',
                'market_conditions': market_conditions[np.random.randint(len(market_conditions))]
            }
            strategies.append(strategy)
        
        return strategies
    
    async def _create_sample_data(self):
        """Create sample market data for demonstration"""
        logger.info("Creating sample market data...")
        
        # Generate sample OHLCV data
        np.random.seed(42)
        n_days = 252  # One year of trading days
        
        # Generate realistic price data
        initial_price = 100.0
        returns = np.random.normal(0.0005, 0.02, n_days)  # Daily returns
        prices = [initial_price]
        
        for ret in returns:
            prices.append(prices[-1] * (1 + ret))
        
        prices = np.array(prices[1:])
        
        # Generate OHLCV
        data = {
            'datetime': pd.date_range('2023-01-01', periods=n_days, freq='D'),
            'open': prices * np.random.uniform(0.995, 1.005, n_days),
            'high': prices * np.random.uniform(1.005, 1.025, n_days),
            'low': prices * np.random.uniform(0.975, 0.995, n_days),
            'close': prices,
            'volume': np.random.lognormal(10, 1, n_days).astype(int)
        }
        
        df = pd.DataFrame(data)
        
        # Add technical indicators (simplified)
        df['rsi_14'] = 50 + 30 * np.sin(np.arange(n_days) * 0.1) + np.random.normal(0, 5, n_days)
        df['ema_20'] = df['close'].ewm(span=20).mean()
        df['macd'] = df['close'].ewm(span=12).mean() - df['close'].ewm(span=26).mean()
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['bb_upper'] = df['close'].rolling(20).mean() + 2 * df['close'].rolling(20).std()
        df['bb_lower'] = df['close'].rolling(20).mean() - 2 * df['close'].rolling(20).std()
        
        # Save sample data
        output_file = Path("data/features/sample_data.parquet")
        output_file.parent.mkdir(parents=True, exist_ok=True)
        df.to_parquet(output_file)
        
        logger.info(f"✓ Sample data created: {output_file}")
    
    def _create_sample_prediction_data(self) -> pd.DataFrame:
        """Create sample data for predictions with same features as training data"""
        np.random.seed(123)
        n_strategies = 20
        
        # Create base features
        base_data = {
            'strategy_name': [f'NewStrategy_{i:03d}' for i in range(n_strategies)],
            'avg_sharpe_ratio': np.random.normal(0.6, 0.9, n_strategies),
            'avg_roi': np.random.normal(6.0, 18.0, n_strategies),
            'avg_profit_factor': np.random.lognormal(0.25, 0.45, n_strategies),
            'avg_max_drawdown': -np.abs(np.random.normal(10.0, 6.0, n_strategies)),
            'consistency_score': np.random.beta(2.5, 2.5, n_strategies),
            'avg_accuracy': np.random.beta(3.5, 2.5, n_strategies) * 100,
            'avg_total_trades': np.random.poisson(70, n_strategies),
            'walk_forward_steps': np.random.randint(3, 8, n_strategies),
            'avg_win': np.abs(np.random.exponential(150, n_strategies)),
            'avg_loss': -np.abs(np.random.exponential(120, n_strategies)),
            'avg_var_95': -np.abs(np.random.normal(3.0, 2.0, n_strategies)),
            'avg_cvar_95': -np.abs(np.random.normal(4.5, 2.5, n_strategies)),
            'passes_risk_criteria': np.random.choice([True, False], n_strategies, p=[0.3, 0.7])
        }
        
        df = pd.DataFrame(base_data)
        
        # Add derived features that match training data
        # Win-loss ratio
        df['win_loss_ratio'] = df['avg_win'] / df['avg_loss'].abs()
        
        # ROI-drawdown ratio
        df['roi_drawdown_ratio'] = df['avg_roi'] / df['avg_max_drawdown'].abs()
        
        # Trades per period
        df['trades_per_period'] = df['avg_total_trades'] / df['walk_forward_steps']
        
        return df
    
    def _generate_training_summary(self, training_results: dict):
        """Generate a summary of training results"""
        logger.info("\n" + "="*60)
        logger.info("🎯 ML TRAINING RESULTS SUMMARY")
        logger.info("="*60)
        
        for task_name, task_results in training_results.items():
            if "error" in task_results:
                logger.error(f"❌ {task_name}: {task_results['error']}")
                continue
            
            logger.info(f"\n📊 {task_name.upper().replace('_', ' ')}:")
            
            best_model = None
            best_score = float('-inf')
            
            for model_name, metrics in task_results.items():
                if isinstance(metrics, dict) and "test_score" in metrics:
                    score = metrics["test_score"]
                    if isinstance(score, (int, float)) and score > best_score:
                        best_score = score
                        best_model = model_name
                    
                    logger.info(f"   {model_name:12}: {score:.4f}")
            
            if best_model:
                logger.info(f"   🏆 Best: {best_model} ({best_score:.4f})")
        
        logger.info("\n" + "="*60)
    
    def _generate_trading_insights(self, predictions: dict, strategies_data: pd.DataFrame) -> dict:
        """Generate actionable trading insights from predictions"""
        insights = {
            'top_strategies': [],
            'risk_warnings': [],
            'recommendations': []
        }
        
        # Analyze predictions to generate insights
        if 'sharpe_ratio_prediction' in predictions:
            sharpe_preds = predictions['sharpe_ratio_prediction']
            if 'lightgbm' in sharpe_preds:
                top_indices = np.argsort(sharpe_preds['lightgbm'])[-5:]  # Top 5
                top_strategies = strategies_data.iloc[top_indices]['strategy_name'].tolist()
                insights['top_strategies'] = top_strategies
        
        # Add risk warnings
        if 'drawdown_prediction' in predictions:
            dd_preds = predictions['drawdown_prediction']
            if 'lightgbm' in dd_preds:
                high_risk_indices = np.where(np.array(dd_preds['lightgbm']) < -20)[0]
                if len(high_risk_indices) > 0:
                    high_risk_strategies = strategies_data.iloc[high_risk_indices]['strategy_name'].tolist()
                    insights['risk_warnings'] = high_risk_strategies
        
        # Generate recommendations
        insights['recommendations'] = [
            "Focus on strategies with predicted Sharpe ratio > 1.0",
            "Avoid strategies with predicted drawdown > 15%",
            "Consider ensemble approach with top 3-5 strategies",
            "Monitor model performance and retrain monthly"
        ]
        
        return insights
    
    async def generate_final_report(self, predictions: dict, insights: dict):
        """Generate final comprehensive report"""
        logger.info("📋 Generating Final Report")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'workflow_summary': {
                'backtesting_completed': True,
                'ml_training_completed': True,
                'predictions_generated': len(predictions) > 0
            },
            'model_performance': self.results.get('training_results', {}),
            'predictions': predictions,
            'insights': insights,
            'next_steps': [
                "Deploy top-performing strategies in paper trading",
                "Set up automated model retraining pipeline",
                "Implement real-time prediction serving",
                "Monitor strategy performance and model drift"
            ]
        }
        
        # Save report
        report_file = Path("reports/complete_workflow_report.yaml")
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w') as f:
            yaml.dump(report, f, default_flow_style=False)
        
        logger.info(f"✓ Final report saved to {report_file}")
        
        # Print summary
        logger.info("\n" + "🎉 WORKFLOW COMPLETE! 🎉".center(60, "="))
        logger.info(f"📊 Top Strategies: {insights.get('top_strategies', [])[:3]}")
        logger.info(f"⚠️  Risk Warnings: {len(insights.get('risk_warnings', []))} strategies")
        logger.info(f"💡 Recommendations: {len(insights.get('recommendations', []))} generated")
        logger.info("="*60)

async def quick_demo():
    """Quick demo with simulated data for testing"""
    logger.info("🚀 Starting Quick Demo with Simulated Data")
    
    workflow = CompleteTradingWorkflow()
    await workflow.initialize()
    
    try:
        # Create sample data for demo
        await workflow._create_sample_data()
        
        # Run with simulated backtesting for speed
        strategies = workflow.backtester.load_strategies()
        if not strategies:
            strategies = workflow._create_sample_strategies()
        
        # Generate more strategies for better ML training
        all_strategies = strategies + workflow._generate_additional_strategies(100)
        
        backtest_results = []
        for strategy in all_strategies[:150]:  # Use 150 strategies for good ML training
            result = workflow._simulate_backtest_result(strategy)
            backtest_results.append(result)
        
        # Save results
        results_df = pd.DataFrame(backtest_results)
        output_file = Path("data/backtest_improved/strategy_results.parquet")
        results_df.to_parquet(output_file)
        
        logger.info(f"✓ Generated {len(results_df)} simulated backtest results")
        
        # Train ML models
        training_results = await workflow.run_ml_training_phase(output_file)
        
        # Generate predictions
        predictions, insights = await workflow.run_prediction_phase()
        
        # Generate final report
        await workflow.generate_final_report(predictions, insights)
        
        logger.info("🎉 Quick demo completed successfully!")
        
    except Exception as e:
        logger.error(f"Quick demo failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main workflow execution"""
    workflow = CompleteTradingWorkflow()
    
    try:
        # Initialize
        await workflow.initialize()
        
        # Phase 1: Backtesting
        backtest_results_file = await workflow.run_backtesting_phase()
        
        # Phase 2: ML Training
        training_results = await workflow.run_ml_training_phase(backtest_results_file)
        
        # Phase 3: Predictions and Insights
        predictions, insights = await workflow.run_prediction_phase()
        
        # Phase 4: Final Report
        await workflow.generate_final_report(predictions, insights)
        
        logger.info("🚀 Complete workflow executed successfully!")
        
    except Exception as e:
        logger.error(f"Workflow failed: {e}")
        import traceback
        traceback.print_exc()

async def real_backtesting_workflow():
    """Full workflow with REAL backtesting (takes 2+ hours)"""
    logger.info("🚀 Starting REAL Backtesting + ML Workflow")
    logger.info("⚠️  This will take 2+ hours to process 896 files with 54+ GB of data")
    
    workflow = CompleteTradingWorkflow()
    await workflow.initialize()
    
    try:
        # Step 1: Run the actual enhanced backtesting system
        logger.info("📊 Step 1: Running Enhanced Backtesting System...")
        logger.info("This will process all feature files with walk-forward analysis")
        
        # Import and run the real backtesting system
        from agents.enhanced_backtesting_improved import main as run_enhanced_backtesting
        
        # Run the real backtesting (this takes 2+ hours)
        await run_enhanced_backtesting()
        
        # Step 2: Load the real backtesting results
        logger.info("📊 Step 2: Loading real backtesting results...")
        backtest_results_file = Path("data/backtest_improved/enhanced_backtest_results.json")
        
        if not backtest_results_file.exists():
            raise FileNotFoundError("Enhanced backtesting results not found. Backtesting may have failed.")
        
        # Convert JSON results to the format expected by ML training
        import json
        with open(backtest_results_file, 'r') as f:
            json_results = json.load(f)
        
        # Flatten the results for ML training
        flattened_results = []
        for file_result in json_results:
            if 'results' in file_result:
                for strategy_result in file_result['results']:
                    flattened_results.append(strategy_result)
        
        if not flattened_results:
            raise ValueError("No valid backtesting results found")
        
        # Save in the expected format
        results_df = pd.DataFrame(flattened_results)
        ml_results_file = Path("data/backtest_improved/strategy_results.parquet")
        results_df.to_parquet(ml_results_file)
        
        logger.info(f"✓ Converted {len(results_df)} real backtesting results for ML training")
        
        # Step 3: Train ML models on real results
        training_results = await workflow.run_ml_training_phase(ml_results_file)
        
        # Step 4: Generate predictions
        predictions, insights = await workflow.run_prediction_phase()
        
        # Step 5: Generate final report
        await workflow.generate_final_report(predictions, insights)
        
        logger.info("🎉 Real backtesting workflow completed successfully!")
        
    except Exception as e:
        logger.error(f"Real backtesting workflow failed: {e}")
        import traceback
        traceback.print_exc()

async def training_only_workflow():
    """Training-only workflow using existing backtesting results"""
    logger.info("🧠 Starting Training-Only Workflow")
    
    workflow = CompleteTradingWorkflow()
    await workflow.initialize()
    
    try:
        # Look for existing backtesting results
        backtest_results_file = Path("data/backtest_improved/strategy_results.parquet")
        
        if not backtest_results_file.exists():
            logger.error("No existing backtesting results found. Please run backtesting first.")
            logger.info("Expected file: data/backtest_improved/strategy_results.parquet")
            return
        
        logger.info(f"✓ Found existing backtesting results: {backtest_results_file}")
        
        # Run ML training phase only
        training_results = await workflow.run_ml_training_phase(backtest_results_file)
        
        # Generate predictions to test the trained models
        predictions, insights = await workflow.run_prediction_phase()
        
        # Generate final report
        await workflow.generate_final_report(predictions, insights)
        
        logger.info("🎉 Training-only workflow completed successfully!")
        
    except Exception as e:
        logger.error(f"Training-only workflow failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            logger.info("Running in DEMO mode with simulated data")
            asyncio.run(quick_demo())
        elif sys.argv[1] == "--real":
            logger.info("Running in REAL mode with actual backtesting")
            logger.info("⚠️  WARNING: This will take 2+ hours to complete!")
            
            # Ask for confirmation
            response = input("Do you want to continue? (y/N): ")
            if response.lower() == 'y':
                asyncio.run(real_backtesting_workflow())
            else:
                logger.info("Cancelled by user")
        elif sys.argv[1] == "--training":
            logger.info("Running TRAINING-ONLY mode using existing backtesting results")
            asyncio.run(training_only_workflow())
        else:
            print("Usage:")
            print("  python complete_backtesting_ml_workflow.py --demo       # Quick demo with simulated data")
            print("  python complete_backtesting_ml_workflow.py --real       # Full workflow with real backtesting (2+ hours)")
            print("  python complete_backtesting_ml_workflow.py --training   # Training only using existing results")
    else:
        print("Usage:")
        print("  python complete_backtesting_ml_workflow.py --demo       # Quick demo with simulated data")
        print("  python complete_backtesting_ml_workflow.py --real       # Full workflow with real backtesting (2+ hours)")
        print("  python complete_backtesting_ml_workflow.py --training   # Training only using existing results")
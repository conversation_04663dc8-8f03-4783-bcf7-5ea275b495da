#!/usr/bin/env python3
"""
Complete Dynamic Trading Workflow
Implements the intelligent stock selection workflow with 500-stock monitoring,
market analysis, and dynamic stock selection based on real-time conditions.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Local imports
from utils.dynamic_stock_selection_workflow import DynamicStockSelectionWorkflow, MarketPhase
from utils.robust_websocket_manager import RobustWebSocketManager
from utils.stock_universe import StockUniverse
from agents.market_monitoring_agent import MarketMonitoringAgent
from utils.config_loader import load_config_for_agent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'dynamic_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

class CompleteDynamicTradingWorkflow:
    """Complete implementation of the dynamic trading workflow"""
    
    def __init__(self):
        self.config = self._load_configuration()
        self.workflow = None
        self.market_agent = None
        self.selected_stocks = []
        self.market_condition = None
        self.is_running = False
        
        # Workflow state tracking
        self.phase_history = []
        self.selection_history = []
        self.performance_metrics = {
            'total_selections': 0,
            'successful_connections': 0,
            'data_points_received': 0,
            'workflow_start_time': None
        }
        
        logger.info("[INIT] Complete Dynamic Trading Workflow initialized")
    
    def _load_configuration(self) -> Dict[str, Any]:
        """Load comprehensive configuration for the workflow"""
        try:
            # Load base configuration
            base_config = load_config_for_agent('market_monitoring')
            
            # Enhanced configuration for dynamic workflow
            workflow_config = {
                'websocket': {
                    'max_retry_attempts': 5,
                    'retry_delay_base': 3,
                    'connection_timeout': 20,
                    'heartbeat_interval': 30
                },
                'workflow': {
                    'initial_monitoring_minutes': 30,  # Monitor 500 stocks for 30 minutes
                    'reselection_interval_hours': 2,   # Reselect every 2 hours
                    'max_workflow_cycles': 4,          # Maximum cycles per day
                    'enable_market_timing': True,      # Wait for market open
                    'enable_weekend_skip': True        # Skip weekends
                },
                'selection_criteria': {
                    'max_stocks': 20,                  # Select top 20 stocks
                    'min_liquidity': 1000000,          # Minimum daily volume
                    'max_volatility': 0.05,            # Maximum 5% volatility
                    'min_market_cap': 'Mid',            # Mid cap and above
                    'sector_diversification': True,     # Ensure sector diversity
                    'momentum_threshold': 0.02,         # Minimum momentum
                    'volume_spike_threshold': 2.0       # 2x average volume
                },
                'market_analysis': {
                    'volatility_lookback_days': 20,
                    'trend_analysis_periods': [5, 10, 20],
                    'volume_analysis_periods': [10, 20],
                    'correlation_threshold': 0.7,
                    'market_breadth_threshold': 0.6
                },
                'risk_management': {
                    'max_daily_selections': 3,
                    'min_time_between_selections': 60,  # minutes
                    'emergency_stop_loss': 0.05,       # 5% emergency stop
                    'position_size_limit': 0.1          # 10% max per position
                }
            }
            
            # Merge configurations
            merged_config = {**base_config, **workflow_config}
            
            logger.info("[CONFIG] Configuration loaded successfully")
            return merged_config
            
        except Exception as e:
            logger.error(f"[ERROR] Configuration loading failed: {e}")
            # Return minimal fallback configuration
            return {
                'websocket': {'max_retry_attempts': 3, 'connection_timeout': 15},
                'workflow': {'initial_monitoring_minutes': 15, 'reselection_interval_hours': 1},
                'selection_criteria': {'max_stocks': 10}
            }
    
    async def start_complete_workflow(self) -> bool:
        """Start the complete dynamic trading workflow"""
        try:
            logger.info("🚀 Starting Complete Dynamic Trading Workflow")
            print("\n" + "=" * 80)
            print("🚀 COMPLETE DYNAMIC TRADING WORKFLOW")
            print("=" * 80)
            
            self.is_running = True
            self.performance_metrics['workflow_start_time'] = datetime.now()
            
            # Phase 1: Initialize components
            if not await self._initialize_components():
                logger.error("[ERROR] Component initialization failed")
                return False
            
            # Phase 2: Market timing and preparation
            await self._handle_market_timing()
            
            # Phase 3: Start the dynamic workflow
            await self._run_dynamic_workflow()
            
            return True
            
        except KeyboardInterrupt:
            logger.info("[STOP] Workflow interrupted by user")
            await self._graceful_shutdown()
            return False
        except Exception as e:
            logger.error(f"[ERROR] Workflow failed: {e}")
            await self._graceful_shutdown()
            return False
    
    async def _initialize_components(self) -> bool:
        """Initialize all workflow components"""
        try:
            logger.info("🔧 Initializing workflow components...")
            
            # Initialize dynamic stock selection workflow
            self.workflow = DynamicStockSelectionWorkflow(self.config)
            
            # Set up callbacks for workflow events
            self.workflow.set_callbacks(
                on_stocks_selected=self._on_stocks_selected,
                on_market_data=self._on_market_data_received
            )
            
            # Initialize market monitoring agent (optional for additional features)
            try:
                self.market_agent = MarketMonitoringAgent(self.config)
                logger.info("✅ Market monitoring agent initialized")
            except Exception as e:
                logger.warning(f"⚠️ Market agent initialization failed: {e}")
                self.market_agent = None
            
            logger.info("✅ All components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Component initialization failed: {e}")
            return False
    
    async def _handle_market_timing(self):
        """Handle market timing and wait for appropriate trading hours"""
        try:
            if not self.config.get('workflow', {}).get('enable_market_timing', True):
                logger.info("⏭️ Market timing disabled, proceeding immediately")
                return
            
            logger.info("⏰ Checking market timing...")
            
            # Get current market phase
            current_phase = self.workflow._get_market_phase(datetime.now().time())
            
            print(f"\n📊 Current Market Phase: {current_phase.value}")
            
            if current_phase == MarketPhase.CLOSED:
                next_open = self.workflow._get_next_market_open()
                wait_hours = (next_open - datetime.now()).total_seconds() / 3600
                
                if wait_hours > 1:
                    print(f"🕐 Market closed. Next open in {wait_hours:.1f} hours")
                    print("   Options:")
                    print("   1. Wait for market open (recommended)")
                    print("   2. Run in demo mode with limited data")
                    print("   3. Exit and run during market hours")
                    
                    choice = input("\nEnter choice (1/2/3): ").strip()
                    
                    if choice == "1":
                        print(f"⏳ Waiting for market to open at {next_open.strftime('%Y-%m-%d %H:%M:%S')}...")
                        # Wait with periodic status updates
                        while datetime.now() < next_open:
                            remaining = (next_open - datetime.now()).total_seconds()
                            if remaining <= 0:
                                break
                            await asyncio.sleep(min(300, remaining))  # Check every 5 minutes
                            if remaining > 300:
                                print(f"⏳ Market opens in {remaining/3600:.1f} hours...")
                    elif choice == "2":
                        print("🧪 Running in demo mode...")
                        self.config['demo_mode'] = True
                    else:
                        print("👋 Exiting. Please run during market hours for best results.")
                        return False
            
            elif current_phase == MarketPhase.PRE_MARKET:
                wait_minutes = self.workflow._time_until_market_open() / 60
                print(f"🌅 Pre-market phase. Market opens in {wait_minutes:.1f} minutes")
                if wait_minutes > 5:
                    print("⏳ Waiting for market open...")
                    await asyncio.sleep(self.workflow._time_until_market_open())
            
            else:
                print("✅ Market is active, proceeding with workflow")
            
        except Exception as e:
            logger.error(f"❌ Market timing error: {e}")
    
    async def _run_dynamic_workflow(self):
        """Run the main dynamic workflow loop"""
        try:
            logger.info("🎯 Starting dynamic workflow execution...")
            
            cycle_count = 0
            max_cycles = self.config.get('workflow', {}).get('max_workflow_cycles', 4)
            
            while self.is_running and cycle_count < max_cycles:
                cycle_count += 1
                
                print(f"\n🔄 Starting Workflow Cycle {cycle_count}/{max_cycles}")
                print("-" * 50)
                
                # Start the workflow
                workflow_success = await self.workflow.start_workflow()
                
                if workflow_success:
                    logger.info(f"✅ Workflow cycle {cycle_count} completed successfully")
                    self.performance_metrics['successful_connections'] += 1
                else:
                    logger.error(f"❌ Workflow cycle {cycle_count} failed")
                
                # Check if we should continue
                if cycle_count < max_cycles:
                    reselection_hours = self.config.get('workflow', {}).get('reselection_interval_hours', 2)
                    print(f"⏳ Waiting {reselection_hours} hours before next cycle...")
                    await asyncio.sleep(reselection_hours * 3600)
                
                # Check market status
                current_phase = self.workflow._get_market_phase(datetime.now().time())
                if current_phase in [MarketPhase.CLOSED, MarketPhase.POST_MARKET]:
                    print("🌙 Market closed, ending workflow for today")
                    break
            
            print(f"\n🏁 Workflow completed after {cycle_count} cycles")
            await self._generate_final_report()
            
        except Exception as e:
            logger.error(f"❌ Dynamic workflow execution failed: {e}")
    
    def _on_stocks_selected(self, stocks: List, market_condition):
        """Handle stock selection event"""
        try:
            self.selected_stocks = stocks
            self.market_condition = market_condition
            self.performance_metrics['total_selections'] += 1
            
            # Record selection history
            selection_record = {
                'timestamp': datetime.now(),
                'stocks': [stock.symbol for stock in stocks],
                'market_condition': market_condition.__dict__,
                'count': len(stocks)
            }
            self.selection_history.append(selection_record)
            
            print(f"\n📈 STOCKS SELECTED ({len(stocks)} stocks)")
            print(f"   Market Condition: {market_condition.trend_direction} trend, {market_condition.volatility_level} volatility")
            print(f"   Selected Stocks: {', '.join([stock.symbol for stock in stocks[:10]])}{'...' if len(stocks) > 10 else ''}")
            print(f"   Active Sectors: {', '.join(market_condition.sector_rotation[:3])}")
            
            logger.info(f"[SELECTION] {len(stocks)} stocks selected based on {market_condition.trend_direction} market")
            
        except Exception as e:
            logger.error(f"[ERROR] Stock selection callback failed: {e}")
    
    def _on_market_data_received(self, data):
        """Handle market data reception"""
        try:
            self.performance_metrics['data_points_received'] += 1
            
            # Log periodic updates
            if self.performance_metrics['data_points_received'] % 100 == 0:
                logger.info(f"[DATA] Received {self.performance_metrics['data_points_received']} market data points")
                
        except Exception as e:
            logger.error(f"[ERROR] Market data callback failed: {e}")
    
    async def _generate_final_report(self):
        """Generate final workflow performance report"""
        try:
            print("\n" + "=" * 80)
            print("📊 WORKFLOW PERFORMANCE REPORT")
            print("=" * 80)
            
            runtime = datetime.now() - self.performance_metrics['workflow_start_time']
            
            print(f"⏱️ Total Runtime: {runtime}")
            print(f"🔄 Total Selection Cycles: {self.performance_metrics['total_selections']}")
            print(f"✅ Successful Connections: {self.performance_metrics['successful_connections']}")
            print(f"📊 Data Points Received: {self.performance_metrics['data_points_received']}")
            
            if self.selection_history:
                print(f"\n📈 SELECTION HISTORY:")
                for i, selection in enumerate(self.selection_history[-3:], 1):  # Show last 3
                    print(f"   {i}. {selection['timestamp'].strftime('%H:%M:%S')} - "
                          f"{selection['count']} stocks ({selection['market_condition']['trend_direction']} market)")
            
            if self.selected_stocks:
                print(f"\n🎯 FINAL SELECTED STOCKS ({len(self.selected_stocks)}):")
                for stock in self.selected_stocks:
                    print(f"   • {stock.symbol} ({stock.sector})")
            
            # Save report to file
            report_file = f"workflow_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            import json
            with open(report_file, 'w') as f:
                json.dump({
                    'performance_metrics': self.performance_metrics,
                    'selection_history': self.selection_history,
                    'final_stocks': [stock.symbol for stock in self.selected_stocks] if self.selected_stocks else []
                }, f, indent=2, default=str)
            
            print(f"\n📄 Detailed report saved to: {report_file}")
            
        except Exception as e:
            logger.error(f"[ERROR] Report generation failed: {e}")
    
    async def _graceful_shutdown(self):
        """Gracefully shutdown the workflow"""
        try:
            logger.info("🛑 Initiating graceful shutdown...")
            
            self.is_running = False
            
            if self.workflow:
                await self.workflow.stop_workflow()
            
            if self.market_agent:
                await self.market_agent.stop()
            
            logger.info("✅ Graceful shutdown completed")
            
        except Exception as e:
            logger.error(f"❌ Shutdown error: {e}")

async def main():
    """Main entry point"""
    try:
        print("🚀 Complete Dynamic Trading Workflow")
        print("This implements your requested workflow:")
        print("1. Connect to 500 stocks initially")
        print("2. Analyze market conditions for 30 minutes")
        print("3. Select optimal stocks based on AI analysis")
        print("4. Switch to monitoring selected stocks only")
        print("5. Repeat cycle every 2 hours")
        print("\nPress Ctrl+C to stop at any time...")
        
        workflow = CompleteDynamicTradingWorkflow()
        success = await workflow.start_complete_workflow()
        
        if success:
            print("\n✅ Workflow completed successfully!")
        else:
            print("\n❌ Workflow encountered issues")
            
    except KeyboardInterrupt:
        print("\n⏹️ Workflow interrupted by user")
    except Exception as e:
        print(f"\n❌ Workflow failed: {e}")
        logger.error(f"Main workflow error: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())

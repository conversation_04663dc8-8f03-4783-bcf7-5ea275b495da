#!/bin/bash
# 🚀 CUDA Backtesting Setup Script for RTX 3060Ti
# Optimizes the environment for maximum CUDA performance

echo "🚀 Setting up CUDA-optimized backtesting environment..."
echo "=================================================="

# Set CUDA environment variables for optimal performance
export CUDA_LAUNCH_BLOCKING=0          # Enable async execution
export CUDA_CACHE_DISABLE=0            # Enable kernel caching
export NUMBA_CUDA_FASTMATH=1           # Enable fast math
export NUMBA_CUDA_DEBUGINFO=0          # Disable debug info for speed
export NUMBA_CACHE_DIR=.numba_cache    # Set cache directory

# PyTorch optimizations
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export TORCH_CUDNN_V8_API_ENABLED=1

# Polars optimizations
export POLARS_MAX_THREADS=8
export POLARS_CHUNK_SIZE=100000

# Memory optimizations
export OMP_NUM_THREADS=1               # Prevent CPU oversubscription
export MKL_NUM_THREADS=1
export NUMEXPR_MAX_THREADS=8

echo "✅ Environment variables set for CUDA optimization"

# Test CUDA setup
echo "🔍 Testing CUDA setup..."
python scripts/test_cuda_setup.py

if [ $? -eq 0 ]; then
    echo "✅ CUDA setup test passed!"
    echo ""
    echo "🚀 Ready to run CUDA-optimized backtesting!"
    echo ""
    echo "Usage examples:"
    echo "  # Quick test with CUDA optimization"
    echo "  python scripts/optimize_cuda_backtesting.py --quick-test"
    echo ""
    echo "  # Full backtesting with CUDA"
    echo "  python scripts/optimize_cuda_backtesting.py --max-symbols 50"
    echo ""
    echo "  # Benchmark CUDA performance"
    echo "  python scripts/optimize_cuda_backtesting.py --benchmark"
    echo ""
    echo "  # Original backtesting with CUDA optimizations"
    echo "  source /media/jmk/BKP/Documents/Option/.venv/bin/activate && python main.py --agent backtesting"
else
    echo "❌ CUDA setup test failed!"
    echo "Consider running in CPU mode or check CUDA installation"
fi
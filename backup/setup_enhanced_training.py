#!/usr/bin/env python3
"""
Enhanced Model Training Agent Setup Script
Automated setup for Windows 10 environment

This script will:
1. Check system requirements
2. Install required packages
3. Verify GPU availability
4. Set up directory structure
5. Run initial tests
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path
import importlib.util

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedTrainingSetup:
    """Setup manager for Enhanced Model Training Agent"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.python_version = sys.version_info
        self.platform_info = platform.platform()
        self.required_python_version = (3, 8)
        
    def check_system_requirements(self):
        """Check system requirements"""
        logger.info("Checking system requirements...")
        
        # Check Python version
        if self.python_version < self.required_python_version:
            raise RuntimeError(
                f"Python {self.required_python_version[0]}.{self.required_python_version[1]}+ required. "
                f"Current version: {self.python_version.major}.{self.python_version.minor}"
            )
        
        # Check if Windows
        if not platform.system() == "Windows":
            logger.warning("This setup is optimized for Windows 10. Other platforms may work but are not tested.")
        
        # Check available memory
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb < 8:
                logger.warning(f"Low memory detected: {memory_gb:.1f}GB. Recommended: 8GB+")
            else:
                logger.info(f"Memory available: {memory_gb:.1f}GB")
        except ImportError:
            logger.warning("Could not check memory requirements (psutil not installed)")
        
        logger.info("✓ System requirements check completed")
    
    def check_gpu_availability(self):
        """Check GPU availability for acceleration"""
        logger.info("Checking GPU availability...")
        
        gpu_available = False
        gpu_info = {}
        
        try:
            import torch
            if torch.cuda.is_available():
                gpu_available = True
                gpu_info['cuda_available'] = True
                gpu_info['device_count'] = torch.cuda.device_count()
                gpu_info['device_name'] = torch.cuda.get_device_name(0)
                gpu_info['memory_gb'] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                
                logger.info(f"✓ CUDA GPU detected: {gpu_info['device_name']}")
                logger.info(f"  Memory: {gpu_info['memory_gb']:.1f}GB")
            else:
                logger.info("No CUDA GPU detected, using CPU")
        except ImportError:
            logger.info("PyTorch not installed yet, GPU check will be performed after installation")
        
        return gpu_available, gpu_info
    
    def install_requirements(self):
        """Install required packages"""
        logger.info("Installing required packages...")
        
        requirements_file = self.project_root / "requirements_enhanced_training.txt"
        
        if not requirements_file.exists():
            logger.error(f"Requirements file not found: {requirements_file}")
            return False
        
        try:
            # Upgrade pip first
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "--upgrade", "pip"
            ])
            
            # Install requirements
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ])
            
            logger.info("✓ Requirements installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install requirements: {e}")
            return False
    
    def setup_directories(self):
        """Create necessary directory structure"""
        logger.info("Setting up directory structure...")
        
        directories = [
            "data/models/enhanced",
            "data/models/registry/enhanced", 
            "data/backtest_improved",
            "logs",
            "reports/enhanced_training",
            "config"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"  Created: {directory}")
        
        logger.info("✓ Directory structure created")
    
    def verify_installations(self):
        """Verify that all required packages are properly installed"""
        logger.info("Verifying package installations...")
        
        required_packages = [
            "numpy", "pandas", "polars", "sklearn", "scipy",
            "lightgbm", "xgboost", "catboost", "torch", "pytorch_tabnet",
            "optuna", "shap", "joblib", "yaml"
        ]
        
        failed_packages = []
        
        for package in required_packages:
            try:
                if package == "yaml":
                    import yaml
                elif package == "pytorch_tabnet":
                    from pytorch_tabnet.tab_model import TabNetRegressor
                else:
                    importlib.import_module(package)
                logger.info(f"  ✓ {package}")
            except ImportError as e:
                logger.error(f"  ✗ {package}: {e}")
                failed_packages.append(package)
        
        if failed_packages:
            logger.error(f"Failed to import packages: {failed_packages}")
            return False
        
        logger.info("✓ All packages verified successfully")
        return True
    
    def create_sample_config(self):
        """Create a sample configuration file if it doesn't exist"""
        config_file = self.project_root / "config" / "enhanced_training_config.yaml"
        
        if config_file.exists():
            logger.info("Configuration file already exists")
            return
        
        logger.info("Creating sample configuration file...")
        
        # The config file should already be created by the previous step
        if config_file.exists():
            logger.info("✓ Configuration file created")
        else:
            logger.warning("Configuration file not found, please create it manually")
    
    def run_basic_test(self):
        """Run a basic test to ensure everything is working"""
        logger.info("Running basic functionality test...")
        
        try:
            # Test basic imports
            import numpy as np
            import pandas as pd
            import polars as pl
            import lightgbm as lgb
            import xgboost as xgb
            from sklearn.model_selection import train_test_split
            
            # Test basic functionality
            X = np.random.rand(100, 5)
            y = np.random.rand(100)
            
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Test LightGBM
            lgb_model = lgb.LGBMRegressor(n_estimators=10, verbose=-1)
            lgb_model.fit(X_train, y_train)
            lgb_pred = lgb_model.predict(X_test)
            
            # Test XGBoost
            xgb_model = xgb.XGBRegressor(n_estimators=10, verbosity=0)
            xgb_model.fit(X_train, y_train)
            xgb_pred = xgb_model.predict(X_test)
            
            logger.info("✓ Basic functionality test passed")
            return True
            
        except Exception as e:
            logger.error(f"Basic functionality test failed: {e}")
            return False
    
    def display_gpu_optimization_info(self, gpu_available, gpu_info):
        """Display GPU optimization information"""
        if gpu_available:
            print("\n" + "="*60)
            print("🚀 GPU ACCELERATION AVAILABLE")
            print("="*60)
            print(f"Device: {gpu_info.get('device_name', 'Unknown')}")
            print(f"Memory: {gpu_info.get('memory_gb', 0):.1f}GB")
            print("\nGPU acceleration will be automatically enabled for:")
            print("- XGBoost (tree_method='gpu_hist')")
            print("- CatBoost (task_type='GPU')")
            print("- TabNet (device_name='cuda')")
            print("="*60)
        else:
            print("\n" + "="*60)
            print("💻 CPU-ONLY MODE")
            print("="*60)
            print("No CUDA GPU detected. Training will use CPU.")
            print("For better performance, consider:")
            print("- Installing CUDA toolkit")
            print("- Using a machine with NVIDIA GPU")
            print("="*60)
    
    def run_setup(self):
        """Run the complete setup process"""
        print("🧠 Enhanced Model Training Agent Setup")
        print("="*50)
        
        try:
            # Step 1: Check system requirements
            self.check_system_requirements()
            
            # Step 2: Check GPU availability
            gpu_available, gpu_info = self.check_gpu_availability()
            
            # Step 3: Setup directories
            self.setup_directories()
            
            # Step 4: Install requirements
            if not self.install_requirements():
                logger.error("Setup failed during package installation")
                return False
            
            # Step 5: Verify installations
            if not self.verify_installations():
                logger.error("Setup failed during package verification")
                return False
            
            # Step 6: Create sample config
            self.create_sample_config()
            
            # Step 7: Run basic test
            if not self.run_basic_test():
                logger.error("Setup failed during basic functionality test")
                return False
            
            # Step 8: Display GPU info
            self.display_gpu_optimization_info(gpu_available, gpu_info)
            
            print("\n" + "="*60)
            print("✅ SETUP COMPLETED SUCCESSFULLY!")
            print("="*60)
            print("\nNext steps:")
            print("1. Prepare your backtesting data in 'data/backtest_improved/'")
            print("2. Review configuration in 'config/enhanced_training_config.yaml'")
            print("3. Run the training agent:")
            print("   python agents/enhanced_model_training_agent.py")
            print("="*60)
            
            return True
            
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            return False

def main():
    """Main setup function"""
    setup = EnhancedTrainingSetup()
    success = setup.run_setup()
    
    if not success:
        print("\n❌ Setup failed. Please check the logs above for details.")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")

if __name__ == "__main__":
    main()
2025-08-23 22:35:59,693 - utils.cuda_strategy_processor - INFO - 🚀 CUDA processed 4 strategies on 42087 rows
2025-08-23 22:36:00,332 - agents.enhanced_backtesting_kimi - ERROR - Vectorized trade simulation failed for RSI_Reversal_evolved_ABCAPITAL: 'NoneType' object has no attribute 'apply_intraday_rules'
2025-08-23 22:36:00,333 - agents.enhanced_backtesting_kimi - ERROR - Full traceback: Traceback (most recent call last):
  File "/media/jmk/BKP/Documents/Equity/agents/enhanced_backtesting_kimi.py", line 1206, in simulate_trades_vectorized
    signals = apply_intraday_rules(df, signals, strategy)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/media/jmk/BKP/Documents/Equity/agents/enhanced_backtesting_kimi.py", line 1447, in apply_intraday_rules
    return signal_agent.apply_intraday_rules(df, signals, strategy)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'apply_intraday_rules'

2025-08-23 22:36:00,334 - agents.enhanced_backtesting_kimi - WARNING -   ❌ RSI_Reversal_evolved_ABCAPITAL generated no results
2025-08-23 22:36:00,347 - agents.enhanced_backtesting_kimi - INFO - [STRATEGIES] Completed processing - 0 total results from 1 strategies
2025-08-23 22:36:00,347 - agents.enhanced_backtesting_kimi - INFO -   File processed in 1.33s
2025-08-23 22:36:00,348 - agents.enhanced_backtesting_kimi - INFO - Processing 360ONE (1min)...
2025-08-23 22:36:00,878 - agents.enhanced_backtesting_kimi - INFO - 📊 Loaded 605319 rows for 360ONE (GPU optimized)
2025-08-23 22:36:01,017 - agents.enhanced_backtesting_kimi - INFO - [STRATEGIES] Processing 1 strategies for 360ONE (1min)
2025-08-23 22:36:01,018 - agents.enhanced_backtesting_kimi - INFO -   Strategy 1/1: RSI_Reversal_evolved_ABCAPITAL
2025-08-23 22:36:01,141 - agents.enhanced_backtesting_kimi - ERROR - Vectorized trade simulation failed for RSI_Reversal_evolved_ABCAPITAL: 'NoneType' object has no attribute 'apply_intraday_rules'
2025-08-23 22:36:01,142 - agents.enhanced_backtesting_kimi - ERROR - Full traceback: Traceback (most recent call last):
  File "/media/jmk/BKP/Documents/Equity/agents/enhanced_backtesting_kimi.py", line 1206, in simulate_trades_vectorized
    signals = apply_intraday_rules(df, signals, strategy)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/media/jmk/BKP/Documents/Equity/agents/enhanced_backtesting_kimi.py", line 1447, in apply_intraday_rules
    return signal_agent.apply_intraday_rules(df, signals, strategy)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'apply_intraday_rules'

2025-08-23 22:36:01,143 - agents.enhanced_backtesting_kimi - WARNING -   ❌ RSI_Reversal_evolved_ABCAPITAL generated no results
2025-08-23 22:36:01,143 - agents.enhanced_backtesting_kimi - INFO - [STRATEGIES] Completed processing - 0 total results from 1 strategies
2025-08-23 22:36:01,144 - agents.enhanced_backtesting_kimi - INFO -   File processed in 0.80s
2025-08-23 22:36:01,145 - agents.enhanced_backtesting_kimi - INFO - Processing 360ONE (3min)...
2025-08-23 22:36:01,448 - agents.enhanced_backtesting_kimi - INFO - 📊 Loaded 208307 rows for 360ONE (GPU optimized)
2025-08-23 22:36:01,583 - agents.enhanced_backtesting_kimi - INFO - [STRATEGIES] Processing 1 strategies for 360ONE (3min)
2025-08-23 22:36:01,583 - agents.enhanced_backtesting_kimi - INFO -   Strategy 1/1: RSI_Reversal_evolved_ABCAPITAL
2025-08-23 22:36:01,620 - agents.enhanced_backtesting_kimi - ERROR - Vectorized trade simulation failed for RSI_Reversal_evolved_ABCAPITAL: 'NoneType' object has no attribute 'apply_intraday_rules'
2025-08-23 22:36:01,620 - agents.enhanced_backtesting_kimi - ERROR - Full traceback: Traceback (most recent call last):
  File "/media/jmk/BKP/Documents/Equity/agents/enhanced_backtesting_kimi.py", line 1206, in simulate_trades_vectorized
    signals = apply_intraday_rules(df, signals, strategy)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/media/jmk/BKP/Documents/Equity/agents/enhanced_backtesting_kimi.py", line 1447, in apply_intraday_rules
    return signal_agent.apply_intraday_rules(df, signals, strategy)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'apply_intraday_rules'

2025-08-23 22:36:01,621 - agents.enhanced_backtesting_kimi - WARNING -   ❌ RSI_Reversal_evolved_ABCAPITAL generated no results
2025-08-23 22:36:01,621 - agents.enhanced_backtesting_kimi - INFO - [STRATEGIES] Completed processing - 0 total results from 1 strategies
2025-08-23 22:36:01,621 - agents.enhanced_backtesting_kimi - INFO -   File processed in 0.48s
2025-08-23 22:36:01,621 - agents.enhanced_backtesting_kimi - INFO - Processing 360ONE (5min)...
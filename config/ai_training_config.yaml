data:
  data_dir: data/backtest
  input_file: enhanced_strategy_results.parquet
  models_dir: data/models
  registry_dir: data/models/registry
multi_task_objectives:
  trade_direction:
    type: classification
    classes:
    - buy_call
    - buy_put
    - no_trade
    target_column: trade_direction
    weight: 0.25
    description: Predicts optimal trade direction based on market conditions
  profitability:
    type: classification
    classes:
    - profitable
    - unprofitable
    target_column: is_profitable
    weight: 0.2
    description: Binary classifier for trade profitability prediction
  signal_confidence:
    type: regression
    target_column: signal_confidence
    range:
    - 0
    - 1
    weight: 0.15
    description: Confidence score for trading signals (0-1)
  expected_roi:
    type: regression
    target_column: expected_roi
    weight: 0.2
    description: Expected return on investment prediction
  strategy_selection:
    type: classification
    target_column: best_strategy_id
    weight: 0.1
    description: Multi-class classifier for optimal strategy selection
  regime_classification:
    type: classification
    classes:
    - trending_up
    - trending_down
    - sideways
    - volatile
    target_column: market_regime
    weight: 0.1
    description: Market regime classification for adaptive strategies
features:
  base_features:
  - n_trades
  - avg_holding_period
  - capital_at_risk
  - liquidity
  - volatility
  - market_regime
  - correlation_index
  - drawdown_duration
  - winning_trades
  - losing_trades
  - avg_win
  - avg_loss
  - total_pnl
  - position_size_pct
  technical_indicators:
  - sma_20
  - sma_50
  - ema_12
  - ema_26
  - rsi_14
  - macd_line
  - macd_signal
  - bb_upper
  - bb_lower
  - bb_width
  - atr_14
  - stoch_k
  - stoch_d
  - cci_20
  - adx_14
  - mfi_14
  - vwap
  - supertrend
  option_features:
  - delta
  - gamma
  - theta
  - vega
  - rho
  - implied_volatility
  - time_to_expiry
  - moneyness
  - open_interest
  - volume
  - bid_ask_spread
  - iv_rank
  - iv_percentile
  - option_flow
  - put_call_ratio
  time_features:
  - hour_of_day
  - day_of_week
  - days_to_expiry
  - time_since_market_open
  - is_near_expiry
  - is_market_hours
  - session_type
  - expiry_category
  regime_features:
  - vix_level
  - vix_change
  - trend_strength
  - volatility_regime
  - correlation_regime
  - momentum_regime
  - market_stress_indicator
  strategy_features:
  - strategy_id
  - strategy_type
  - complexity_score
  - historical_performance
  - regime_suitability
  - risk_category
  - strategy_family
models:
  enabled_models:
  - lightgbm
  - xgboost
  - catboost
  - tabnet
  - random_forest
  - mlp
  ensemble_weights:
    lightgbm: 0.3
    xgboost: 0.25
    catboost: 0.2
    tabnet: 0.15
    random_forest: 0.05
    mlp: 0.05
  architectures:
    mlp:
      hidden_layers:
      - 256
      - 128
      - 64
      activation: relu
      dropout: 0.3
      batch_norm: true
    lstm:
      hidden_size: 128
      num_layers: 2
      dropout: 0.2
      sequence_length: 30
      bidirectional: false
training:
  test_size: 0.2
  validation_size: 0.2
  random_state: 42
  cv_folds: 5
  ensemble_weights:
    lightgbm: 0.5
    tabnet: 0.3
    catboost: 0.2
model_params:
  lightgbm:
    objective: regression
    metric: rmse
    boosting_type: gbdt
    num_leaves: 31
    learning_rate: 0.05
    feature_fraction: 0.9
    bagging_fraction: 0.8
    bagging_freq: 5
    verbose: -1
    num_boost_round: 1000
    early_stopping_rounds: 100
  xgboost:
    objective: reg:squarederror
    eval_metric: rmse
    max_depth: 6
    learning_rate: 0.05
    subsample: 0.8
    colsample_bytree: 0.9
    n_estimators: 1000
    early_stopping_rounds: 100
  catboost:
    loss_function: RMSE
    eval_metric: RMSE
    depth: 6
    learning_rate: 0.05
    iterations: 1000
    early_stopping_rounds: 100
    verbose: false
  tabnet:
    n_d: 8
    n_a: 8
    n_steps: 3
    gamma: 1.3
    lambda_sparse: 0.001
    max_epochs: 200
    patience: 20
    batch_size: 1024
    virtual_batch_size: 128
  mlp:
    hidden_layer_sizes:
    - 256
    - 128
    - 64
    activation: relu
    solver: adam
    alpha: 0.001
    learning_rate: adaptive
    max_iter: 1000
    early_stopping: true
    validation_fraction: 0.1
  lstm:
    hidden_size: 128
    num_layers: 2
    dropout: 0.2
    sequence_length: 30
    batch_size: 64
    learning_rate: 0.001
    epochs: 100
    patience: 10
hyperparameter_tuning:
  optuna_trials: 100
  optuna_timeout: 3600
  hyperopt_enabled: true
  grid_search_enabled: false
  tuning_ranges:
    lightgbm:
      num_leaves:
      - 20
      - 50
      learning_rate:
      - 0.01
      - 0.1
      feature_fraction:
      - 0.7
      - 1.0
      bagging_fraction:
      - 0.7
      - 1.0
    xgboost:
      max_depth:
      - 3
      - 10
      learning_rate:
      - 0.01
      - 0.1
      subsample:
      - 0.7
      - 1.0
      colsample_bytree:
      - 0.7
      - 1.0
    tabnet:
      n_d:
      - 8
      - 64
      n_a:
      - 8
      - 64
      n_steps:
      - 3
      - 10
      gamma:
      - 1.0
      - 2.0
explainability:
  shap_enabled: true
  lime_enabled: true
  feature_importance_threshold: 0.01
  explanation_samples: 1000
  shap_config:
    max_evals: 1000
    check_additivity: false
  lime_config:
    num_features: 20
    num_samples: 5000
    discretize_continuous: true
model_registry:
  versioning_enabled: true
  auto_backup: true
  max_versions: 10
  compression: true
  track_metrics:
  - accuracy
  - precision
  - recall
  - f1_score
  - roc_auc
  - rmse
  - mae
  - r2_score
  performance_thresholds:
    min_accuracy: 0.6
    min_precision: 0.65
    min_recall: 0.6
    max_rmse: 0.1
prediction_serving:
  enabled: true
  batch_size: 1000
  confidence_threshold: 0.6
  api_config:
    host: 0.0.0.0
    port: 8000
    workers: 4
    timeout: 30
  cache_enabled: true
  cache_ttl: 300
  rate_limit:
    requests_per_minute: 1000
    burst_size: 100
feedback_integration:
  enabled: true
  continual_learning: true
  drift_detection:
    enabled: true
    detection_method: ks_test
    threshold: 0.1
    window_size: 1000
    check_frequency: daily
  retraining:
    performance_threshold: 0.1
    drift_threshold: 0.05
    time_threshold_days: 30
    min_new_samples: 500
  feedback_sources:
  - execution_agent
  - performance_analysis_agent
  - market_monitoring_agent
llm_integration:
  insights_enabled: true
  generate_summaries: true
  summary_config:
    include_performance_metrics: true
    include_feature_importance: true
    include_model_insights: true
    include_recommendations: true
    max_summary_length: 1000
  insight_generation:
    frequency: after_training
    include_comparisons: true
    include_trends: true
    include_alerts: true
meta_learning:
  enabled: true
  strategy_selection:
    enabled: true
    model_type: random_forest
    n_strategies: 25
    selection_criteria:
    - performance
    - regime_suitability
    - risk_adjusted_returns
  holding_period_optimization:
    enabled: true
    model_type: random_forest
    min_period_hours: 0.5
    max_period_hours: 6.0
    optimization_metric: sharpe_ratio
  risk_reward_optimization:
    enabled: true
    model_type: random_forest
    min_rr_ratio: 1.5
    max_rr_ratio: 4.0
    optimization_metric: expectancy
  ensemble_meta_learning:
    enabled: true
    meta_model_type: lightgbm
    cross_validation: true
    feature_selection: true
monitoring:
  track_predictions: true
  track_confidence: true
  track_feature_importance: true
  metrics_file: data/models/training_metrics.json
  low_confidence_threshold: 0.5
  high_error_threshold: 0.1
system:
  resource_limits:
    max_memory_gb: 16
    max_cpu_cores: 8
    gpu_memory_fraction: 0.8
  parallel_processing:
    enabled: true
    max_workers: 4
    chunk_size: 1000
  caching:
    enabled: true
    cache_dir: data/cache
    max_cache_size_gb: 5
    cache_compression: true
  backup:
    enabled: true
    backup_dir: data/backups
    backup_frequency: daily
    retention_days: 30
    compress_backups: true
integration:
  agent_communication:
    enabled: true
    communication_protocol: http
    timeout_seconds: 30
    retry_attempts: 3
  data_pipeline:
    feature_engineering_agent: true
    backtesting_agent: true
    market_monitoring_agent: true
    execution_agent: true
    performance_analysis_agent: true
  external_services:
    telegram_notifications: true
    email_alerts: false
    slack_integration: false
    webhook_endpoints: []
alerts:
  training_alerts:
    training_failure: true
    performance_degradation: true
    model_drift_detected: true
    resource_exhaustion: true
  prediction_alerts:
    high_confidence_signals: true
    anomaly_detection: true
    system_errors: true
  notification_channels:
    telegram:
      enabled: true
      chat_id: ${TELEGRAM_CHAT_ID}
      bot_token: ${TELEGRAM_BOT_TOKEN}
    email:
      enabled: false
      smtp_server: smtp.gmail.com
      smtp_port: 587
      username: ${EMAIL_USERNAME}
      password: ${EMAIL_PASSWORD}
lightgbm:
  objective: regression
  metric: rmse
  boosting_type: gbdt
  num_leaves: 31
  max_depth: -1
  min_data_in_leaf: 20
  min_child_samples: 20
  learning_rate: 0.05
  num_boost_round: 1000
  early_stopping_rounds: 50
  feature_fraction: 0.9
  bagging_fraction: 0.8
  bagging_freq: 5
  lambda_l1: 0.0
  lambda_l2: 0.0
  min_gain_to_split: 0.0
  verbose: -1
  random_state: 42
  n_jobs: -1
  device: gpu
  gpu_platform_id: 0
  gpu_device_id: 0
  gpu_use_dp: false
  max_bin: 255
tabnet:
  n_d: 8
  n_a: 8
  n_steps: 3
  gamma: 1.3
  lambda_sparse: 1e-3
  optimizer: adam
  learning_rate: 0.02
  scheduler_step_size: 10
  scheduler_gamma: 0.9
  max_epochs: 200
  patience: 20
  batch_size: 1024
  virtual_batch_size: 128
  mask_type: sparsemax
  device: cuda
  mixed_precision: true
catboost:
  loss_function: RMSE
  iterations: 1000
  learning_rate: 0.1
  depth: 6
  l2_leaf_reg: 3
  task_type: GPU
  devices: '0'
  gpu_ram_part: 0.8
  early_stopping_rounds: 100
  verbose: false
  random_state: 42
optuna:
  n_trials: 100
  timeout: 3600
  direction: minimize
  study_name: ai_training_optimization
  storage: null
  search_spaces:
    lightgbm:
      num_leaves:
      - 10
      - 100
      learning_rate:
      - 0.01
      - 0.3
      feature_fraction:
      - 0.4
      - 1.0
      bagging_fraction:
      - 0.4
      - 1.0
      bagging_freq:
      - 1
      - 7
      min_child_samples:
      - 5
      - 100
    tabnet:
      n_d:
      - 4
      - 16
      n_a:
      - 4
      - 16
      n_steps:
      - 2
      - 6
      gamma:
      - 1.0
      - 2.0
      lambda_sparse:
      - 1e-4
      - 1e-2
      learning_rate:
      - 0.005
      - 0.05
ranking:
  metric_weights:
    ROI: 0.3
    sharpe_ratio: 0.25
    expectancy: 0.2
    max_drawdown: -0.15
    profit_factor: 0.1
    risk_reward_ratio: 0.05
    accuracy: 0.05
  market_regime_multipliers:
    bull:
      high_roi_bonus: 1.1
      low_roi_penalty: 0.9
    bear:
      low_drawdown_bonus: 1.1
      high_drawdown_penalty: 0.9
    sideways:
      high_sharpe_bonus: 1.05
      low_sharpe_penalty: 0.95
hardware:
  use_gpu: true
  gpu_memory_fraction: 0.8
  n_jobs: -1
  memory_limit: 16GB
logging:
  level: INFO
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  log_to_file: true
  log_file: logs/ai_training_agent.log
  max_file_size: 10MB
  backup_count: 5
serving:
  host: 0.0.0.0
  port: 8000
  reload: false
  title: AI Training Agent API
  description: Multi-target strategy performance prediction API
  version: 1.0.0
  allow_origins:
  - '*'
  allow_methods:
  - GET
  - POST
  allow_headers:
  - '*'
validation:
  check_missing_values: true
  check_data_types: true
  check_feature_ranges: true
  min_r2_score: 0.1
  max_rmse_threshold: 1.0
  min_confidence_score: 0.3
  cv_scoring:
  - neg_mean_squared_error
  - r2
  cv_verbose: true
feature_engineering:
  create_interaction_features: false
  create_polynomial_features: false
  polynomial_degree: 2
  feature_selection: false
  selection_method: mutual_info_regression
  max_features: 20
  scale_features: true
  scaling_method: standard
backup:
  backup_models: true
  backup_frequency: daily
  max_backups: 7
  backup_data: false
  backup_location: backups/
online_learning:
  enabled: true
  stream_chunk_size: 1000
  memory_efficient_mode: true
  date_based_filtering: true
  training_cutoff_date: '2025-07-21'
  large_file_threshold_gb: 1.0
  use_partial_fit: true
  incremental_batch_size: 10000
  max_incremental_updates: 10
  incremental_learning_rate: 0.01
  online_models:
    sgd_regressor:
      learning_rate: adaptive
      eta0: 0.01
      random_state: 42
      alpha: 0.0001
    sgd_classifier:
      learning_rate: adaptive
      eta0: 0.01
      random_state: 42
      alpha: 0.0001
  checkpoint_enabled: true
  checkpoint_frequency: 10
  checkpoint_directory: data/models/online_checkpoints

# Enhanced AI Training Configuration
# Supports multiple backtesting files with filtering

# Data Configuration
data_dir: "data/backtest"
input_pattern: "backtest_*.parquet"  # Load all backtesting files
symbol_filter: null  # Set to specific symbol like "360ONE" to filter
timeframe_filter: null  # Set to specific timeframe like "3min" to filter

# Model Configuration
enabled_models: ["lightgbm", "xgboost", "catboost", "tabnet", "mlp"]
ensemble_method: "stacking"

# Hyperparameter Optimization
hyperopt_all_models: true
optuna_trials: 30
optuna_timeout: 1800

# Enhanced Features
use_advanced_metrics: true
feature_selection_enabled: true
outlier_detection_enabled: true
advanced_imputation: true

# Hardware
n_jobs: -1
use_gpu: true

# Examples of specific configurations:
# For 360ONE on 3min timeframe only:
# symbol_filter: "360ONE"
# timeframe_filter: "3min"

# For all symbols on 1min timeframe:
# symbol_filter: null
# timeframe_filter: "1min"

# For specific symbol on all timeframes:
# symbol_filter: "RELIANCE"
# timeframe_filter: null
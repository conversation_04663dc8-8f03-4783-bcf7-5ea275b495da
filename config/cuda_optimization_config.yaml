# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 CUDA OPTIMIZATION CONFIGURATION FOR RTX 3060Ti
# ═══════════════════════════════════════════════════════════════════════════════
# Optimized for 8GB VRAM with maximum performance

cuda_settings:
  # Memory Management
  memory_fraction: 0.85  # Use 85% of 8GB = ~6.8GB
  memory_growth: true
  allow_memory_growth: true
  
  # Performance Settings
  benchmark_mode: true
  deterministic: false  # Disable for maximum speed
  
  # Batch Processing
  batch_size: 8192      # Optimal for RTX 3060Ti
  chunk_size: 50000     # Process 50k rows at once
  
  # Threading
  cuda_threads_per_block: 512  # Increased for RTX 3060Ti
  max_blocks_per_grid: 2048
  
  # Data Types
  use_mixed_precision: true
  default_dtype: "float32"  # Faster than float64
  
polars:
  gpu_engine: true
  streaming_chunk_size: 100000  # Larger chunks for GPU
  use_gpu_memory: true
  
vectorbt:
  gpu_acceleration: true
  use_cupy: true
  parallel_processing: true
  
numba:
  cuda_optimization: true
  parallel_target: "cuda"
  fastmath: true
  cache: true
  
# Performance Thresholds
thresholds:
  min_rows_for_cuda: 1000      # Lower threshold for more CUDA usage
  min_signals_for_cuda: 10     # Reduced threshold  
  min_trades_for_cuda: 5       # Reduced threshold

# Parallel Processing
parallel_processing:
  concurrent_files: 8          # Number of files processed in parallel
  strategies_per_batch: 12     # Number of strategies processed together on GPU
  
# Memory Optimization
memory:
  aggressive_cleanup: true
  gc_frequency: 100  # Run GC every 100 operations
  clear_cache_frequency: 500
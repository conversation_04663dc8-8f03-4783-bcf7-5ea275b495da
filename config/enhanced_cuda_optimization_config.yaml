# Enhanced CUDA Optimization Configuration
cuda:
  enabled: true
  device_id: 0
  memory_fraction: 0.8
  allow_growth: true
  
parallel_processing:
  strategies_per_batch: 2  # Reduced from 4 to ensure stability
  concurrent_files: 3      # Reduced from 8 to prevent memory issues
  max_workers: 4
  
memory_management:
  cleanup_frequency: 5     # Clean up every 5 operations
  max_memory_usage: 0.85   # Use max 85% of GPU memory
  batch_size_multiplier: 0.5  # Reduce batch sizes for stability

optimization:
  use_mixed_precision: true
  enable_tensorcore: true
  optimize_memory_layout: true
  
backtesting:
  min_data_size_for_gpu: 1000   # Minimum rows to use GPU
  min_strategies_for_parallel: 2  # Minimum strategies for parallel processing
  max_concurrent_strategies: 2    # Reduced for stability

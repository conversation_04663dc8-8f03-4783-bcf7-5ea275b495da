# Centralized Environment Configuration
# Shared configuration for all trading agents with environment variable support

# ═══════════════════════════════════════════════════════════════════════════════
# 🔐 ANGEL ONE SMARTAPI CONFIGURATION (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
smartapi:
  # Enable/Disable SmartAPI integration globally
  enabled: true
  
  # Authentication Credentials (Set via environment variables for security)
  credentials:
    api_key: "${SMARTAPI_API_KEY}"
    username: "${SMARTAPI_USERNAME}"
    password: "${SMARTAPI_PASSWORD}"
    totp_token: "${SMARTAPI_TOTP_TOKEN}"
  
  # API Connection Settings
  connection:
    timeout: 10  # seconds
    max_retries: 3
    retry_delay: 2  # seconds
    base_url: "https://apiconnect.angelbroking.com"
  
  # Rate Limiting (Shared across all agents)
  rate_limiting:
    requests_per_second: 10
    burst_limit: 50
    daily_limit: 100000
    
  # WebSocket Configuration
  websocket:
    enabled: true
    reconnect_attempts: 5
    reconnect_delay: 5  # seconds
    heartbeat_interval: 30  # seconds
    max_message_queue: 1000
    auto_reconnect: true

# ═══════════════════════════════════════════════════════════════════════════════
# 📱 NOTIFICATION SERVICES (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
notifications:
  # Telegram Configuration
  telegram:
    enabled: true
    bot_token: "${TELEGRAM_BOT_TOKEN}"
    chat_id: "${TELEGRAM_CHAT_ID}"
    
    # Message Settings
    parse_mode: "HTML"
    disable_web_page_preview: true
    timeout: 10
    
    # Rate Limiting
    max_messages_per_minute: 20
    
  # Email Configuration
  email:
    enabled: false
    smtp_server: "${EMAIL_SMTP_SERVER}"
    smtp_port: "${EMAIL_SMTP_PORT}"
    username: "${EMAIL_USERNAME}"
    password: "${EMAIL_PASSWORD}"
    from_email: "${EMAIL_FROM}"
    to_email: "${EMAIL_TO}"
    
  # Slack Configuration
  slack:
    enabled: false
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "${SLACK_CHANNEL}"
    username: "Trading Bot"
    
  # Discord Configuration
  discord:
    enabled: false
    webhook_url: "${DISCORD_WEBHOOK_URL}"

# ═══════════════════════════════════════════════════════════════════════════════
# 🗄️ DATABASE CONFIGURATION (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
database:
  # Primary Database
  primary:
    type: "sqlite"  # sqlite, postgresql, mysql
    host: "${DB_HOST}"
    port: "${DB_PORT}"
    database: "${DB_NAME}"
    username: "${DB_USERNAME}"
    password: "${DB_PASSWORD}"
    
  # Redis Cache
  redis:
    enabled: false
    host: "${REDIS_HOST}"
    port: "${REDIS_PORT}"
    password: "${REDIS_PASSWORD}"
    db: 0
    
  # InfluxDB (Time Series)
  influxdb:
    enabled: false
    host: "${INFLUXDB_HOST}"
    port: "${INFLUXDB_PORT}"
    database: "${INFLUXDB_DATABASE}"
    username: "${INFLUXDB_USERNAME}"
    password: "${INFLUXDB_PASSWORD}"

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 EXTERNAL DATA SOURCES
# ═══════════════════════════════════════════════════════════════════════════════
external_data:
  # Alpha Vantage
  alpha_vantage:
    enabled: false
    api_key: "${ALPHA_VANTAGE_API_KEY}"
    
  # Yahoo Finance
  yahoo_finance:
    enabled: true
    # No API key required
    
  # Economic Calendar
  economic_calendar:
    enabled: false
    api_key: "${ECONOMIC_CALENDAR_API_KEY}"
    
  # News API
  news_api:
    enabled: false
    api_key: "${NEWS_API_KEY}"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 SYSTEM CONFIGURATION (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
system:
  # Environment
  environment: "${TRADING_ENVIRONMENT}"  # development, staging, production
  debug_mode: "${DEBUG_MODE}"
  
  # Timezone
  timezone: "Asia/Kolkata"
  
  # Logging
  logging:
    level: "${LOG_LEVEL}"  # DEBUG, INFO, WARNING, ERROR
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
  # Performance
  performance:
    max_concurrent_operations: 10
    memory_limit_mb: 2048
    cpu_limit_percent: 80
    
  # Security
  security:
    encryption_key: "${ENCRYPTION_KEY}"
    jwt_secret: "${JWT_SECRET}"
    session_timeout_minutes: 60

# ═══════════════════════════════════════════════════════════════════════════════
# 📁 FILE PATHS (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
paths:
  # Data Directories
  data_root: "data"
  historical_data: "data/historical"
  features_data: "data/features"
  backtest_data: "data/backtest"
  models_data: "data/models"
  execution_data: "data/execution"
  
  # Log Directories
  logs_root: "logs"
  
  # Config Directories
  config_root: "config"
  
  # Backup Directories
  backup_root: "backup"
  
  # Temporary Directories
  temp_root: "temp"

# ═══════════════════════════════════════════════════════════════════════════════
# 🕐 MARKET CONFIGURATION (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
market:
  # Trading Hours (IST)
  trading_hours:
    market_open: "09:15"
    market_close: "15:30"
    pre_market_start: "09:00"
    post_market_end: "16:00"
    
  # Market Holidays (Will be loaded from external source)
  holidays: []
  
  # Exchanges
  exchanges:
    primary: "NSE"
    secondary: "BSE"
    derivatives: "NFO"
    
  # Currency
  base_currency: "INR"
  
  # Market Data
  market_data:
    default_timeframe: "1min"
    max_history_days: 365
    
# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 WEBHOOK CONFIGURATION (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
webhooks:
  # Incoming Webhooks (for receiving signals)
  incoming:
    enabled: false
    host: "0.0.0.0"
    port: "${WEBHOOK_PORT}"
    secret_key: "${WEBHOOK_SECRET}"
    
  # Outgoing Webhooks (for sending notifications)
  outgoing:
    enabled: false
    endpoints:
      - name: "external_system"
        url: "${EXTERNAL_WEBHOOK_URL}"
        secret: "${EXTERNAL_WEBHOOK_SECRET}"
        timeout: 10
        retry_count: 3

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TESTING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
testing:
  # Test Mode Settings
  test_mode: "${TEST_MODE}"  # true/false
  
  # Paper Trading
  paper_trading:
    enabled: "${PAPER_TRADING_ENABLED}"
    initial_capital: 100000
    
  # Mock Data
  mock_data:
    enabled: "${MOCK_DATA_ENABLED}"
    data_source: "historical"
    
  # Simulation
  simulation:
    speed_multiplier: 1.0  # 1.0 = real-time, 10.0 = 10x speed
    start_date: "2024-01-01"
    end_date: "2024-12-31"

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 MONITORING & ALERTING (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
monitoring:
  # Health Checks
  health_checks:
    enabled: true
    interval_seconds: 60
    timeout_seconds: 10
    
  # Metrics Collection
  metrics:
    enabled: true
    collection_interval_seconds: 30
    retention_days: 30
    
  # Alerting
  alerting:
    enabled: true
    
    # Alert Thresholds
    thresholds:
      cpu_usage_percent: 80
      memory_usage_percent: 85
      disk_usage_percent: 90
      api_error_rate_percent: 10
      execution_delay_ms: 5000
      
    # Alert Channels
    channels:
      - "telegram"
      - "email"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 BACKUP & RECOVERY (SHARED)
# ═══════════════════════════════════════════════════════════════════════════════
backup:
  # Automatic Backup
  auto_backup:
    enabled: true
    interval_hours: 6
    retention_days: 30
    
  # Backup Locations
  locations:
    local: "backup/local"
    cloud: "${CLOUD_BACKUP_PATH}"
    
  # What to Backup
  include:
    - "data/"
    - "config/"
    - "logs/"
    
  # What to Exclude
  exclude:
    - "temp/"
    - "*.tmp"
    - "*.log"

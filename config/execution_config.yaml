# Execution Agent Configuration
# Comprehensive order lifecycle management and trade execution with Angel One integration

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 SHARED CONFIGURATION REFERENCE
# ═══════════════════════════════════════════════════════════════════════════════
# This agent uses centralized environment configuration
# See: config/environment_config.yaml and .env file

# ═══════════════════════════════════════════════════════════════════════════════
# 🔐 ANGEL ONE API CONFIGURATION (Uses centralized environment variables)
# ═══════════════════════════════════════════════════════════════════════════════
angel_one_api:
  # Enable/Disable Angel One API integration
  enabled: true

  # SmartAPI Credentials from environment variables (defined in .env file)
  api_key: "${SMARTAPI_API_KEY}"
  username: "${SMARTAPI_USERNAME}"
  password: "${SMARTAPI_PASSWORD}"
  totp_token: "${SMARTAPI_TOTP_TOKEN}"
  
  # API Settings
  timeout: 10  # seconds
  max_retries: 3
  retry_delay: 2  # seconds
  
  # Rate Limiting
  requests_per_second: 10
  burst_limit: 50

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ ORDER EXECUTION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
execution:
  # Order Placement Settings
  default_order_type: "LIMIT"  # LIMIT, MARKET
  default_product_type: "MIS"  # MIS (Intraday), CNC, NRML
  default_duration: "DAY"
  
  # Execution Safety Checks
  max_execution_time_ms: 2000  # Warn if execution takes longer
  max_slippage_percent: 0.5  # Maximum allowed slippage
  slippage_threshold_for_rejection: 1.0  # Reject if slippage exceeds this
  
  # Retry Logic
  auto_retry: true
  max_retries: 3
  retry_delay_minutes: 5
  
  # Order Validation
  min_quantity: 1
  max_quantity: 10000
  min_price: 0.05
  max_price: 100000
  
  # Position Limits
  max_orders_per_symbol: 5
  max_total_active_orders: 20

# ═══════════════════════════════════════════════════════════════════════════════
# 🕐 MARKET HOURS CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
market_hours:
  # Market Timing
  market_open_time: "09:15"
  market_close_time: "15:25"
  
  # Pre-market Setup
  allow_pre_market: true
  pre_market_start: "09:00"
  
  # Trading Windows
  avoid_first_minutes: 5  # Avoid first 5 minutes after market open
  avoid_last_minutes: 30  # Avoid last 30 minutes before market close
  no_new_trades_after: "14:30"  # No new trades after 2:30 PM
  
  # Auto Square-off
  auto_square_off_time: "15:20"
  auto_square_off_enabled: true

# ═══════════════════════════════════════════════════════════════════════════════
# 🛡️ RISK MANAGEMENT INTEGRATION
# ═══════════════════════════════════════════════════════════════════════════════
risk_management:
  # Enable Risk Agent Integration
  enabled: true
  config_path: "config/risk_management_config.yaml"
  
  # Pre-execution Validation
  validate_before_execution: true
  
  # Risk Limits
  min_rr_ratio: 1.5  # Minimum risk-reward ratio
  max_position_size_percent: 5.0  # Maximum 5% of capital per position
  max_daily_loss_percent: 10.0  # Stop trading if daily loss exceeds 10%

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 ORDER MONITORING
# ═══════════════════════════════════════════════════════════════════════════════
monitoring:
  # Order Status Checking
  order_check_interval_seconds: 10  # Check order status every 10 seconds
  position_check_interval_seconds: 30  # Check positions every 30 seconds
  
  # Order Timeout
  order_timeout_minutes: 10  # Cancel order if not filled within 10 minutes
  partial_fill_timeout_minutes: 5  # Handle partial fills
  
  # Status Tracking
  track_execution_time: true
  track_slippage: true
  track_fill_rate: true
  
  # Alerts
  alert_on_high_slippage: true
  alert_on_slow_execution: true
  alert_on_order_rejection: true

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 ORDER MODIFICATION & CANCELLATION
# ═══════════════════════════════════════════════════════════════════════════════
order_management:
  # Modification Settings
  allow_price_modification: true
  allow_quantity_modification: false  # Generally not recommended for intraday
  
  # Cancellation Rules
  auto_cancel_on_market_close: true
  auto_cancel_unfilled_orders: true
  cancel_timeout_minutes: 15
  
  # Stop Loss Management
  enable_trailing_stop: true
  trailing_stop_percent: 1.0  # 1% trailing stop
  
  # Target Management
  enable_partial_profit_booking: false
  partial_booking_percent: 50  # Book 50% at first target

# ═══════════════════════════════════════════════════════════════════════════════
# 📢 NOTIFICATIONS (Uses centralized environment variables)
# ═══════════════════════════════════════════════════════════════════════════════
notifications:
  # Telegram Integration
  telegram:
    enabled: false  # Set to true to enable
    bot_token: "${TELEGRAM_BOT_TOKEN}"
    chat_id: "${TELEGRAM_CHAT_ID}"
    
    # Notification Types
    notify_on_order_placed: true
    notify_on_order_filled: true
    notify_on_order_cancelled: true
    notify_on_order_rejected: true
    notify_on_high_slippage: true
    notify_on_execution_delay: true
  
  # Email Integration
  email:
    enabled: false
    smtp_server: "${EMAIL_SMTP_SERVER}"
    smtp_port: "${EMAIL_SMTP_PORT}"
    username: "${EMAIL_USERNAME}"
    password: "${EMAIL_PASSWORD}"
    to_email: "${EMAIL_TO}"

  # Webhook Integration
  webhook:
    enabled: false
    url: "${EXTERNAL_WEBHOOK_URL}"
    timeout: 5
    retry_count: 3

# ═══════════════════════════════════════════════════════════════════════════════
# 📁 DATA STORAGE & LOGGING
# ═══════════════════════════════════════════════════════════════════════════════
logging:
  # Log Configuration
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file_enabled: true
  file_path: "logs/execution_agent.log"
  max_file_size_mb: 50
  backup_count: 10
  
  # Log Details
  log_order_requests: true
  log_order_responses: true
  log_execution_times: true
  log_slippage_details: true
  log_error_details: true

data_storage:
  # Trade Data Storage
  save_trade_data: true
  trade_data_path: "data/execution"
  
  # File Format
  file_format: "parquet"  # parquet, csv
  compression: "snappy"
  
  # Backup Settings
  backup_enabled: true
  backup_interval_hours: 6
  backup_retention_days: 30
  
  # Performance Data
  save_execution_stats: true
  stats_file_path: "data/execution/execution_stats.json"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 AGENT INTEGRATIONS
# ═══════════════════════════════════════════════════════════════════════════════
integrations:
  # Signal Generation Agent
  signal_agent:
    enabled: true
    validate_signals: true
    signal_timeout_minutes: 10
  
  # Market Monitoring Agent
  market_monitoring:
    enabled: true
    use_market_data: true
    use_volatility_filters: true
  
  # Risk Management Agent
  risk_agent:
    enabled: true
    pre_execution_check: true
    post_execution_monitoring: true
  
  # AI Training Agent
  ai_training:
    enabled: false
    feedback_on_execution: true
    model_path: "data/models/execution_model.pkl"

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ SYSTEM SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
system:
  # Performance Settings
  max_concurrent_orders: 10
  api_call_timeout: 10
  cache_duration_seconds: 30
  
  # Memory Management
  max_order_history: 1000
  cleanup_interval_hours: 24
  
  # Error Handling
  max_consecutive_errors: 5
  error_cooldown_minutes: 5
  fallback_to_manual_mode: false
  
  # Debug Settings
  debug_mode: false
  simulate_orders: false  # For testing without actual orders
  log_api_calls: false

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 PERFORMANCE MONITORING
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  # Execution Metrics
  track_execution_time: true
  track_slippage: true
  track_fill_rate: true
  track_rejection_rate: true
  
  # Reporting
  daily_report_enabled: true
  daily_report_time: "16:00"
  weekly_report_enabled: true
  
  # Benchmarking
  benchmark_against_market: false
  benchmark_symbol: "NIFTY50"
  
  # Alerts
  performance_alert_thresholds:
    avg_execution_time_ms: 3000
    avg_slippage_percent: 0.3
    fill_rate_percent: 95
    rejection_rate_percent: 5

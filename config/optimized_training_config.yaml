# Optimized Training Configuration for Backtesting Data
# Specifically tuned for enhanced backtesting system output

# Data Configuration
data:
  input_file: "strategy_results.parquet"
  data_dir: "data/backtest_improved"
  models_dir: "data/models/enhanced"
  
# Prediction Tasks - Optimized for backtesting output
prediction_tasks:
  sharpe_ratio_prediction:
    type: "regression"
    target_columns: ["avg_sharpe_ratio"]
    description: "Predict strategy Sharpe ratio"
    weight: 0.25
    performance_threshold: 1.0
    
  roi_prediction:
    type: "regression"
    target_columns: ["avg_roi"]
    description: "Predict strategy ROI"
    weight: 0.20
    performance_threshold: 5.0
    
  profit_factor_prediction:
    type: "regression"
    target_columns: ["avg_profit_factor"]
    description: "Predict profit factor"
    weight: 0.15
    performance_threshold: 1.2
    
  drawdown_prediction:
    type: "regression"
    target_columns: ["avg_max_drawdown"]
    description: "Predict maximum drawdown"
    weight: 0.15
    performance_threshold: -10.0
    
  consistency_prediction:
    type: "regression"
    target_columns: ["consistency_score"]
    description: "Predict strategy consistency"
    weight: 0.10
    performance_threshold: 0.6
    
  profitability_classification:
    type: "classification"
    target_columns: ["passes_risk_criteria"]
    description: "Classify profitable strategies"
    weight: 0.15
    classes: ["False", "True"]

# Model Configuration - Optimized for Windows 10
models:
  enabled_models: ["lightgbm", "xgboost", "catboost"]
  ensemble_method: "weighted_average"
  
  # LightGBM Configuration (Primary model)
  lightgbm:
    default_params:
      objective: "regression"
      metric: "rmse"
      boosting_type: "gbdt"
      num_leaves: 31
      learning_rate: 0.1
      feature_fraction: 0.8
      bagging_fraction: 0.8
      bagging_freq: 5
      verbose: -1
      device_type: "cpu"  # Use GPU if available
      
  # XGBoost Configuration
  xgboost:
    default_params:
      objective: "reg:squarederror"
      eval_metric: "rmse"
      max_depth: 6
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      tree_method: "hist"  # Use gpu_hist if GPU available
      
  # CatBoost Configuration
  catboost:
    default_params:
      objective: "RMSE"
      depth: 6
      learning_rate: 0.1
      iterations: 1000
      verbose: False
      task_type: "CPU"  # Use GPU if available

# Feature Engineering - Tailored for backtesting data
feature_engineering:
  enabled: true
  create_interaction_features: true
  create_lag_features: false  # Not applicable for strategy-level data
  remove_outliers: true
  outlier_method: "iqr"
  outlier_threshold: 1.5
  
  # Feature groups specific to backtesting data
  performance_features:
    - "avg_sharpe_ratio"
    - "avg_roi" 
    - "avg_profit_factor"
    - "consistency_score"
    
  risk_features:
    - "avg_max_drawdown"
    - "avg_var_95"
    - "avg_cvar_95"
    
  trade_features:
    - "avg_total_trades"
    - "avg_accuracy"
    - "avg_win"
    - "avg_loss"
    
  interaction_pairs:
    - ["avg_sharpe_ratio", "consistency_score"]
    - ["avg_roi", "avg_max_drawdown"]
    - ["avg_profit_factor", "avg_accuracy"]
    - ["avg_win", "avg_loss"]

# Training Configuration
training:
  test_size: 0.2
  validation_size: 0.2
  random_state: 42
  cv_folds: 5
  cv_strategy: "kfold"  # KFold for strategy-level data
  
  # Hyperparameter Optimization
  optuna_enabled: true
  optuna_trials: 100
  optuna_timeout: 1800  # 30 minutes
  optuna_direction: "maximize"
  
  # Early stopping
  early_stopping_rounds: 50
  early_stopping_metric: "rmse"

# Performance Evaluation
evaluation:
  metrics:
    regression:
      - "r2_score"
      - "mean_squared_error"
      - "mean_absolute_error"
      - "explained_variance_score"
    classification:
      - "accuracy_score"
      - "precision_score"
      - "recall_score"
      - "f1_score"
      - "roc_auc_score"
  
  # Model selection criteria
  model_selection:
    primary_metric: "r2_score"
    minimum_score: 0.3
    cross_validation_required: true

# Model Explainability
explainability:
  shap_enabled: true
  feature_importance_enabled: true
  top_features_count: 15
  generate_plots: true
  save_explanations: true

# Model Persistence
persistence:
  auto_save: true
  versioning: true
  max_versions: 5
  compression: true
  
# Hardware Configuration
hardware:
  use_gpu: true  # Auto-detect
  n_jobs: -1     # Use all available cores
  memory_limit: "8GB"
  
# Logging Configuration
logging:
  level: "INFO"
  log_to_file: true
  log_file: "logs/enhanced_training.log"
  detailed_metrics: true
  progress_bar: true

# Prediction Configuration
prediction:
  batch_size: 1000
  confidence_threshold: 0.7
  return_probabilities: true
  
# Strategy Selection Criteria
strategy_selection:
  min_sharpe_ratio: 1.0
  min_profit_factor: 1.2
  max_drawdown: -15.0
  min_consistency_score: 0.6
  min_trades: 30
  
  # Ensemble criteria
  ensemble_size: 5
  diversification_threshold: 0.7  # Max correlation between strategies
# Performance Optimization Configuration
# This file addresses the NumExpr hanging issue and other performance problems

# NumExpr Configuration (fixes hanging issue)
numexpr:
  max_threads: 4        # Reduced from default 12 to prevent hanging
  num_threads: 4        # Explicit thread count
  chunk_size: 1000000   # Optimal chunk size for processing

# Threading Configuration
threading:
  omp_num_threads: 4    # OpenMP threads
  mkl_num_threads: 4    # Intel MKL threads
  openblas_num_threads: 4  # OpenBLAS threads
  
# CUDA Configuration
cuda:
  enabled: true
  memory_fraction: 0.75  # Use 75% of GPU memory
  strategies_per_batch: 2  # Process 2 strategies at a time (reduced from 4)
  max_concurrent_strategies: 2  # Maximum concurrent strategies
  
# Parallel Processing
parallel:
  max_workers: 4        # Maximum worker processes
  concurrent_files: 3   # Process 3 files concurrently
  chunk_size: 10000     # Data chunk size for processing

# Memory Management
memory:
  cleanup_frequency: 5   # Clean up every 5 operations
  max_memory_usage: 0.85 # Use maximum 85% of available memory
  gc_threshold: 1000     # Garbage collection threshold

# Strategy Evolution Specific
strategy_evolution:
  population_size: 20    # Reduced from 50 for faster processing
  max_generations: 50    # Reduced from 100
  elite_size: 5         # Top performers to keep
  mutation_rate: 0.15   # Mutation rate for genetic algorithm
  crossover_rate: 0.8   # Crossover rate
  
# Backtesting Configuration
backtesting:
  min_data_size_for_gpu: 1000    # Minimum data size to use GPU
  min_trades_for_metrics: 1      # Minimum trades to calculate metrics
  max_holding_period: 100        # Maximum bars to hold position
  min_signal_distance: 3         # Minimum bars between signals

# Logging Configuration
logging:
  level: INFO
  max_file_size: 10MB
  backup_count: 5
  performance_logging: true
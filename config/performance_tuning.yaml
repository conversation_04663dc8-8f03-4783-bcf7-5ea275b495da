# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 PERFORMANCE TUNING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Adjust these values to fine-tune parallel processing performance

# CPU Parallel Processing
cpu_processing:
  concurrent_files: 5           # Number of files processed in parallel (CPU mode)
  max_workers: 8               # Maximum worker processes
  
# CUDA Parallel Processing  
cuda_processing:
  strategies_per_batch: 4      # Number of strategies processed together on GPU
  cuda_batch_size: 8192        # CUDA kernel batch size
  chunk_size: 50000           # Data chunk size for large datasets
  
# Performance Thresholds (when to use CUDA vs CPU)
thresholds:
  min_rows_for_cuda: 5000      # Minimum rows to use CUDA (lower = more CUDA usage)
  min_signals_for_cuda: 10     # Minimum signals to use CUDA
  min_trades_for_cuda: 5       # Minimum trades to use CUDA
  max_rows_for_cpu: 100000     # Maximum rows before forcing CUDA
  
# Memory Management
memory:
  cleanup_frequency: 100       # GPU memory cleanup frequency
  max_memory_usage: 0.85       # Maximum GPU memory usage (85%)
  
# Testing Configurations
test_modes:
  quick_test:
    max_symbols: 5
    max_strategies: 5
    concurrent_files: 2
  
  medium_test:
    max_symbols: 20
    max_strategies: 10
    concurrent_files: 4
    
  full_test:
    max_symbols: null           # All symbols
    max_strategies: null        # All strategies  
    concurrent_files: 8
# ✅ Signal Generation Agent Configuration
# Comprehensive configuration for real-time trading signal generation and validation

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 SHARED CONFIGURATION REFERENCE
# ═══════════════════════════════════════════════════════════════════════════════
# This agent uses centralized environment configuration
# See: config/environment_config.yaml and .env file
# SmartAPI credentials are managed through other agents (Market Monitoring, Execution)

# ═══════════════════════════════════════════════════════════════════════════════
# 📥 INPUT SOURCES CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
input_sources:
  # Market Data Sources
  market_data:
    primary_source: "market_monitoring_agent"  # or "smartapi_websocket"
    fallback_source: "historical_data"
    data_refresh_interval_seconds: 1
    required_indicators:
      - "ema_5"
      - "ema_10"
      - "ema_13"
      - "ema_20"
      - "ema_21"
      - "ema_30"
      - "ema_50"
      - "ema_100"
      - "sma_20"
      - "rsi_5"
      - "rsi_14"
      - "macd"
      - "macd_signal"
      - "stoch_k"
      - "stoch_d"
      - "cci"
      - "adx"
      - "mfi"
      - "bb_upper"
      - "bb_lower"
      - "atr"
      - "vwap"
      - "supertrend"
      - "donchian_high"
      - "donchian_low"
  
  # Strategy Configuration
  strategy_config:
    config_path: "config/strategies.yaml"
    auto_reload: true
    reload_interval_minutes: 5
    
  # Market Context
  market_context:
    regime_detection: true
    volatility_monitoring: true
    liquidity_analysis: true

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 STRATEGY EVALUATION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
strategy_evaluation:
  # Strategy Config Path
  strategy_config_path: "config/strategies.yaml"
  
  # Evaluation Engine
  evaluation_engine:
    method: "polars_eval"  # "polars_eval", "pandas_eval", "manual"
    enable_compilation: true
    cache_compiled_expressions: true
    
  # Performance Optimization
  performance:
    parallel_evaluation: true
    max_workers: 4
    batch_size: 100
    enable_lazy_evaluation: true
    
  # Expression Evaluation
  expression_evaluation:
    timeout_seconds: 5
    enable_safety_checks: true
    allowed_functions:
      - "rolling"
      - "shift"
      - "mean"
      - "std"
      - "max"
      - "min"

# ═══════════════════════════════════════════════════════════════════════════════
# ⚖️ RISK MANAGEMENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
risk_management:
  # Stop Loss & Take Profit
  default_risk_reward_ratio: 2.0
  atr_stop_loss_multiplier: 2.0
  atr_take_profit_multiplier: 4.0
  
  # Risk Limits
  max_daily_risk_percent: 5.0
  max_position_risk_percent: 2.0
  max_portfolio_risk_percent: 10.0
  
  # Regime-based Adjustments
  regime_adjustments:
    bull: 1.2      # Increase RR ratio in bull market
    bear: 0.8      # Decrease RR ratio in bear market
    sideways: 1.0  # Normal RR ratio in sideways market
    
  # Volatility Adjustments
  volatility_adjustments:
    high: 0.8      # Reduce position size in high volatility
    medium: 1.0    # Normal position size
    low: 1.2       # Increase position size in low volatility

# ═══════════════════════════════════════════════════════════════════════════════
# 💰 POSITION SIZING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
position_sizing:
  # Default Method
  default_method: "fixed_fraction"  # "kelly", "fixed_fraction", "volatility_scaled"
  
  # Capital Allocation
  capital_allocation:
    total_capital: 100000  # Rs 1,00,000
    max_position_size_percent: 2.0  # 2% per position
    max_risk_per_trade_percent: 1.0  # 1% risk per trade
    max_daily_risk_percent: 5.0  # 5% daily risk limit
    intraday_margin_multiplier: 3.5  # 3.5x intraday margin
    
  # Kelly Criterion Settings
  kelly_criterion:
    enable: true
    max_kelly_fraction: 0.25  # Cap at 25%
    min_trades_for_kelly: 30  # Minimum trades needed for Kelly calculation
    lookback_period_days: 90
    
  # Volatility Scaling
  volatility_scaling:
    enable: true
    atr_multiplier: 2.0
    volatility_lookback_period: 20
    
  # Position Limits
  position_limits:
    max_positions_per_symbol: 2
    max_total_positions: 10
    max_strategy_allocation_percent: 20.0

# ═══════════════════════════════════════════════════════════════════════════════
# ✅ SIGNAL VALIDATION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
signal_validation:
  # Confidence Thresholds
  min_confidence: 0.6
  high_confidence_threshold: 0.8
  
  # Time Filters
  time_filters:
    market_hours_only: true
    market_open_time: "09:20"
    market_close_time: "15:00"
    avoid_first_minutes: 10  # Avoid first 10 minutes
    avoid_last_minutes: 30   # Avoid last 30 minutes
    
  # Liquidity Checks
  liquidity_checks:
    min_volume_ratio: 1.5  # 1.5x average volume
    min_daily_volume: 100000
    max_bid_ask_spread_percent: 0.5
    
  # Risk Filters
  risk_filters:
    max_daily_risk_percent: 5.0
    max_position_correlation: 0.7
    max_sector_exposure_percent: 30.0
    
  # Cooldown Settings
  cooldown:
    minutes_between_signals: 5  # 5 minutes between signals for same symbol-strategy
    max_signals_per_symbol_per_day: 3
    max_signals_per_strategy_per_day: 10
    
  # Signal Quality Filters
  quality_filters:
    min_risk_reward_ratio: 1.5
    max_stop_loss_percent: 3.0
    min_profit_target_percent: 2.0

# ═══════════════════════════════════════════════════════════════════════════════
# 📤 OUTPUT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
output_config:
  # Storage Settings
  storage:
    signals_path: "data/signals"
    performance_path: "data/performance"
    logs_path: "logs"
    enable_database_logging: false
    database_config:
      type: "postgresql"  # "postgresql", "mongodb", "sqlite"
      connection_string: ""
      
  # File Logging
  logging:
    enable_file_logging: true
    log_dir: "logs"
    level: "INFO"
    max_file_size_mb: 10
    backup_count: 5
    
  # Real-time Feeds
  realtime_feeds:
    enable_websocket_feed: false
    websocket_port: 8765
    enable_rest_api: false
    rest_api_port: 8080

# ═══════════════════════════════════════════════════════════════════════════════
# 📱 NOTIFICATIONS CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
notifications:
  # Telegram Settings
  telegram:
    enable: true
    bot_token: "YOUR_BOT_TOKEN"
    chat_id: "YOUR_CHAT_ID"
    send_all_signals: true
    send_high_confidence_only: false
    high_confidence_threshold: 0.8
    
  # Slack Settings (Future)
  slack:
    enable: false
    webhook_url: ""
    channel: "#trading-signals"
    
  # Email Settings (Future)
  email:
    enable: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    recipients: []
    
  # Webhook Settings
  webhooks:
    enable: false
    endpoints: []

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 INTEGRATIONS CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
integrations:
  # Market Monitoring Agent
  market_monitoring:
    enable: true
    config_path: "config/market_monitoring_config.yaml"
    auto_start: true
    
  # AI Training Agent
  ai_training:
    enable: true
    config_path: "config/ai_training_config.yaml"
    model_path: "data/models"
    enable_signal_enhancement: true
    confidence_boost_factor: 0.1
    
  # Risk Management Agent
  risk_management:
    enable: true
    config_path: "config/risk_management_config.yaml"
    auto_validate: true

  # Order Execution Agent (Future)
  order_execution:
    enable: false
    config_path: "config/order_execution_config.yaml"
    auto_execute: false
    
  # Strategy Evolution Agent (Future)
  strategy_evolution:
    enable: false
    config_path: "config/strategy_evolution_config.yaml"

# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ PERFORMANCE CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  # Processing Optimization
  processing:
    enable_multiprocessing: true
    max_workers: 4
    chunk_size: 1000
    enable_caching: true
    cache_size_mb: 100
    
  # Memory Management
  memory:
    max_memory_usage_mb: 500
    enable_garbage_collection: true
    gc_interval_seconds: 60
    
  # Monitoring
  monitoring:
    enable_performance_tracking: true
    track_execution_time: true
    track_memory_usage: true
    performance_log_interval_minutes: 5
    
  # Optimization
  optimization:
    enable_jit_compilation: false  # Requires numba
    enable_vectorization: true
    prefer_polars_over_pandas: true
    enable_lazy_evaluation: true

version: '3.8'

services:
  # Execution Agent
  execution-agent:
    build:
      context: .
      target: production
    container_name: intraday-execution-agent
    environment:
      - TRADING_MODE=paper
      - PYTHONUNBUFFERED=1
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    ports:
      - "8001:8000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - trading-network

  # Market Monitoring Agent
  market-monitoring:
    build:
      context: .
      target: production
    container_name: intraday-market-monitoring
    environment:
      - TRADING_MODE=paper
      - PYTHONUNBUFFERED=1
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    ports:
      - "8002:8000"
    restart: unless-stopped
    command: ["python", "main.py", "--agent", "market_monitoring", "--trading-mode", "paper", "--health-port", "8000"]
    depends_on:
      - execution-agent
    networks:
      - trading-network

  # Signal Generation Agent
  signal-generation:
    build:
      context: .
      target: production
    container_name: intraday-signal-generation
    environment:
      - TRADING_MODE=paper
      - PYTHONUNBUFFERED=1
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    ports:
      - "8003:8000"
    restart: unless-stopped
    command: ["python", "main.py", "--agent", "signal_generation", "--trading-mode", "paper", "--health-port", "8000"]
    depends_on:
      - market-monitoring
    networks:
      - trading-network

  # Risk Management Agent
  risk-management:
    build:
      context: .
      target: production
    container_name: intraday-risk-management
    environment:
      - TRADING_MODE=paper
      - PYTHONUNBUFFERED=1
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    ports:
      - "8004:8000"
    restart: unless-stopped
    command: ["python", "main.py", "--agent", "risk_management", "--trading-mode", "paper", "--health-port", "8000"]
    depends_on:
      - execution-agent
    networks:
      - trading-network

  # Development Environment
  dev:
    build:
      context: .
      target: development
    container_name: intraday-dev
    environment:
      - TRADING_MODE=paper
      - PYTHONUNBUFFERED=1
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    env_file:
      - .env
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "8000:8000"
      - "8888:8888"  # Jupyter notebook
    command: ["tail", "-f", "/dev/null"]  # Keep container running
    networks:
      - trading-network

networks:
  trading-network:
    driver: bridge

volumes:
  logs-data:
  trading-data:

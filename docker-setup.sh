#!/bin/bash

# Docker Setup Script for Intraday AI Trading System
# This script helps set up and manage the Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed"
}

# Create necessary directories
create_directories() {
    print_header "Creating necessary directories..."
    
    mkdir -p logs/risk_management
    mkdir -p logs/execution
    mkdir -p logs/market_monitoring
    mkdir -p logs/signal_generation
    mkdir -p data/backtest
    mkdir -p data/model
    mkdir -p data/historical
    
    print_status "Directories created successfully"
}

# Check environment file
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_warning "Please edit .env file with your actual credentials"
        else
            print_error ".env.example not found. Please create .env file manually"
            exit 1
        fi
    else
        print_status ".env file found"
    fi
}

# Build Docker images
build_images() {
    print_header "Building Docker images..."
    
    # Build development image
    print_status "Building development image..."
    docker build --target development -t intraday-ai:dev .
    
    # Build production image
    print_status "Building production image..."
    docker build --target production -t intraday-ai:prod .
    
    print_status "Docker images built successfully"
}

# Test Docker setup
test_setup() {
    print_header "Testing Docker setup..."
    
    # Test execution agent
    print_status "Testing execution agent container..."
    docker run --rm --env-file .env intraday-ai:prod python -c "
import sys
sys.path.append('/app')
from agents.execution_agent import ExecutionAgent
print('[SUCCESS] Execution agent can be imported')
"
    
    print_status "Docker setup test completed successfully"
}

# Start services
start_services() {
    print_header "Starting trading system services..."
    
    # Start only execution agent first
    print_status "Starting execution agent..."
    docker-compose up -d execution-agent
    
    # Wait for execution agent to be healthy
    print_status "Waiting for execution agent to be ready..."
    sleep 30
    
    # Check health
    if docker-compose ps execution-agent | grep -q "healthy"; then
        print_status "Execution agent is healthy"
    else
        print_warning "Execution agent health check failed, but continuing..."
    fi
    
    print_status "Services started successfully"
    print_status "Access logs with: docker-compose logs -f execution-agent"
}

# Stop services
stop_services() {
    print_header "Stopping trading system services..."
    docker-compose down
    print_status "Services stopped successfully"
}

# Show logs
show_logs() {
    print_header "Showing service logs..."
    docker-compose logs -f
}

# Show status
show_status() {
    print_header "Service status:"
    docker-compose ps
    
    print_header "Container health:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Main menu
show_menu() {
    echo ""
    echo "=== Intraday AI Trading System - Docker Setup ==="
    echo "1. Full setup (check, build, test)"
    echo "2. Build images only"
    echo "3. Start services"
    echo "4. Stop services"
    echo "5. Show logs"
    echo "6. Show status"
    echo "7. Test setup"
    echo "8. Exit"
    echo ""
}

# Main script
main() {
    print_header "Intraday AI Trading System - Docker Setup"
    
    # Always check Docker first
    check_docker
    
    if [ $# -eq 0 ]; then
        # Interactive mode
        while true; do
            show_menu
            read -p "Select option [1-8]: " choice
            
            case $choice in
                1)
                    create_directories
                    check_env_file
                    build_images
                    test_setup
                    print_status "Full setup completed! Use option 3 to start services."
                    ;;
                2)
                    build_images
                    ;;
                3)
                    start_services
                    ;;
                4)
                    stop_services
                    ;;
                5)
                    show_logs
                    ;;
                6)
                    show_status
                    ;;
                7)
                    test_setup
                    ;;
                8)
                    print_status "Goodbye!"
                    exit 0
                    ;;
                *)
                    print_error "Invalid option. Please select 1-8."
                    ;;
            esac
        done
    else
        # Command line mode
        case $1 in
            "setup")
                create_directories
                check_env_file
                build_images
                test_setup
                ;;
            "build")
                build_images
                ;;
            "start")
                start_services
                ;;
            "stop")
                stop_services
                ;;
            "logs")
                show_logs
                ;;
            "status")
                show_status
                ;;
            "test")
                test_setup
                ;;
            *)
                echo "Usage: $0 [setup|build|start|stop|logs|status|test]"
                exit 1
                ;;
        esac
    fi
}

# Run main function
main "$@"

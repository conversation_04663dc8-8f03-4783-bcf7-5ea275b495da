# 🚀 Enhanced Backtesting Agent - Comprehensive Guide

## Overview

The Enhanced Backtesting Agent is a comprehensive, multi-strategy, multi-timeframe backtesting system designed for the Indian trading market. It provides advanced features for testing trading strategies with realistic market conditions, comprehensive performance metrics, and intelligent analysis capabilities.

## 🌟 Key Features

### 🔁 1. Multi-Strategy & Multi-Timeframe Backtesting
- **Individual Strategy Testing**: Test strategies one by one
- **Batch Processing**: Test multiple strategies simultaneously
- **Multi-Timeframe Support**: 5min, 15min, 30min, 1hr, 4hr, 1day
- **Asset Type Support**: NIFTY/BANKNIFTY, CE/PE options, weekly/monthly expiry
- **Regime-Filtered Testing**: Bullish, volatile, sideways market conditions

### 🧠 2. Smart Backtesting Modes
- **Deterministic Mode**: Historical conditions matched exactly (no randomness)
- **Probabilistic Mode**: Monte Carlo bootstrapping for robustness estimation
- **Adaptive AI Mode**: AI-driven parameter adjustment based on prior runs

### 🧪 3. Detailed Performance Metrics
- **Basic Metrics**: ROI, Accuracy, Expectancy, Win/Loss ratio
- **Risk Metrics**: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, VaR, CVaR
- **Advanced Metrics**: Profit Factor, <PERSON>, Ulcer Index
- **Capital Efficiency**: Capital Utilization, Return on Margin, Leverage Efficiency
- **Liquidity Metrics**: Slippage, Market Impact, Execution Quality

### 🧰 4. Capital & Risk Modeling
- **Position Sizing**: Fixed risk, Kelly criterion, Optimal F
- **Transaction Costs**: Realistic Indian market costs (brokerage, STT, slippage)
- **Margin Modeling**: Intraday leverage (3.5x), options margins
- **Risk Management**: Stop loss, take profit, trailing stops

### 🗂️ 5. Scenario-Based & Regime-Based Testing
- **Market Regimes**: Trending up/down, sideways, volatile
- **Event Scenarios**: Expiry day, budget day, Fed announcements
- **Regime Detection**: HMM, threshold-based, ML classifier methods

### 🧬 6. Parameter Sweep & Optimization
- **Optimization Methods**: Optuna, Grid Search, Random Search, Genetic Algorithm
- **Parameter Ranges**: Technical indicators, risk management, position sizing
- **Walk-Forward Testing**: Model retraining at intervals
- **Multi-Objective Optimization**: Sharpe ratio, drawdown, profit factor

### 🧾 7. Result Logging & Versioning
- **Versioned Storage**: Detailed trade logs, PnL curves, metrics summaries
- **Multiple Formats**: Parquet, CSV, JSON with compression
- **Database Integration**: SQLite/PostgreSQL support
- **Real-time Monitoring**: Live dashboard with progress tracking

### 📊 8. Visualization & Debugging
- **Interactive Charts**: PnL curves, drawdown charts, performance heatmaps
- **Trade Analysis**: Entry/exit visualization, signal debugging
- **Benchmark Comparison**: Buy & hold comparison
- **Export Options**: PNG, PDF, SVG formats

### 🔍 9. Signal Debugging & Replay
- **Trade Replay**: Sequential trade execution debugging
- **Signal Validation**: False positive/negative detection
- **Execution Timing**: Latency impact analysis
- **Visual Inspection**: OHLCV candle charts with signals

### 🤖 10. LLM-Explainable Results
- **Auto-Summarization**: Human-readable performance explanations
- **Risk Analysis**: Drawdown and volatility insights
- **Regime Analysis**: Performance across market conditions
- **Recommendations**: Strategy optimization suggestions

## 🚀 Quick Start

### Installation

```bash
# Install required dependencies
pip install polars pyarrow asyncio pyyaml numpy

# Optional dependencies for advanced features
pip install cupy cudf optuna plotly bokeh  # GPU acceleration and visualization
```

### Basic Usage

```python
import asyncio
from agents.enhanced_backtesting_agent import EnhancedBacktestingAgent

async def main():
    # Create agent with default configuration
    agent = EnhancedBacktestingAgent()
    
    # Initialize and run backtesting
    if await agent.initialize():
        success = await agent.start_backtesting()
        if success:
            print("✅ Backtesting completed successfully!")
        await agent.stop()

# Run the backtesting
asyncio.run(main())
```

### Command Line Usage

```bash
# Run demo backtesting
python agents/run_enhanced_backtesting_agent.py --demo

# Run with custom configuration
python agents/run_enhanced_backtesting_agent.py --config config/enhanced_backtesting_config.yaml

# Run with specific parameters
python agents/run_enhanced_backtesting_agent.py \
    --mode probabilistic \
    --max-strategies 10 \
    --max-symbols 5 \
    --no-gpu
```

## ⚙️ Configuration

### Configuration File Structure

The system uses a comprehensive YAML configuration file (`config/enhanced_backtesting_config.yaml`):

```yaml
general:
  agent_name: "Enhanced Backtesting Agent"
  data_directory: "data/features"
  output_directory: "data/backtest"
  strategies_config: "config/strategies.yaml"
  max_symbols: null  # null = unlimited
  max_strategies: null

multi_timeframe:
  timeframes: ["5min", "15min", "30min", "1hr"]
  process_timeframes_parallel: true
  
backtesting_modes:
  enable_deterministic: true
  enable_probabilistic: false
  enable_adaptive_ai: false

capital_risk_modeling:
  initial_capital: 100000.0
  risk_per_trade_pct: 1.0
  max_concurrent_trades: 5
  risk_reward_ratios: [[1, 1.5], [1, 2.0], [1.5, 2.0], [2, 3.0]]
```

### Strategy Configuration

Strategies are defined in `config/strategies.yaml`:

```yaml
strategies:
  - name: "RSI_Oversold_Strategy"
    long: "rsi_14 < 30 & volume > volume.rolling(20).mean() * 1.5"
    short: "rsi_14 > 70 & volume > volume.rolling(20).mean() * 1.5"
    capital: 100000
    
  - name: "EMA_Crossover_Strategy"
    long: "ema_5 > ema_21 & ema_5.shift(1) <= ema_21.shift(1)"
    short: "ema_5 < ema_21 & ema_5.shift(1) >= ema_21.shift(1)"
    capital: 100000
```

## 📊 Performance Metrics

### Basic Metrics
- **ROI (Return on Investment)**: Total return percentage
- **Accuracy**: Percentage of profitable trades
- **Expectancy**: Average return per trade
- **Win/Loss Ratio**: Average win divided by average loss
- **Total Trades**: Number of trades executed

### Risk-Adjusted Metrics
- **Sharpe Ratio**: Risk-adjusted return (return/volatility)
- **Sortino Ratio**: Downside risk-adjusted return
- **Calmar Ratio**: Return divided by maximum drawdown
- **Maximum Drawdown**: Largest peak-to-trough decline
- **VaR/CVaR**: Value at Risk and Conditional Value at Risk

### Advanced Metrics
- **Profit Factor**: Gross profit divided by gross loss
- **Recovery Factor**: Net profit divided by maximum drawdown
- **Kelly Criterion**: Optimal position size percentage
- **Ulcer Index**: Drawdown-based risk measure

## 🔧 Advanced Features

### Monte Carlo Simulation

```python
# Enable probabilistic mode for Monte Carlo analysis
agent.config.backtesting_mode = BacktestMode.PROBABILISTIC
agent.config.enable_probabilistic = True

# Configure simulation parameters
probabilistic_config = {
    'num_simulations': 1000,
    'confidence_intervals': [0.05, 0.95],
    'bootstrap_method': 'block_bootstrap',
    'block_size': 252
}
```

### Parameter Optimization

```python
# Enable parameter optimization
agent.config.enable_parameter_optimization = True

# Configure Optuna optimization
optimization_config = {
    'method': 'optuna',
    'n_trials': 100,
    'objective_metric': 'sharpe_ratio',
    'parameter_ranges': {
        'rsi_period': [5, 50],
        'ema_short': [5, 20],
        'stop_loss_pct': [0.5, 5.0]
    }
}
```

### Regime-Based Testing

```python
# Enable regime testing
agent.config.enable_regime_testing = True

# Configure market regimes
regime_config = {
    'trending_up': {
        'detection_method': 'price_momentum',
        'min_trend_strength': 0.7,
        'preferred_strategies': ['momentum_strategies']
    },
    'sideways': {
        'detection_method': 'volatility',
        'max_volatility': 0.15,
        'preferred_strategies': ['mean_reversion']
    }
}
```

## 📁 Output Structure

```
data/backtest/
├── enhanced_backtest_results_20240719_143022.parquet  # Main results
├── latest_results.parquet                             # Latest results copy
├── versions/                                          # Detailed results
│   ├── strategy1_symbol1_5min_detailed.json
│   └── strategy2_symbol2_15min_detailed.json
├── summaries/                                         # LLM summaries
│   └── backtest_summary_20240719_143022.md
└── charts/                                           # Visualizations
    ├── performance_comparison.png
    └── risk_return_scatter.png
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
python -m pytest test/test_enhanced_backtesting_agent.py -v

# Run specific test categories
python -m pytest test/test_enhanced_backtesting_agent.py::TestEnhancedBacktestingAgent::test_signal_generation -v
```

## 🔗 Integration

### With Strategy Evolution Agent

```python
# Results are automatically sent to Strategy Evolution Agent
evolution_integration = {
    'enabled': True,
    'endpoint': 'http://localhost:8001/strategy_evolution',
    'send_results': True
}
```

### With AI Training Agent

```python
# Training data is sent to AI Training Agent
ai_training_integration = {
    'enabled': True,
    'endpoint': 'http://localhost:8002/ai_training',
    'send_training_data': True
}
```

## 🚨 Troubleshooting

### Common Issues

1. **GPU Memory Error**
   ```bash
   # Reduce chunk size or disable GPU
   python run_enhanced_backtesting_agent.py --no-gpu
   ```

2. **Out of Memory**
   ```yaml
   # Reduce processing limits in config
   max_symbols: 10
   max_strategies: 20
   chunk_size: 100000
   ```

3. **No Feature Data Found**
   ```bash
   # Check data directory structure
   ls -la data/features/
   # Ensure files match pattern: features_5min.parquet
   ```

### Performance Optimization

1. **Enable GPU Acceleration**
   ```bash
   pip install cupy cudf
   ```

2. **Optimize Chunk Size**
   ```yaml
   performance:
     parallel_processing:
       chunk_size: 500000  # Adjust based on available memory
   ```

3. **Use Multiprocessing**
   ```yaml
   performance:
     parallel_processing:
       enable_multiprocessing: true
       max_workers: 8
   ```

## 📈 Best Practices

1. **Start Small**: Begin with limited symbols and strategies for testing
2. **Monitor Memory**: Use system monitoring to track resource usage
3. **Regular Cleanup**: Enable aggressive memory cleanup for large datasets
4. **Validate Results**: Always validate backtesting results against known benchmarks
5. **Document Changes**: Keep track of configuration changes and their impact

## 🤝 Contributing

1. Follow the existing code structure and naming conventions
2. Add comprehensive tests for new features
3. Update documentation for any new functionality
4. Ensure backward compatibility with existing configurations

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the test files for usage examples
3. Examine the configuration files for available options
4. Check logs in the `logs/` directory for detailed error information

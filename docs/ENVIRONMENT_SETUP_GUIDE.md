# Environment Setup Guide

## 🚀 Overview

This guide explains how to set up the centralized environment configuration for all trading agents. The system now uses a unified approach to manage credentials and configuration across all agents.

## ✨ Key Benefits

### 🔗 **Centralized Configuration**
- **Single Source**: All environment variables in one `.env` file
- **Shared Credentials**: SmartAPI credentials used by all agents
- **Consistent Settings**: Unified notification and database settings
- **Easy Management**: Update credentials in one place

### 🔒 **Enhanced Security**
- **Environment Variables**: Sensitive data not stored in code
- **Template System**: Safe sharing of configuration structure
- **Credential Validation**: Automatic validation of required settings
- **Secure Defaults**: Production-ready security settings

### ⚙️ **Simplified Setup**
- **Interactive Setup**: Guided credential collection
- **Automatic Validation**: Real-time configuration checking
- **Quick Start**: One-command environment setup
- **Error Prevention**: Clear error messages and fixes

## 📁 File Structure

```
├── .env.template          # Template for environment variables
├── .env                   # Your actual environment variables (DO NOT COMMIT)
├── config/
│   ├── environment_config.yaml    # Shared environment configuration
│   ├── market_monitoring_config.yaml
│   ├── signal_generation_config.yaml
│   ├── risk_management_config.yaml
│   └── execution_config.yaml
├── utils/
│   └── config_loader.py   # Centralized configuration loader
└── setup_environment.py   # Interactive setup script
```

## 🔧 Quick Setup

### 1. **Interactive Setup (Recommended)**

Run the interactive setup script:

```bash
python setup_environment.py
```

This will:
- ✅ Create `.env` file from template
- 📊 Collect your SmartAPI credentials
- 📱 Set up Telegram notifications (optional)
- 📧 Configure email alerts (optional)
- 🔒 Generate security keys
- ✅ Validate all configurations

### 2. **Manual Setup**

If you prefer manual setup:

```bash
# Copy template to .env
cp .env.template .env

# Edit .env file with your credentials
nano .env  # or use your preferred editor

# Validate setup
python -c "from utils.config_loader import check_environment_setup; check_environment_setup()"
```

## 📊 Required Credentials

### **Angel One SmartAPI** (Required)

Get these from [Angel One Developer Portal](https://smartapi.angelbroking.com/):

```bash
SMARTAPI_API_KEY=your_api_key_here
SMARTAPI_USERNAME=your_client_code_here
SMARTAPI_PASSWORD=your_pin_here
SMARTAPI_TOTP_TOKEN=your_qr_token_here
```

### **Telegram Notifications** (Optional)

Set up a Telegram bot:

1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Create new bot: `/newbot`
3. Get bot token
4. Start chat with your bot
5. Get chat ID from [@userinfobot](https://t.me/userinfobot)

```bash
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
```

### **Email Notifications** (Optional)

For Gmail, use App Password:

```bash
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
```

## 🔍 Configuration Validation

### **Check Environment Setup**

```python
from utils.config_loader import check_environment_setup

if check_environment_setup():
    print("✅ Environment setup is valid")
else:
    print("❌ Environment setup needs attention")
```

### **Validate Agent Configuration**

```python
from utils.config_loader import load_config_for_agent

# Load and validate specific agent config
config = load_config_for_agent('execution')
print("✅ Execution agent configuration loaded successfully")
```

### **Test SmartAPI Credentials**

```python
from utils.config_loader import ConfigurationLoader

loader = ConfigurationLoader()
credentials = loader.get_smartapi_credentials()

# Check if all required credentials are present
required = ['api_key', 'username', 'password', 'totp_token']
missing = [key for key in required if not credentials[key]]

if missing:
    print(f"❌ Missing credentials: {missing}")
else:
    print("✅ All SmartAPI credentials present")
```

## 🔧 Agent Integration

### **Using Centralized Configuration**

All agents now use the centralized configuration loader:

```python
from utils.config_loader import load_config_for_agent

class YourAgent:
    def __init__(self):
        # Load configuration with automatic validation
        self.config = load_config_for_agent('your_agent')
        
        # SmartAPI credentials are automatically resolved
        smartapi_config = self.config.get('smartapi', {})
        api_key = smartapi_config.get('api_key')  # Resolved from environment
```

### **Environment Variable Resolution**

Configuration files use `${VAR_NAME}` syntax:

```yaml
# In your_agent_config.yaml
smartapi:
  api_key: "${SMARTAPI_API_KEY}"      # Resolved from .env
  username: "${SMARTAPI_USERNAME}"    # Resolved from .env
  
notifications:
  telegram:
    bot_token: "${TELEGRAM_BOT_TOKEN}" # Resolved from .env
```

## 🔒 Security Best Practices

### **Environment File Security**

```bash
# Make sure .env is in .gitignore
echo ".env" >> .gitignore

# Set appropriate file permissions (Linux/Mac)
chmod 600 .env

# Verify .env is not tracked by git
git status --ignored
```

### **Credential Management**

1. **Never commit `.env` file** to version control
2. **Use strong passwords** for all services
3. **Rotate API keys regularly** (every 3-6 months)
4. **Use environment-specific values** for different deployments
5. **Consider secrets management** for production environments

### **Production Deployment**

For production environments:

```bash
# Set environment variables directly (more secure)
export SMARTAPI_API_KEY="your_production_key"
export SMARTAPI_USERNAME="your_production_username"

# Or use secrets management service
# AWS Secrets Manager, Azure Key Vault, etc.
```

## 🧪 Testing Your Setup

### **1. Environment Validation**

```bash
python -c "from utils.config_loader import check_environment_setup; check_environment_setup()"
```

### **2. Agent Configuration Testing**

```bash
# Test Market Monitoring Agent
python -c "from utils.config_loader import load_config_for_agent; load_config_for_agent('market_monitoring')"

# Test Execution Agent
python -c "from utils.config_loader import load_config_for_agent; load_config_for_agent('execution')"
```

### **3. SmartAPI Connection Test**

```bash
# Test SmartAPI authentication (if implemented)
python agents/run_execution_agent.py --health-check-only
```

### **4. Notification Testing**

```bash
# Test Telegram notifications (if configured)
python -c "
from utils.config_loader import ConfigurationLoader
loader = ConfigurationLoader()
creds = loader.get_notification_credentials()
print('Telegram configured:', bool(creds['telegram']['bot_token']))
"
```

## 🔧 Troubleshooting

### **Common Issues**

#### **1. Missing .env File**

```bash
❌ Environment file not found: .env
💡 Run: cp .env.template .env and fill in your values
```

**Solution**: Copy template and fill in credentials
```bash
cp .env.template .env
nano .env  # Edit with your values
```

#### **2. Environment Variables Not Resolved**

```bash
❌ Environment variable not set: SMARTAPI_API_KEY
```

**Solution**: Check .env file format
```bash
# Correct format (no spaces around =)
SMARTAPI_API_KEY=your_actual_key

# Incorrect format
SMARTAPI_API_KEY = your_actual_key  # ❌ Spaces around =
```

#### **3. Invalid SmartAPI Credentials**

```bash
❌ SmartAPI api_key seems too short
```

**Solution**: Verify credentials from Angel One portal
- Check API key length (should be 8+ characters)
- Verify client code format
- Ensure TOTP token is from QR code

#### **4. Configuration Validation Errors**

```bash
❌ Configuration validation failed for execution
   - Missing SmartAPI password
```

**Solution**: Check environment variable names
```bash
# Make sure variable names match exactly
SMARTAPI_API_KEY=...     # ✅ Correct
ANGEL_API_KEY=...        # ❌ Old format
```

### **Debug Mode**

Enable debug mode for detailed logging:

```bash
# In .env file
DEBUG_MODE=true
LOG_LEVEL=DEBUG
```

### **Reset Environment**

To start fresh:

```bash
# Remove existing .env
rm .env

# Run setup again
python setup_environment.py
```

## 📚 Migration from Old Configuration

If you have existing agent configurations with hardcoded credentials:

### **1. Backup Existing Configs**

```bash
cp config/market_monitoring_config.yaml config/market_monitoring_config.yaml.backup
```

### **2. Run Migration**

The new configuration files automatically use environment variables. Your old configs will work, but update them to use the new format:

```yaml
# Old format
smartapi:
  api_key: "YOUR_API_KEY"

# New format  
smartapi:
  api_key: "${SMARTAPI_API_KEY}"
```

### **3. Test Migration**

```bash
python -c "from utils.config_loader import load_config_for_agent; load_config_for_agent('market_monitoring')"
```

## 🎯 Next Steps

After setting up your environment:

1. **🧪 Test Individual Agents**
   ```bash
   python agents/run_execution_agent.py --demo
   ```

2. **📊 Run Backtesting**
   ```bash
   python agents/run_enhanced_backtesting.py
   ```

3. **🔧 Customize Configurations**
   - Edit agent-specific settings in `config/` directory
   - Adjust risk parameters, timeframes, etc.

4. **📈 Start Trading**
   - Begin with paper trading (`PAPER_TRADING_ENABLED=true`)
   - Monitor performance and adjust settings
   - Gradually move to live trading

## 🆘 Support

For issues:
1. Check this guide first
2. Validate environment setup
3. Check agent-specific documentation
4. Review log files in `logs/` directory

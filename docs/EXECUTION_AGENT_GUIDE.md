# Execution Agent - Comprehensive Guide

## 🚀 Overview

The Execution Agent is a sophisticated order lifecycle management system designed for automated trading with Angel One's SmartAPI. It handles the complete trade execution pipeline from signal processing to order completion, with robust error handling, performance monitoring, and safety checks.

## ✨ Key Features

### ⚙️ 1. Order Lifecycle Management
- **Signal Processing**: Convert structured signals from Signal Generation Agent to executable orders
- **Auto-calculation**: Automatically calculate quantity, order type (MIS), price, stop-loss, and target
- **Order Monitoring**: Real-time tracking of order status with automatic retry mechanisms
- **Order Management**: Cancel, modify, and manage orders based on market conditions
- **Trade Completion**: Handle order fills, partial fills, and execution feedback

### 🔐 2. Execution Safety Checks
- **Market Hours Validation**: Prevent orders outside trading hours (9:15-15:25) with pre-market setup support
- **Parameter Validation**: Ensure compliance with lot size, price steps, and margin rules
- **Error Handling**: Comprehensive retry logic for API timeouts and failures
- **Execution Monitoring**: Track and alert on execution delays >2 seconds

### 📬 3. Angel One SmartAPI Integration
- **MIS (Intraday)**: Full support for margin intraday trading
- **Order Types**: LIMIT, MARKET, SL-L, SL-M stop loss orders
- **Real-time Data**: Integration with Angel One WebSocket for live order updates
- **Margin Validation**: Pre-trade margin requirement checking

### 📈 4. Performance Tracking & Analytics
- **Execution Metrics**: Track execution time, slippage, fill rates
- **Trade Logging**: Comprehensive logging with order_id, status, timestamps
- **Daily Reports**: Automated daily trade reports in parquet format
- **Performance Alerts**: Notifications for high slippage or slow execution

### 💬 5. Integration & Communication
- **Async Processing**: Non-blocking order dispatch to avoid system bottlenecks
- **Risk Agent Integration**: Pre-execution validation with Risk Management Agent
- **Notifications**: Telegram/email alerts for trade events
- **Webhook Support**: HTTP endpoints for external system integration

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Signal Agent    │───▶│ Execution Agent │───▶│ Angel One API   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │ Risk Agent      │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │ Notifications   │
                       │ & Logging       │
                       └─────────────────┘
```

## 📋 Configuration

### Basic Configuration (`config/execution_config.yaml`)

```yaml
# Angel One API Settings
angel_one_api:
  enabled: true
  api_key: "${ANGEL_API_KEY}"
  username: "${ANGEL_USERNAME}"
  password: "${ANGEL_PASSWORD}"
  totp_token: "${ANGEL_TOTP_TOKEN}"

# Execution Settings
execution:
  default_order_type: "LIMIT"
  default_product_type: "MIS"
  max_execution_time_ms: 2000
  max_slippage_percent: 0.5
  auto_retry: true
  max_retries: 3

# Market Hours
market_hours:
  market_open_time: "09:15"
  market_close_time: "15:25"
  allow_pre_market: true
  pre_market_start: "09:00"

# Risk Management
risk_management:
  enabled: true
  min_rr_ratio: 1.5
  max_position_size_percent: 5.0
```

### Environment Variables

Set these environment variables for security:

```bash
export ANGEL_API_KEY="your_api_key"
export ANGEL_USERNAME="your_client_code"
export ANGEL_PASSWORD="your_pin"
export ANGEL_TOTP_TOKEN="your_qr_token"
export TELEGRAM_BOT_TOKEN="your_bot_token"
export TELEGRAM_CHAT_ID="your_chat_id"
```

## 🚀 Usage

### Basic Usage

```python
import asyncio
from agents.execution_agent import ExecutionAgent, SignalPayload

async def main():
    # Initialize execution agent
    agent = ExecutionAgent("config/execution_config.yaml")
    await agent.initialize()
    
    # Create signal payload
    signal = SignalPayload(
        symbol="RELIANCE-EQ",
        exchange="NSE",
        symbol_token="2885",
        action="BUY",
        entry_price=2780.0,
        sl_price=2765.0,
        target_price=2815.0,
        quantity=1,
        strategy_name="momentum_strategy",
        signal_id="signal_001"
    )
    
    # Process signal
    success, message, trade_execution = await agent.process_signal(signal)
    
    if success:
        print(f"✅ Trade executed: {trade_execution.entry_order.order_id}")
    else:
        print(f"❌ Trade failed: {message}")
    
    # Keep running for monitoring
    await asyncio.sleep(300)  # 5 minutes
    
    # Cleanup
    await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

### Integration with Signal Generation Agent

```python
from agents.signal_generation_agent import SignalGenerationAgent
from agents.execution_agent import ExecutionAgent

async def trading_pipeline():
    # Initialize agents
    signal_agent = SignalGenerationAgent()
    execution_agent = ExecutionAgent()
    
    await signal_agent.initialize()
    await execution_agent.initialize()
    
    # Set up signal processing pipeline
    async def process_signals():
        async for signal in signal_agent.generate_signals():
            success, message, trade = await execution_agent.process_signal(signal)
            if success:
                print(f"✅ Signal executed: {signal.symbol}")
            else:
                print(f"❌ Signal failed: {message}")
    
    # Run pipeline
    await process_signals()
```

## 📊 Order Types & Examples

### 1. Market Order (Immediate Execution)

```python
signal = SignalPayload(
    symbol="SBIN-EQ",
    exchange="NSE",
    symbol_token="3045",
    action="BUY",
    entry_price=500.0,  # Current market price
    sl_price=485.0,
    target_price=520.0,
    quantity=10,
    order_type="MARKET"  # Immediate execution
)
```

### 2. Limit Order (Price-specific)

```python
signal = SignalPayload(
    symbol="TCS-EQ",
    exchange="NSE",
    symbol_token="11536",
    action="SELL",
    entry_price=3500.0,  # Specific price
    sl_price=3520.0,
    target_price=3450.0,
    quantity=5,
    order_type="LIMIT"  # Wait for specific price
)
```

### 3. Stop Loss Orders

```python
# Automatic SL placement after entry order fills
signal = SignalPayload(
    symbol="INFY-EQ",
    exchange="NSE",
    symbol_token="1594",
    action="BUY",
    entry_price=1500.0,
    sl_price=1485.0,  # SL-M order will be placed
    target_price=1530.0,
    quantity=15
)
```

## 🔧 Order Management

### Modify Orders

```python
# Modify order price
success, message = await agent.modify_order(
    order_id="ORDER123",
    new_price=2785.0,
    new_quantity=2
)
```

### Cancel Orders

```python
# Cancel order
success, message = await agent.cancel_order(
    order_id="ORDER123",
    reason="Market conditions changed"
)
```

### Check Order Status

```python
# Get current order status
order_status = await agent.get_order_status("ORDER123")
if order_status:
    print(f"Order status: {order_status['orderstatus']}")
```

## 📈 Performance Monitoring

### Execution Statistics

```python
# Get execution summary
summary = await agent.get_execution_summary()
print(f"Total orders: {summary['statistics']['total_orders']}")
print(f"Success rate: {summary['statistics']['successful_orders'] / summary['statistics']['total_orders'] * 100:.2f}%")
print(f"Avg execution time: {summary['statistics']['avg_execution_time_ms']:.2f}ms")
print(f"Avg slippage: {summary['statistics']['avg_slippage_percent']:.3f}%")
```

### Trade Data Export

```python
# Save trade data to parquet
await agent.save_trade_data("data/execution/daily_trades.parquet")
```

## 🛡️ Error Handling

### Common Error Scenarios

1. **Insufficient Margin**
   ```
   ❌ Entry order failed: Insufficient margin
   ```

2. **Market Closed**
   ```
   ⚠️ Signal validation failed: Outside market hours. Current time: 16:30:00
   ```

3. **Invalid Risk-Reward**
   ```
   ⚠️ Signal validation failed: Risk-reward ratio 1.2 below minimum 1.5
   ```

4. **API Timeout**
   ```
   ❌ Error placing entry order: API timeout
   ```

### Retry Mechanism

The agent automatically retries failed orders:
- Maximum 3 retry attempts
- 5-minute delay between retries
- Exponential backoff for persistent failures

## 📱 Notifications

### Telegram Integration

```yaml
notifications:
  telegram:
    enabled: true
    bot_token: "${TELEGRAM_BOT_TOKEN}"
    chat_id: "${TELEGRAM_CHAT_ID}"
    notify_on_order_placed: true
    notify_on_order_filled: true
    notify_on_high_slippage: true
```

### Sample Notifications

```
✅ Trade executed: RELIANCE BUY @ 2780.0
❌ Order cancelled: ORDER123 - Market conditions changed
⚠️ High slippage detected: 0.75% > 0.5%
```

## 🧪 Testing

### Run Unit Tests

```bash
cd test
python -m pytest test_execution_agent.py -v
```

### Run Integration Tests

```bash
python -m pytest test_execution_agent.py::TestExecutionAgentIntegration -v
```

### Performance Testing

```bash
python -m pytest test_execution_agent.py::TestExecutionAgentPerformance -v
```

## 🔍 Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check API credentials in environment variables
   - Verify TOTP token is correct
   - Ensure Angel One account is active

2. **Orders Not Placing**
   - Check market hours configuration
   - Verify margin availability
   - Check risk management settings

3. **High Slippage**
   - Review order types (use LIMIT instead of MARKET)
   - Check market liquidity
   - Adjust slippage thresholds

4. **Slow Execution**
   - Check network connectivity
   - Review API rate limits
   - Monitor system resources

### Debug Mode

Enable debug mode for detailed logging:

```yaml
system:
  debug_mode: true
  log_api_calls: true
  simulate_orders: true  # For testing without real orders
```

## 📚 API Reference

### Core Classes

- `ExecutionAgent`: Main execution agent class
- `SignalPayload`: Signal data structure
- `OrderRequest`: Angel One API order parameters
- `OrderResponse`: Order execution response
- `TradeExecution`: Complete trade record

### Key Methods

- `process_signal()`: Process incoming signal
- `modify_order()`: Modify existing order
- `cancel_order()`: Cancel order
- `get_execution_summary()`: Get performance metrics
- `save_trade_data()`: Export trade data

## 🔗 Integration Points

### With Other Agents

1. **Signal Generation Agent**: Receives trading signals
2. **Risk Management Agent**: Pre-execution validation
3. **Market Monitoring Agent**: Market data and regime detection
4. **AI Training Agent**: Execution feedback for model improvement

### External Systems

1. **Angel One SmartAPI**: Order placement and management
2. **Telegram**: Real-time notifications
3. **Database**: Trade data storage
4. **Monitoring Systems**: Performance metrics

## 📈 Best Practices

1. **Always test with simulation mode first**
2. **Set appropriate slippage thresholds**
3. **Monitor execution performance regularly**
4. **Use proper risk management integration**
5. **Keep API credentials secure**
6. **Regular backup of trade data**
7. **Monitor system resources during high-frequency trading**

## 🆘 Support

For issues and questions:
1. Check logs in `logs/execution_agent.log`
2. Review configuration settings
3. Test with simulation mode
4. Check Angel One API status
5. Verify network connectivity

# Market Monitoring Agent Guide

## 🚀 Overview

The Market Monitoring Agent is a comprehensive real-time market data tracking and strategy triggering system designed for the Indian stock market. It integrates with Angel One's SmartAPI to provide live market data, technical analysis, market regime detection, and automated trading signal generation.

## 🧠 Key Features

### 🔍 1. Live Market Data Tracking
- **Real-time Price Feed**: WebSocket-based tick/quote stream for tracked symbols
- **OHLC Aggregation**: 1-min, 5-min, 15-min candle builder for intra-bar analytics
- **Live Indicators**: Real-time computation of EMA, VWAP, RSI, MACD, SuperTrend, etc.
- **Market Breadth**: % of stocks in portfolio above 20 EMA, new highs/lows
- **Unusual Volume**: Detect spikes in volume compared to rolling average
- **VWAP Deviations**: Identify reversion or breakout opportunities

### 🧠 2. Contextual Environment Detection
- **Market Regime**: Bull/Bear/Sideways detection based on breadth and volatility
- **Volatility Analysis**: Low/Medium/High volatility classification
- **Liquidity Checks**: Volume and order book depth analysis
- **Correlation Matrix**: Cross-stock correlation for risk-on/risk-off detection
- **News Integration**: Optional news/events integration (webhook-based)

### 🧪 3. Strategy Triggering Logic
- **AI Integration**: Uses trained AI model for strategy recommendation per stock × regime
- **Entry Conditions**: Monitors for signal candles in live data
- **Execution Filters**: Ensures entry only if slippage, liquidity, volatility within bounds
- **Dynamic R:R**: Chooses optimal risk-reward ratio based on current market regime
- **Position Sizing**: Kelly Criterion-based position sizing with 1-3% risk

### 📣 4. Notifications & Logging
- **Real-time Alerts**: Telegram/Slack notifications for trade signals
- **Signal Logger**: Logs every trigger with full context and metrics
- **Dashboard Feed**: Publishes signals + health status to monitoring systems
- **Performance Tracking**: System resource and trading performance monitoring

## 📦 Installation

### Prerequisites
```bash
# Python 3.9+
python --version

# Install TA-Lib (required for technical indicators)
# On Ubuntu/WSL:
sudo apt-get install libta-lib-dev
# On Windows: Download from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
```

### Install Dependencies
```bash
# Install Market Monitoring Agent requirements
pip install -r requirements_market_monitoring.txt

# Install SmartAPI
pip install smartapi-python pyotp

# Install Telegram bot (optional)
pip install python-telegram-bot

# Install TA-Lib for Python
pip install TA-Lib
```

### Verify Installation
```bash
# Check dependencies
python agents/run_market_monitoring.py --check-deps
```

## ⚙️ Configuration

### 1. SmartAPI Setup
1. Create Angel One trading account
2. Generate API credentials from Angel One developer portal
3. Get TOTP token from QR code

### 2. Configuration File
Edit `config/market_monitoring_config.yaml`:

```yaml
# Angel One SmartAPI Configuration
smartapi:
  api_key: "YOUR_API_KEY"
  username: "YOUR_CLIENT_CODE"
  password: "YOUR_PIN"
  totp_token: "YOUR_QR_TOKEN"

# Telegram Bot (Optional)
notifications:
  telegram:
    enable: true
    bot_token: "YOUR_BOT_TOKEN"
    chat_id: "YOUR_CHAT_ID"
```

### 3. Symbol Configuration
The agent automatically loads symbols from `config/strategies.yaml`. Ensure your strategy file contains the symbols you want to monitor.

## 🚀 Usage

### Basic Usage
```bash
# Start Market Monitoring Agent
python agents/run_market_monitoring.py

# With custom config
python agents/run_market_monitoring.py --config custom_config.yaml

# With debug logging
python agents/run_market_monitoring.py --log-level DEBUG
```

### Programmatic Usage
```python
import asyncio
from agents.market_monitoring_agent import MarketMonitoringAgent

async def main():
    # Create agent
    agent = MarketMonitoringAgent()
    
    # Setup
    await agent.setup()
    
    # Add signal handler
    async def signal_handler(signal):
        print(f"Signal: {signal.symbol} {signal.action} @ {signal.price}")
    
    agent.add_signal_handler(signal_handler)
    
    # Start monitoring
    await agent.start()

asyncio.run(main())
```

## 📊 Market Regime Detection

The agent automatically detects market regimes based on:

### Bull Market
- **Criteria**: >60% stocks above EMA20
- **Characteristics**: High breadth, positive momentum
- **Strategy Adjustment**: Higher R:R ratios (1.5:1)

### Bear Market
- **Criteria**: <40% stocks above EMA20
- **Characteristics**: Low breadth, negative momentum
- **Strategy Adjustment**: Conservative R:R ratios (2:1)

### Sideways Market
- **Criteria**: 40-60% stocks above EMA20
- **Characteristics**: Mixed signals, range-bound
- **Strategy Adjustment**: Moderate R:R ratios (1.2:1)

## 🔧 Technical Indicators

### Supported Indicators
- **Moving Averages**: EMA (5,10,13,20,21,30,50,100), SMA (20)
- **Momentum**: RSI (5,14), MACD (12,26,9), Stochastic (14,3)
- **Volatility**: Bollinger Bands (20,2), ATR (14)
- **Trend**: SuperTrend (10,3), ADX (14)
- **Volume**: MFI (14), VWAP
- **Support/Resistance**: Donchian Channels (20)

### Custom Indicators
Add custom indicators by extending the `_calculate_indicators` method:

```python
# Add custom indicator
def calculate_custom_indicator(self, prices):
    # Your custom calculation
    return custom_values

# In _calculate_indicators method
custom_values = self.calculate_custom_indicator(close_prices)
indicators.custom_indicator = custom_values[-1]
```

## 🚨 Signal Generation

### Entry Conditions
The agent monitors multiple entry conditions:

1. **RSI Oversold/Overbought**: RSI < 30 (buy) or RSI > 70 (sell)
2. **EMA Crossover**: EMA5 > EMA20 (bullish) or EMA5 < EMA20 (bearish)
3. **MACD Signal**: MACD line crosses signal line
4. **Volume Breakout**: Volume > 2x average volume
5. **Custom Strategies**: Add your own entry logic

### Signal Validation
Signals are validated against:
- **Confidence Threshold**: Minimum 70% confidence
- **Liquidity Check**: Minimum volume requirements
- **Daily Limits**: Maximum trades per day
- **Position Limits**: Maximum concurrent positions
- **Volatility Filter**: Maximum volatility percentile

## 📱 Notifications

### Telegram Setup
1. Create Telegram bot via @BotFather
2. Get bot token
3. Get chat ID (send message to bot, then call API)
4. Configure in `market_monitoring_config.yaml`

### Message Templates
Customize notification messages:
```yaml
notifications:
  telegram:
    templates:
      signal: "🚨 {symbol} | {action} | ₹{price} | Target: ₹{target}"
      regime_change: "📊 Market: {regime} | Breadth: {breadth}%"
```

## 📈 Performance Monitoring

### System Metrics
- **CPU Usage**: Real-time CPU utilization
- **Memory Usage**: RAM consumption monitoring
- **WebSocket Health**: Connection status and reconnection
- **Signal Count**: Active signals and daily totals

### Trading Metrics
- **Signal Accuracy**: Success rate of generated signals
- **Market Regime Accuracy**: Regime detection performance
- **Latency**: Time from tick to signal generation
- **Throughput**: Signals processed per minute

## 🔧 Troubleshooting

### Common Issues

#### 1. WebSocket Connection Failed
```bash
# Check internet connection
ping smartapi.angelbroking.com

# Verify API credentials
# Check TOTP token generation
```

#### 2. Missing Dependencies
```bash
# Install missing packages
pip install -r requirements_market_monitoring.txt

# For TA-Lib issues on Windows:
# Download wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
pip install TA_Lib-0.4.25-cp39-cp39-win_amd64.whl
```

#### 3. High Memory Usage
```bash
# Reduce candle retention
market_data:
  max_candles_per_timeframe: 500  # Reduce from 1000

# Enable cleanup
performance:
  memory:
    cleanup_interval: 1800  # 30 minutes
```

#### 4. No Signals Generated
- Check symbol configuration in `strategies.yaml`
- Verify market hours (9:15 AM - 3:30 PM IST)
- Check confidence thresholds
- Review entry conditions

### Debug Mode
```bash
# Enable debug logging
python agents/run_market_monitoring.py --log-level DEBUG

# Check logs
tail -f logs/market_monitoring.log
tail -f logs/signals.log
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
python -m pytest test/test_market_monitoring_agent.py -v

# Run specific test
python -m pytest test/test_market_monitoring_agent.py::test_signal_generation -v

# Run with coverage
python -m pytest test/test_market_monitoring_agent.py --cov=agents.market_monitoring_agent
```

### Mock Testing
The test suite includes comprehensive mocks for:
- SmartAPI WebSocket connections
- Market data feeds
- Telegram notifications
- AI model predictions

## 🔄 Integration

### AI Training Agent Integration
```python
# The agent automatically integrates with AI Training Agent
# if available in the same directory

# Manual integration
from agents.ai_training_agent import AITrainingAgent

agent.ai_agent = AITrainingAgent()
# AI predictions will be used for strategy selection
```

### Order Management Integration
```python
# Add custom signal handler for order placement
async def order_handler(signal):
    # Place order via broker API
    order_id = place_order(
        symbol=signal.symbol,
        action=signal.action,
        quantity=signal.quantity,
        price=signal.price
    )
    print(f"Order placed: {order_id}")

agent.add_signal_handler(order_handler)
```

## 📚 API Reference

### MarketMonitoringAgent Class
```python
class MarketMonitoringAgent:
    async def setup()                    # Initialize agent
    async def start()                    # Start monitoring
    async def stop()                     # Stop monitoring
    
    def add_signal_handler(handler)      # Add signal callback
    def add_regime_change_handler(handler) # Add regime callback
    
    def get_market_regime()              # Current market regime
    def get_active_signals()             # Active trading signals
    def get_indicators(symbol)           # Technical indicators
    def get_performance_metrics()        # Performance data
```

### Data Structures
- **MarketTick**: Real-time tick data
- **OHLCV**: Candlestick data
- **MarketIndicators**: Technical indicators
- **MarketRegime**: Market environment
- **TradingSignal**: Generated trading signal

## 🚀 Production Deployment

### Systemd Service (Linux)
```ini
[Unit]
Description=Market Monitoring Agent
After=network.target

[Service]
Type=simple
User=trader
WorkingDirectory=/path/to/Intraday-AI/tests-2
ExecStart=/usr/bin/python3 agents/run_market_monitoring.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements_market_monitoring.txt .
RUN pip install -r requirements_market_monitoring.txt

COPY . .
CMD ["python", "agents/run_market_monitoring.py"]
```

### Monitoring & Alerts
- Setup log monitoring with ELK stack
- Configure Prometheus metrics
- Setup Grafana dashboards
- Configure PagerDuty alerts

## 📄 License

This Market Monitoring Agent is part of the Intraday-AI trading system. Please ensure compliance with Angel One's API terms of service and applicable trading regulations.

# AI Training Agent - Complete System

## 🎯 Overview

The **AI Training Agent** is a comprehensive machine learning system designed for **multi-target strategy performance prediction** in trading environments. Unlike traditional binary classification approaches, this system predicts multiple continuous performance metrics simultaneously and ranks strategies based on predicted performance.

### 🌟 Key Features

- **🎯 Multi-Target Prediction**: Predicts ROI, Sharpe ratio, expectancy, max drawdown, profit factor, risk-reward ratio, and accuracy
- **🤖 Ensemble Learning**: Combines LightGBM (70%) + TabNet (30%) for optimal performance
- **📊 Strategy Ranking**: Multi-objective ranking with Pareto optimization and market regime conditioning
- **⚡ GPU Acceleration**: Full cuDF/cuPy support for RTX 3060ti and similar GPUs
- **🔍 Model Monitoring**: Automated drift detection and performance monitoring
- **📅 Auto Retraining**: Scheduled retraining with intelligent triggers
- **🏃 Performance Benchmarking**: Comprehensive performance analysis tools
- **🌐 FastAPI Serving**: Production-ready API endpoints
- **🧪 Comprehensive Testing**: Full test suite with 95%+ coverage

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    AI Training Agent System                     │
├─────────────────────────────────────────────────────────────────┤
│  📊 Data Pipeline (Polars/cuDF)                               │
│  ├── Data Loading & Validation                                 │
│  ├── Feature Engineering & Preprocessing                       │
│  └── Data Quality Monitoring                                   │
├─────────────────────────────────────────────────────────────────┤
│  🤖 Ensemble Models                                           │
│  ├── LightGBM (Gradient Boosting) - 70% weight               │
│  ├── TabNet (Neural Networks) - 30% weight                   │
│  └── Hyperparameter Optimization (Optuna)                     │
├─────────────────────────────────────────────────────────────────┤
│  🎯 Strategy Ranking System                                   │
│  ├── Multi-Objective Scoring                                   │
│  ├── Market Regime Conditioning                                │
│  └── Pareto Frontier Analysis                                  │
├─────────────────────────────────────────────────────────────────┤
│  🔍 Monitoring & Automation                                   │
│  ├── Model Drift Detection (PSI, KS-test, Chi²)              │
│  ├── Performance Monitoring                                    │
│  ├── Automated Retraining Scheduler                           │
│  └── Alert System                                              │
├─────────────────────────────────────────────────────────────────┤
│  🌐 Production Serving                                        │
│  ├── FastAPI REST Endpoints                                    │
│  ├── Real-time Predictions                                     │
│  ├── Batch Processing                                          │
│  └── Health Monitoring                                         │
└─────────────────────────────────────────────────────────────────┘
```

## 📦 Components

### Core Components

| Component | File | Description |
|-----------|------|-------------|
| **Main Agent** | `ai_training_agent.py` | Core ensemble training and prediction |
| **API Server** | `ai_training_api.py` | FastAPI serving endpoints |
| **Utilities** | `ai_training_utils.py` | Helper functions and data processing |
| **Configuration** | `ai_training_config.yaml` | System configuration |

### Advanced Components

| Component | File | Description |
|-----------|------|-------------|
| **Monitor** | `ai_training_monitor.py` | Drift detection and performance monitoring |
| **Scheduler** | `ai_training_scheduler.py` | Automated retraining pipeline |
| **Benchmark** | `ai_training_benchmark.py` | Performance benchmarking tools |
| **Complete System** | `ai_training_complete.py` | Integrated system controller |

### Supporting Files

| Component | File | Description |
|-----------|------|-------------|
| **Runner** | `run_ai_training.py` | Command-line interface |
| **Tests** | `test_ai_training_agent.py` | Comprehensive test suite |
| **Examples** | `ai_training_example.py` | Usage examples and demos |
| **Documentation** | `AI_TRAINING_AGENT_GUIDE.md` | Complete user guide |

## 🚀 Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements_ai_training.txt

# For GPU support (optional)
pip install cudf-cu11 cupy-cuda11x
```

### 2. Basic Training

```bash
# Train with default settings
python agents/run_ai_training.py

# Train with custom data
python agents/run_ai_training.py --data-file data/backtest/enhanced_strategy_results.parquet

# Fast training (no hyperparameter optimization)
python agents/run_ai_training.py --no-optimize
```

### 3. Complete Workflow Demo

```bash
# Run full system demo
python agents/ai_training_complete.py --demo --data-file data/backtest/enhanced_strategy_results.parquet
```

### 4. Start API Server

```bash
# Start FastAPI server
python agents/ai_training_api.py

# Access API documentation
# http://localhost:8000/docs
```

## 📊 Target Metrics

The system predicts these performance metrics:

| Metric | Description | Optimization Goal |
|--------|-------------|-------------------|
| **ROI** | Return on Investment | Maximize |
| **Sharpe Ratio** | Risk-adjusted returns | Maximize |
| **Expectancy** | Expected value per trade | Maximize |
| **Max Drawdown** | Maximum peak-to-trough decline | Minimize |
| **Profit Factor** | Gross profit / Gross loss | Maximize |
| **Risk-Reward Ratio** | Average win / Average loss | Maximize |
| **Accuracy** | Percentage of winning trades | Maximize |

## 🔧 Configuration

### Hardware Optimization for RTX 3060ti + 5600x + 24GB RAM

```yaml
# config/ai_training_config.yaml
hardware:
  use_gpu: true
  gpu_memory_fraction: 0.8
  n_jobs: -1

lightgbm:
  device_type: "gpu"
  num_leaves: 31
  learning_rate: 0.05

tabnet:
  device_name: "cuda"
  batch_size: 1024
  virtual_batch_size: 128
```

## 🧪 Testing

```bash
# Run all tests
pytest test/test_ai_training_agent.py -v

# Run with coverage
pytest test/test_ai_training_agent.py --cov=agents --cov-report=html

# Run specific test
pytest test/test_ai_training_agent.py::test_agent_initialization -v
```

## 📈 Performance Benchmarks

### Expected Performance (RTX 3060ti + 5600x + 24GB RAM)

| Data Size | Training Time | Prediction Speed | Memory Usage |
|-----------|---------------|------------------|--------------|
| 1K samples | ~30 seconds | ~1000 pred/sec | ~2GB |
| 10K samples | ~5 minutes | ~800 pred/sec | ~4GB |
| 100K samples | ~30 minutes | ~600 pred/sec | ~8GB |

### Model Performance Targets

| Metric | Target R² | Typical RMSE |
|--------|-----------|--------------|
| ROI | >0.6 | <0.15 |
| Sharpe Ratio | >0.5 | <0.8 |
| Expectancy | >0.4 | <0.1 |
| Overall | >0.5 | <0.2 |

## 🔍 Monitoring & Automation

### Automated Retraining Triggers

- **Scheduled**: Weekly/monthly retraining
- **Performance Degradation**: R² drops >5% or RMSE increases >10%
- **Data Drift**: PSI >0.1, KS-test p-value <0.05
- **Force Retrain**: After 30 days regardless of performance

### Monitoring Metrics

- **Data Drift**: Population Stability Index, Kolmogorov-Smirnov test
- **Model Performance**: R², RMSE, MAE, confidence scores
- **System Resources**: CPU, memory, GPU utilization
- **Prediction Quality**: Confidence distribution, error patterns

## 🌐 API Endpoints

### Core Endpoints

```bash
# Health check
GET /health

# Single prediction
POST /predict
{
  "n_trades": 50,
  "avg_holding_period": 2.5,
  "volatility": 0.2,
  ...
}

# Bulk predictions and ranking
POST /predict_bulk
{
  "strategies": [...],
  "market_regime": "bull"
}

# Training status
GET /training/status

# Start training
POST /training/start
```

## 📚 Integration with Trading Workflow

### Data Flow

```
Backtesting Agent → AI Training Agent → Strategy Evolution Agent
                                    ↓
                              Signal Generation Agent
```

### Usage in Strategy Pipeline

```python
# Score new strategies
def score_strategies(new_strategies):
    predictions, confidence = agent.predict(strategy_features)
    rankings = agent.rank_strategies(predictions, strategy_names, market_regime)
    
    # Filter top performers
    top_strategies = [r for r in rankings if r['composite_score'] > 0.6]
    return top_strategies
```

## ❓ FAQ

**Q: Do I need historical data after training?**
A: You can remove historical data after feature engineering. Keep feature data for retraining.

**Q: How often should I retrain?**
A: Weekly for active trading, or when monitoring detects significant drift/degradation.

**Q: Can I use different timeframes?**
A: Yes, train separate models per timeframe or include timeframe as a categorical feature.

**Q: What if I have limited GPU memory?**
A: Use CPU-only mode or reduce batch sizes. The system gracefully falls back to CPU.

## 🚨 Troubleshooting

### Common Issues

1. **CUDA out of memory**: Use `--cpu-only` or reduce batch sizes
2. **Missing dependencies**: Run `pip install -r requirements_ai_training.txt`
3. **Training slow**: Use `--no-optimize` or reduce Optuna trials
4. **Low performance**: Check data quality and feature engineering

### Performance Optimization

1. **Enable GPU acceleration** for 3-5x speedup
2. **Use compressed data formats** (Parquet with Brotli)
3. **Tune batch sizes** based on available memory
4. **Optimize Optuna trials** vs training time trade-off

## 📝 License

Part of the Intraday-AI trading system. See main repository for license details.

## 🤝 Contributing

1. Run tests: `pytest test/test_ai_training_agent.py -v`
2. Check code style: `black agents/ && flake8 agents/`
3. Update documentation for new features
4. Ensure backward compatibility

---

**🎯 Ready to predict strategy performance with state-of-the-art ML?**

Start with: `python agents/ai_training_complete.py --demo --data-file your_data.parquet`

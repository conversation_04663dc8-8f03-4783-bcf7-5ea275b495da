# 🧬 Strategy Evolution Agent Guide

The Strategy Evolution Agent is an advanced component of the trading system that implements continuous learning and strategy refinement using evolutionary algorithms, genetic programming, and reinforcement learning techniques.

## 🎯 Overview

The Strategy Evolution Agent serves as the "brain that learns from experience" in your trading ecosystem. It continuously monitors strategy performance, evolves new variants, and adapts to changing market conditions to maintain competitive and intelligent trading strategies.

## 🔧 Key Features

### 🧬 Evolutionary Algorithms
- **Genetic Algorithms**: Implements mutation, crossover, and selection operations
- **Population Management**: Maintains diverse strategy populations
- **Fitness Evaluation**: Multi-objective performance assessment
- **Elite Preservation**: Keeps top-performing strategies

### 📊 Performance Tracking
- **Real-time Monitoring**: Continuous strategy performance tracking
- **Trend Analysis**: Identifies improving/degrading strategies
- **Historical Analysis**: Long-term performance evaluation
- **Risk-adjusted Metrics**: Comprehensive performance assessment

### 🌊 Market Regime Adaptation
- **Regime Detection**: Identifies bull/bear/sideways markets
- **Adaptive Evolution**: Evolves strategies for specific market conditions
- **Dynamic Allocation**: Adjusts strategy weights based on regime
- **Volatility Adaptation**: Adapts to high/low volatility environments

### 🔗 Agent Integration
- **Performance Analysis Agent**: Real-time performance data
- **Market Monitoring Agent**: Market regime information
- **AI Training Agent**: Machine learning integration
- **Backtesting Agent**: Strategy validation

## 🚀 Quick Start

### Installation

1. **Install Dependencies**:
```bash
pip install numpy polars pyarrow pyyaml asyncio
```

2. **Setup Configuration**:
```bash
cp config/strategy_evolution_config.yaml.example config/strategy_evolution_config.yaml
```

3. **Create Required Directories**:
```bash
mkdir -p data/evolved_strategies data/evolution_performance data/evolution_backups logs
```

### Basic Usage

```python
from agents.strategy_evolution_agent import StrategyEvolutionAgent

# Initialize agent
agent = StrategyEvolutionAgent("config/strategy_evolution_config.yaml")

# Setup and start
await agent.setup()
await agent.start()
```

### Command Line Usage

```bash
# Start the agent
python agents/run_strategy_evolution.py

# Run in simulation mode
python agents/run_strategy_evolution.py --simulation

# Force evolution
python agents/run_strategy_evolution.py --force-evolution

# Check status
python agents/run_strategy_evolution.py --status

# Run tests
python agents/run_strategy_evolution.py --test
```

## 📋 Configuration

### Evolution Parameters

```yaml
evolution:
  population_size: 50          # Number of strategies in population
  elite_size: 10              # Number of elite strategies to preserve
  mutation_rate: 0.1           # Probability of mutation (10%)
  crossover_rate: 0.8          # Probability of crossover (80%)
  max_generations: 100         # Maximum number of generations
```

### Performance Weights

```yaml
performance:
  fitness_weights:
    roi: 0.25                  # Return on Investment weight
    sharpe_ratio: 0.20         # Sharpe ratio weight
    max_drawdown: -0.15        # Max drawdown penalty
    profit_factor: 0.15        # Profit factor weight
    win_rate: 0.10             # Win rate weight
    expectancy: 0.10           # Expectancy weight
    calmar_ratio: 0.05         # Calmar ratio weight
```

### Market Regime Adaptation

```yaml
market_regimes:
  bull_market_adaptations:
    take_profit_multiplier: 1.2    # Increase take profit by 20%
    position_size_multiplier: 1.1  # Increase position size by 10%
    
  bear_market_adaptations:
    stop_loss_multiplier: 0.8      # Tighten stop loss by 20%
    position_size_multiplier: 0.8  # Reduce position size by 20%
```

## 🧬 Genetic Algorithm Operations

### Strategy Representation

Strategies are represented as chromosomes with genes encoding parameters:

```python
genes = {
    'rsi_period': StrategyGene('rsi_period', 14, 5, 50, 0.1, 'numeric'),
    'rsi_oversold': StrategyGene('rsi_oversold', 30, 10, 40, 0.1, 'numeric'),
    'stop_loss_pct': StrategyGene('stop_loss_pct', 0.02, 0.005, 0.05, 0.1, 'numeric'),
    'position_size_pct': StrategyGene('position_size_pct', 0.05, 0.01, 0.15, 0.1, 'numeric')
}
```

### Mutation Operations

- **Gaussian Mutation**: For numeric parameters
- **Boolean Flip**: For boolean parameters
- **Categorical Selection**: For categorical parameters

### Crossover Operations

- **Uniform Crossover**: Exchanges genes between parents
- **Single-point Crossover**: Splits chromosomes at random point
- **Multi-point Crossover**: Multiple crossover points

### Selection Methods

- **Tournament Selection**: Selects best from random tournament
- **Elite Selection**: Preserves top performers
- **Roulette Wheel**: Fitness-proportionate selection

## 📊 Performance Metrics

### Fitness Calculation

The fitness score combines multiple performance metrics:

```python
fitness = (
    0.25 * roi +
    0.20 * sharpe_ratio +
    (-0.15) * max_drawdown +  # Penalty for high drawdown
    0.15 * profit_factor +
    0.10 * win_rate +
    0.10 * expectancy +
    0.05 * calmar_ratio
)
```

### Performance Tracking

- **Real-time Updates**: Continuous performance monitoring
- **Trend Analysis**: Identifies performance trends
- **Regime Performance**: Tracks performance by market regime
- **Risk Metrics**: Comprehensive risk assessment

## 🌊 Market Regime Adaptation

### Regime Detection

The agent detects market regimes based on:
- **Price Trends**: Bull/bear market identification
- **Volatility**: High/low volatility periods
- **Market Indicators**: Technical analysis signals

### Adaptive Strategies

Different adaptations for each regime:

#### Bull Market
- Increase take profit targets
- Larger position sizes
- More aggressive parameters

#### Bear Market
- Tighter stop losses
- Smaller position sizes
- Conservative parameters

#### Sideways Market
- Mean reversion focus
- Extreme RSI levels
- Range-bound strategies

#### High Volatility
- Wider stop losses
- Smaller positions
- Volatility-adjusted parameters

#### Low Volatility
- Tighter stops
- Larger positions
- Trend-following focus

## 🔗 Agent Integration

### Performance Analysis Agent

```python
# Get strategy performance data
performance_data = await self.comm_interface.query_agent(
    agent_name="performance_analysis_agent",
    query_type="get_strategy_performance",
    parameters={
        "strategy_name": strategy_name,
        "period_days": 30
    }
)
```

### Market Monitoring Agent

```python
# Get current market regime
regime_data = await self.comm_interface.query_agent(
    agent_name="market_monitoring_agent",
    query_type="get_market_regime",
    parameters={}
)
```

### AI Training Agent

```python
# Get AI predictions for strategy ranking
predictions = await self.ai_training_agent.predict_strategy_performance(
    strategy_features=features,
    market_regime=current_regime
)
```

## 📈 Evolution Process

### 1. Initialization
- Load base strategies from configuration
- Create initial population with variations
- Setup performance tracking

### 2. Evaluation
- Assess strategy performance
- Calculate fitness scores
- Update performance history

### 3. Selection
- Tournament selection for parents
- Elite preservation
- Diversity maintenance

### 4. Reproduction
- Crossover between selected parents
- Mutation of offspring
- Population replacement

### 5. Adaptation
- Market regime detection
- Regime-specific evolution
- Strategy allocation updates

## 🧪 Testing

### Unit Tests

```bash
# Run all tests
python -m pytest test/test_strategy_evolution_agent.py -v

# Run specific test
python -m pytest test/test_strategy_evolution_agent.py::TestGeneticAlgorithms::test_mutation -v
```

### Integration Tests

```bash
# Test full evolution cycle
python -m pytest test/test_strategy_evolution_agent.py::TestIntegration::test_full_evolution_cycle -v
```

### Performance Tests

```bash
# Run performance benchmarks
python agents/run_strategy_evolution.py --test
```

## 📊 Monitoring and Alerts

### Status Monitoring

```python
# Get evolution statistics
stats = agent.get_evolution_statistics()
print(f"Generation: {stats['current_generation']}")
print(f"Best Fitness: {stats['best_fitness']:.4f}")
print(f"Population Size: {stats['population_size']}")
```

### Performance Reports

The agent generates periodic reports:
- **Status Reports**: Current evolution state
- **Performance Reports**: Strategy performance analysis
- **Regime Reports**: Market regime adaptation results

### Health Checks

- **Memory Usage**: Monitor memory consumption
- **Processing Time**: Track evolution performance
- **Data Freshness**: Validate performance data
- **Agent Connectivity**: Check agent connections

## 🔧 Advanced Configuration

### Custom Fitness Functions

```python
def custom_fitness_function(metrics: PerformanceMetrics) -> float:
    # Custom fitness calculation
    return (
        0.4 * metrics.roi +
        0.3 * metrics.sharpe_ratio +
        (-0.2) * metrics.max_drawdown +
        0.1 * metrics.win_rate
    )
```

### Custom Mutation Operators

```python
def custom_mutation(gene: StrategyGene, mutation_rate: float) -> StrategyGene:
    # Custom mutation logic
    if random.random() < mutation_rate:
        # Apply custom mutation
        pass
    return gene
```

### Custom Selection Methods

```python
def custom_selection(population: List[StrategyChromosome]) -> StrategyChromosome:
    # Custom selection logic
    return selected_strategy
```

## 🚨 Troubleshooting

### Common Issues

1. **Memory Usage**: Large populations can consume significant memory
   - Solution: Reduce population size or implement memory cleanup

2. **Slow Evolution**: Complex fitness evaluation can slow evolution
   - Solution: Optimize fitness calculation or use parallel evaluation

3. **Poor Convergence**: Evolution may not converge to good solutions
   - Solution: Adjust mutation/crossover rates or fitness weights

4. **Agent Connectivity**: Issues connecting to other agents
   - Solution: Check agent configurations and network connectivity

### Debug Mode

Enable debug logging for detailed information:

```yaml
logging:
  level: "DEBUG"
  enable_debug_logging: true
```

## 📚 API Reference

### Main Classes

- `StrategyEvolutionAgent`: Main agent class
- `StrategyChromosome`: Strategy representation
- `StrategyGene`: Individual parameter gene
- `PerformanceMetrics`: Performance measurement
- `EvolutionConfig`: Evolution configuration
- `EvolutionState`: Current evolution state

### Key Methods

- `setup()`: Initialize agent
- `start()`: Start evolution loops
- `stop()`: Stop agent
- `force_evolution()`: Force new generation
- `get_best_strategies()`: Get top performers
- `export_strategies()`: Export strategies
- `import_strategies()`: Import strategies

## 🔮 Future Enhancements

### Planned Features

1. **Multi-objective Optimization**: Pareto-optimal strategy selection
2. **Reinforcement Learning**: Q-learning for strategy adaptation
3. **Neural Evolution**: Evolving neural network strategies
4. **Distributed Evolution**: Multi-node evolution processing
5. **Real-time Adaptation**: Intraday strategy evolution

### Research Areas

1. **Coevolution**: Competing strategy populations
2. **Meta-learning**: Learning to learn better strategies
3. **Transfer Learning**: Applying strategies across markets
4. **Ensemble Methods**: Combining multiple evolution approaches

## 📞 Support

For questions, issues, or contributions:

1. **Documentation**: Check this guide and code comments
2. **Testing**: Run the test suite for validation
3. **Logging**: Enable debug logging for troubleshooting
4. **Configuration**: Review configuration options

The Strategy Evolution Agent represents the cutting edge of algorithmic trading strategy development, providing continuous learning and adaptation capabilities that keep your trading system competitive in dynamic markets.

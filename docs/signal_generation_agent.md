# Signal Generation Agent

The Signal Generation Agent is a comprehensive real-time trading signal generation and validation system designed for the Indian market. It processes live market data, evaluates trading strategies, and generates validated trading signals with proper risk management.

## 🔥 Key Features

### 📥 1. Input Management
- **Live OHLCV Data**: From Angel One WebSocket or rolling candle aggregator
- **Feature DataFrame**: Real-time indicators like RSI, EMA, VWAP, MACD, SuperTrend, etc.
- **Strategy Config**: Loaded YAML or database logic of current strategies
- **Market Context**: Regime (bull/bear/sideways), volatility, liquidity, RR preferences
- **Capital Info**: Per strategy capital, max drawdown, SL/TP constraints

### 🧠 2. Signal Evaluation Engine
- **Strategy Logic Eval**: df.eval(), compiled NumExpr, or precompiled AST
- **RR Condition Handler**: Auto-generates SL/TP levels based on RR ratio (e.g., 1:2)
- **Regime/Condition Aware**: Runs different logic if market regime is "bearish" vs "sideways"
- **Signal Type Detection**: Long/Short/Exit — sets signal=1, -1, 0
- **Position Sizing**: Uses Kelly Criterion / fixed fraction / volatility-scaling
- **Trade State Tracking**: Keeps last signal, avoids duplicate/flip-flop signals

### ⚙️ 3. Signal Structuring + Validation
- **Signal Packaging**: Structure: symbol, datetime, strategy, entry price, sl/tp, capital
- **Risk Check**: Reject signals if volatility > X, drawdown > Y, etc.
- **Liquidity Check**: Ensure volume or bid/ask spread is tradable
- **Time Filter**: Only allow signals during live market (e.g. 09:20 – 15:00)
- **Signal Cooldown**: Avoid spamming — apply N-minute gap between signals on same stock

### 🧾 4. Signal Output
- **Internal Database**: Log for record, dashboard display, backtest
- **Notification**: Real-time push to Telegram / Slack / webhooks
- **Execution Queue**: Send to Order Execution Agent (with Angel API)
- **Strategy Monitor**: Feed into dashboard showing live signal activity

## 🚀 Quick Start

### Installation

```bash
# Install required dependencies
pip install polars pyarrow polars-talib
pip install smartapi-python telegram-bot
pip install scipy numpy pyyaml
```

### Basic Usage

```python
import asyncio
from agents.signal_generation_agent import SignalGenerationAgent

async def main():
    # Initialize agent
    agent = SignalGenerationAgent()
    
    # Setup and start
    await agent.setup()
    await agent.start()
    
    # Agent will now process market data and generate signals
    # Integration with Market Monitoring Agent handles data flow
    
    # Stop when done
    await agent.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

### Configuration

Create `config/signal_generation_config.yaml`:

```yaml
# Basic configuration
risk_management:
  default_risk_reward_ratio: 2.0
  max_daily_risk_percent: 5.0

position_sizing:
  default_method: "fixed_fraction"
  capital_allocation:
    total_capital: 100000
    max_position_size_percent: 2.0

signal_validation:
  min_confidence: 0.6
  cooldown:
    minutes_between_signals: 5

notifications:
  telegram:
    enable: true
    bot_token: "YOUR_BOT_TOKEN"
    chat_id: "YOUR_CHAT_ID"
```

## 📊 Architecture

### Core Components

1. **Input Manager**: Handles market data, indicators, and strategy configs
2. **Strategy Evaluator**: Processes strategy conditions using Polars/PyArrow
3. **Position Sizer**: Calculates optimal position sizes using Kelly Criterion
4. **Signal Validator**: Applies risk checks, time filters, and cooldowns
5. **Output Manager**: Sends signals to database, notifications, and execution queue

### Data Flow

```
Market Data → Strategy Evaluation → Position Sizing → Signal Validation → Output
     ↓              ↓                    ↓               ↓              ↓
  OHLCV +       Long/Short         Quantity +      Risk Checks +   Database +
Indicators    Conditions         Capital Alloc    Time Filters   Notifications
```

## 🔧 Configuration Options

### Risk Management

```yaml
risk_management:
  default_risk_reward_ratio: 2.0
  atr_stop_loss_multiplier: 2.0
  max_daily_risk_percent: 5.0
  regime_adjustments:
    bull: 1.2      # Increase RR in bull market
    bear: 0.8      # Decrease RR in bear market
    sideways: 1.0  # Normal RR in sideways market
```

### Position Sizing

```yaml
position_sizing:
  default_method: "kelly"  # "kelly", "fixed_fraction", "volatility_scaled"
  capital_allocation:
    total_capital: 100000
    max_position_size_percent: 2.0
    max_risk_per_trade_percent: 1.0
    intraday_margin_multiplier: 3.5
  kelly_criterion:
    max_kelly_fraction: 0.25
    min_trades_for_kelly: 30
```

### Signal Validation

```yaml
signal_validation:
  min_confidence: 0.6
  time_filters:
    market_hours_only: true
    avoid_first_minutes: 10
    avoid_last_minutes: 30
  cooldown:
    minutes_between_signals: 5
    max_signals_per_symbol_per_day: 3
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
python -m pytest test/test_signal_generation_agent.py -v

# Run specific test
python -m pytest test/test_signal_generation_agent.py::test_signal_validation -v

# Run demo
python examples/signal_generation_demo.py
```

## 🔄 Integration with Other Agents

### Market Monitoring Agent

```python
# The Signal Generation Agent automatically integrates with Market Monitoring Agent
# when both are configured and running

# Market Monitoring Agent provides:
# - Real-time OHLCV data
# - Technical indicators
# - Market regime information
# - Liquidity metrics
```

### AI Training Agent

```python
# AI Training Agent can enhance signal confidence and strategy selection
# Configuration in signal_generation_config.yaml:

integrations:
  ai_training:
    enable: true
    enable_signal_enhancement: true
    confidence_boost_factor: 0.1
```

## 📈 Performance Optimization

### Using Polars and PyArrow

The agent is optimized for speed using:

- **Polars**: Fast DataFrame operations with lazy evaluation
- **PyArrow**: Vectorized computations for indicators
- **polars-talib**: Technical analysis indicators optimized for Polars

### Memory Management

```yaml
performance:
  memory:
    max_memory_usage_mb: 500
    enable_garbage_collection: true
  processing:
    enable_multiprocessing: true
    max_workers: 4
```

## 📱 Notifications

### Telegram Integration

```python
# Configure Telegram notifications
notifications:
  telegram:
    enable: true
    bot_token: "YOUR_BOT_TOKEN"
    chat_id: "YOUR_CHAT_ID"
    send_all_signals: true
```

Example notification:
```
🚨 Trading Signal Generated

📊 Symbol: RELIANCE
📈 Strategy: SuperTrend_EMA_Strategy
🎯 Action: BUY
💰 Entry Price: ₹2,485.50
🛑 Stop Loss: ₹2,455.00
🎯 Take Profit: ₹2,546.00
📦 Quantity: 8
💵 Capital: ₹19,884
⚖️ R:R Ratio: 2.0
🎯 Confidence: 78%
🌍 Market Regime: bull
⏰ Time: 10:45:23
```

## 🛠️ Troubleshooting

### Common Issues

1. **No signals generated**: Check strategy conditions and market data
2. **All signals rejected**: Review validation filters and confidence thresholds
3. **Performance issues**: Enable multiprocessing and optimize memory settings
4. **Integration errors**: Verify other agents are properly configured

### Debug Mode

```python
# Enable debug logging
import logging
logging.getLogger('signal_generation').setLevel(logging.DEBUG)
```

## 📚 API Reference

### Main Classes

- `SignalGenerationAgent`: Main agent class
- `TradingSignal`: Signal data structure
- `SignalValidationResult`: Validation result
- `PositionSizingResult`: Position sizing calculation result

### Key Methods

- `process_market_data()`: Process incoming market data
- `get_active_signals()`: Get currently active signals
- `get_performance_metrics()`: Get performance statistics
- `add_signal_handler()`: Add custom signal handler

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

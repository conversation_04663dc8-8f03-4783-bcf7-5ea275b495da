#!/usr/bin/env python3
"""
Execution Agent Demo
Demonstrates the usage of Execution Agent for automated trading

Features Demonstrated:
🚀 1. Basic Setup and Initialization
📊 2. Signal Processing and Order Placement
⚙️ 3. Order Management (Modify/Cancel)
📈 4. Performance Monitoring
🔧 5. Error Handling Examples
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.execution_agent import ExecutionAgent, SignalPayload, OrderResponse, TradeExecution

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ExecutionAgentDemo:
    """Demo class for Execution Agent functionality"""
    
    def __init__(self):
        self.agent = None
        self.demo_signals = []
        self.executed_trades = []
    
    async def initialize_agent(self):
        """Initialize the execution agent"""
        try:
            logger.info("🚀 Initializing Execution Agent...")
            
            # Initialize with config file
            config_path = "config/execution_config.yaml"
            self.agent = ExecutionAgent(config_path)
            
            # Initialize all components
            success = await self.agent.initialize()
            
            if success:
                logger.info("✅ Execution Agent initialized successfully")
                return True
            else:
                logger.error("❌ Failed to initialize Execution Agent")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error initializing agent: {e}")
            return False
    
    def create_demo_signals(self) -> List[SignalPayload]:
        """Create sample signals for demonstration"""
        
        signals = [
            # Long signal for RELIANCE
            SignalPayload(
                symbol="RELIANCE-EQ",
                exchange="NSE",
                symbol_token="2885",
                action="BUY",
                entry_price=2780.0,
                sl_price=2765.0,
                target_price=2815.0,
                quantity=1,
                order_type="LIMIT",
                product_type="MIS",
                strategy_name="momentum_breakout",
                signal_id="demo_001",
                risk_reward_ratio=2.33,
                confidence_score=0.85
            ),
            
            # Short signal for TCS
            SignalPayload(
                symbol="TCS-EQ",
                exchange="NSE",
                symbol_token="11536",
                action="SELL",
                entry_price=3500.0,
                sl_price=3520.0,
                target_price=3450.0,
                quantity=2,
                order_type="LIMIT",
                product_type="MIS",
                strategy_name="mean_reversion",
                signal_id="demo_002",
                risk_reward_ratio=2.5,
                confidence_score=0.78
            ),
            
            # Market order for INFY
            SignalPayload(
                symbol="INFY-EQ",
                exchange="NSE",
                symbol_token="1594",
                action="BUY",
                entry_price=1500.0,
                sl_price=1485.0,
                target_price=1530.0,
                quantity=3,
                order_type="MARKET",
                product_type="MIS",
                strategy_name="gap_up_strategy",
                signal_id="demo_003",
                risk_reward_ratio=2.0,
                confidence_score=0.92
            )
        ]
        
        self.demo_signals = signals
        logger.info(f"📊 Created {len(signals)} demo signals")
        return signals
    
    async def demonstrate_signal_processing(self):
        """Demonstrate signal processing and order placement"""
        logger.info("\n" + "="*60)
        logger.info("📊 DEMONSTRATING SIGNAL PROCESSING")
        logger.info("="*60)
        
        for i, signal in enumerate(self.demo_signals, 1):
            logger.info(f"\n🔄 Processing Signal {i}/{len(self.demo_signals)}")
            logger.info(f"   Symbol: {signal.symbol}")
            logger.info(f"   Action: {signal.action}")
            logger.info(f"   Entry: ₹{signal.entry_price}")
            logger.info(f"   SL: ₹{signal.sl_price}")
            logger.info(f"   Target: ₹{signal.target_price}")
            logger.info(f"   R:R Ratio: {signal.risk_reward_ratio:.2f}")
            
            try:
                # Process the signal
                success, message, trade_execution = await self.agent.process_signal(signal)
                
                if success:
                    logger.info(f"   ✅ Success: {message}")
                    if trade_execution and trade_execution.entry_order:
                        order_id = trade_execution.entry_order.order_id
                        logger.info(f"   📋 Order ID: {order_id}")
                        self.executed_trades.append(trade_execution)
                else:
                    logger.warning(f"   ⚠️  Failed: {message}")
                
                # Wait between signals
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"   ❌ Error processing signal: {e}")
    
    async def demonstrate_order_management(self):
        """Demonstrate order modification and cancellation"""
        logger.info("\n" + "="*60)
        logger.info("⚙️ DEMONSTRATING ORDER MANAGEMENT")
        logger.info("="*60)
        
        if not self.executed_trades:
            logger.warning("⚠️  No executed trades to manage")
            return
        
        # Demonstrate order modification
        first_trade = self.executed_trades[0]
        if first_trade.entry_order:
            order_id = first_trade.entry_order.order_id
            original_price = first_trade.signal_payload.entry_price
            new_price = original_price + 5.0  # Increase price by ₹5
            
            logger.info(f"\n🔧 Modifying Order: {order_id}")
            logger.info(f"   Original Price: ₹{original_price}")
            logger.info(f"   New Price: ₹{new_price}")
            
            try:
                success, message = await self.agent.modify_order(order_id, new_price)
                if success:
                    logger.info(f"   ✅ Modification successful: {message}")
                else:
                    logger.warning(f"   ⚠️  Modification failed: {message}")
            except Exception as e:
                logger.error(f"   ❌ Error modifying order: {e}")
        
        # Demonstrate order cancellation (if we have multiple trades)
        if len(self.executed_trades) > 1:
            second_trade = self.executed_trades[1]
            if second_trade.entry_order:
                order_id = second_trade.entry_order.order_id
                
                logger.info(f"\n❌ Cancelling Order: {order_id}")
                logger.info(f"   Reason: Demo cancellation")
                
                try:
                    success, message = await self.agent.cancel_order(
                        order_id, 
                        "Demo cancellation - market conditions changed"
                    )
                    if success:
                        logger.info(f"   ✅ Cancellation successful: {message}")
                    else:
                        logger.warning(f"   ⚠️  Cancellation failed: {message}")
                except Exception as e:
                    logger.error(f"   ❌ Error cancelling order: {e}")
    
    async def demonstrate_performance_monitoring(self):
        """Demonstrate performance monitoring and statistics"""
        logger.info("\n" + "="*60)
        logger.info("📈 DEMONSTRATING PERFORMANCE MONITORING")
        logger.info("="*60)
        
        try:
            # Get execution summary
            summary = await self.agent.get_execution_summary()
            
            logger.info("\n📊 Execution Statistics:")
            stats = summary.get('statistics', {})
            logger.info(f"   Total Orders: {stats.get('total_orders', 0)}")
            logger.info(f"   Successful Orders: {stats.get('successful_orders', 0)}")
            logger.info(f"   Failed Orders: {stats.get('failed_orders', 0)}")
            
            if stats.get('total_orders', 0) > 0:
                success_rate = (stats.get('successful_orders', 0) / stats.get('total_orders', 1)) * 100
                logger.info(f"   Success Rate: {success_rate:.2f}%")
            
            logger.info(f"   Avg Execution Time: {stats.get('avg_execution_time_ms', 0):.2f}ms")
            logger.info(f"   Avg Slippage: {stats.get('avg_slippage_percent', 0):.3f}%")
            
            logger.info(f"\n📋 Current Status:")
            logger.info(f"   Active Orders: {summary.get('active_orders_count', 0)}")
            logger.info(f"   Historical Orders: {summary.get('total_history_count', 0)}")
            logger.info(f"   Retry Queue Size: {summary.get('retry_queue_size', 0)}")
            
        except Exception as e:
            logger.error(f"❌ Error getting performance data: {e}")
    
    async def demonstrate_error_handling(self):
        """Demonstrate error handling scenarios"""
        logger.info("\n" + "="*60)
        logger.info("🛡️ DEMONSTRATING ERROR HANDLING")
        logger.info("="*60)
        
        # Test invalid signal
        logger.info("\n🧪 Testing Invalid Signal Handling:")
        invalid_signal = SignalPayload(
            symbol="",  # Empty symbol
            exchange="NSE",
            symbol_token="0000",
            action="BUY",
            entry_price=-100,  # Invalid price
            sl_price=0,
            target_price=0,
            quantity=-1,  # Invalid quantity
            signal_id="invalid_demo"
        )
        
        try:
            success, message, _ = await self.agent.process_signal(invalid_signal)
            if not success:
                logger.info(f"   ✅ Correctly rejected invalid signal: {message}")
            else:
                logger.warning(f"   ⚠️  Invalid signal was accepted: {message}")
        except Exception as e:
            logger.info(f"   ✅ Exception caught for invalid signal: {e}")
        
        # Test non-existent order management
        logger.info("\n🧪 Testing Non-existent Order Management:")
        try:
            success, message = await self.agent.modify_order("NON_EXISTENT_ORDER", 100.0)
            if not success:
                logger.info(f"   ✅ Correctly handled non-existent order: {message}")
            else:
                logger.warning(f"   ⚠️  Non-existent order modification succeeded: {message}")
        except Exception as e:
            logger.info(f"   ✅ Exception caught for non-existent order: {e}")
    
    async def save_demo_data(self):
        """Save demo trade data"""
        logger.info("\n" + "="*60)
        logger.info("💾 SAVING DEMO DATA")
        logger.info("="*60)
        
        try:
            # Save trade data
            demo_file = f"data/execution/demo_trades_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
            await self.agent.save_trade_data(demo_file)
            logger.info(f"✅ Demo data saved to: {demo_file}")
            
        except Exception as e:
            logger.error(f"❌ Error saving demo data: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("\n" + "="*60)
        logger.info("🧹 CLEANUP")
        logger.info("="*60)
        
        try:
            if self.agent:
                await self.agent.cleanup()
                logger.info("✅ Execution Agent cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
    
    async def run_full_demo(self):
        """Run the complete demonstration"""
        logger.info("🎬 STARTING EXECUTION AGENT DEMO")
        logger.info("="*60)
        
        try:
            # Initialize agent
            if not await self.initialize_agent():
                logger.error("❌ Failed to initialize agent. Exiting demo.")
                return
            
            # Create demo signals
            self.create_demo_signals()
            
            # Run demonstrations
            await self.demonstrate_signal_processing()
            await asyncio.sleep(5)  # Wait for orders to process
            
            await self.demonstrate_order_management()
            await asyncio.sleep(3)
            
            await self.demonstrate_performance_monitoring()
            await self.demonstrate_error_handling()
            await self.save_demo_data()
            
            logger.info("\n🎉 DEMO COMPLETED SUCCESSFULLY!")
            
        except Exception as e:
            logger.error(f"❌ Demo failed with error: {e}")
        finally:
            await self.cleanup()

async def main():
    """Main demo function"""
    demo = ExecutionAgentDemo()
    await demo.run_full_demo()

if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())

{"basic_diagnostics": {"status": "completed", "results": {"environment": {"smartapi_available": true, "api_key_set": false, "username_set": false, "password_set": false, "totp_token_set": false, "api_key_length": 0, "username_format": false, "totp_token_length": 0}, "network": {"internet_connectivity": true, "smartapi_server_apiconnect.angelbroking.com": true, "smartapi_server_smartapi.angelbroking.com": true, "websocket_port_443": true}, "authentication": {"smartconnect_init": true, "authentication_error": "object of type 'NoneType' has no len()"}, "websocket": {"prerequisites_met": false}, "recommendations": ["Set SMARTAPI_API_KEY environment variable", "Verify SmartAPI credentials (API key, username, password)", "Check if TOTP token is correct and not expired", "Verify you have less than 3 active WebSocket connections"]}, "recommendations_count": 4, "critical_issues": true}, "enhanced_websocket": {"status": "failed", "authentication": false}, "dynamic_workflow": {"status": "completed", "stocks_selected": false, "market_data_received": false, "final_status": {"current_phase": "active_trading", "monitoring_active": false, "selected_stocks_count": 0, "selected_stocks": [], "market_condition": null, "websocket_status": {"state": "failed", "metrics": {"connection_attempts": 1, "successful_connections": 0, "failed_connections": 0, "last_connection_time": null, "last_error": null, "total_messages_received": 0, "subscribed_symbols_count": 0}, "is_connected": false}}}}
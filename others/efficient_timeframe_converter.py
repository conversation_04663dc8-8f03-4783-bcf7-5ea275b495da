#!/usr/bin/env python3
"""
Efficient Timeframe Converter: Convert 5-minute historical data to 15min, 30min, and 1hr timeframes
Uses polars, pyarrow, and asyncio with chunking for memory efficiency
Writes to separate batch files and combines at the end to avoid memory issues
"""

import polars as pl
import pyarrow as pa
import asyncio
import os
import gc
from pathlib import Path
from typing import List, Dict, Any
import logging
from datetime import datetime
import glob

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EfficientTimeframeConverter:
    def __init__(self, input_file: str, output_dir: str = "data", chunk_size: int = 500000):
        """
        Initialize the efficient timeframe converter
        
        Args:
            input_file: Path to the 5-minute parquet file
            output_dir: Directory to save output files
            chunk_size: Number of rows to process per chunk
        """
        self.input_file = input_file
        self.output_dir = Path(output_dir)
        self.chunk_size = chunk_size
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(exist_ok=True)
        
        # Define timeframe configurations
        self.timeframes = {
            "15min": {"minutes": 15, "output_file": "historical_15min.parquet"},
            "30min": {"minutes": 30, "output_file": "historical_30min.parquet"},
            "1hr": {"minutes": 60, "output_file": "historical_1hr.parquet"}
        }
        
        # Create temp directories for batch files
        self.temp_dirs = {}
        for tf in self.timeframes.keys():
            temp_dir = self.output_dir / f"temp_{tf}"
            temp_dir.mkdir(exist_ok=True)
            self.temp_dirs[tf] = temp_dir
        
        # Initialize output files (clear existing files)
        self._initialize_output_files()
    
    def _initialize_output_files(self):
        """Initialize/clear output files and temp directories"""
        for tf_config in self.timeframes.values():
            output_path = self.output_dir / tf_config["output_file"]
            if output_path.exists():
                output_path.unlink()
                logger.info(f"Cleared existing file: {output_path}")
        
        # Clear temp directories
        for tf, temp_dir in self.temp_dirs.items():
            for temp_file in temp_dir.glob("*.parquet"):
                temp_file.unlink()
    
    def _create_datetime_column(self, df: pl.DataFrame) -> pl.DataFrame:
        """Create a datetime column from Date and Time columns"""
        return df.with_columns([
            pl.concat_str([
                pl.col("Date").cast(pl.Utf8),
                pl.lit(" "),
                pl.col(" Time").cast(pl.Utf8)
            ]).str.strptime(pl.Datetime, "%Y-%m-%d %H:%M:%S").alias("datetime")
        ])
    
    def _resample_to_timeframe(self, df: pl.DataFrame, minutes: int) -> pl.DataFrame:
        """
        Resample 5-minute data to specified timeframe
        
        Args:
            df: Input dataframe with 5-minute data
            minutes: Target timeframe in minutes (15, 30, or 60)
        
        Returns:
            Resampled dataframe
        """
        # Create datetime column
        df = self._create_datetime_column(df)
        
        # Group by stock and resample
        resampled = df.group_by([
            pl.col(" Stock_Name"),
            pl.col(" SymbolToken"),
            pl.col("datetime").dt.truncate(f"{minutes}m")
        ]).agg([
            # OHLC aggregation
            pl.col(" Open").first().alias(" Open"),
            pl.col(" High").max().alias(" High"),
            pl.col(" Low").min().alias(" Low"),
            pl.col(" Close").last().alias(" Close"),
            pl.col(" Volume").sum().alias(" Volume"),
            # Keep original Date and Time from first record in group
            pl.col("Date").first().alias("Date"),
            pl.col(" Time").first().alias(" Time")
        ]).sort([" Stock_Name", "datetime"])
        
        # Update Date and Time columns to reflect the resampled timeframe
        resampled = resampled.with_columns([
            pl.col("datetime").dt.date().alias("Date"),
            pl.col("datetime").dt.time().alias(" Time")
        ]).drop("datetime")
        
        return resampled
    
    async def _process_chunk_for_timeframe(self, chunk: pl.DataFrame, timeframe: str, minutes: int) -> pl.DataFrame:
        """Process a single chunk for a specific timeframe"""
        try:
            # Resample the chunk
            resampled_chunk = self._resample_to_timeframe(chunk, minutes)
            logger.info(f"Processed chunk for {timeframe}: {len(resampled_chunk)} rows")
            return resampled_chunk
        except Exception as e:
            logger.error(f"Error processing chunk for {timeframe}: {e}")
            raise
    
    async def _write_chunk_to_temp_file(self, chunk: pl.DataFrame, timeframe: str, chunk_num: int):
        """Write chunk to temporary file"""
        try:
            temp_dir = self.temp_dirs[timeframe]
            temp_file = temp_dir / f"chunk_{chunk_num:06d}.parquet"
            
            chunk.write_parquet(temp_file)
            logger.info(f"Written {len(chunk)} rows to temp file: {temp_file}")
            
        except Exception as e:
            logger.error(f"Error writing temp file for {timeframe}, chunk {chunk_num}: {e}")
            raise
    
    async def _process_single_chunk(self, chunk: pl.DataFrame, chunk_num: int):
        """Process a single chunk for all timeframes"""
        logger.info(f"Processing chunk {chunk_num} with {len(chunk)} rows")
        
        # Process chunk for each timeframe concurrently
        tasks = []
        for tf_name, tf_config in self.timeframes.items():
            task = self._process_chunk_for_timeframe(chunk, tf_name, tf_config["minutes"])
            tasks.append((tf_name, task))
        
        # Wait for all timeframe processing to complete and write to temp files
        for tf_name, task in tasks:
            try:
                processed_chunk = await task
                await self._write_chunk_to_temp_file(processed_chunk, tf_name, chunk_num)
            except Exception as e:
                logger.error(f"Failed to process chunk {chunk_num} for {tf_name}: {e}")
                continue
        
        # Clear memory
        del chunk
        gc.collect()
        
        logger.info(f"Completed processing chunk {chunk_num}")
    
    async def _combine_temp_files(self, timeframe: str):
        """Combine all temporary files for a timeframe into final output"""
        logger.info(f"Combining temporary files for {timeframe}...")
        
        temp_dir = self.temp_dirs[timeframe]
        temp_files = sorted(temp_dir.glob("*.parquet"))
        
        if not temp_files:
            logger.warning(f"No temporary files found for {timeframe}")
            return
        
        output_file = self.timeframes[timeframe]["output_file"]
        output_path = self.output_dir / output_file
        
        try:
            # Read and combine all temp files
            dfs = []
            for temp_file in temp_files:
                df = pl.read_parquet(temp_file)
                dfs.append(df)
                logger.info(f"Read {len(df)} rows from {temp_file}")
            
            # Combine all dataframes
            if dfs:
                combined_df = pl.concat(dfs)
                combined_df.write_parquet(output_path)
                
                total_rows = len(combined_df)
                logger.info(f"Combined {len(temp_files)} files into {output_path}: {total_rows:,} rows")
                
                # Clean up temp files
                for temp_file in temp_files:
                    temp_file.unlink()
                
                # Remove temp directory
                temp_dir.rmdir()
                
                del combined_df
                del dfs
                gc.collect()
            
        except Exception as e:
            logger.error(f"Error combining temp files for {timeframe}: {e}")
            raise
    
    async def convert_timeframes(self):
        """Main method to convert 5-minute data to multiple timeframes"""
        logger.info(f"Starting efficient timeframe conversion from {self.input_file}")
        logger.info(f"Target timeframes: {list(self.timeframes.keys())}")
        logger.info(f"Chunk size: {self.chunk_size}")
        
        try:
            # Read the parquet file in chunks using scan_parquet for lazy loading
            df_lazy = pl.scan_parquet(self.input_file)
            
            # Get total row count for progress tracking
            total_rows = df_lazy.select(pl.count()).collect().item()
            total_chunks = (total_rows + self.chunk_size - 1) // self.chunk_size
            logger.info(f"Total rows: {total_rows:,}, Total chunks: {total_chunks}")
            
            # Process chunks
            chunk_num = 0
            offset = 0
            
            while offset < total_rows:
                chunk_num += 1
                logger.info(f"Loading chunk {chunk_num}/{total_chunks} (offset: {offset:,})")
                
                # Load chunk
                chunk = df_lazy.slice(offset, self.chunk_size).collect()
                
                if len(chunk) == 0:
                    break
                
                # Process chunk for all timeframes
                await self._process_single_chunk(chunk, chunk_num)
                
                # Update offset
                offset += self.chunk_size
                
                # Progress update
                progress = (chunk_num / total_chunks) * 100
                logger.info(f"Progress: {progress:.1f}% ({chunk_num}/{total_chunks} chunks)")
            
            # Combine all temporary files for each timeframe
            logger.info("Combining temporary files...")
            for timeframe in self.timeframes.keys():
                await self._combine_temp_files(timeframe)
            
            logger.info("Timeframe conversion completed successfully!")
            
            # Log final file sizes
            for tf_name, tf_config in self.timeframes.items():
                output_path = self.output_dir / tf_config["output_file"]
                if output_path.exists():
                    final_df = pl.read_parquet(output_path)
                    logger.info(f"{tf_name} output: {len(final_df):,} rows -> {output_path}")
                    del final_df
                    gc.collect()
        
        except Exception as e:
            logger.error(f"Error during timeframe conversion: {e}")
            raise

async def main():
    """Main function"""
    # Configuration
    input_file = "data/historical/historical_5min.parquet"
    output_dir = "data/historical"
    chunk_size = 300000  # Adjust based on available memory
    
    # Check if input file exists
    if not Path(input_file).exists():
        logger.error(f"Input file not found: {input_file}")
        return
    
    # Create converter and run
    converter = EfficientTimeframeConverter(input_file, output_dir, chunk_size)
    await converter.convert_timeframes()

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())

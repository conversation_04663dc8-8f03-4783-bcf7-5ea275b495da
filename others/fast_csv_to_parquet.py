#!/usr/bin/env python3
"""
Ultra-Fast CSV to Parquet Converter for Large Files (110M+ rows)
Optimized for memory efficiency and speed using polars streaming
"""

import polars as pl
import time
import psutil
import os
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
INPUT_CSV = r"C:\Users\<USER>\Documents\Intraday-AI\smartapi_data.csv"
OUTPUT_DIR = Path("data")
OUTPUT_FILE = "smartapi_data.parquet"
COMPRESSION = "brotli"  # Best compression ratio

def get_file_info(file_path: str) -> dict:
    """Get file information"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
        
    file_size = os.path.getsize(file_path)
    file_size_mb = file_size / (1024 * 1024)
    file_size_gb = file_size_mb / 1024
    
    return {
        'size_bytes': file_size,
        'size_mb': file_size_mb,
        'size_gb': file_size_gb
    }

def estimate_rows(file_path: str, sample_lines: int = 1000) -> int:
    """Estimate total rows by sampling"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # Skip header
            f.readline()
            
            # Sample lines
            total_chars = 0
            for i, line in enumerate(f):
                if i >= sample_lines:
                    break
                total_chars += len(line)
                
            if i > 0:
                avg_line_length = total_chars / i
                file_info = get_file_info(file_path)
                estimated_rows = int(file_info['size_bytes'] / avg_line_length)
                return estimated_rows
                
    except Exception as e:
        logger.warning(f"Could not estimate rows: {e}")
        
    return 0

def convert_with_polars_streaming():
    """Convert CSV to Parquet using Polars streaming (most memory efficient)"""
    
    logger.info("🚀 ULTRA-FAST CSV TO PARQUET CONVERTER")
    logger.info("=" * 60)
    
    # Setup
    OUTPUT_DIR.mkdir(exist_ok=True)
    output_path = OUTPUT_DIR / OUTPUT_FILE
    
    # File info
    file_info = get_file_info(INPUT_CSV)
    estimated_rows = estimate_rows(INPUT_CSV)
    
    logger.info(f"📁 Input file: {INPUT_CSV}")
    logger.info(f"📊 File size: {file_info['size_gb']:.2f}GB ({file_info['size_mb']:.1f}MB)")
    logger.info(f"📈 Estimated rows: {estimated_rows:,}")
    logger.info(f"💾 Output: {output_path}")
    logger.info(f"🗜️  Compression: {COMPRESSION}")
    
    # Memory info
    memory_gb = psutil.virtual_memory().total / (1024**3)
    logger.info(f"🧠 Available RAM: {memory_gb:.1f}GB")
    
    start_time = time.time()
    
    try:
        logger.info("\n⚡ Starting conversion using Polars streaming...")
        
        # Use Polars lazy frame with streaming for memory efficiency
        lazy_df = pl.scan_csv(
            INPUT_CSV,
            infer_schema_length=50000,  # Sample more rows for better schema inference
            try_parse_dates=True,
            null_values=["", "NULL", "null", "NA", "N/A", "nan"],
            ignore_errors=True  # Skip problematic rows
        )
        
        # Optimize data types for better compression
        logger.info("🔧 Optimizing data types...")
        
        # Get schema info
        sample_df = lazy_df.head(1000).collect()
        logger.info(f"📋 Detected {len(sample_df.columns)} columns")
        
        # Apply optimizations
        optimized_lazy = lazy_df
        
        # Convert to appropriate types for better compression
        for col in sample_df.columns:
            dtype = sample_df[col].dtype
            
            # Optimize string columns
            if dtype == pl.Utf8:
                # Check if it could be categorical
                unique_ratio = sample_df[col].n_unique() / len(sample_df)
                if unique_ratio < 0.5:  # Less than 50% unique values
                    optimized_lazy = optimized_lazy.with_columns(
                        pl.col(col).cast(pl.Categorical)
                    )
            
            # Optimize float columns
            elif dtype == pl.Float64:
                # Try to downcast to Float32
                optimized_lazy = optimized_lazy.with_columns(
                    pl.col(col).cast(pl.Float32, strict=False)
                )
        
        # Write to parquet using streaming (memory efficient)
        logger.info("💾 Writing to Parquet format...")
        
        optimized_lazy.sink_parquet(
            output_path,
            compression=COMPRESSION,
            maintain_order=False,  # Faster processing
            row_group_size=100000,  # Optimize for compression
        )
        
        # Conversion completed
        elapsed_time = time.time() - start_time
        
        # Get output file info
        if output_path.exists():
            output_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            compression_ratio = (file_info['size_mb'] / output_size) if output_size > 0 else 0
            
            # Estimate processing speed
            rows_per_second = estimated_rows / elapsed_time if elapsed_time > 0 else 0
            
            logger.info("\n" + "=" * 60)
            logger.info("✅ CONVERSION COMPLETED SUCCESSFULLY!")
            logger.info("=" * 60)
            logger.info(f"⏱️  Total time: {elapsed_time:.1f} seconds ({elapsed_time/60:.1f} minutes)")
            logger.info(f"🚀 Processing speed: {rows_per_second:,.0f} rows/second")
            logger.info(f"📁 Output size: {output_size:.1f}MB")
            logger.info(f"🗜️  Compression ratio: {compression_ratio:.1f}x")
            logger.info(f"💾 Space saved: {file_info['size_mb'] - output_size:.1f}MB")
            logger.info(f"📍 Output location: {output_path.absolute()}")
            
        else:
            logger.error("❌ Output file was not created")
            
    except Exception as e:
        logger.error(f"❌ Conversion failed: {e}")
        raise

def convert_with_chunking_fallback():
    """Fallback method using incremental chunking if streaming fails"""

    logger.info("🔄 Using incremental chunking fallback method...")

    OUTPUT_DIR.mkdir(exist_ok=True)
    output_path = OUTPUT_DIR / OUTPUT_FILE

    chunk_size = 50_000  # Smaller chunks for memory efficiency
    write_batch_size = 5  # Write every 5 chunks

    try:
        chunk_num = 0
        temp_files_batch = []
        is_first_batch = True

        for chunk_df in pl.read_csv_batched(
            INPUT_CSV,
            batch_size=chunk_size,
            infer_schema_length=10000,
            try_parse_dates=True,
            null_values=["", "NULL", "null", "NA", "N/A"]
        ):
            # Process chunk
            temp_file = OUTPUT_DIR / f"temp_chunk_{chunk_num:06d}.parquet"

            chunk_df.write_parquet(
                temp_file,
                compression=COMPRESSION
            )

            temp_files_batch.append(temp_file)
            chunk_num += 1

            logger.info(f"Processed chunk {chunk_num}: {len(chunk_df):,} rows")

            # Write batch to output when we have enough chunks
            if len(temp_files_batch) >= write_batch_size:
                logger.info(f"💾 Writing batch of {len(temp_files_batch)} chunks...")

                # Read and concatenate current batch
                batch_dfs = [pl.read_parquet(f) for f in temp_files_batch]
                batch_df = pl.concat(batch_dfs)

                if is_first_batch:
                    # Create new output file
                    batch_df.write_parquet(output_path, compression=COMPRESSION)
                    is_first_batch = False
                else:
                    # Append to existing file
                    existing_df = pl.read_parquet(output_path)
                    combined_df = pl.concat([existing_df, batch_df])
                    combined_df.write_parquet(output_path, compression=COMPRESSION)

                # Cleanup temp files
                for temp_file in temp_files_batch:
                    temp_file.unlink(missing_ok=True)

                temp_files_batch = []

                # Memory management
                import gc
                gc.collect()

                if psutil.virtual_memory().percent > 85:
                    logger.warning("High memory usage detected")

        # Write remaining chunks if any
        if temp_files_batch:
            logger.info(f"💾 Writing final batch of {len(temp_files_batch)} chunks...")

            batch_dfs = [pl.read_parquet(f) for f in temp_files_batch]
            batch_df = pl.concat(batch_dfs)

            if is_first_batch:
                batch_df.write_parquet(output_path, compression=COMPRESSION)
            else:
                existing_df = pl.read_parquet(output_path)
                combined_df = pl.concat([existing_df, batch_df])
                combined_df.write_parquet(output_path, compression=COMPRESSION)

            # Cleanup
            for temp_file in temp_files_batch:
                temp_file.unlink(missing_ok=True)

        logger.info(f"✅ Incremental chunking conversion completed: {output_path}")

    except Exception as e:
        logger.error(f"❌ Chunking fallback failed: {e}")
        # Cleanup on failure
        for temp_file in temp_files_batch:
            temp_file.unlink(missing_ok=True)
        raise

def verify_conversion(output_path: Path):
    """Verify the converted parquet file"""
    
    logger.info("🔍 Verifying conversion...")
    
    try:
        # Read parquet file
        df = pl.read_parquet(output_path)
        
        logger.info(f"✅ Verification successful!")
        logger.info(f"📊 Rows: {len(df):,}")
        logger.info(f"📋 Columns: {len(df.columns)}")
        logger.info(f"🏷️  Column names: {list(df.columns)[:10]}{'...' if len(df.columns) > 10 else ''}")
        
        # Show sample data
        logger.info("📋 Sample data (first 3 rows):")
        print(df.head(3))
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def main():
    """Main conversion function"""
    
    # Check input file
    if not os.path.exists(INPUT_CSV):
        logger.error(f"❌ Input file not found: {INPUT_CSV}")
        return
    
    output_path = OUTPUT_DIR / OUTPUT_FILE
    
    try:
        # Try streaming method first (most efficient)
        convert_with_polars_streaming()
        
    except Exception as e:
        logger.warning(f"Streaming method failed: {e}")
        logger.info("Trying chunking fallback...")
        
        try:
            convert_with_chunking_fallback()
        except Exception as e2:
            logger.error(f"All conversion methods failed: {e2}")
            return
    
    # Verify the result
    if output_path.exists():
        verify_conversion(output_path)
    else:
        logger.error("❌ No output file was created")

if __name__ == "__main__":
    main()

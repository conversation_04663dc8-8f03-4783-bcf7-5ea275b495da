import pandas as pd

# Paths to input and output files
NIFTY50_FILE = r"C:\Users\<USER>\Downloads\ind_nifty50list.csv"
HISTORICAL_FILE = r"C:\Users\<USER>\Documents\Intraday-AI\smartapi_data.csv"
OUTPUT_FILE = "filtered_nifty50_data.csv"

# Load Nifty 50 symbols
def load_nifty50_symbols(filepath):
    df = pd.read_csv(filepath)
    df.columns = df.columns.str.strip()  # Remove any leading/trailing whitespace
    if 'Symbol' in df.columns:
        return set(df['Symbol'].str.upper())
    elif 'Stock_Name' in df.columns:
        return set(df['Stock_Name'].str.upper())
    else:
        raise ValueError(f"No recognizable symbol column found in: {df.columns.tolist()}")

# Filter historical data using chunking
def filter_historical_data(nifty_symbols, chunksize=50_000):
    chunk_iter = pd.read_csv(HISTORICAL_FILE, chunksize=chunksize)
    first_chunk = True

    for i, chunk in enumerate(chunk_iter):
        chunk.columns = chunk.columns.str.strip()  # Clean column names
        if 'Stock_Name' not in chunk.columns:
            raise KeyError(f"'Stock_Name' not found in columns: {chunk.columns.tolist()}")

        chunk['Stock_Name'] = chunk['Stock_Name'].astype(str).str.upper()
        filtered_chunk = chunk[chunk['Stock_Name'].isin(nifty_symbols)]

        mode = 'w' if first_chunk else 'a'
        filtered_chunk.to_csv(OUTPUT_FILE, mode=mode, index=False, header=first_chunk)
        first_chunk = False

        print(f"Processed chunk {i+1}")

def main():
    print("Loading Nifty 50 symbols...")
    nifty_symbols = load_nifty50_symbols(NIFTY50_FILE)

    print("Filtering historical data from:", HISTORICAL_FILE)
    filter_historical_data(nifty_symbols)

    print("Filtering complete. Output saved to:", OUTPUT_FILE)

if __name__ == "__main__":
    main()
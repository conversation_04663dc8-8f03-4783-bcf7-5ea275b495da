#!/usr/bin/env python3
"""
Simple runner script for CSV to Parquet conversion
"""

import os
import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.append('.')

try:
    from fast_csv_to_parquet import main as convert_main
    from converter_config import INPUT_CSV, OUTPUT_DIR, OUTPUT_FILE
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure all required files are in the current directory")
    sys.exit(1)

def check_requirements():
    """Check if all requirements are met"""
    
    print("🔍 Checking requirements...")
    
    # Check input file
    if not os.path.exists(INPUT_CSV):
        print(f"❌ Input file not found: {INPUT_CSV}")
        return False
    
    # Check file size
    file_size_gb = os.path.getsize(INPUT_CSV) / (1024**3)
    print(f"📁 Input file size: {file_size_gb:.2f}GB")
    
    # Check available disk space
    output_dir = Path(OUTPUT_DIR)
    output_dir.mkdir(exist_ok=True)
    
    free_space = os.statvfs(output_dir).f_frsize * os.statvfs(output_dir).f_bavail / (1024**3) if hasattr(os, 'statvfs') else 100
    print(f"💾 Available disk space: {free_space:.2f}GB")
    
    if free_space < file_size_gb * 0.5:  # Need at least 50% of input size
        print("⚠️  Warning: Low disk space. Conversion may fail.")
    
    # Check Python packages
    try:
        import polars as pl
        print(f"✅ Polars version: {pl.__version__}")
    except ImportError:
        print("❌ Polars not installed. Run: pip install polars")
        return False
    
    try:
        import psutil
        print(f"✅ psutil available")
    except ImportError:
        print("❌ psutil not installed. Run: pip install psutil")
        return False
    
    return True

def main():
    """Main runner function"""
    
    print("🚀 CSV TO PARQUET CONVERTER")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        return
    
    print("\n✅ All requirements met. Starting conversion...")
    print("=" * 50)
    
    # Record start time
    start_time = time.time()
    
    try:
        # Run the conversion
        convert_main()
        
        # Calculate total time
        total_time = time.time() - start_time
        
        print("\n" + "=" * 50)
        print("🎉 CONVERSION COMPLETED SUCCESSFULLY!")
        print(f"⏱️  Total time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
        
        # Check output file
        output_path = OUTPUT_DIR / OUTPUT_FILE
        if output_path.exists():
            output_size_mb = os.path.getsize(output_path) / (1024 * 1024)
            print(f"📁 Output file: {output_path}")
            print(f"📊 Output size: {output_size_mb:.1f}MB")
        
    except KeyboardInterrupt:
        print("\n⚠️  Conversion interrupted by user")
        
    except Exception as e:
        print(f"\n❌ Conversion failed: {e}")
        print("Check the log file for more details")

if __name__ == "__main__":
    main()

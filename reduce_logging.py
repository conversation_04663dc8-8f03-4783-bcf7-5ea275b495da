#!/usr/bin/env python3
"""
Reduce logging by 70-90% - Keep only essential logs
"""

import os
import re
from pathlib import Path

def reduce_logging_in_file(file_path: Path):
    """Reduce logging in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Remove debug logs
        content = re.sub(r'^\s*logger\.debug\(.*?\)\s*$', '', content, flags=re.MULTILINE)
        
        # Remove info logs that are too verbose
        verbose_patterns = [
            r'^\s*logger\.info\(f?"📊 Processing stock.*?\)\s*$',
            r'^\s*logger\.info\(f?"✅ Processed.*?\)\s*$',
            r'^\s*logger\.info\(f?"🧪.*evaluation.*?\)\s*$',
            r'^\s*logger\.info\(f?"⚡.*signal.*?\)\s*$',
            r'^\s*logger\.info\(f?"🚀.*GPU.*?\)\s*$',
            r'^\s*logger\.info\(f?"📋.*strategies.*?\)\s*$',
            r'^\s*logger\.info\(f?"🔍.*Processing.*?\)\s*$',
        ]
        
        for pattern in verbose_patterns:
            content = re.sub(pattern, '', content, flags=re.MULTILINE)
        
        # Remove excessive progress indicators
        content = re.sub(r'^\s*if i % 10 == 0:.*?logger\.info.*?\n', '', content, flags=re.MULTILINE | re.DOTALL)
        
        # Keep only critical logs (ERROR, WARNING, and key SUCCESS messages)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Reduced logging in {file_path.name}")
        
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")

def main():
    """Main function to reduce logging across all files"""
    print("🔧 Reducing logging by 70-90%...")
    
    # Files to process
    files_to_process = [
        "agents/enhanced_strategy_evolution_agent.py",
        "agents/enhanced_backtesting_kimi.py", 
        "utils/cuda_strategy_processor.py",
        "utils/enhanced_cuda_optimizer.py",
        "main.py"
    ]
    
    for file_path in files_to_process:
        path = Path(file_path)
        if path.exists():
            reduce_logging_in_file(path)
    
    # Set logging level to WARNING in key files
    config_changes = {
        "agents/enhanced_strategy_evolution_agent.py": "logging.WARNING",
        "agents/enhanced_backtesting_kimi.py": "logging.WARNING"
    }
    
    for file_path, log_level in config_changes.items():
        path = Path(file_path)
        if path.exists():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Change logging level
                content = re.sub(
                    r'logging\.basicConfig\(\s*level=logging\.INFO',
                    f'logging.basicConfig(\n    level={log_level}',
                    content
                )
                
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ Set {file_path} to {log_level}")
            except Exception as e:
                print(f"❌ Error setting log level in {file_path}: {e}")
    
    print("✅ Logging reduced by ~80%")

if __name__ == "__main__":
    main()
# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 CONSOLIDATED REQUIREMENTS FOR INTRADAY AI TRADING SYSTEM
# ═══════════════════════════════════════════════════════════════════════════════
# Updated with 2024-2025 optimizations and GPU acceleration support
# Compatible with RTX 3060Ti and CUDA 12.x

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 CORE ML LIBRARIES (UPDATED TO LATEST OPTIMIZED VERSIONS)
# ═══════════════════════════════════════════════════════════════════════════════
# Primary ML Framework - LightGBM (Latest with improved GPU support)
lightgbm>=4.3.0

# Hyperparameter Optimization - Optuna (Latest with better pruning)
optuna>=3.6.0

# Traditional ML Support
scikit-learn>=1.4.0
xgboost>=2.0.0

# Ensemble Enhancement - CatBoost (NEW ADDITION)
catboost>=1.2.0

# Deep Learning - PyTorch (Latest optimized for CUDA)
# Using CUDA 12.x compatible versions
torch>=2.2.0+cu121
torchvision>=0.17.0+cu121
torchaudio>=2.2.0+cu121
--extra-index-url https://download.pytorch.org/whl/cu121

# TabNet for Deep Tabular Learning
pytorch-tabnet>=4.1.0

# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ HIGH-PERFORMANCE DATA PROCESSING (POLARS + PYARROW STACK)
# ═══════════════════════════════════════════════════════════════════════════════
# Primary Data Processing - Polars (Latest with GPU engine support)
polars>=0.20.0

# Arrow Backend - PyArrow (Latest optimized version)
pyarrow>=15.0.0

# Legacy Support (Minimal usage)
pandas>=2.2.0
numpy>=1.26.0

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 TECHNICAL ANALYSIS & FINANCIAL LIBRARIES
# ═══════════════════════════════════════════════════════════════════════════════
# Polars-native TA-Lib (Faster than traditional TA-Lib)
polars-talib>=0.1.0

# Traditional TA-Lib (Fallback) - Install manually if needed
# TA-Lib>=0.4.28  # Use: pip install TA-Lib-0.4.28-cp312-cp312-win_amd64.whl

# Financial calculations
scipy>=1.12.0

# Signal Processing Libraries
PyWavelets>=1.4.1
# pykalman>=0.9.5  # Optional - install manually if needed: pip install pykalman
filterpy>=1.4.5  # Alternative to pykalman

# Advanced Technical Indicators
ta>=0.10.2
finta>=1.3

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 BROKER INTEGRATION & MARKET DATA
# ═══════════════════════════════════════════════════════════════════════════════
# Angel One SmartAPI Integration
smartapi-python>=1.4.8
pyotp>=2.9.0
websocket-client>=1.7.0

# HTTP Requests
requests>=2.31.0
aiohttp>=3.9.0

# Market Data Sources (YFinance for historical data - no rate limits)
yfinance>=0.2.18

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 ASYNC & CONCURRENCY
# ═══════════════════════════════════════════════════════════════════════════════
# Async File Operations
aiofiles>=23.2.0

# Async Processing
asyncio-throttle>=1.0.2

# ═══════════════════════════════════════════════════════════════════════════════
# 🌐 WEB FRAMEWORK & API
# ═══════════════════════════════════════════════════════════════════════════════
# FastAPI for Model Serving & API
fastapi>=0.109.0
uvicorn[standard]>=0.27.0
pydantic>=2.6.0
python-multipart>=0.0.9

# ═══════════════════════════════════════════════════════════════════════════════
# 📱 NOTIFICATIONS & COMMUNICATION
# ═══════════════════════════════════════════════════════════════════════════════
# Telegram Integration
python-telegram-bot>=20.7

# Email Support (smtplib is built into Python)
# smtplib2>=0.2.1  # Not needed - smtplib is built-in

# ═══════════════════════════════════════════════════════════════════════════════
# 🗄️ DATABASE & STORAGE
# ═══════════════════════════════════════════════════════════════════════════════
# SQLite (Built-in)
# PostgreSQL Support
psycopg2-binary>=2.9.9

# Redis Cache
redis>=5.0.1

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 MONITORING & LOGGING
# ═══════════════════════════════════════════════════════════════════════════════
# System Monitoring
psutil>=5.9.8
GPUtil>=1.4.0

# Logging
logzero>=1.7.0

# Progress Bars
tqdm>=4.66.0

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 CONFIGURATION & UTILITIES
# ═══════════════════════════════════════════════════════════════════════════════
# Configuration Management
PyYAML>=6.0.1
python-dotenv>=1.0.0

# Date/Time Utilities
python-dateutil>=2.8.2

# Job Scheduling
schedule>=1.2.0

# Serialization
joblib>=1.3.2
# pickle5>=0.0.12  # Not needed for Python 3.8+ - pickle is built-in

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 LLM & AI INTERFACE
# ═══════════════════════════════════════════════════════════════════════════════
# Local LLM Support (Install manually if needed)
# ollama>=0.1.7  # Install manually: pip install ollama

# LangChain for LLM Integration
langchain>=0.1.0
langchain-community>=0.0.20
langchain-core>=0.1.0

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 VISUALIZATION & PLOTTING
# ═══════════════════════════════════════════════════════════════════════════════
# Plotting Libraries
matplotlib>=3.8.0
seaborn>=0.13.0
plotly>=5.18.0

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TESTING & DEVELOPMENT
# ═══════════════════════════════════════════════════════════════════════════════
# Testing Framework
pytest>=8.0.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0

# Code Quality
black>=24.0.0
flake8>=7.0.0
mypy>=1.8.0

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 MODEL INTERPRETABILITY & ANALYSIS
# ═══════════════════════════════════════════════════════════════════════════════
# Model Explanation
shap>=0.44.0
lime>=0.2.0.1

# Experiment Tracking (Optional)
mlflow>=2.10.0
wandb>=0.16.0

# ═══════════════════════════════════════════════════════════════════════════════
# 🔥 GPU ACCELERATION (OPTIONAL - INSTALL ONLY IF YOU HAVE NVIDIA GPU)
# ═══════════════════════════════════════════════════════════════════════════════
# Uncomment the following lines if you have NVIDIA GPU with CUDA 12.x

# GPU Data Processing - Optimized for RTX 3060Ti
cudf-cu12>=24.0.0
cupy-cuda12x>=12.0.0

# GPU PyTorch - CUDA 12.1 optimized
# torch>=2.2.0+cu121
# torchvision>=0.17.0+cu121
# torchaudio>=2.2.0+cu121
# --extra-index-url https://download.pytorch.org/whl/cu121

# Numba CUDA - Optimized versions
cuda-python>=12.0.0
numba>=0.59.0

# Additional GPU acceleration libraries
rapids-cudf>=24.0.0  # RAPIDS for GPU DataFrame operations
cuml>=24.0.0         # RAPIDS ML for GPU machine learning
cusignal>=24.0.0     # GPU signal processing

# Memory optimization
psutil>=5.9.8        # For memory monitoring
GPUtil>=1.4.0        # For GPU monitoring

# ═══════════════════════════════════════════════════════════════════════════════
# 📝 INSTALLATION INSTRUCTIONS
# ═══════════════════════════════════════════════════════════════════════════════
# 1. For CPU-only installation:
#    pip install -r requirements.txt
#
# 2. For GPU acceleration (NVIDIA GPU with CUDA 12.x):
#    - Uncomment GPU sections above
#    - Install CUDA Toolkit 12.x from NVIDIA
#    - pip install -r requirements.txt
#
# 3. For development:
#    pip install -r requirements.txt
#    pip install -e .
#
# 4. Verify installation:
#    python -c "import lightgbm, polars, torch; print('✅ Installation successful')"

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 PERFORMANCE NOTES FOR RTX 3060Ti
# ═══════════════════════════════════════════════════════════════════════════════
# - LightGBM GPU: 40-75% faster than CPU
# - Polars GPU Engine: Up to 13x speedup on compute-bound queries
# - PyTorch Mixed Precision: 1.5-2x speedup for TabNet training
# - CatBoost GPU: 10x faster training on large datasets
# - Optimal GPU memory usage: ~6-7GB for full pipeline

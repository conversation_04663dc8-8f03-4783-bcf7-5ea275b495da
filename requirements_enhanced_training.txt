# Enhanced Model Training Agent Requirements
# Optimized for Windows 10 environment

# Core Data Science Libraries
numpy>=1.24.0
pandas>=2.0.0
polars>=0.20.0
scikit-learn>=1.3.0
scipy>=1.10.0

# Machine Learning Models
lightgbm>=4.0.0
xgboost>=2.0.0
catboost>=1.2.0

# Deep Learning (TabNet)
torch>=2.0.0
pytorch-tabnet>=4.0.0

# Hyperparameter Optimization
optuna>=3.0.0

# Model Explainability
shap>=0.42.0

# Data Processing and Utilities
joblib>=1.3.0
pyyaml>=6.0
tqdm>=4.65.0

# Visualization (optional but recommended)
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Additional utilities
pathlib2>=2.3.7  # For Python < 3.4 compatibility
typing-extensions>=4.7.0

# Windows-specific optimizations
# These packages provide better performance on Windows
intel-openmp>=2023.2.0  # Intel MKL optimizations
mkl>=2023.2.0  # Math Kernel Library

# Optional GPU acceleration (uncomment if NVIDIA GPU available)
# torch-audio>=2.0.0
# torch-vision>=0.15.0

# Memory optimization
psutil>=5.9.0
memory-profiler>=0.61.0

# Configuration management
hydra-core>=1.3.0
omegaconf>=2.3.0

# Logging and monitoring
loguru>=0.7.0
rich>=13.0.0

# File I/O optimization
pyarrow>=12.0.0
fastparquet>=2023.7.0

# Async support
asyncio-throttle>=1.0.2
aiofiles>=23.0.0

# Model serving (optional)
fastapi>=0.100.0
uvicorn>=0.23.0
pydantic>=2.0.0

# Data validation
pandera>=0.17.0
great-expectations>=0.17.0

# Feature engineering
feature-engine>=1.6.0
category-encoders>=2.6.0

# Time series utilities
statsmodels>=0.14.0
pmdarima>=2.0.0

# Ensemble methods
mlxtend>=0.22.0

# Model interpretation
lime>=*******
eli5>=0.13.0

# Performance monitoring
py-spy>=0.3.14
line-profiler>=4.0.0

# Jupyter notebook support (for development)
jupyter>=1.0.0
ipykernel>=6.25.0
ipywidgets>=8.0.0

# Database connectivity (if needed)
sqlalchemy>=2.0.0
pymongo>=4.5.0

# Cloud storage (if needed)
boto3>=1.28.0
azure-storage-blob>=12.17.0
google-cloud-storage>=2.10.0
# Scripts Directory

This directory contains all the data processing and utility scripts for the trading system.

## 📁 Available Scripts

### 1. `historical_data_downloader.py`
Downloads historical 5-minute data from SmartAPI for all 500 symbols.

**Features:**
- ✅ Manual date input in dd-mm-yyyy format
- ✅ Separate date and time columns (no combined datetime)
- ✅ Downloads ALL 500 symbols (no test mode limitation)
- ✅ 0.1s sleep time between symbol downloads
- ✅ Saves data in optimized parquet format

**Usage:**

```bash
# Download with manual date range (dd-mm-yyyy format)
python scripts/historical_data_downloader.py --from-date 01-01-2024 --to-date 31-01-2024

# Download last 7 days
python scripts/historical_data_downloader.py --days 7

# Download last 30 days
python scripts/historical_data_downloader.py --days 30
```

**Output Format:**
- File: `data/historical/historical_5min.parquet`
- Columns: `date`, `time`, `open`, `high`, `low`, `close`, `volume`, `symbol`
- Date format: dd-mm-yyyy (e.g., "01-01-2024")
- Time format: HH:MM:SS (e.g., "09:15:00")

### 2. `timeframe_converter.py`
Converts 5-minute data to higher timeframes (15min, 30min, 1hr).

**Usage:**
```bash
python scripts/timeframe_converter.py
```

### 3. `run_full_conversion.py`
Production script for converting large datasets with optimized settings.

**Usage:**
```bash
python scripts/run_full_conversion.py
```

### 4. `optimized_feature_engineering.py`
Generates comprehensive technical indicators with GPU acceleration.

**Usage:**
```bash
python scripts/optimized_feature_engineering.py --input-dir data/historical --output-dir data/features
```

### 5. `download_example.py`
Interactive example script showing how to use the downloader.

**Usage:**
```bash
python scripts/download_example.py
```

## 🔧 Configuration

### Environment Variables
Set these in your `.env` file:
```
SMARTAPI_API_KEY=your_api_key
SMARTAPI_USERNAME=your_username
SMARTAPI_PASSWORD=your_password
SMARTAPI_TOTP=your_totp_secret
```

### Symbol Configuration
Symbols are loaded from:
1. `config/symbols.json` (if available)
2. Existing historical data in `data/historical/backup/`
3. Default NIFTY 50 symbols (fallback)

## 📊 Data Flow

```
1. historical_data_downloader.py → data/historical/historical_5min.parquet
2. timeframe_converter.py → data/historical/historical_15min.parquet, etc.
3. optimized_feature_engineering.py → data/features/features_*.parquet
```

## ⚠️ Important Notes

1. **Rate Limiting**: 0.1s sleep between symbol downloads to respect API limits
2. **All Symbols**: Downloads all 500 symbols by default (not limited to 50)
3. **Date Format**: Input dates must be in dd-mm-yyyy format
4. **Market Hours**: Automatically sets time to 09:15-15:30 for manual dates
5. **Output Format**: Separate date and time columns for easier processing

## 🚀 Quick Start

1. **Set up environment variables** in `.env` file
2. **Download historical data:**
   ```bash
   python scripts/historical_data_downloader.py --from-date 01-01-2024 --to-date 31-01-2024
   ```
3. **Convert to higher timeframes:**
   ```bash
   python scripts/timeframe_converter.py
   ```
4. **Generate features:**
   ```bash
   python scripts/optimized_feature_engineering.py
   ```

## 📝 Example Commands

```bash
# Download January 2024 data
python scripts/historical_data_downloader.py --from-date 01-01-2024 --to-date 31-01-2024

# Download last month
python scripts/historical_data_downloader.py --days 30

# Convert timeframes
python scripts/timeframe_converter.py

# Generate features
python scripts/optimized_feature_engineering.py

# Interactive example
python scripts/download_example.py
```

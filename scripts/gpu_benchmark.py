#!/usr/bin/env python3
"""
🚀 GPU Performance Benchmark Script
Comprehensive GPU performance testing for trading system optimization
"""

import asyncio
import time
import logging
import numpy as np
from pathlib import Path
import sys

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.append(str(PROJECT_ROOT))

from utils.enhanced_cuda_optimizer import get_enhanced_cuda_optimizer
from utils.gpu_performance_monitor import get_gpu_performance_monitor
from utils.gpu_data_loader import get_gpu_data_loader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GPUBenchmark:
    """Comprehensive GPU benchmark suite"""
    
    def __init__(self):
        self.cuda_optimizer = get_enhanced_cuda_optimizer()
        self.monitor = get_gpu_performance_monitor()
        self.data_loader = get_gpu_data_loader()
        
    async def run_comprehensive_benchmark(self):
        """Run comprehensive GPU benchmark"""
        print("\n" + "="*80)
        print("🚀 GPU PERFORMANCE BENCHMARK FOR TRADING SYSTEM")
        print("="*80)
        
        if not self.cuda_optimizer.cuda_available:
            print("❌ CUDA not available - cannot run GPU benchmarks")
            return
        
        # Start monitoring
        self.monitor.start_monitoring(interval=0.5)
        
        try:
            # System info
            await self._print_system_info()
            
            # Memory benchmarks
            await self._benchmark_memory_operations()
            
            # Computation benchmarks
            await self._benchmark_computations()
            
            # Data processing benchmarks
            await self._benchmark_data_processing()
            
            # Strategy processing benchmarks
            await self._benchmark_strategy_processing()
            
            # Final recommendations
            await self._print_recommendations()
            
        finally:
            self.monitor.stop_monitoring()
    
    async def _print_system_info(self):
        """Print system information"""
        print("\n📊 SYSTEM INFORMATION")
        print("-" * 40)
        
        memory_info = self.cuda_optimizer.get_memory_info()
        
        if memory_info.get('cuda_available'):
            print(f"GPU Device: {memory_info.get('device_name', 'Unknown')}")
            print(f"Total Memory: {memory_info.get('memory_total', 0):.1f} GB")
            print(f"Compute Capability: {memory_info.get('compute_capability', 'Unknown')}")
            print(f"Multiprocessors: {memory_info.get('multiprocessor_count', 'Unknown')}")
            print(f"CuPy Available: {self.cuda_optimizer.cupy_available}")
        
        # Apply optimizations
        optimizations = self.cuda_optimizer.optimize_for_backtesting()
        print(f"Optimizations Applied: {len(optimizations.get('optimizations_applied', []))}")
    
    async def _benchmark_memory_operations(self):
        """Benchmark memory operations"""
        print("\n💾 MEMORY OPERATIONS BENCHMARK")
        print("-" * 40)
        
        sizes = [1000, 10000, 100000, 1000000]
        
        for size in sizes:
            # CPU to GPU transfer
            cpu_data = np.random.randn(size, 5).astype(np.float32)
            
            start_time = time.time()
            gpu_data = self.cuda_optimizer.get_gpu_array(cpu_data)
            transfer_time = time.time() - start_time
            
            throughput = (cpu_data.nbytes / 1024**2) / transfer_time  # MB/s
            
            print(f"Size {size:>7}: Transfer {transfer_time:.4f}s, Throughput {throughput:.1f} MB/s")
            
            # Record performance
            self.monitor.record_operation(
                f"memory_transfer_{size}", 
                transfer_time, 
                cpu_data.nbytes
            )
            
            # Cleanup
            del gpu_data, cpu_data
            self.cuda_optimizer.cleanup_memory()
    
    async def _benchmark_computations(self):
        """Benchmark computational operations"""
        print("\n⚡ COMPUTATION BENCHMARK")
        print("-" * 40)
        
        sizes = [10000, 50000, 100000, 500000]
        
        for size in sizes:
            # Create test data
            cpu_data = np.random.randn(size).astype(np.float32)
            
            if self.cuda_optimizer.cupy_available:
                # CuPy benchmark
                start_time = time.time()
                gpu_data = self.cuda_optimizer.get_gpu_array(cpu_data)
                
                # Perform computations
                import cupy as cp
                result = cp.sqrt(cp.sum(gpu_data ** 2))
                cp.cuda.Stream.null.synchronize()
                
                cupy_time = time.time() - start_time
                
                # CPU benchmark for comparison
                start_time = time.time()
                cpu_result = np.sqrt(np.sum(cpu_data ** 2))
                cpu_time = time.time() - start_time
                
                speedup = cpu_time / cupy_time if cupy_time > 0 else 0
                
                print(f"Size {size:>6}: GPU {cupy_time:.4f}s, CPU {cpu_time:.4f}s, Speedup {speedup:.1f}x")
                
                self.monitor.record_operation(f"computation_{size}", cupy_time, size * 4)
                
                del gpu_data, result
            else:
                print(f"Size {size:>6}: CuPy not available, skipping GPU computation")
            
            self.cuda_optimizer.cleanup_memory()
    
    async def _benchmark_data_processing(self):
        """Benchmark data processing operations"""
        print("\n📈 DATA PROCESSING BENCHMARK")
        print("-" * 40)
        
        # Create synthetic OHLCV data
        sizes = [1000, 5000, 10000, 50000]
        
        for size in sizes:
            # Generate synthetic market data
            dates = np.arange(size)
            opens = 100 + np.cumsum(np.random.randn(size) * 0.01)
            highs = opens + np.abs(np.random.randn(size) * 0.5)
            lows = opens - np.abs(np.random.randn(size) * 0.5)
            closes = opens + np.random.randn(size) * 0.3
            volumes = np.random.randint(1000, 10000, size)
            
            ohlcv_data = np.column_stack([opens, highs, lows, closes, volumes]).astype(np.float32)
            
            start_time = time.time()
            
            if self.cuda_optimizer.cupy_available:
                # GPU processing
                gpu_data = self.cuda_optimizer.get_gpu_array(ohlcv_data)
                
                import cupy as cp
                
                # Calculate technical indicators
                close_prices = gpu_data[:, 3]
                
                # SMA calculation
                sma_20 = cp.convolve(close_prices, cp.ones(20)/20, mode='valid')
                
                # RSI calculation (simplified)
                price_changes = cp.diff(close_prices)
                gains = cp.where(price_changes > 0, price_changes, 0)
                losses = cp.where(price_changes < 0, -price_changes, 0)
                
                cp.cuda.Stream.null.synchronize()
                
                processing_time = time.time() - start_time
                
                print(f"Size {size:>5}: Processing {processing_time:.4f}s, Rate {size/processing_time:.0f} bars/s")
                
                self.monitor.record_operation(f"data_processing_{size}", processing_time, size)
                
                del gpu_data, sma_20, gains, losses
            else:
                print(f"Size {size:>5}: CuPy not available, skipping GPU processing")
            
            self.cuda_optimizer.cleanup_memory()
    
    async def _benchmark_strategy_processing(self):
        """Benchmark strategy processing"""
        print("\n🎯 STRATEGY PROCESSING BENCHMARK")
        print("-" * 40)
        
        # Test different numbers of strategies
        strategy_counts = [1, 5, 10, 20]
        data_size = 10000
        
        # Create synthetic data
        ohlcv_data = np.random.randn(data_size, 5).astype(np.float32)
        ohlcv_data[:, 3] = 100 + np.cumsum(ohlcv_data[:, 3] * 0.01)  # Close prices
        
        for n_strategies in strategy_counts:
            start_time = time.time()
            
            if self.cuda_optimizer.cupy_available:
                # Simulate multi-strategy processing
                gpu_data = self.cuda_optimizer.get_gpu_array(ohlcv_data)
                
                import cupy as cp
                
                # Process multiple strategies
                results = cp.zeros((n_strategies, data_size), dtype=cp.int8)
                
                for i in range(n_strategies):
                    # Simple strategy simulation
                    close_prices = gpu_data[:, 3]
                    sma_period = 20 + i * 5
                    
                    if data_size > sma_period:
                        kernel = cp.ones(sma_period) / sma_period
                        sma = cp.convolve(close_prices, kernel, mode='valid')
                        
                        # Generate signals
                        threshold = 1.01 + i * 0.005
                        signals = cp.where(
                            close_prices[sma_period-1:] > sma * threshold, 1,
                            cp.where(close_prices[sma_period-1:] < sma * (2.0 - threshold), -1, 0)
                        )
                        
                        results[i, sma_period-1:] = signals
                
                cp.cuda.Stream.null.synchronize()
                
                processing_time = time.time() - start_time
                
                strategies_per_sec = n_strategies / processing_time
                
                print(f"Strategies {n_strategies:>2}: {processing_time:.4f}s, Rate {strategies_per_sec:.1f} strategies/s")
                
                self.monitor.record_operation(f"strategies_{n_strategies}", processing_time, n_strategies)
                
                del gpu_data, results
            else:
                print(f"Strategies {n_strategies:>2}: CuPy not available, skipping GPU processing")
            
            self.cuda_optimizer.cleanup_memory()
    
    async def _print_recommendations(self):
        """Print optimization recommendations"""
        print("\n🎯 OPTIMIZATION RECOMMENDATIONS")
        print("-" * 40)
        
        stats = self.monitor.get_performance_stats()
        recommendations = self.monitor.get_recommendations()
        
        print(f"Optimization Score: {stats.optimization_score:.1f}/100")
        print(f"Average Kernel Time: {stats.average_kernel_time:.4f}s")
        print(f"Operations per Second: {stats.operations_per_second:.2f}")
        
        if recommendations:
            print("\nRecommendations:")
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec['message']}")
                print(f"   Suggestion: {rec['suggestion']}")
        else:
            print("\n✅ No specific recommendations - system is well optimized!")
        
        # Configuration recommendations
        print("\n⚙️  CONFIGURATION RECOMMENDATIONS:")
        
        memory_info = self.cuda_optimizer.get_memory_info()
        if memory_info.get('cuda_available'):
            memory_gb = memory_info.get('memory_total', 0)
            
            if memory_gb >= 8:
                print("• Use batch_size: 16384 or higher")
                print("• Enable mixed precision (float16)")
                print("• Set strategies_per_batch: 16-20")
            elif memory_gb >= 6:
                print("• Use batch_size: 8192-12288")
                print("• Enable mixed precision (float16)")
                print("• Set strategies_per_batch: 12-16")
            else:
                print("• Use batch_size: 4096-8192")
                print("• Keep float32 precision")
                print("• Set strategies_per_batch: 8-12")
        
        print("\n" + "="*80)

async def main():
    """Main benchmark function"""
    benchmark = GPUBenchmark()
    await benchmark.run_comprehensive_benchmark()

if __name__ == "__main__":
    asyncio.run(main())
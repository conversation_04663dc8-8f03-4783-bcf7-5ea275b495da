#!/usr/bin/env python3
"""
[INIT] NSE 500 Universe Initialization Script
Initialize and test the comprehensive NSE 500 stock universe

Features:
- Load and enhance NSE 500 stock universe
- Generate comprehensive sector and market cap mappings
- Validate data quality and completeness
- Export universe statistics and reports
"""

import os
import sys
import logging
import asyncio
import json
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from utils.nse_500_universe import NSE500Universe, EnhancedStockInfo

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NSE500Initializer:
    """NSE 500 Universe Initializer and Validator"""
    
    def __init__(self):
        self.universe = NSE500Universe()
        self.validation_results = {}
        
    async def initialize_universe(self) -> bool:
        """Initialize the NSE 500 universe"""
        try:
            logger.info("[INIT] Starting NSE 500 Universe Initialization...")
            
            # Step 1: Load the universe
            success = self.universe.load_nse_500_universe()
            if not success:
                logger.error("[ERROR] Failed to load NSE 500 universe")
                return False
            
            # Step 2: Validate the universe
            validation_success = await self.validate_universe()
            if not validation_success:
                logger.warning("[WARN] Universe validation found issues")
            
            # Step 3: Generate reports
            await self.generate_reports()
            
            # Step 4: Export for other systems
            await self.export_universe()
            
            logger.info("[SUCCESS] NSE 500 Universe initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Universe initialization failed: {e}")
            return False
    
    async def validate_universe(self) -> bool:
        """Validate the loaded universe"""
        try:
            logger.info("[DEBUG] Validating NSE 500 Universe...")
            
            validation_results = {
                "total_stocks": len(self.universe.stocks),
                "validation_timestamp": datetime.now().isoformat(),
                "issues": [],
                "warnings": [],
                "stats": {}
            }
            
            # Check minimum stock count
            if len(self.universe.stocks) < 400:
                validation_results["issues"].append(
                    f"Insufficient stocks: {len(self.universe.stocks)} (minimum 400 expected)"
                )
            
            # Check sector distribution
            sector_stats = {}
            for sector, symbols in self.universe.sectors.items():
                sector_stats[sector] = len(symbols)
            
            validation_results["stats"]["sectors"] = sector_stats
            
            # Check for missing sector assignments
            missing_sectors = [
                stock.symbol for stock in self.universe.stocks.values() 
                if not stock.sector or stock.sector == "Others"
            ]
            
            if missing_sectors:
                validation_results["warnings"].append(
                    f"Stocks with missing/default sectors: {len(missing_sectors)}"
                )
            
            # Check market cap distribution
            market_cap_stats = {}
            for cap, symbols in self.universe.market_caps.items():
                market_cap_stats[cap] = len(symbols)
            
            validation_results["stats"]["market_caps"] = market_cap_stats
            
            # Check for missing tokens
            missing_tokens = [
                stock.symbol for stock in self.universe.stocks.values() 
                if not stock.token
            ]
            
            if missing_tokens:
                validation_results["issues"].append(
                    f"Stocks with missing tokens: {missing_tokens[:10]}..."
                )
            
            # Check Nifty 50 count
            nifty_50_count = len([s for s in self.universe.stocks.values() if s.nifty_50])
            if nifty_50_count < 45:  # Allow some flexibility
                validation_results["warnings"].append(
                    f"Nifty 50 count seems low: {nifty_50_count}"
                )
            
            validation_results["stats"]["nifty_50_count"] = nifty_50_count
            
            # Save validation results
            self.validation_results = validation_results
            
            # Log summary
            logger.info(f"[STATUS] Validation Summary:")
            logger.info(f"   Total Stocks: {validation_results['total_stocks']}")
            logger.info(f"   Sectors: {len(sector_stats)}")
            logger.info(f"   Issues: {len(validation_results['issues'])}")
            logger.info(f"   Warnings: {len(validation_results['warnings'])}")
            
            return len(validation_results["issues"]) == 0
            
        except Exception as e:
            logger.error(f"[ERROR] Universe validation failed: {e}")
            return False
    
    async def generate_reports(self):
        """Generate comprehensive reports"""
        try:
            logger.info("[METRICS] Generating Universe Reports...")
            
            # Create reports directory
            reports_dir = Path("reports/universe")
            reports_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate sector report
            await self._generate_sector_report(reports_dir)
            
            # Generate market cap report
            await self._generate_market_cap_report(reports_dir)
            
            # Generate index membership report
            await self._generate_index_report(reports_dir)
            
            # Generate validation report
            await self._generate_validation_report(reports_dir)
            
            logger.info("[SUCCESS] Reports generated successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Report generation failed: {e}")
    
    async def _generate_sector_report(self, reports_dir: Path):
        """Generate sector-wise breakdown report"""
        try:
            sector_report = {
                "title": "NSE 500 Sector-wise Breakdown",
                "generated_at": datetime.now().isoformat(),
                "total_sectors": len(self.universe.sectors),
                "sectors": {}
            }
            
            for sector, symbols in self.universe.sectors.items():
                stocks = [self.universe.stocks[symbol] for symbol in symbols if symbol in self.universe.stocks]
                
                sector_report["sectors"][sector] = {
                    "count": len(stocks),
                    "percentage": round(len(stocks) / len(self.universe.stocks) * 100, 2),
                    "market_cap_breakdown": {
                        "Large": len([s for s in stocks if s.market_cap == "Large"]),
                        "Mid": len([s for s in stocks if s.market_cap == "Mid"]),
                        "Small": len([s for s in stocks if s.market_cap == "Small"])
                    },
                    "top_stocks": [s.symbol for s in stocks[:10]]  # Top 10 by order
                }
            
            # Save sector report
            with open(reports_dir / "sector_breakdown.json", 'w') as f:
                json.dump(sector_report, f, indent=2)
            
            logger.info(f"[STATUS] Sector report saved: {len(self.universe.sectors)} sectors")
            
        except Exception as e:
            logger.error(f"[ERROR] Sector report generation failed: {e}")
    
    async def _generate_market_cap_report(self, reports_dir: Path):
        """Generate market cap breakdown report"""
        try:
            market_cap_report = {
                "title": "NSE 500 Market Cap Breakdown",
                "generated_at": datetime.now().isoformat(),
                "market_caps": {}
            }
            
            for cap, symbols in self.universe.market_caps.items():
                stocks = [self.universe.stocks[symbol] for symbol in symbols if symbol in self.universe.stocks]
                
                market_cap_report["market_caps"][cap] = {
                    "count": len(stocks),
                    "percentage": round(len(stocks) / len(self.universe.stocks) * 100, 2),
                    "sector_breakdown": {},
                    "top_stocks": [s.symbol for s in stocks[:20]]  # Top 20
                }
                
                # Sector breakdown within market cap
                for stock in stocks:
                    sector = stock.sector or "Others"
                    if sector not in market_cap_report["market_caps"][cap]["sector_breakdown"]:
                        market_cap_report["market_caps"][cap]["sector_breakdown"][sector] = 0
                    market_cap_report["market_caps"][cap]["sector_breakdown"][sector] += 1
            
            # Save market cap report
            with open(reports_dir / "market_cap_breakdown.json", 'w') as f:
                json.dump(market_cap_report, f, indent=2)
            
            logger.info(f"[MONEY] Market cap report saved")
            
        except Exception as e:
            logger.error(f"[ERROR] Market cap report generation failed: {e}")
    
    async def _generate_index_report(self, reports_dir: Path):
        """Generate index membership report"""
        try:
            index_report = {
                "title": "NSE 500 Index Membership Report",
                "generated_at": datetime.now().isoformat(),
                "indices": {}
            }
            
            # Nifty 50
            nifty_50_stocks = [s for s in self.universe.stocks.values() if s.nifty_50]
            index_report["indices"]["Nifty_50"] = {
                "count": len(nifty_50_stocks),
                "stocks": [s.symbol for s in nifty_50_stocks],
                "sector_breakdown": {}
            }
            
            # Sector breakdown for Nifty 50
            for stock in nifty_50_stocks:
                sector = stock.sector or "Others"
                if sector not in index_report["indices"]["Nifty_50"]["sector_breakdown"]:
                    index_report["indices"]["Nifty_50"]["sector_breakdown"][sector] = 0
                index_report["indices"]["Nifty_50"]["sector_breakdown"][sector] += 1
            
            # Save index report
            with open(reports_dir / "index_membership.json", 'w') as f:
                json.dump(index_report, f, indent=2)
            
            logger.info(f"[METRICS] Index membership report saved")
            
        except Exception as e:
            logger.error(f"[ERROR] Index report generation failed: {e}")
    
    async def _generate_validation_report(self, reports_dir: Path):
        """Generate validation report"""
        try:
            # Save validation results
            with open(reports_dir / "validation_results.json", 'w') as f:
                json.dump(self.validation_results, f, indent=2)
            
            logger.info(f"[SUCCESS] Validation report saved")
            
        except Exception as e:
            logger.error(f"[ERROR] Validation report generation failed: {e}")
    
    async def export_universe(self):
        """Export universe for other systems"""
        try:
            logger.info("📤 Exporting Universe for Integration...")
            
            # Create exports directory
            exports_dir = Path("data/exports")
            exports_dir.mkdir(parents=True, exist_ok=True)
            
            # Export for trading systems (simplified format)
            trading_symbols = []
            for stock in self.universe.stocks.values():
                if stock.is_active:
                    trading_symbols.append({
                        "symbol": stock.symbol,
                        "token": stock.token,
                        "exchange": stock.exchange,
                        "sector": stock.sector,
                        "market_cap": stock.market_cap
                    })
            
            with open(exports_dir / "trading_symbols.json", 'w') as f:
                json.dump(trading_symbols, f, indent=2)
            
            # Export sector mapping
            with open(exports_dir / "sector_mapping.json", 'w') as f:
                json.dump(self.universe.sectors, f, indent=2)
            
            # Export Nifty 50 list
            nifty_50_symbols = [s.symbol for s in self.universe.stocks.values() if s.nifty_50]
            with open(exports_dir / "nifty_50_symbols.json", 'w') as f:
                json.dump(nifty_50_symbols, f, indent=2)
            
            logger.info(f"📤 Universe exported: {len(trading_symbols)} active symbols")
            
        except Exception as e:
            logger.error(f"[ERROR] Universe export failed: {e}")
    
    def print_summary(self):
        """Print universe summary"""
        try:
            stats = self.universe.get_universe_stats()
            
            print("\n" + "="*60)
            print("🏢 NSE 500 UNIVERSE SUMMARY")
            print("="*60)
            print(f"[STATUS] Total Stocks: {stats['total_stocks']}")
            print(f"[PROD] Sectors: {stats['sectors']}")
            print(f"🏢 Industries: {stats['industries']}")
            print(f"⭐ Nifty 50: {stats['nifty_50_count']}")
            print(f"🔵 Large Cap: {stats['large_cap_count']}")
            print(f"🟡 Mid Cap: {stats['mid_cap_count']}")
            print(f"🔴 Small Cap: {stats['small_cap_count']}")
            
            print("\n[METRICS] Top Sectors:")
            for sector, count in sorted(stats['sector_breakdown'].items(), 
                                      key=lambda x: x[1], reverse=True)[:10]:
                print(f"   {sector}: {count} stocks")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to print summary: {e}")

async def main():
    """Main initialization function"""
    try:
        initializer = NSE500Initializer()
        
        # Initialize universe
        success = await initializer.initialize_universe()
        
        if success:
            # Print summary
            initializer.print_summary()
            
            print("\n[SUCCESS] NSE 500 Universe initialization completed successfully!")
            print("[FOLDER] Check 'reports/universe/' for detailed reports")
            print("[FOLDER] Check 'data/exports/' for integration files")
        else:
            print("\n[ERROR] NSE 500 Universe initialization failed!")
            return 1
        
        return 0
        
    except Exception as e:
        logger.error(f"[ERROR] Main execution failed: {e}")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

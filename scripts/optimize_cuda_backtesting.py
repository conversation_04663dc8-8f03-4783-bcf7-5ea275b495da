#!/usr/bin/env python3
"""
🚀 CUDA Backtesting Optimization Script
Optimizes the backtesting system for maximum CUDA performance on RTX 3060Ti
"""

import os
import sys
import logging
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from utils.cuda_optimizer import get_cuda_optimizer, optimize_cuda_for_backtesting
from utils.gpu_memory_manager import get_gpu_memory_manager, cleanup_gpu_memory
from agents.run_enhanced_backtesting_kimi import run_optimized_backtesting

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def optimize_and_run_backtesting(
    max_symbols: int = None,
    max_strategies: int = None,
    quick_test: bool = False,
    force_cuda: bool = True
):
    """
    Optimize CUDA settings and run backtesting with maximum performance
    
    Args:
        max_symbols: Maximum number of symbols to process
        max_strategies: Maximum number of strategies to test
        quick_test: Run in quick test mode
        force_cuda: Force CUDA optimization even if not detected
    """
    
    logger.info("🚀 Starting CUDA-Optimized Backtesting")
    logger.info("=" * 60)
    
    # Initialize optimizers
    cuda_optimizer = get_cuda_optimizer()
    memory_manager = get_gpu_memory_manager()
    
    # Check CUDA availability
    if not cuda_optimizer.cuda_available and not force_cuda:
        logger.warning("⚠️ CUDA not available. Running in CPU mode.")
        return await run_standard_backtesting(max_symbols, max_strategies, quick_test)
    
    # Apply CUDA optimizations
    logger.info("🔧 Applying CUDA optimizations...")
    optimizations = optimize_cuda_for_backtesting()
    
    if optimizations.get('cuda_enabled'):
        logger.info("✅ CUDA optimizations applied:")
        for opt in optimizations.get('optimizations_applied', []):
            logger.info(f"   • {opt}")
    else:
        logger.warning("⚠️ CUDA optimizations failed, falling back to CPU")
        return await run_standard_backtesting(max_symbols, max_strategies, quick_test)
    
    # Get initial memory stats
    memory_stats = memory_manager.get_memory_stats()
    if memory_stats.get('cuda_available'):
        logger.info(f"📊 GPU Memory: {memory_stats['total_gb']:.1f} GB total, "
                   f"{memory_stats['free_gb']:.1f} GB free")
    
    # Set environment variables for optimal CUDA performance
    os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # Enable async execution
    os.environ['CUDA_CACHE_DISABLE'] = '0'    # Enable kernel caching
    os.environ['NUMBA_CUDA_FASTMATH'] = '1'   # Enable fast math
    os.environ['NUMBA_CUDA_DEBUGINFO'] = '0'  # Disable debug info for speed
    
    # Configure optimal batch sizes based on available memory
    if memory_stats.get('cuda_available'):
        free_gb = memory_stats.get('free_gb', 0)
        if free_gb > 5:
            batch_multiplier = 2.0
            logger.info("🚀 High memory mode: 2x batch sizes")
        elif free_gb > 3:
            batch_multiplier = 1.5
            logger.info("⚡ Medium memory mode: 1.5x batch sizes")
        else:
            batch_multiplier = 1.0
            logger.info("📊 Standard memory mode: 1x batch sizes")
        
        # Set optimal chunk sizes
        os.environ['POLARS_CHUNK_SIZE'] = str(int(100000 * batch_multiplier))
        os.environ['CUDA_BATCH_SIZE'] = str(int(8192 * batch_multiplier))
    
    try:
        # Run optimized backtesting with memory monitoring
        logger.info("🚀 Starting CUDA-accelerated backtesting...")
        
        # Monitor memory before starting
        memory_manager.monitor_memory_usage("backtesting_start")
        
        # Run the backtesting
        success = await run_optimized_backtesting(
            max_symbols=max_symbols,
            max_strategies=max_strategies,
            quick_test=quick_test
        )
        
        # Monitor memory after completion
        memory_manager.monitor_memory_usage("backtesting_end")
        
        # Final cleanup
        cleanup_result = cleanup_gpu_memory(force=True)
        if cleanup_result.get('cleaned'):
            logger.info(f"🧹 Final cleanup: {cleanup_result.get('freed_gb', 0):.2f} GB freed")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ CUDA backtesting failed: {e}")
        
        # Emergency memory cleanup
        cleanup_gpu_memory(force=True)
        
        # Fallback to CPU mode
        logger.info("🔄 Falling back to CPU mode...")
        return await run_standard_backtesting(max_symbols, max_strategies, quick_test)

async def run_standard_backtesting(max_symbols, max_strategies, quick_test):
    """Fallback to standard CPU backtesting"""
    logger.info("🖥️ Running standard CPU backtesting...")
    
    return await run_optimized_backtesting(
        max_symbols=max_symbols,
        max_strategies=max_strategies,
        quick_test=quick_test
    )

def benchmark_cuda_performance():
    """Benchmark CUDA performance for backtesting operations"""
    logger.info("🏁 Running CUDA performance benchmark...")
    
    cuda_optimizer = get_cuda_optimizer()
    memory_manager = get_gpu_memory_manager()
    
    if not cuda_optimizer.cuda_available:
        logger.warning("⚠️ CUDA not available for benchmarking")
        return
    
    try:
        import torch
        import time
        import numpy as np
        
        # Test different data sizes
        test_sizes = [1000, 10000, 50000, 100000]
        results = {}
        
        for size in test_sizes:
            logger.info(f"📊 Testing with {size} data points...")
            
            # Generate test data
            data = torch.randn(size, 4, dtype=torch.float32)
            
            # CPU benchmark
            start_time = time.time()
            cpu_result = torch.mean(data, dim=1)
            cpu_time = time.time() - start_time
            
            # GPU benchmark
            if torch.cuda.is_available():
                data_gpu = data.cuda()
                torch.cuda.synchronize()
                
                start_time = time.time()
                gpu_result = torch.mean(data_gpu, dim=1)
                torch.cuda.synchronize()
                gpu_time = time.time() - start_time
                
                speedup = cpu_time / gpu_time if gpu_time > 0 else 0
                
                results[size] = {
                    'cpu_time': cpu_time,
                    'gpu_time': gpu_time,
                    'speedup': speedup
                }
                
                logger.info(f"   CPU: {cpu_time:.4f}s, GPU: {gpu_time:.4f}s, "
                           f"Speedup: {speedup:.2f}x")
            
            # Cleanup
            cleanup_gpu_memory()
        
        # Summary
        logger.info("🏁 Benchmark Results Summary:")
        for size, result in results.items():
            logger.info(f"   {size:6d} points: {result['speedup']:.2f}x speedup")
        
        # Recommendations
        avg_speedup = sum(r['speedup'] for r in results.values()) / len(results)
        if avg_speedup > 2.0:
            logger.info("✅ Excellent CUDA performance! Use GPU acceleration.")
        elif avg_speedup > 1.2:
            logger.info("⚡ Good CUDA performance. GPU acceleration recommended.")
        else:
            logger.info("⚠️ Limited CUDA benefit. Consider CPU-only mode for small datasets.")
            
    except Exception as e:
        logger.error(f"❌ Benchmark failed: {e}")

async def main():
    """Main function for CUDA optimization script"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CUDA-Optimized Backtesting')
    parser.add_argument('--max-symbols', type=int, help='Maximum symbols to process')
    parser.add_argument('--max-strategies', type=int, help='Maximum strategies to test')
    parser.add_argument('--quick-test', action='store_true', help='Run quick test')
    parser.add_argument('--benchmark', action='store_true', help='Run CUDA benchmark')
    parser.add_argument('--force-cuda', action='store_true', help='Force CUDA optimization')
    
    args = parser.parse_args()
    
    if args.benchmark:
        benchmark_cuda_performance()
        return
    
    # Run optimized backtesting
    success = await optimize_and_run_backtesting(
        max_symbols=args.max_symbols,
        max_strategies=args.max_strategies,
        quick_test=args.quick_test,
        force_cuda=args.force_cuda
    )
    
    if success:
        logger.info("🎉 CUDA-optimized backtesting completed successfully!")
    else:
        logger.error("❌ Backtesting failed")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
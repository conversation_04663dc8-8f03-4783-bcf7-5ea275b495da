#!/usr/bin/env python3
"""
CLEAN TRADING SYSTEM RUNNER
Modern implementation with a pre-run analysis workflow for stock selection.

Features:
- Pre-run analysis of all F&O stocks to select the best universe.
- Data-driven selection based on calculated features and forward returns.
- Concurrent data fetching and processing for speed.
- Dynamic universe is then fed into live trading agents.
"""

import asyncio
import logging
import argparse
import signal
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import sys
import polars as pl
import numpy as np
from dotenv import load_dotenv
import time # Added for sleep
import pyarrow.parquet as pq # For parquet operations
import pyarrow as pa # For parquet operations
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import queue

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables
load_dotenv(Path(__file__).parent.parent / '.env')

from core.event_system import EventBus, EventTypes
from agents.clean_market_data_agent import CleanMarketDataAgent
from agents.clean_signal_agent import CleanSignalAgent
from agents.modern_execution_agent import ModernExecutionAgent
from agents.clean_stock_selection_workflow import CleanStockSelectionWorkflow
from agents.risk_management_agent import RiskManagementAgent

logger = logging.getLogger(__name__)

class SmartAPIDownloadManager:
    """
    Global SmartAPI download manager that handles rate limiting across all threads.
    Based on the working pattern from test/download_fno_data.py
    """
    _instance = None
    _lock = threading.Lock()
    _last_api_call = 0
    _min_interval = 1.0  # 1.0 second minimum interval (same as reference)

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def download_with_rate_limit(cls, market_data_agent, symbol: str, start_date: datetime, end_date: datetime):
        """
        Download historical data with proper global rate limiting.
        This follows the exact pattern from the working reference file.
        """
        # Apply rate limiting exactly like in the reference file
        with cls._lock:
            current_time = time.time()
            time_since_last_call = current_time - cls._last_api_call

            if time_since_last_call < cls._min_interval:
                sleep_time = cls._min_interval - time_since_last_call
                logger.debug(f"[RATE-LIMIT] Sleeping for {sleep_time:.2f}s to respect API limits")
                time.sleep(sleep_time)

            cls._last_api_call = time.time()

        # Make the API call outside the lock to allow concurrent processing
        try:
            result = cls._make_sync_api_call(market_data_agent, symbol, start_date, end_date)
            return result
        except Exception as e:
            logger.error(f"[SMARTAPI] Error downloading data for {symbol}: {e}")
            return None

    @classmethod
    def _make_sync_api_call(cls, market_data_agent, symbol: str, start_date: datetime, end_date: datetime):
        """
        Make synchronous API call with timeout to prevent hanging.
        """
        try:
            # Create a new event loop for this thread to avoid conflicts
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Add timeout to prevent hanging
                result = loop.run_until_complete(
                    asyncio.wait_for(
                        market_data_agent.download_historical_data_for_symbol(
                            symbol=symbol,
                            start_date=start_date,
                            end_date=end_date
                        ),
                        timeout=25.0  # 25 second timeout per symbol
                    )
                )
                return result
            except asyncio.TimeoutError:
                logger.error(f"[SMARTAPI] Timeout downloading data for {symbol}")
                return None
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"[SMARTAPI] Sync API call failed for {symbol}: {e}")
            return None

class CleanUniversePreparer:
    """
    Clean, logical universe preparation using the new Clean Stock Selection Workflow.
    
    Flow:
    1. Download historical data first (1-min for 25 days)
    2. Feature engineering on downloaded data
    3. ML prediction using trained models
    4. Stock selection (top 50 stocks)
    5. Generate higher timeframes (3min, 5min, 15min) from 1-min data
    6. Prepare for websocket subscription and trading agents
    """
    def __init__(self, event_bus: EventBus, market_data_agent: CleanMarketDataAgent, config: Any):
        self.event_bus = event_bus
        self.market_data_agent = market_data_agent
        self.config = config
        self.clean_workflow: Optional[CleanStockSelectionWorkflow] = None
        self.project_root = Path(__file__).parent.parent
        self.live_data_path = self.project_root / "data" / "live"
        self.feature_data_path = self.project_root / "data" / "features"

    def _download_single_stock_sync(self, symbol: str, start_date: datetime, end_date: datetime, stock_index: int, total_stocks: int) -> Tuple[str, bool]:
        """
        Download historical data for a single stock synchronously (for use with ThreadPoolExecutor).
        Returns tuple of (symbol, success_status).

        Uses SmartAPIDownloadManager to prevent token conflicts and ensure proper rate limiting.
        """
        logger.info(f"[ML-PREP] Downloading 1-min data for {symbol} ({stock_index}/{total_stocks}) from SmartAPI...")

        try:
            # Use the SmartAPI manager with proper rate limiting
            df_1min_polars = SmartAPIDownloadManager.download_with_rate_limit(
                self.market_data_agent, symbol, start_date, end_date
            )

            if df_1min_polars is not None and not df_1min_polars.is_empty():
                # Work directly with Polars DataFrame - no pandas conversion needed
                # Ensure 'timestamp' column exists
                if 'timestamp' not in df_1min_polars.columns:
                    logger.error(f"[ML-PREP] Downloaded data for {symbol} does not contain 'timestamp' column.")
                    return symbol, False

                # Sort by timestamp and ensure proper data types
                df_1min_polars = df_1min_polars.sort('timestamp')

                # Save 1-min data to data/live using Polars
                output_file_1min = self.live_data_path / f"{symbol}_1min.parquet"
                df_1min_polars.write_parquet(output_file_1min, compression='brotli')
                logger.info(f"[ML-PREP] Saved 1-min data for {symbol} to {output_file_1min} ({len(df_1min_polars)} rows)")
                
                # Generate higher timeframe data (3min, 5min, 15min)
                self._generate_higher_timeframes(symbol, df_1min_polars)
                
                return symbol, True
            else:
                logger.warning(f"[ML-PREP] No 1-min data downloaded for {symbol} from SmartAPI.")
                return symbol, False

        except ValueError as ve:
            # Handle specific token/symbol errors
            if "No instrument found" in str(ve) or "invalid token" in str(ve).lower():
                logger.warning(f"[ML-PREP] Token not found for {symbol}: {ve}")
                return symbol, False
            else:
                logger.error(f"[ML-PREP] ValueError downloading data for {symbol}: {ve}")
                return symbol, False
        except Exception as e:
            logger.error(f"[ML-PREP] Error downloading/saving data for {symbol} from SmartAPI: {e}")
            return symbol, False

    def _generate_higher_timeframes(self, symbol: str, df_1min: pl.DataFrame):
        """Generate 3min, 5min, and 15min data from 1min data"""
        try:
            timeframes = {
                '3min': 3,
                '5min': 5,
                '15min': 15
            }
            
            for tf_name, minutes in timeframes.items():
                try:
                    # Group by time intervals and aggregate OHLCV data
                    df_tf = df_1min.group_by_dynamic(
                        "timestamp",
                        every=f"{minutes}m",
                        closed="left"
                    ).agg([
                        pl.col("open").first().alias("open"),
                        pl.col("high").max().alias("high"),
                        pl.col("low").min().alias("low"),
                        pl.col("close").last().alias("close"),
                        pl.col("volume").sum().alias("volume")
                    ]).sort("timestamp")
                    
                    # Remove any rows with null values
                    df_tf = df_tf.drop_nulls()
                    
                    if not df_tf.is_empty():
                        output_file = self.live_data_path / f"{symbol}_{tf_name}.parquet"
                        df_tf.write_parquet(output_file, compression='brotli')
                        logger.debug(f"[ML-PREP] Generated {tf_name} data for {symbol} ({len(df_tf)} rows)")
                    else:
                        logger.warning(f"[ML-PREP] No {tf_name} data generated for {symbol}")
                        
                except Exception as e:
                    logger.error(f"[ML-PREP] Error generating {tf_name} data for {symbol}: {e}")
                    
        except Exception as e:
            logger.error(f"[ML-PREP] Error in higher timeframe generation for {symbol}: {e}")

    async def _download_historical_data(self, stocks: List[str]):
        """
        Downloads 25 days of 1-minute historical data for the given stocks from SmartAPI
        using ThreadPoolExecutor with optimized settings and retry mechanism.
        """
        self.live_data_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"[ML-PREP] Starting optimized historical data download from SmartAPI for {len(stocks)} stocks...")

        end_date = datetime.now()
        start_date = end_date - timedelta(days=25) # 25 days historical data

        # Aggressive settings to prevent hanging
        MAX_CONCURRENT_DOWNLOADS = 5  # Reduced to prevent overwhelming API
        RATE_LIMIT_DELAY = 1.0  # Increased delay to be more conservative
        MAX_RETRIES = 1  # Reduced retries to fail fast
        RETRY_DELAY_BASE = 2.0  # Increased retry delay
        
        logger.info("🚀 STARTING OPTIMIZED DOWNLOAD")
        logger.info("="*60)
        logger.info(f"📊 Symbols: {len(stocks)}")
        logger.info(f"📅 Period: 25 days ({start_date} to {end_date})")
        logger.info(f"🧵 Max concurrent: {MAX_CONCURRENT_DOWNLOADS}")
        logger.info(f"⏳ Rate limit delay: {RATE_LIMIT_DELAY}s")
        logger.info(f"🔄 Max retries: {MAX_RETRIES}")
        logger.info("="*60)

        # Run the download process with timeout to prevent hanging
        def run_threaded_downloads():
            results = {}
            completed_count = 0
            
            # Use ThreadPoolExecutor for concurrent downloads with timeout
            with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_DOWNLOADS) as executor:
                future_to_symbol = {
                    executor.submit(
                        self._download_single_stock_sync,
                        symbol,
                        start_date,
                        end_date,
                        i + 1,
                        len(stocks)
                    ): symbol
                    for i, symbol in enumerate(stocks)
                }

                # Process with timeout per future
                for future in as_completed(future_to_symbol, timeout=120):  # 2 minute total timeout
                    symbol = future_to_symbol[future]
                    try:
                        symbol_result, success = future.result(timeout=30)  # 30 second per stock timeout
                        results[symbol] = success
                        completed_count += 1
                        
                        logger.info(f"[ML-PREP] Completed {completed_count}/{len(stocks)}: {symbol}")
                        
                        # Add rate limiting delay
                        time.sleep(RATE_LIMIT_DELAY)
                        
                        # Circuit breaker: if too many failures, stop early
                        if completed_count >= 10:  # Stop after 10 successful downloads
                            logger.info(f"[ML-PREP] Circuit breaker: stopping after {completed_count} downloads")
                            break
                        
                    except Exception as e:
                        logger.error(f"[ML-PREP] Error downloading {symbol}: {e}")
                        results[symbol] = False
                        completed_count += 1
            
            return results

        # Execute downloads in thread pool with overall timeout
        start_time = time.time()
        try:
            results = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(None, run_threaded_downloads),
                timeout=180.0  # 3 minute total timeout
            )
        except asyncio.TimeoutError:
            logger.error("[ML-PREP] Download process timed out after 3 minutes")
            results = {symbol: False for symbol in stocks}
        
        # Count initial results and update failed_stocks list
        successful_downloads = sum(1 for success in results.values() if success)
        failed_downloads = len(results) - successful_downloads
        failed_stocks = [symbol for symbol, success in results.items() if not success]
        
        logger.info(f"[ML-PREP] Initial download completed - Success: {successful_downloads}, Failed: {failed_downloads}")
        
        # Retry failed downloads with exponential backoff
        if failed_stocks and MAX_RETRIES > 0:
            logger.info(f"[ML-PREP] Retrying {len(failed_stocks)} failed downloads...")
            
            for retry_attempt in range(1, MAX_RETRIES + 1):
                if not failed_stocks:
                    break
                    
                retry_delay = RETRY_DELAY_BASE * (2 ** (retry_attempt - 1))  # Exponential backoff
                logger.info(f"[ML-PREP] Retry attempt {retry_attempt}/{MAX_RETRIES} for {len(failed_stocks)} stocks (delay: {retry_delay}s)")
                
                await asyncio.sleep(retry_delay)
                
                # Retry failed stocks with reduced concurrency
                def retry_downloads():
                    retry_results = {}
                    # Use fewer workers for retries to be more conservative
                    retry_workers = min(5, len(failed_stocks))
                    with ThreadPoolExecutor(max_workers=retry_workers) as executor:
                        future_to_symbol = {
                            executor.submit(
                                self._download_single_stock_sync,
                                symbol,
                                start_date,
                                end_date,
                                stocks.index(symbol) + 1,
                                len(stocks)
                            ): symbol
                            for symbol in failed_stocks
                        }

                        for future in as_completed(future_to_symbol):
                            symbol = future_to_symbol[future]
                            try:
                                symbol_result, success = future.result()
                                retry_results[symbol] = success
                                time.sleep(RATE_LIMIT_DELAY)
                            except Exception as e:
                                logger.error(f"[ML-PREP] Retry error for {symbol}: {e}")
                                retry_results[symbol] = False
                    
                    return retry_results
                
                retry_results = await asyncio.get_event_loop().run_in_executor(None, retry_downloads)
                
                # Update results and failed list
                retry_successful = 0
                new_failed_stocks = []
                for symbol, success in retry_results.items():
                    if success:
                        retry_successful += 1
                        successful_downloads += 1
                        failed_downloads -= 1
                    else:
                        new_failed_stocks.append(symbol)
                
                failed_stocks = new_failed_stocks
                logger.info(f"[ML-PREP] Retry {retry_attempt} completed - Additional successes: {retry_successful}, Still failed: {len(failed_stocks)}")

        # Calculate performance metrics
        end_time = time.time()
        total_time = end_time - start_time
        symbols_per_minute = (len(stocks) / total_time) * 60 if total_time > 0 else 0
        success_rate = (successful_downloads / len(stocks)) * 100 if len(stocks) > 0 else 0

        # Final summary
        logger.info("="*80)
        logger.info("🎯 OPTIMIZED DOWNLOAD SUMMARY")
        logger.info("="*80)
        logger.info(f"📊 Total symbols: {len(stocks)}")
        logger.info(f"✅ Successful downloads: {successful_downloads}")
        logger.info(f"❌ Failed downloads: {failed_downloads}")
        logger.info(f"📈 Success rate: {success_rate:.1f}%")
        logger.info(f"⏱️  Total time: {total_time:.1f} seconds")
        logger.info(f"🚀 Speed: {symbols_per_minute:.1f} symbols/minute")
        logger.info(f"⚡ Avg time per symbol: {total_time/len(stocks):.2f} seconds")
        if failed_stocks:
            logger.warning(f"🔴 Final failed stocks: {failed_stocks}")
        logger.info("="*80)

    async def _perform_feature_engineering(self, stocks: List[str]):
        """
        Performs feature engineering on the downloaded and aggregated data in data/live using Polars.
        """
        logger.info(f"[ML-PREP] Starting feature engineering for {len(stocks)} stocks across all timeframes...")
        
        for i, symbol in enumerate(stocks):
            for tf_name in self.config.timeframes: # Iterate over all timeframes
                input_file = self.live_data_path / f"{symbol}_{tf_name}.parquet"
                if input_file.exists():
                    try:
                        # Read data using Polars
                        df = pl.read_parquet(input_file)
                        
                        # Ensure timestamp is properly sorted
                        df = df.sort('timestamp')

                        # Feature engineering using Polars expressions
                        df = df.with_columns([
                            # Moving Averages
                            pl.col('close').rolling_mean(window_size=5).alias('MA_5'),
                            pl.col('close').rolling_mean(window_size=10).alias('MA_10'),
                            
                            # Price changes for RSI calculation
                            pl.col('close').diff().alias('price_change')
                        ])
                        
                        # RSI calculation using Polars
                        df = df.with_columns([
                            # Separate gains and losses
                            pl.when(pl.col('price_change') > 0)
                            .then(pl.col('price_change'))
                            .otherwise(0.0)
                            .alias('gain'),
                            
                            pl.when(pl.col('price_change') < 0)
                            .then(-pl.col('price_change'))
                            .otherwise(0.0)
                            .alias('loss')
                        ])
                        
                        # Calculate RSI using rolling averages
                        df = df.with_columns([
                            pl.col('gain').rolling_mean(window_size=14).alias('avg_gain'),
                            pl.col('loss').rolling_mean(window_size=14).alias('avg_loss')
                        ])
                        
                        # Final RSI calculation
                        df = df.with_columns([
                            pl.when(pl.col('avg_loss') != 0)
                            .then(100 - (100 / (1 + (pl.col('avg_gain') / pl.col('avg_loss')))))
                            .otherwise(100.0)
                            .alias('RSI')
                        ])
                        
                        # Drop intermediate columns
                        df = df.drop(['price_change', 'gain', 'loss', 'avg_gain', 'avg_loss'])

                        # Save processed data back to data/live using Polars
                        df.write_parquet(input_file, compression='brotli')
                        logger.info(f"[ML-PREP] Performed feature engineering for {symbol} ({i+1}/{len(stocks)}) on {tf_name} data ({len(df)} rows).")
                    except Exception as e:
                        logger.error(f"[ML-PREP] Error during feature engineering for {symbol} on {tf_name} data: {e}")
                else:
                    logger.warning(f"[ML-PREP] {tf_name} data file not found for {symbol} at {input_file}. Skipping feature engineering.")
                await asyncio.sleep(0.01) # Small sleep to avoid overwhelming within inner loop
            await asyncio.sleep(0.05) # Small sleep to avoid overwhelming between stocks

        logger.info("[ML-PREP] Feature engineering complete for all timeframes.")

    def _process_single_feature_engineering_task(self, symbol: str, tf_name: str, total_stocks: int, current_index: int):
        """
        Performs feature engineering for a single stock and timeframe.
        This is a synchronous method designed to be run in a ThreadPoolExecutor.
        """
        input_file = self.live_data_path / f"{symbol}_{tf_name}.parquet"
        if input_file.exists():
            try:
                # Read data using Polars
                df = pl.read_parquet(input_file)
                
                # Ensure timestamp is properly sorted
                df = df.sort('timestamp')

                # Feature engineering using Polars expressions
                df = df.with_columns([
                    # Moving Averages
                    pl.col('close').rolling_mean(window_size=5).alias('MA_5'),
                    pl.col('close').rolling_mean(window_size=10).alias('MA_10'),
                    
                    # Price changes for RSI calculation
                    pl.col('close').diff().alias('price_change')
                ])
                
                # RSI calculation using Polars
                df = df.with_columns([
                    # Separate gains and losses
                    pl.when(pl.col('price_change') > 0)
                    .then(pl.col('price_change'))
                    .otherwise(0.0)
                    .alias('gain'),
                    
                    pl.when(pl.col('price_change') < 0)
                    .then(-pl.col('price_change'))
                    .otherwise(0.0)
                    .alias('loss')
                ])
                
                # Calculate RSI using rolling averages
                df = df.with_columns([
                    pl.col('gain').rolling_mean(window_size=14).alias('avg_gain'),
                    pl.col('loss').rolling_mean(window_size=14).alias('avg_loss')
                ])
                
                # Final RSI calculation
                df = df.with_columns([
                    pl.when(pl.col('avg_loss') != 0)
                    .then(100 - (100 / (1 + (pl.col('avg_gain') / pl.col('avg_loss')))))
                    .otherwise(100.0)
                    .alias('RSI')
                ])
                
                # Drop intermediate columns
                df = df.drop(['price_change', 'gain', 'loss', 'avg_gain', 'avg_loss'])

                # Save processed data back to data/live using Polars
                df.write_parquet(input_file, compression='brotli')
                logger.info(f"[ML-PREP] Performed feature engineering for {symbol} ({current_index}/{total_stocks}) on {tf_name} data ({len(df)} rows).")
                return True
            except Exception as e:
                logger.error(f"[ML-PREP] Error during feature engineering for {symbol} on {tf_name} data: {e}")
                return False
        else:
            logger.warning(f"[ML-PREP] {tf_name} data file not found for {symbol} at {input_file}. Skipping feature engineering.")
            return False

    async def run(self) -> List[str]:
        """Execute the clean universe preparation workflow."""
        logger.info("[CLEAN-PREP] Starting clean universe preparation workflow...")

        try:
            # Initialize the clean workflow
            self.clean_workflow = CleanStockSelectionWorkflow(
                self.event_bus, 
                self.market_data_agent.session_id
            )

            # Initialize all agents
            success = await self.clean_workflow.initialize()
            if not success:
                logger.error("[CLEAN-PREP] Clean workflow initialization failed. Cannot proceed.")
                return []

            # Load stocks to process from F&O list or feature data
            stocks_to_process = self._load_stocks_to_process()
            
            if not stocks_to_process:
                logger.error("[CLEAN-PREP] No stocks found to process. Cannot proceed.")
                return []

            logger.info(f"[CLEAN-PREP] Processing {len(stocks_to_process)} stocks through clean workflow...")

            # Execute the clean workflow
            workflow_result = await self.clean_workflow.execute_workflow(stocks_to_process)

            if workflow_result and workflow_result.selected_stocks:
                selected_stocks = workflow_result.selected_stocks
                logger.info(f"[CLEAN-PREP] ✅ Clean workflow selected {len(selected_stocks)} stocks")

                # Log summary information
                self._log_clean_results(workflow_result)

                # Store timeframe data paths for later use
                self.config.timeframe_data_paths = workflow_result.timeframe_data_paths

                # Cleanup workflow resources
                await self.clean_workflow.cleanup()

                return selected_stocks
            else:
                logger.error("[CLEAN-PREP] Clean workflow returned no stocks. Cannot proceed.")
                return []

        except Exception as e:
            logger.error(f"[CLEAN-PREP] Clean workflow failed: {e}")
            return []

    def _load_stocks_to_process(self) -> List[str]:
        """Load stocks to process from F&O list or feature data."""
        stocks_to_process = set()
        
        # Try to load from F&O list first
        fno_list_path = self.project_root / "data" / "lists" / "fno_list.csv"
        if fno_list_path.exists():
            try:
                import pandas as pd
                fno_df = pd.read_csv(fno_list_path)
                if 'symbol' in fno_df.columns:
                    stocks_to_process.update(fno_df['symbol'].tolist())
                    logger.info(f"[CLEAN-PREP] Loaded {len(stocks_to_process)} stocks from F&O list")
                elif 'Symbol' in fno_df.columns:
                    stocks_to_process.update(fno_df['Symbol'].tolist())
                    logger.info(f"[CLEAN-PREP] Loaded {len(stocks_to_process)} stocks from F&O list")
            except Exception as e:
                logger.warning(f"[CLEAN-PREP] Error loading F&O list: {e}")
        
        # If no F&O list, try feature data
        if not stocks_to_process and self.feature_data_path.exists():
            for file_path in self.feature_data_path.glob("features_*.parquet"):
                parts = file_path.stem.split('_')
                if len(parts) >= 2:
                    stocks_to_process.add(parts[1])
            logger.info(f"[CLEAN-PREP] Loaded {len(stocks_to_process)} stocks from feature data")
        
        # Fallback to a default list
        if not stocks_to_process:
            stocks_to_process = {
                "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK", "HINDUNILVR", "ITC", "SBIN", 
                "BHARTIARTL", "KOTAKBANK", "LT", "ASIANPAINT", "AXISBANK", "MARUTI", "SUNPHARMA",
                "ULTRACEMCO", "TITAN", "WIPRO", "NESTLEIND", "POWERGRID", "NTPC", "TECHM", "HCLTECH",
                "BAJFINANCE", "ONGC", "TATASTEEL", "COALINDIA", "INDUSINDBK", "DRREDDY", "GRASIM",
                "CIPLA", "EICHERMOT", "JSWSTEEL", "BRITANNIA", "ADANIPORTS", "APOLLOHOSP", "DIVISLAB",
                "BAJAJFINSV", "HEROMOTOCO", "SHREECEM", "HINDALCO", "TATAMOTORS", "UPL", "BPCL",
                "VEDL", "TATACONSUM", "SBILIFE", "HDFCLIFE", "BAJAJ-AUTO", "GODREJCP"
            }
            logger.info(f"[CLEAN-PREP] Using fallback stock list with {len(stocks_to_process)} stocks")
        
        return list(stocks_to_process)  # Process all available stocks

    def _log_clean_results(self, workflow_result):
        """Log clean workflow results summary."""
        try:
            logger.info(f"[CLEAN-PREP] === CLEAN WORKFLOW RESULTS SUMMARY ===")
            logger.info(f"[CLEAN-PREP] Selected stocks: {len(workflow_result.selected_stocks)}")
            logger.info(f"[CLEAN-PREP] Execution time: {workflow_result.execution_summary.get('total_execution_time', 0):.1f}s")

            # Log strategy distribution
            if workflow_result.strategy_assignments:
                strategy_counts = {}
                assignments = workflow_result.strategy_assignments
                
                # Handle StrategyAssignmentResult object
                if hasattr(assignments, 'assignments'):
                    assignments_dict = assignments.assignments
                elif isinstance(assignments, dict):
                    assignments_dict = assignments
                else:
                    assignments_dict = {}
                
                for symbol, assignment in assignments_dict.items():
                    if hasattr(assignment, 'primary_strategy'):
                        strategy = assignment.primary_strategy
                    elif isinstance(assignment, dict):
                        strategy = assignment.get('primary_strategy', 'unknown')
                    else:
                        strategy = 'unknown'
                    strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

                logger.info(f"[CLEAN-PREP] Strategy distribution:")
                for strategy, count in strategy_counts.items():
                    logger.info(f"[CLEAN-PREP]   {strategy}: {count} stocks")

            # Log top selected stocks
            top_stocks = workflow_result.selected_stocks[:10]
            logger.info(f"[CLEAN-PREP] Top 10 selected stocks: {top_stocks}")

            # Log warnings if any
            if workflow_result.warnings:
                logger.info(f"[CLEAN-PREP] Warnings ({len(workflow_result.warnings)}):")
                for warning in workflow_result.warnings[:5]:
                    logger.info(f"[CLEAN-PREP]   • {warning}")

            # Log recommendations
            if workflow_result.recommendations:
                logger.info(f"[CLEAN-PREP] Recommendations ({len(workflow_result.recommendations)}):")
                for rec in workflow_result.recommendations[:3]:
                    logger.info(f"[CLEAN-PREP]   • {rec}")

            # Log timeframe data availability
            if workflow_result.timeframe_data_paths:
                logger.info(f"[CLEAN-PREP] Timeframe data generated for {len(workflow_result.timeframe_data_paths)} stocks")

        except Exception as e:
            logger.error(f"[CLEAN-PREP] Error logging clean results: {e}")

    def _log_ml_results(self, workflow_result):
        """Log ML workflow results summary"""
        try:
            logger.info(f"[ML-PREP] === ML WORKFLOW RESULTS SUMMARY ===")
            logger.info(f"[ML-PREP] Selected stocks: {len(workflow_result.selected_stocks)}")
            logger.info(f"[ML-PREP] Execution time: {workflow_result.execution_summary.get('total_execution_time', 0):.1f}s")

            # Log strategy distribution
            if workflow_result.strategy_assignments:
                strategy_counts = {}
                # Handle both StrategyAssignmentResult object and dict
                if hasattr(workflow_result.strategy_assignments, 'assignments'):
                    assignments = workflow_result.strategy_assignments.assignments
                else:
                    assignments = workflow_result.strategy_assignments
                
                for assignment in assignments.values():
                    if hasattr(assignment, 'primary_strategy'):
                        strategy = assignment.primary_strategy
                    else:
                        strategy = assignment.get('primary_strategy', 'unknown')
                    strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

                logger.info(f"[ML-PREP] Strategy distribution:")
                for strategy, count in strategy_counts.items():
                    logger.info(f"[ML-PREP]   {strategy}: {count} stocks")

            # Log top selected stocks
            top_stocks = workflow_result.selected_stocks[:10]
            logger.info(f"[ML-PREP] Top 10 selected stocks: {top_stocks}")

            # Log warnings if any
            if workflow_result.warnings:
                logger.info(f"[ML-PREP] Warnings ({len(workflow_result.warnings)}):")
                for warning in workflow_result.warnings[:5]:  # Log first 5 warnings
                    logger.info(f"[ML-PREP]   • {warning}")

            # Log recommendations
            if workflow_result.recommendations:
                logger.info(f"[ML-PREP] Recommendations ({len(workflow_result.recommendations)}):")
                for rec in workflow_result.recommendations[:3]:  # Log first 3 recommendations
                    logger.info(f"[ML-PREP]   • {rec}")

        except Exception as e:
            logger.error(f"[ML-PREP] Error logging ML results: {e}")

class CleanTradingSystem:
    """Orchestrates the entire trading system from pre-run analysis to live trading."""
    
    def __init__(self, mode: str = "paper", skip_download: bool = False):
        self.mode = mode
        self.running = False
        self.session_id = f"clean_trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.event_bus = EventBus()
        self.config = self._create_config(mode)
        self.skip_download = skip_download # Store skip_download
        self.agents: Dict[str, Any] = {}

    def _create_config(self, mode: str) -> Any:
        """Creates a simple config object."""
        class Config:
            def __init__(self, mode):
                self.mode = mode
                self.selected_stocks = []
                self.timeframes = ["1min", "3min", "5min", "15min"] # Added 3min
                self.initial_balance = 1000000 # Default initial balance for paper trading
                self.timeframe_data_paths = {}  # Store paths to generated timeframe data
                # Default risk limits used by RiskManagementAgent
                self.risk_limits = {
                    'max_position_size': 0.1,  # 10% of portfolio
                    'max_daily_loss': 0.05,    # 5% daily loss limit
                    'max_drawdown': 0.15       # 15% maximum drawdown
                }
        return Config(mode)

    async def start(self):
        """Main startup sequence for the trading system."""
        logger.info(f"[START] Starting Clean Trading System in {self.mode} mode...")
        await self.event_bus.start_processor()

        # 1. Initialize Market Data Agent. Its startup will be called after stock selection.
        self.agents['market_data'] = CleanMarketDataAgent(self.event_bus, self.config, self.session_id)

        # 2. Skip ML workflow if skip_download is enabled or use timeout
        if self.skip_download:
            logger.info("[START] Skipping ML universe preparation as requested")
            self.config.selected_stocks = ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]
        else:
            # Run the clean universe preparation workflow
            preparer = CleanUniversePreparer(self.event_bus, self.agents['market_data'], self.config)
            
            try:
                self.config.selected_stocks = await asyncio.wait_for(preparer.run(), timeout=900.0)  # 15 minute timeout - reasonable for market hours
            except asyncio.TimeoutError:
                logger.error("[START] Clean universe preparation timed out. Using fallback stock list.")
                self.config.selected_stocks = ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]
            except Exception as e:
                logger.error(f"[START] Clean universe preparation failed: {e}. Using fallback stock list.")
                self.config.selected_stocks = ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]

        if not self.config.selected_stocks:
            logger.error("[START] Universe selection failed. Shutting down.")
            await self.stop()
            return

        # 3. Now that stocks are selected, start the Market Data Agent.
        # This ensures instruments are initialized with the selected universe.
        # Note: Historical data is already downloaded by the clean workflow
        await self.agents['market_data'].start()

        # 4. Initialize websocket subscription for selected stocks
        logger.info(f"[START] Initializing websocket subscription for {len(self.config.selected_stocks)} selected stocks...")
        await self._initialize_websocket_subscription()

        # 5. Initialize and start trading agents (market monitoring, risk, execution)
        logger.info("[START] Initializing trading agents...")
        await self._initialize_trading_agents()

        # 6. Broadcast the final universe to all agents for their own setup
        await self.event_bus.publish("BROADCAST_UNIVERSE", {'universe': self.config.selected_stocks}, source="TradingSystem")

        self.running = True
        logger.info("[SUCCESS] All agents started. System is now in live trading mode.")
        
        # Print trading system status
        await self._print_trading_status()

        # Main loop for health checks with proper interrupt handling
        try:
            while self.running:
                await asyncio.sleep(30) # Health check every 30 seconds
                await self._check_agent_health()
        except KeyboardInterrupt:
            logger.info("[INTERRUPT] KeyboardInterrupt received, initiating graceful shutdown...")
            self.running = False
            await self.stop()
        except Exception as e:
            logger.error(f"[ERROR] Unexpected error in main loop: {e}")
            self.running = False
            await self.stop()

    async def _check_agent_health(self):
        health_statuses = await asyncio.gather(*(agent.health_check() for agent in self.agents.values()), return_exceptions=True)
        for status in health_statuses:
            if isinstance(status, Exception):
                logger.error(f"[HEALTH] Health check failed with exception: {status}")
                continue
                
            if not isinstance(status, dict):
                logger.error(f"[HEALTH] Invalid health status format: {status}")
                continue
                
            agent_name = status.get('agent_name', 'Unknown')
            if not status.get('healthy', False):
                logger.warning(f"[HEALTH] Agent {agent_name} is unhealthy. Status: {status}")

    async def _initialize_websocket_subscription(self):
        """Initialize websocket subscription for selected stocks."""
        try:
            # The market data agent should handle websocket subscription
            # for the selected stocks automatically when it receives the universe
            logger.info(f"[START] Websocket subscription will be handled by market data agent for {len(self.config.selected_stocks)} stocks")
            
            # Log the timeframe data availability
            if hasattr(self.config, 'timeframe_data_paths') and self.config.timeframe_data_paths:
                logger.info(f"[START] Timeframe data available for {len(self.config.timeframe_data_paths)} stocks")
                for symbol, timeframes in list(self.config.timeframe_data_paths.items())[:5]:  # Log first 5
                    logger.info(f"[START]   {symbol}: {list(timeframes.keys())}")
            
        except Exception as e:
            logger.error(f"[START] Error initializing websocket subscription: {e}")

    async def _initialize_trading_agents(self):
        """Initialize and start trading agents (signal generation, execution, market monitoring, risk management)."""
        try:
            # Initialize signal generation agent
            self.agents['signal_generation'] = CleanSignalAgent(self.event_bus, self.config, self.session_id)
            
            # Initialize execution agent
            self.agents['execution'] = ModernExecutionAgent(self.event_bus, self.config, self.session_id)
            
            # TODO: Add market monitoring agent when available
            # self.agents['market_monitoring'] = MarketMonitoringAgent(self.event_bus, self.config, self.session_id)
            
            # Add risk management agent
            self.agents['risk_management'] = RiskManagementAgent(self.event_bus, self.config, self.session_id)
            
            # Start agents
            if not await self.agents['signal_generation'].startup():
                raise Exception("Failed to start signal generation agent")

            # Start execution agent robustly across BaseAgent variants
            exec_agent = self.agents['execution']
            logger.info("[START] Starting execution agent (robust mode)...")
            try:
                if hasattr(exec_agent, 'startup'):
                    logger.info("[START] execution.startup() detected; invoking startup()")
                    await exec_agent.startup()
                else:
                    logger.info("[START] execution.start(): initializing via initialize() then start()")
                    init_ok = await exec_agent.initialize()
                    logger.info(f"[START] execution.initialize() -> {init_ok}")
                    await exec_agent.start()
                    logger.info("[START] execution.start() invoked")
                logger.info("[START] ✅ Execution agent started")
            except Exception as e:
                logger.exception("[START] ❌ Exception while starting execution agent")
                raise Exception("Failed to start execution agent") from e

            # TODO: Start additional agents when available
            # if not await self.agents['market_monitoring'].start():
            #     raise Exception("Failed to start market monitoring agent")

            # Start risk management agent
            if not await self.agents['risk_management'].initialize():
                raise Exception("Failed to initialize risk management agent")
            await self.agents['risk_management'].start()

            logger.info("[START] ✅ All available trading agents initialized and started successfully")
            
        except Exception as e:
            logger.error(f"[START] Error initializing trading agents: {e}")
            raise

    async def _print_trading_status(self):
        """Print comprehensive trading system status."""
        try:
            logger.info("=" * 80)
            logger.info("🚀 CLEAN TRADING SYSTEM - LIVE STATUS")
            logger.info("=" * 80)
            
            # System Configuration
            logger.info(f"📊 Trading Mode: {self.mode.upper()}")
            logger.info(f"📈 Selected Stocks: {len(self.config.selected_stocks)}")
            logger.info(f"⏰ Signal Generation: Every 30 seconds")
            logger.info(f"🔄 Timeframes: {', '.join(self.config.timeframes)}")
            
            # Active Agents
            logger.info(f"🤖 Active Agents: {len(self.agents)}")
            for agent_name in self.agents.keys():
                logger.info(f"   ✅ {agent_name}")
            
            # Stock Universe
            logger.info(f"📋 Top 10 Selected Stocks:")
            for i, symbol in enumerate(self.config.selected_stocks[:10], 1):
                logger.info(f"   {i:2d}. {symbol}")
            
            if len(self.config.selected_stocks) > 10:
                logger.info(f"   ... and {len(self.config.selected_stocks) - 10} more")
            
            # Live Data Status
            try:
                md_agent = self.agents.get('market_data')
                ws_connected = getattr(getattr(md_agent, 'smartapi_client', None), 'websocket_connected', False)
                ws_status = "CONNECTED (Real)" if ws_connected else "DISCONNECTED"
            except Exception:
                ws_status = "UNKNOWN"
            logger.info(f"🔴 Live Data Streaming: ACTIVE")
            logger.info(f"📡 Websocket Connection: {ws_status}")
            logger.info(f"📊 Live Candle Aggregation: ACTIVE")

            # Trading Capabilities
            logger.info(f"🎯 Signal Generation: ACTIVE")
            logger.info(f"💰 Paper Trading: ENABLED")
            logger.info(f"⚠️  Risk Management: ACTIVE")
            logger.info(f"📈 Market Monitoring: PENDING (Future Version)")
            
            # Next Steps
            logger.info("🔄 System Status: READY FOR TRADING")
            logger.info("⏳ Waiting for market signals...")
            logger.info("=" * 80)
            
        except Exception as e:
            logger.error(f"Error printing trading status: {e}")

    async def stop(self):
        logger.info("[STOP] Stopping Clean Trading System...")
        self.running = False
        await asyncio.gather(*(agent.shutdown() for agent in self.agents.values()), return_exceptions=True)
        await self.event_bus.stop_processor()
        logger.info("[SUCCESS] Clean Trading System stopped.")

    def _setup_signal_handlers(self):
        # Enhanced signal handling for both Windows and Unix systems
        if sys.platform != "win32":
            try:
                loop = asyncio.get_running_loop()
                for sig in (signal.SIGINT, signal.SIGTERM):
                    loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(self._signal_handler(s)))
                logger.info("[SIGNAL] Unix signal handlers registered successfully")
            except Exception as e:
                logger.warning(f"[SIGNAL] Failed to register Unix signal handlers: {e}")
        else:
            # For Windows, we'll handle KeyboardInterrupt in the main loop
            logger.info("[SIGNAL] Windows platform detected - KeyboardInterrupt will be handled in main loop")

    async def _signal_handler(self, sig):
        logger.info(f"[SIGNAL] Received signal {sig.name}, initiating graceful shutdown...")
        self.running = False
        await self.stop()

async def main():
    parser = argparse.ArgumentParser(description='Clean Trading System')
    parser.add_argument('--mode', choices=['paper', 'live'], default='paper', help='Trading mode')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='Logging level')
    parser.add_argument('--skip-download', action='store_true', help='Skip ML workflow entirely and use basic stock list')
    args = parser.parse_args()

    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)
    logging.basicConfig(
        level=args.log_level.upper(),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(), logging.FileHandler(log_dir / 'clean_trading_system.log')]
    )

    trading_system = CleanTradingSystem(args.mode, args.skip_download)
    trading_system._setup_signal_handlers()

    try:
        await trading_system.start()
    except KeyboardInterrupt:
        logger.info("[INTERRUPT] KeyboardInterrupt in main(), initiating shutdown...")
        if trading_system.running:
            await trading_system.stop()
    except Exception as e:
        logger.error(f"[FATAL] System error: {e}", exc_info=True)
        if trading_system.running:
            await trading_system.stop()
    finally:
        if trading_system.running:
            await trading_system.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[EXIT] System shutdown complete.")
    except Exception as e:
        logger.error(f"[FATAL] Unhandled exception: {e}", exc_info=True)
#!/usr/bin/env python3
"""
CLEAN TRADING SYSTEM RUNNER
Modern implementation with a pre-run analysis workflow for stock selection.

Features:
- Pre-run analysis of all F&O stocks to select the best universe.
- Data-driven selection based on calculated features and forward returns.
- Concurrent data fetching and processing for speed.
- Dynamic universe is then fed into live trading agents.
"""

import asyncio
import logging
import argparse
import signal
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import sys
import polars as pl
import numpy as np
from dotenv import load_dotenv
import time # Added for sleep
import pyarrow.parquet as pq # For parquet operations
import pyarrow as pa # For parquet operations
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import queue

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables
load_dotenv(Path(__file__).parent.parent / '.env')

from core.event_system import EventBus, EventTypes
from agents.clean_market_data_agent import CleanMarketDataAgent
from agents.clean_signal_agent import CleanSignalAgent
from agents.modern_execution_agent import ModernExecutionAgent
from agents.clean_stock_selection_workflow import CleanStockSelectionWorkflow
from agents.risk_management_agent import RiskManagementAgent

logger = logging.getLogger(__name__)

class SmartAPIDownloadManager:
    """
    Global SmartAPI download manager that handles rate limiting across all threads.
    Based on the working pattern from test/download_fno_data.py
    """
    _instance = None
    _lock = threading.Lock()
    _last_api_call = 0
    _min_interval = 1.0  # 1.0 second minimum interval (same as reference)

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def download_with_rate_limit(cls, market_data_agent, symbol: str, start_date: datetime, end_date: datetime):
        """
        Download historical data with proper global rate limiting.
        This follows the exact pattern from the working reference file.
        """
        # Apply rate limiting exactly like in the reference file
        with cls._lock:
            current_time = time.time()
            time_since_last_call = current_time - cls._last_api_call

            if time_since_last_call < cls._min_interval:
                sleep_time = cls._min_interval - time_since_last_call
                logger.debug(f"[RATE-LIMIT] Sleeping for {sleep_time:.2f}s to respect API limits")
                time.sleep(sleep_time)

            cls._last_api_call = time.time()

        # Make the API call outside the lock to allow concurrent processing
        try:
            result = cls._make_sync_api_call(market_data_agent, symbol, start_date, end_date)
            return result
        except Exception as e:
            logger.error(f"[SMARTAPI] Error downloading data for {symbol}: {e}")
            return None

    @classmethod
    def _make_sync_api_call(cls, market_data_agent, symbol: str, start_date: datetime, end_date: datetime):
        """
        Make synchronous API call with timeout to prevent hanging.
        """
        try:
            # Create a new event loop for this thread to avoid conflicts
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Add timeout to prevent hanging
                result = loop.run_until_complete(
                    asyncio.wait_for(
                        market_data_agent.download_historical_data_for_symbol(
                            symbol=symbol,
                            start_date=start_date,
                            end_date=end_date
                        ),
                        timeout=25.0  # 25 second timeout per symbol
                    )
                )
                return result
            except asyncio.TimeoutError:
                logger.error(f"[SMARTAPI] Timeout downloading data for {symbol}")
                return None
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"[SMARTAPI] Sync API call failed for {symbol}: {e}")
            return None

class CleanUniversePreparer:
    """
    Clean, logical universe preparation using the new Clean Stock Selection Workflow.
    
    Flow:
    1. Download historical data first (1-min for 25 days)
    2. Feature engineering on downloaded data
    3. ML prediction using trained models
    4. Stock selection (top 50 stocks)
    5. Generate higher timeframes (3min, 5min, 15min) from 1-min data
    6. Prepare for websocket subscription and trading agents
    """
    def __init__(self, event_bus: EventBus, market_data_agent: CleanMarketDataAgent, config: Any):
        self.event_bus = event_bus
        self.market_data_agent = market_data_agent
        self.config = config
        self.clean_workflow: Optional[CleanStockSelectionWorkflow] = None
        self.project_root = Path(__file__).parent.parent
        self.live_data_path = self.project_root / "data" / "live"
        self.feature_data_path = self.project_root / "data" / "features"

    def _download_single_stock_sync(self, symbol: str, start_date: datetime, end_date: datetime, stock_index: int, total_stocks: int) -> Tuple[str, bool]:
        """
        Download historical data for a single stock synchronously (for use with ThreadPoolExecutor).
        Returns tuple of (symbol, success_status).

        Uses SmartAPIDownloadManager to prevent token conflicts and ensure proper rate limiting.
        """
        logger.info(f"[ML-PREP] Downloading 1-min data for {symbol} ({stock_index}/{total_stocks}) from SmartAPI...")

        try:
            # Use the SmartAPI manager with proper rate limiting
            df_1min_polars = SmartAPIDownloadManager.download_with_rate_limit(
                self.market_data_agent, symbol, start_date, end_date
            )

            if df_1min_polars is not None and not df_1min_polars.is_empty():
                # Work directly with Polars DataFrame - no pandas conversion needed
                # Ensure 'timestamp' column exists
                if 'timestamp' not in df_1min_polars.columns:
                    logger.error(f"[ML-PREP] Downloaded data for {symbol} does not contain 'timestamp' column.")
                    return symbol, False

                # Sort by timestamp and ensure proper data types
                df_1min_polars = df_1min_polars.sort('timestamp')

                # Save 1-min data to data/live using Polars
                output_file_1min = self.live_data_path / f"{symbol}_1min.parquet"
                df_1min_polars.write_parquet(output_file_1min, compression='brotli')
                logger.info(f"[ML-PREP] Saved 1-min data for {symbol} to {output_file_1min} ({len(df_1min_polars)} rows)")
                
                # Generate higher timeframe data (3min, 5min, 15min)
                self._generate_higher_timeframes(symbol, df_1min_polars)
                
                return symbol, True
            else:
                logger.warning(f"[ML-PREP] No 1-min data downloaded for {symbol} from SmartAPI.")
                return symbol, False

        except ValueError as ve:
            # Handle specific token/symbol errors
            if "No instrument found" in str(ve) or "invalid token" in str(ve).lower():
                logger.warning(f"[ML-PREP] Token not found for {symbol}: {ve}")
                return symbol, False
            else:
                logger.error(f"[ML-PREP] ValueError downloading data for {symbol}: {ve}")
                return symbol, False
        except Exception as e:
            logger.error(f"[ML-PREP] Error downloading/saving data for {symbol} from SmartAPI: {e}")
            return symbol, False

    def _generate_higher_timeframes(self, symbol: str, df_1min: pl.DataFrame):
        """Generate 3min, 5min, and 15min data from 1min data"""
        try:
            timeframes = {
                '3min': 3,
                '5min': 5,
                '15min': 15
            }
            
            for tf_name, minutes in timeframes.items():
                try:
                    # Group by time intervals and aggregate OHLCV data
                    df_tf = df_1min.group_by_dynamic(
                        "timestamp",
                        every=f"{minutes}m",
                        closed="left"
                    ).agg([
                        pl.col("open").first().alias("open"),
                        pl.col("high").max().alias("high"),
                        pl.col("low").min().alias("low"),
                        pl.col("close").last().alias("close"),
                        pl.col("volume").sum().alias("volume")
                    ]).sort("timestamp")
                    
                    # Remove any rows with null values
                    df_tf = df_tf.drop_nulls()
                    
                    if not df_tf.is_empty():
                        output_file = self.live_data_path / f"{symbol}_{tf_name}.parquet"
                        df_tf.write_parquet(output_file, compression='brotli')
                        logger.debug(f"[ML-PREP] Generated {tf_name} data for {symbol} ({len(df_tf)} rows)")
                    else:
                        logger.warning(f"[ML-PREP] No {tf_name} data generated for {symbol}")
                        
                except Exception as e:
                    logger.error(f"[ML-PREP] Error generating {tf_name} data for {symbol}: {e}")
                    
        except Exception as e:
            logger.error(f"[ML-PREP] Error in higher timeframe generation for {symbol}: {e}")

    async def _download_historical_data(self, stocks: List[str]):
        """
        Downloads 25 days of 1-minute historical data for the given stocks from SmartAPI
        using ThreadPoolExecutor with optimized settings and retry mechanism.
        """
        self.live_data_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"[ML-PREP] Starting optimized historical data download from SmartAPI for {len(stocks)} stocks...")

        end_date = datetime.now()
        start_date = end_date - timedelta(days=25) # 25 days historical data

        # Aggressive settings to prevent hanging
        MAX_CONCURRENT_DOWNLOADS = 5  # Reduced to prevent overwhelming API
        RATE_LIMIT_DELAY = 1.0  # Increased delay to be more conservative
        MAX_RETRIES = 1  # Reduced retries to fail fast
        RETRY_DELAY_BASE = 2.0  # Increased retry delay
        
        logger.info("🚀 STARTING OPTIMIZED DOWNLOAD")
        logger.info("="*60)
        logger.info(f"📊 Symbols: {len(stocks)}")
        logger.info(f"📅 Period: 25 days ({start_date} to {end_date})")
        logger.info(f"🧵 Max concurrent: {MAX_CONCURRENT_DOWNLOADS}")
        logger.info(f"⏳ Rate limit delay: {RATE_LIMIT_DELAY}s")
        logger.info(f"🔄 Max retries: {MAX_RETRIES}")
        logger.info("="*60)

        # Run the download process with timeout to prevent hanging
        def run_threaded_downloads():
            results = {}
            completed_count = 0
            
            # Use ThreadPoolExecutor for concurrent downloads with timeout
            with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_DOWNLOADS) as executor:
                future_to_symbol = {
                    executor.submit(
                        self._download_single_stock_sync,
                        symbol,
                        start_date,
                        end_date,
                        i + 1,
                        len(stocks)
                    ): symbol
                    for i, symbol in enumerate(stocks)
                }

                # Process with timeout per future
                for future in as_completed(future_to_symbol, timeout=120):  # 2 minute total timeout
                    symbol = future_to_symbol[future]
                    try:
                        symbol_result, success = future.result(timeout=30)  # 30 second per stock timeout
                        results[symbol] = success
                        completed_count += 1
                        
                        logger.info(f"[ML-PREP] Completed {completed_count}/{len(stocks)}: {symbol}")
                        
                        # Add rate limiting delay
                        time.sleep(RATE_LIMIT_DELAY)
                        
                        # Circuit breaker: if too many failures, stop early
                        if completed_count >= 10:  # Stop after 10 successful downloads
                            logger.info(f"[ML-PREP] Circuit breaker: stopping after {completed_count} downloads")
                            break
                        
                    except Exception as e:
                        logger.error(f"[ML-PREP] Error downloading {symbol}: {e}")
                        results[symbol] = False
                        completed_count += 1
            
            return results

        # Execute downloads in thread pool with overall timeout
        start_time = time.time()
        try:
            results = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(None, run_threaded_downloads),
                timeout=180.0  # 3 minute total timeout
            )
        except asyncio.TimeoutError:
            logger.error("[ML-PREP] Download process timed out after 3 minutes")
            results = {symbol: False for symbol in stocks}
        
        # Count initial results and update failed_stocks list
        successful_downloads = sum(1 for success in results.values() if success)
        failed_downloads = len(results) - successful_downloads
        failed_stocks = [symbol for symbol, success in results.items() if not success]
        
        logger.info(f"[ML-PREP] Initial download completed - Success: {successful_downloads}, Failed: {failed_downloads}")
        
        # Retry failed downloads with exponential backoff
        if failed_stocks and MAX_RETRIES > 0:
            logger.info(f"[ML-PREP] Retrying {len(failed_stocks)} failed downloads...")
            
            for retry_attempt in range(1, MAX_RETRIES + 1):
                if not failed_stocks:
                    break
                    
                retry_delay = RETRY_DELAY_BASE * (2 ** (retry_attempt - 1))  # Exponential backoff
                logger.info(f"[ML-PREP] Retry attempt {retry_attempt}/{MAX_RETRIES} for {len(failed_stocks)} stocks (delay: {retry_delay}s)")
                
                await asyncio.sleep(retry_delay)
                
                # Retry failed stocks with reduced concurrency
                def retry_downloads():
                    retry_results = {}
                    # Use fewer workers for retries to be more conservative
                    retry_workers = min(5, len(failed_stocks))
                    with ThreadPoolExecutor(max_workers=retry_workers) as executor:
                        future_to_symbol = {
                            executor.submit(
                                self._download_single_stock_sync,
                                symbol,
                                start_date,
                                end_date,
                                stocks.index(symbol) + 1,
                                len(stocks)
                            ): symbol
                            for symbol in failed_stocks
                        }

                        for future in as_completed(future_to_symbol):
                            symbol = future_to_symbol[future]
                            try:
                                symbol_result, success = future.result()
                                retry_results[symbol] = success
                                time.sleep(RATE_LIMIT_DELAY)
                            except Exception as e:
                                logger.error(f"[ML-PREP] Retry error for {symbol}: {e}")
                                retry_results[symbol] = False
                    
                    return retry_results
                
                retry_results = await asyncio.get_event_loop().run_in_executor(None, retry_downloads)
                
                # Update results and failed list
                retry_successful = 0
                new_failed_stocks = []
                for symbol, success in retry_results.items():
                    if success:
                        retry_successful += 1
                        successful_downloads += 1
                        failed_downloads -= 1
                    else:
                        new_failed_stocks.append(symbol)
                
                failed_stocks = new_failed_stocks
                logger.info(f"[ML-PREP] Retry {retry_attempt} completed - Additional successes: {retry_successful}, Still failed: {len(failed_stocks)}")

        # Calculate performance metrics
        end_time = time.time()
        total_time = end_time - start_time
        symbols_per_minute = (len(stocks) / total_time) * 60 if total_time > 0 else 0
        success_rate = (successful_downloads / len(stocks)) * 100 if len(stocks) > 0 else 0

        # Final summary
        logger.info("="*80)
        logger.info("🎯 OPTIMIZED DOWNLOAD SUMMARY")
        logger.info("="*80)
        logger.info(f"📊 Total symbols: {len(stocks)}")
        logger.info(f"✅ Successful downloads: {successful_downloads}")
        logger.info(f"❌ Failed downloads: {failed_downloads}")
        logger.info(f"📈 Success rate: {success_rate:.1f}%")
        logger.info(f"⏱️  Total time: {total_time:.1f} seconds")
        logger.info(f"🚀 Speed: {symbols_per_minute:.1f} symbols/minute")
        logger.info(f"⚡ Avg time per symbol: {total_time/len(stocks):.2f} seconds")
        if failed_stocks:
            logger.warning(f"🔴 Final failed stocks: {failed_stocks}")
        logger.info("="*80)

    async def _perform_feature_engineering(self, stocks: List[str]):
        """
        Performs feature engineering on the downloaded and aggregated data in data/live using Polars.
        """
        logger.info(f"[ML-PREP] Starting feature engineering for {len(stocks)} stocks across all timeframes...")
        
        for i, symbol in enumerate(stocks):
            for tf_name in self.config.timeframes: # Iterate over all timeframes
                input_file = self.live_data_path / f"{symbol}_{tf_name}.parquet"
                if input_file.exists():
                    try:
                        # Read data using Polars
                        df = pl.read_parquet(input_file)
                        
                        # Ensure timestamp is properly sorted
                        df = df.sort('timestamp')

                        # Feature engineering using Polars expressions
                        df = df.with_columns([
                            # Moving Averages
                            pl.col('close').rolling_mean(window_size=5).alias('MA_5'),
                            pl.col('close').rolling_mean(window_size=10).alias('MA_10'),
                            
                            # Price changes for RSI calculation
                            pl.col('close').diff().alias('price_change')
                        ])
                        
                        # RSI calculation using Polars
                        df = df.with_columns([
                            # Separate gains and losses
                            pl.when(pl.col('price_change') > 0)
                            .then(pl.col('price_change'))
                            .otherwise(0.0)
                            .alias('gain'),
                            
                            pl.when(pl.col('price_change') < 0)
                            .then(-pl.col('price_change'))
                            .otherwise(0.0)
                            .alias('loss')
                        ])
                        
                        # Calculate RSI using rolling averages
                        df = df.with_columns([
                            pl.col('gain').rolling_mean(window_size=14).alias('avg_gain'),
                            pl.col('loss').rolling_mean(window_size=14).alias('avg_loss')
                        ])
                        
                        # Final RSI calculation
                        df = df.with_columns([
                            pl.when(pl.col('avg_loss') != 0)
                            .then(100 - (100 / (1 + (pl.col('avg_gain') / pl.col('avg_loss')))))
                            .otherwise(100.0)
                            .alias('RSI')
                        ])
                        
                        # Drop intermediate columns
                        df = df.drop(['price_change', 'gain', 'loss', 'avg_gain', 'avg_loss'])

                        # Save processed data back to data/live using Polars
                        df.write_parquet(input_file, compression='brotli')
                        logger.info(f"[ML-PREP] Performed feature engineering for {symbol} ({i+1}/{len(stocks)}) on {tf_name} data ({len(df)} rows).")
                    except Exception as e:
                        logger.error(f"[ML-PREP] Error during feature engineering for {symbol} on {tf_name} data: {e}")
                else:
                    logger.warning(f"[ML-PREP] {tf_name} data file not found for {symbol} at {input_file}. Skipping feature engineering.")
                await asyncio.sleep(0.01) # Small sleep to avoid overwhelming within inner loop
            await asyncio.sleep(0.05) # Small sleep to avoid overwhelming between stocks

        logger.info("[ML-PREP] Feature engineering complete for all timeframes.")

    def _process_single_feature_engineering_task(self, symbol: str, tf_name: str, total_stocks: int, current_index: int):
        """
        Performs feature engineering for a single stock and timeframe.
        This is a synchronous method designed to be run in a ThreadPoolExecutor.
        """
        input_file = self.live_data_path / f"{symbol}_{tf_name}.parquet"
        if input_file.exists():
            try:
                # Read data using Polars
                df = pl.read_parquet(input_file)
                
                # Ensure timestamp is properly sorted
                df = df.sort('timestamp')

                # Feature engineering using Polars expressions
                df = df.with_columns([
                    # Moving Averages
                    pl.col('close').rolling_mean(window_size=5).alias('MA_5'),
                    pl.col('close').rolling_mean(window_size=10).alias('MA_10'),
                    
                    # Price changes for RSI calculation
                    pl.col('close').diff().alias('price_change')
                ])
                
                # RSI calculation using Polars
                df = df.with_columns([
                    # Separate gains and losses
                    pl.when(pl.col('price_change') > 0)
                    .then(pl.col('price_change'))
                    .otherwise(0.0)
                    .alias('gain'),
                    
                    pl.when(pl.col('price_change') < 0)
                    .then(-pl.col('price_change'))
                    .otherwise(0.0)
                    .alias('loss')
                ])
                
                # Calculate RSI using rolling averages
                df = df.with_columns([
                    pl.col('gain').rolling_mean(window_size=14).alias('avg_gain'),
                    pl.col('loss').rolling_mean(window_size=14).alias('avg_loss')
                ])
                
                # Final RSI calculation
                df = df.with_columns([
                    pl.when(pl.col('avg_loss') != 0)
                    .then(100 - (100 / (1 + (pl.col('avg_gain') / pl.col('avg_loss')))))
                    .otherwise(100.0)
                    .alias('RSI')
                ])
                
                # Drop intermediate columns
                df = df.drop(['price_change', 'gain', 'loss', 'avg_gain', 'avg_loss'])

                # Save processed data back to data/live using Polars
                df.write_parquet(input_file, compression='brotli')
                logger.info(f"[ML-PREP] Performed feature engineering for {symbol} ({current_index}/{total_stocks}) on {tf_name} data ({len(df)} rows).")
                return True
            except Exception as e:
                logger.error(f"[ML-PREP] Error during feature engineering for {symbol} on {tf_name} data: {e}")
                return False
        else:
            logger.warning(f"[ML-PREP] {tf_name} data file not found for {symbol} at {input_file}. Skipping feature engineering.")
            return False

    async def run(self) -> List[str]:
        """Execute the clean universe preparation workflow."""
        logger.info("[CLEAN-PREP] Starting clean universe preparation workflow...")

        try:
            # Initialize the clean workflow
            self.clean_workflow = CleanStockSelectionWorkflow(
                self.event_bus, 
                self.market_data_agent.session_id
            )

            # Initialize all agents
            success = await self.clean_workflow.initialize()
            if not success:
                logger.error("[CLEAN-PREP] Clean workflow initialization failed. Cannot proceed.")
                return []

            # Load stocks to process from F&O list or feature data
            stocks_to_process = self._load_stocks_to_process()
            
            if not stocks_to_process:
                logger.error("[CLEAN-PREP] No stocks found to process. Cannot proceed.")
                return []

            logger.info(f"[CLEAN-PREP] Processing {len(stocks_to_process)} stocks through clean workflow...")

            # Execute the clean workflow
            workflow_result = await self.clean_workflow.execute_workflow(stocks_to_process)

            if workflow_result and workflow_result.selected_stocks:
                selected_stocks = workflow_result.selected_stocks
                logger.info(f"[CLEAN-PREP] ✅ Clean workflow selected {len(selected_stocks)} stocks")

                # Log summary information
                self._log_clean_results(workflow_result)

                # Store timeframe data paths for later use
                self.config.timeframe_data_paths = workflow_result.timeframe_data_paths

                # Cleanup workflow resources
                await self.clean_workflow.cleanup()

                return selected_stocks
            else:
                logger.error("[CLEAN-PREP] Clean workflow returned no stocks. Cannot proceed.")
                return []

        except Exception as e:
            logger.error(f"[CLEAN-PREP] Clean workflow failed: {e}")
            return []

    def _load_stocks_to_process(self) -> List[str]:
        """Load stocks to process from F&O list or feature data."""
        stocks_to_process = set()
        
        # Try to load from F&O list first
        fno_list_path = self.project_root / "data" / "lists" / "fno_list.csv"
        if fno_list_path.exists():
            try:
                import pandas as pd
                fno_df = pd.read_csv(fno_list_path)
                if 'symbol' in fno_df.columns:
                    stocks_to_process.update(fno_df['symbol'].tolist())
                    logger.info(f"[CLEAN-PREP] Loaded {len(stocks_to_process)} stocks from F&O list")
                elif 'Symbol' in fno_df.columns:
                    stocks_to_process.update(fno_df['Symbol'].tolist())
                    logger.info(f"[CLEAN-PREP] Loaded {len(stocks_to_process)} stocks from F&O list")
            except Exception as e:
                logger.warning(f"[CLEAN-PREP] Error loading F&O list: {e}")
        
        # If no F&O list, try feature data
        if not stocks_to_process and self.feature_data_path.exists():
            for file_path in self.feature_data_path.glob("features_*.parquet"):
                parts = file_path.stem.split('_')
                if len(parts) >= 2:
                    stocks_to_process.add(parts[1])
            logger.info(f"[CLEAN-PREP] Loaded {len(stocks_to_process)} stocks from feature data")
        
        # Fallback to a default list
        if not stocks_to_process:
            stocks_to_process = {
                "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK", "HINDUNILVR", "ITC", "SBIN", 
                "BHARTIARTL", "KOTAKBANK", "LT", "ASIANPAINT", "AXISBANK", "MARUTI", "SUNPHARMA",
                "ULTRACEMCO", "TITAN", "WIPRO", "NESTLEIND", "POWERGRID", "NTPC", "TECHM", "HCLTECH",
                "BAJFINANCE", "ONGC", "TATASTEEL", "COALINDIA", "INDUSINDBK", "DRREDDY", "GRASIM",
                "CIPLA", "EICHERMOT", "JSWSTEEL", "BRITANNIA", "ADANIPORTS", "APOLLOHOSP", "DIVISLAB",
                "BAJAJFINSV", "HEROMOTOCO", "SHREECEM", "HINDALCO", "TATAMOTORS", "UPL", "BPCL",
                "VEDL", "TATACONSUM", "SBILIFE", "HDFCLIFE", "BAJAJ-AUTO", "GODREJCP"
            }
            logger.info(f"[CLEAN-PREP] Using fallback stock list with {len(stocks_to_process)} stocks")
        
        return list(stocks_to_process)  # Process all available stocks

    def _log_clean_results(self, workflow_result):
        """Log clean workflow results summary."""
        try:
            logger.info(f"[CLEAN-PREP] === CLEAN WORKFLOW RESULTS SUMMARY ===")
            logger.info(f"[CLEAN-PREP] Selected stocks: {len(workflow_result.selected_stocks)}")
            logger.info(f"[CLEAN-PREP] Execution time: {workflow_result.execution_summary.get('total_execution_time', 0):.1f}s")

            # Log strategy distribution
            if workflow_result.strategy_assignments:
                strategy_counts = {}
                assignments = workflow_result.strategy_assignments
                
                # Handle StrategyAssignmentResult object
                if hasattr(assignments, 'assignments'):
                    assignments_dict = assignments.assignments
                elif isinstance(assignments, dict):
                    assignments_dict = assignments
                else:
                    assignments_dict = {}
                
                for symbol, assignment in assignments_dict.items():
                    if hasattr(assignment, 'primary_strategy'):
                        strategy = assignment.primary_strategy
                    elif isinstance(assignment, dict):
                        strategy = assignment.get('primary_strategy', 'unknown')
                    else:
                        strategy = 'unknown'
                    strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

                logger.info(f"[CLEAN-PREP] Strategy distribution:")
                for strategy, count in strategy_counts.items():
                    logger.info(f"[CLEAN-PREP]   {strategy}: {count} stocks")

            # Log top selected stocks
            top_stocks = workflow_result.selected_stocks[:10]
            logger.info(f"[CLEAN-PREP] Top 10 selected stocks: {top_stocks}")

            # Log warnings if any
            if workflow_result.warnings:
                logger.info(f"[CLEAN-PREP] Warnings ({len(workflow_result.warnings)}):")
                for warning in workflow_result.warnings[:5]:
                    logger.info(f"[CLEAN-PREP]   • {warning}")

            # Log recommendations
            if workflow_result.recommendations:
                logger.info(f"[CLEAN-PREP] Recommendations ({len(workflow_result.recommendations)}):")
                for rec in workflow_result.recommendations[:3]:
                    logger.info(f"[CLEAN-PREP]   • {rec}")

            # Log timeframe data availability
            if workflow_result.timeframe_data_paths:
                logger.info(f"[CLEAN-PREP] Timeframe data generated for {len(workflow_result.timeframe_data_paths)} stocks")

        except Exception as e:
            logger.error(f"[CLEAN-PREP] Error logging clean results: {e}")

    def _log_ml_results(self, workflow_result):
        """Log ML workflow results summary"""
        try:
            logger.info(f"[ML-PREP] === ML WORKFLOW RESULTS SUMMARY ===")
            logger.info(f"[ML-PREP] Selected stocks: {len(workflow_result.selected_stocks)}")
            logger.info(f"[ML-PREP] Execution time: {workflow_result.execution_summary.get('total_execution_time', 0):.1f}s")

            # Log strategy distribution
            if workflow_result.strategy_assignments:
                strategy_counts = {}
                # Handle both StrategyAssignmentResult object and dict
                if hasattr(workflow_result.strategy_assignments, 'assignments'):
                    assignments = workflow_result.strategy_assignments.assignments
                else:
                    assignments = workflow_result.strategy_assignments
                
                for assignment in assignments.values():
                    if hasattr(assignment, 'primary_strategy'):
                        strategy = assignment.primary_strategy
                    else:
                        strategy = assignment.get('primary_strategy', 'unknown')
                    strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

                logger.info(f"[ML-PREP] Strategy distribution:")
                for strategy, count in strategy_counts.items():
                    logger.info(f"[ML-PREP]   {strategy}: {count} stocks")

            # Log top selected stocks
            top_stocks = workflow_result.selected_stocks[:10]
            logger.info(f"[ML-PREP] Top 10 selected stocks: {top_stocks}")

            # Log warnings if any
            if workflow_result.warnings:
                logger.info(f"[ML-PREP] Warnings ({len(workflow_result.warnings)}):")
                for warning in workflow_result.warnings[:5]:  # Log first 5 warnings
                    logger.info(f"[ML-PREP]   • {warning}")

            # Log recommendations
            if workflow_result.recommendations:
                logger.info(f"[ML-PREP] Recommendations ({len(workflow_result.recommendations)}):")
                for rec in workflow_result.recommendations[:3]:  # Log first 3 recommendations
                    logger.info(f"[ML-PREP]   • {rec}")

        except Exception as e:
            logger.error(f"[ML-PREP] Error logging ML results: {e}")

class CleanTradingSystem:
    """Orchestrates the entire trading system from pre-run analysis to live trading."""
    
    def __init__(self, mode: str = "paper", skip_download: bool = False):
        self.mode = mode
        self.running = False
        self.session_id = f"clean_trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.event_bus = EventBus()
        self.config = self._create_config(mode)
        self.skip_download = skip_download # Store skip_download
        self.agents: Dict[str, Any] = {}

    def _create_config(self, mode: str) -> Any:
        """Creates a simple config object."""
        class Config:
            def __init__(self, mode):
                self.mode = mode
                self.selected_stocks = []
                self.timeframes = ["1min", "3min", "5min", "15min"] # Added 3min
                self.initial_balance = 1000000 # Default initial balance for paper trading
                self.timeframe_data_paths = {}  # Store paths to generated timeframe data
                # Default risk limits used by RiskManagementAgent
                self.risk_limits = {
                    'max_position_size': 0.1,  # 10% of portfolio
                    'max_daily_loss': 0.05,    # 5% daily loss limit
                    'max_drawdown': 0.15       # 15% maximum drawdown
                }
        return Config(mode)

    async def start(self):
        """Main startup sequence for the trading system."""
        logger.info(f"[START] Starting Clean Trading System in {self.mode} mode...")
        await self.event_bus.start_processor()

        # 1. Initialize Market Data Agent. Its startup will be called after stock selection.
        self.agents['market_data'] = CleanMarketDataAgent(self.event_bus, self.config, self.session_id)

        # 2. Skip ML workflow if skip_download is enabled or use timeout
        if self.skip_download:
            logger.info("[START] Skipping ML universe preparation as requested")
            self.config.selected_stocks = ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]
        else:
            # Run the clean universe preparation workflow
            preparer = CleanUniversePreparer(self.event_bus, self.agents['market_data'], self.config)
            
            try:
                self.config.selected_stocks = await asyncio.wait_for(preparer.run(), timeout=900.0)  # 15 minute timeout - reasonable for market hours
            except asyncio.TimeoutError:
                logger.error("[START] Clean universe preparation timed out. Using fallback stock list.")
                self.config.selected_stocks = ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]
            except Exception as e:
                logger.error(f"[START] Clean universe preparation failed: {e}. Using fallback stock list.")
                self.config.selected_stocks = ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]

        if not self.config.selected_stocks:
            logger.error("[START] Universe selection failed. Shutting down.")
            await self.stop()
            return

        # 3. Now that stocks are selected, start the Market Data Agent.
        # This ensures instruments are initialized with the selected universe.
        # Note: Historical data is already downloaded by the clean workflow
        await self.agents['market_data'].startup()

        # 4. Initialize websocket subscription for selected stocks
        logger.info(f"[START] Initializing websocket subscription for {len(self.config.selected_stocks)} selected stocks...")
        await self._initialize_websocket_subscription()

        # 5. Initialize and start trading agents (market monitoring, risk, execution)
        logger.info("[START] Initializing trading agents...")
        await self._initialize_trading_agents()

        # 6. Broadcast the final universe to all agents for their own setup
        await self.event_bus.publish("BROADCAST_UNIVERSE", {'universe': self.config.selected_stocks}, source="TradingSystem")

        self.running = True
        logger.info("[SUCCESS] All agents started. System is now in live trading mode.")
        
        # Print trading system status
        await self._print_trading_status()

        # Main loop for health checks with proper interrupt handling
        try:
            while self.running:
                await asyncio.sleep(30) # Health check every 30 seconds
                await self._check_agent_health()
        except KeyboardInterrupt:
            logger.info("[INTERRUPT] KeyboardInterrupt received, initiating graceful shutdown...")
            self.running = False
            await self.stop()
        except Exception as e:
            logger.error(f"[ERROR] Unexpected error in main loop: {e}")
            self.running = False
            await self.stop()

    async def _check_agent_health(self):
        health_statuses = await asyncio.gather(*(agent.health_check() for agent in self.agents.values()), return_exceptions=True)
        for status in health_statuses:
            if isinstance(status, Exception):
                logger.error(f"[HEALTH] Health check failed with exception: {status}")
                continue
                
            if not isinstance(status, dict):
                logger.error(f"[HEALTH] Invalid health status format: {status}")
                continue
                
            agent_name = status.get('agent_name', 'Unknown')
            if not status.get('healthy', False):
                logger.warning(f"[HEALTH] Agent {agent_name} is unhealthy. Status: {status}")

    async def _initialize_websocket_subscription(self):
        """Initialize websocket subscription for selected stocks."""
        try:
            # The market data agent should handle websocket subscription
            # for the selected stocks automatically when it receives the universe
            logger.info(f"[START] Websocket subscription will be handled by market data agent for {len(self.config.selected_stocks)} stocks")
            
            # Log the timeframe data availability
            if hasattr(self.config, 'timeframe_data_paths') and self.config.timeframe_data_paths:
                logger.info(f"[START] Timeframe data available for {len(self.config.timeframe_data_paths)} stocks")
                for symbol, timeframes in list(self.config.timeframe_data_paths.items())[:5]:  # Log first 5
                    logger.info(f"[START]   {symbol}: {list(timeframes.keys())}")
            
        except Exception as e:
            logger.error(f"[START] Error initializing websocket subscription: {e}")

    async def _initialize_trading_agents(self):
        """Initialize and start trading agents (signal generation, execution, market monitoring, risk management)."""
        try:
            # Initialize signal generation agent
            self.agents['signal_generation'] = CleanSignalAgent(self.event_bus, self.config, self.session_id)
            
            # Initialize execution agent
            self.agents['execution'] = ModernExecutionAgent(self.event_bus, self.config, self.session_id)
            
            # TODO: Add market monitoring agent when available
            # self.agents['market_monitoring'] = MarketMonitoringAgent(self.event_bus, self.config, self.session_id)
            
            # Add risk management agent
            self.agents['risk_management'] = RiskManagementAgent(self.event_bus, self.config, self.session_id)
            
            # Start agents
            if not await self.agents['signal_generation'].startup():
                raise Exception("Failed to start signal generation agent")

            # Start execution agent robustly across BaseAgent variants
            exec_agent = self.agents['execution']
            logger.info("[START] Starting execution agent (robust mode)...")
            try:
                if hasattr(exec_agent, 'startup'):
                    logger.info("[START] execution.startup() detected; invoking startup()")
                    await exec_agent.startup()
                else:
                    logger.info("[START] execution.start(): initializing via initialize() then start()")
                    init_ok = await exec_agent.initialize()
                    logger.info(f"[START] execution.initialize() -> {init_ok}")
                    await exec_agent.start()
                    logger.info("[START] execution.start() invoked")
                logger.info("[START] ✅ Execution agent started")
            except Exception as e:
                logger.exception("[START] ❌ Exception while starting execution agent")
                raise Exception("Failed to start execution agent") from e

            # TODO: Start additional agents when available
            # if not await self.agents['market_monitoring'].start():
            #     raise Exception("Failed to start market monitoring agent")

            # Start risk management agent
            if not await self.agents['risk_management'].initialize():
                raise Exception("Failed to initialize risk management agent")
            await self.agents['risk_management'].start()

            logger.info("[START] ✅ All available trading agents initialized and started successfully")
            
        except Exception as e:
            logger.error(f"[START] Error initializing trading agents: {e}")
            raise

    async def _print_trading_status(self):
        """Print comprehensive trading system status."""
        try:
            logger.info("=" * 80)
            logger.info("🚀 CLEAN TRADING SYSTEM - LIVE STATUS")
            logger.info("=" * 80)
            
            # System Configuration
            logger.info(f"📊 Trading Mode: {self.mode.upper()}")
            logger.info(f"📈 Selected Stocks: {len(self.config.selected_stocks)}")
            logger.info(f"⏰ Signal Generation: Every 30 seconds")
            logger.info(f"🔄 Timeframes: {', '.join(self.config.timeframes)}")
            
            # Active Agents
            logger.info(f"🤖 Active Agents: {len(self.agents)}")
            for agent_name in self.agents.keys():
                logger.info(f"   ✅ {agent_name}")
            
            # Stock Universe
            logger.info(f"📋 Top 10 Selected Stocks:")
            for i, symbol in enumerate(self.config.selected_stocks[:10], 1):
                logger.info(f"   {i:2d}. {symbol}")
            
            if len(self.config.selected_stocks) > 10:
                logger.info(f"   ... and {len(self.config.selected_stocks) - 10} more")
            
            # Live Data Status
            try:
                md_agent = self.agents.get('market_data')
                ws_connected = getattr(getattr(md_agent, 'smartapi_client', None), 'websocket_connected', False)
                ws_status = "CONNECTED (Real)" if ws_connected else "DISCONNECTED"
            except Exception:
                ws_status = "UNKNOWN"
            logger.info(f"🔴 Live Data Streaming: ACTIVE")
            logger.info(f"📡 Websocket Connection: {ws_status}")
            logger.info(f"📊 Live Candle Aggregation: ACTIVE")

            # Trading Capabilities
            logger.info(f"🎯 Signal Generation: ACTIVE")
            logger.info(f"💰 Paper Trading: ENABLED")
            logger.info(f"⚠️  Risk Management: ACTIVE")
            logger.info(f"📈 Market Monitoring: PENDING (Future Version)")
            
            # Next Steps
            logger.info("🔄 System Status: READY FOR TRADING")
            logger.info("⏳ Waiting for market signals...")
            logger.info("=" * 80)
            
        except Exception as e:
            logger.error(f"Error printing trading status: {e}")

    async def stop(self):
        logger.info("[STOP] Stopping Clean Trading System...")
        self.running = False
        await asyncio.gather(*(agent.shutdown() for agent in self.agents.values()), return_exceptions=True)
        await self.event_bus.stop_processor()
        logger.info("[SUCCESS] Clean Trading System stopped.")

    def _setup_signal_handlers(self):
        # Enhanced signal handling for both Windows and Unix systems
        if sys.platform != "win32":
            try:
                loop = asyncio.get_running_loop()
                for sig in (signal.SIGINT, signal.SIGTERM):
                    loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(self._signal_handler(s)))
                logger.info("[SIGNAL] Unix signal handlers registered successfully")
            except Exception as e:
                logger.warning(f"[SIGNAL] Failed to register Unix signal handlers: {e}")
        else:
            # For Windows, we'll handle KeyboardInterrupt in the main loop
            logger.info("[SIGNAL] Windows platform detected - KeyboardInterrupt will be handled in main loop")

    async def _signal_handler(self, sig):
        logger.info(f"[SIGNAL] Received signal {sig.name}, initiating graceful shutdown...")
        self.running = False
        await self.stop()

async def main():
    parser = argparse.ArgumentParser(description='Clean Trading System')
    parser.add_argument('--mode', choices=['paper', 'live'], default='paper', help='Trading mode')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='Logging level')
    parser.add_argument('--skip-download', action='store_true', help='Skip ML workflow entirely and use basic stock list')
    args = parser.parse_args()

    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)
    logging.basicConfig(
        level=args.log_level.upper(),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(), logging.FileHandler(log_dir / 'clean_trading_system.log')]
    )

    trading_system = CleanTradingSystem(args.mode, args.skip_download)
    trading_system._setup_signal_handlers()

    try:
        await trading_system.start()
    except KeyboardInterrupt:
        logger.info("[INTERRUPT] KeyboardInterrupt in main(), initiating shutdown...")
        if trading_system.running:
            await trading_system.stop()
    except Exception as e:
        logger.error(f"[FATAL] System error: {e}", exc_info=True)
        if trading_system.running:
            await trading_system.stop()
    finally:
        if trading_system.running:
            await trading_system.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[EXIT] System shutdown complete.")
    except Exception as e:
        logger.error(f"[FATAL] Unhandled exception: {e}", exc_info=True)

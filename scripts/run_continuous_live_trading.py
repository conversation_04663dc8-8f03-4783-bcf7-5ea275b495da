#!/usr/bin/env python3
"""
MODERN CONTINUOUS LIVE TRADING SYSTEM
AI-Driven Continuous Market Monitoring and Trading with Real WebSocket Data

Features:
🚀 Multi-Mode Trading Support:
   - LIVE: Real trading with real money using SmartAPI
   - PAPER: Virtual trading with real market data
   - DEMO: Simulation mode with mock data

📊 Enhanced Market Monitoring:
   - Real-time WebSocket data streaming from Angel One SmartAPI 2.0
   - Modern timeframe patterns (1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr)
   - Intelligent batch processing with API rate limit handling

🧠 AI-Powered Decision Making:
   - Microservices-based agent architecture
   - Event-driven communication between agents
   - Scalable and maintainable design

💰 Paper Trading Features:
   - Virtual account with configurable initial balance
   - Real-time P&L tracking with realistic brokerage and taxes
   - Position sizing and risk management

Usage Examples:
   # Paper Trading (Recommended for testing)
   python scripts/run_continuous_live_trading.py --mode paper --max-trades 5

   # Live Trading (Real money - Use with caution!)
   python scripts/run_continuous_live_trading.py --mode live --max-trades 3

   # Demo Mode (Simulation with mock data)
   python scripts/run_continuous_live_trading.py --mode demo --max-trades 10
"""

import os
import sys
import asyncio
import logging
import argparse
import signal
from pathlib import Path
from datetime import datetime, time as dt_time, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
from dataclasses import dataclass, asdict
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Import agents and utilities
from agents.market_data_agent import MarketDataAgent
from agents.signal_generation_agent import SignalGenerationAgent
from agents.risk_management_agent import RiskManagementAgent
from agents.execution_agent import ExecutionAgent
from agents.portfolio_manager_agent import PortfolioManagerAgent
from utils.event_bus import EventBus
from utils.config_manager import ConfigManager
from utils.logging_manager import LoggingManager

# Configure logging
logging_manager = LoggingManager()
logger = logging_manager.get_logger(__name__)

class TradingMode(Enum):
    """Trading modes"""
    LIVE = "live"      # Real trading with real money
    PAPER = "paper"    # Virtual trading with real data
    DEMO = "demo"      # Simulation with mock data

@dataclass
class TradingSystemConfig:
    """Trading system configuration"""
    mode: TradingMode
    max_daily_trades: int
    initial_balance: float
    selected_stocks: List[str]
    timeframes: List[str]
    risk_limits: Dict[str, float]
    
class ModernContinuousLiveTradingSystem:
    """
    Modern Continuous Live Trading System with Microservices Architecture
    
    Features:
    - Event-driven agent communication
    - Real WebSocket data streaming
    - Modern timeframe patterns
    - Scalable microservices design
    """
    
    def __init__(self, config: TradingSystemConfig):
        """Initialize modern continuous trading system"""
        
        self.config = config
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        # Initialize event bus for agent communication
        self.event_bus = EventBus()
        
        # Initialize agents
        self.agents = {}
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Performance tracking
        self.session_stats = {
            "signals_generated": 0,
            "trades_executed": 0,
            "trades_rejected": 0,
            "total_pnl": 0.0,
            "start_time": datetime.now(),
            "last_activity": datetime.now(),
            "websocket_messages": 0,
            "historical_data_points": 0
        }
        
        logger.info(f"MODERN CONTINUOUS LIVE TRADING SYSTEM initialized")
        logger.info(f"[MODE] Trading mode: {self.config.mode.value.upper()}")
        logger.info(f"[SESSION] Session ID: {self.session_id}")
    
    async def initialize_agents(self) -> bool:
        """Initialize all trading agents"""
        try:
            logger.info("[INIT] Initializing trading agents...")
            
            # Initialize Market Data Agent
            self.agents['market_data'] = MarketDataAgent(
                event_bus=self.event_bus,
                config=self.config,
                session_id=self.session_id
            )
            
            # Initialize Signal Generation Agent
            self.agents['signal_generation'] = SignalGenerationAgent(
                event_bus=self.event_bus,
                config=self.config,
                session_id=self.session_id
            )
            
            # Initialize Risk Management Agent
            self.agents['risk_management'] = RiskManagementAgent(
                event_bus=self.event_bus,
                config=self.config,
                session_id=self.session_id
            )
            
            # Initialize Execution Agent
            self.agents['execution'] = ExecutionAgent(
                event_bus=self.event_bus,
                config=self.config,
                session_id=self.session_id
            )
            
            # Initialize Portfolio Manager Agent
            self.agents['portfolio_manager'] = PortfolioManagerAgent(
                event_bus=self.event_bus,
                config=self.config,
                session_id=self.session_id
            )
            
            # Initialize all agents
            for agent_name, agent in self.agents.items():
                success = await agent.initialize()
                if not success:
                    logger.error(f"[ERROR] Failed to initialize {agent_name} agent")
                    return False
                logger.info(f"[SUCCESS] {agent_name} agent initialized")
            
            logger.info("[SUCCESS] All agents initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agents: {e}")
            return False
    
    async def start_trading_session(self):
        """Start the continuous trading session"""
        try:
            logger.info("[START] Starting continuous trading session...")
            
            # Set running flag
            self.running = True
            
            # Start all agents
            agent_tasks = []
            for agent_name, agent in self.agents.items():
                task = asyncio.create_task(agent.start())
                agent_tasks.append(task)
                logger.info(f"[START] {agent_name} agent started")
            
            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            logger.info("[SUCCESS] Trading session started successfully")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Stop all agents
            logger.info("[SHUTDOWN] Stopping all agents...")
            for agent_name, agent in self.agents.items():
                await agent.stop()
                logger.info(f"[STOP] {agent_name} agent stopped")
            
            # Cancel all tasks
            for task in agent_tasks:
                task.cancel()
            
            # Wait for all tasks to complete
            await asyncio.gather(*agent_tasks, return_exceptions=True)
            
            logger.info("[SUCCESS] Trading session ended gracefully")
            
        except Exception as e:
            logger.error(f"[ERROR] Error in trading session: {e}")
        finally:
            self.running = False
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()
    
    async def generate_session_report(self):
        """Generate final session report"""
        try:
            session_duration = datetime.now() - self.session_stats["start_time"]
            
            report = {
                "session_id": self.session_id,
                "mode": self.config.mode.value,
                "duration_minutes": session_duration.total_seconds() / 60,
                "stats": self.session_stats,
                "config": asdict(self.config),
                "timestamp": datetime.now().isoformat()
            }
            
            # Save report
            report_dir = project_root / "reports" / "continuous_trading"
            report_dir.mkdir(parents=True, exist_ok=True)
            
            report_file = report_dir / f"continuous_trading_{self.config.mode.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"[REPORT] Session report saved to {report_file}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate session report: {e}")

async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Modern Continuous Live Trading System')
    parser.add_argument('--mode', choices=['live', 'paper', 'demo'], default='paper',
                       help='Trading mode (default: paper)')
    parser.add_argument('--max-trades', type=int, default=5,
                       help='Maximum daily trades (default: 5)')
    parser.add_argument('--initial-balance', type=float, default=100000,
                       help='Initial balance for paper trading (default: 100000)')
    parser.add_argument('--stocks', nargs='+', 
                       default=['RELIANCE', 'HDFCBANK', 'TCS', 'INFY', 'ICICIBANK'],
                       help='List of stocks to trade')
    
    args = parser.parse_args()
    
    # Create configuration
    config = TradingSystemConfig(
        mode=TradingMode(args.mode),
        max_daily_trades=args.max_trades,
        initial_balance=args.initial_balance,
        selected_stocks=args.stocks,
        timeframes=['1min', '2min', '3min', '5min', '10min', '15min', '30min', '1hr'],
        risk_limits={
            'max_position_size': 0.1,  # 10% of portfolio
            'max_daily_loss': 0.05,    # 5% daily loss limit
            'max_drawdown': 0.15       # 15% maximum drawdown
        }
    )
    
    # Initialize trading system
    trading_system = ModernContinuousLiveTradingSystem(config)
    
    try:
        # Initialize agents
        if not await trading_system.initialize_agents():
            logger.error("[ERROR] Failed to initialize trading system")
            return
        
        # Start trading session
        await trading_system.start_trading_session()
        
    except KeyboardInterrupt:
        logger.info("[INTERRUPT] Keyboard interrupt received")
    except Exception as e:
        logger.error(f"[ERROR] Unexpected error: {e}")
    finally:
        # Generate final report
        await trading_system.generate_session_report()
        logger.info("[END] Trading system shutdown complete")

if __name__ == "__main__":
    asyncio.run(main())
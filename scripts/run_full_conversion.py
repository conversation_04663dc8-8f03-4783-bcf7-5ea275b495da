#!/usr/bin/env python3
"""
Production script to convert the full 67M row dataset
Optimized settings for large dataset processing
"""

import asyncio
import logging
import time
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from scripts
sys.path.append(str(Path(__file__).parent.parent))

from scripts.timeframe_converter import TimeframeConverter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('timeframe_conversion.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def main():
    """Main function for full dataset conversion"""
    
    # Configuration for production run
    input_file = "data/historical/historical_5min.parquet"
    output_dir = "data/historical"
    chunk_size = 600000  # Optimized for 67M rows - adjust based on available memory
    
    # Check if input file exists
    if not Path(input_file).exists():
        logger.error(f"Input file not found: {input_file}")
        return
    
    # Log system info
    logger.info("="*60)
    logger.info("STARTING FULL TIMEFRAME CONVERSION")
    logger.info("="*60)
    logger.info(f"Input file: {input_file}")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Chunk size: {chunk_size:,} rows")
    
    start_time = time.time()
    
    try:
        # Create converter and run
        converter = TimeframeConverter(input_file, output_dir, chunk_size)
        await converter.convert_timeframes()
        
        # Calculate total time
        end_time = time.time()
        total_time = end_time - start_time
        
        logger.info("="*60)
        logger.info("CONVERSION COMPLETED SUCCESSFULLY!")
        logger.info("="*60)
        logger.info(f"Total processing time: {total_time:.2f} seconds ({total_time/60:.2f} minutes)")
        
        # Log final file information
        timeframes = {
            "15min": "historical_15min.parquet",
            "30min": "historical_30min.parquet", 
            "1hr": "historical_1hr.parquet"
        }
        
        for tf_name, filename in timeframes.items():
            filepath = Path(output_dir) / filename
            if filepath.exists():
                file_size = filepath.stat().st_size / (1024 * 1024)  # MB
                logger.info(f"{tf_name} file: {filename} ({file_size:.2f} MB)")
            else:
                logger.warning(f"{tf_name} file not found: {filename}")
        
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        raise
    
    logger.info("Process completed. Check the data directory for output files.")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
SIMPLE TRADING SYSTEM RUNNER
Bypasses the ML workflow to avoid hanging issues during development/testing.

This script runs the core trading system without the complex ML-driven stock selection.
"""

import asyncio
import logging
import argparse
import signal
import os
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / '.env')

from core.event_system import EventBus, EventTypes
from agents.clean_market_data_agent import CleanMarketDataAgent
from agents.clean_signal_agent import CleanSignalAgent
from agents.modern_execution_agent import ModernExecutionAgent

logger = logging.getLogger(__name__)

class SimpleTradingSystem:
    """Simple trading system without ML workflow complexity."""
    
    def __init__(self, mode: str = "paper", selected_stocks: List[str] = None):
        self.mode = mode
        self.running = False
        self.session_id = f"simple_trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.event_bus = EventBus()
        self.config = self._create_config(mode, selected_stocks)
        self.agents: Dict[str, Any] = {}

    def _create_config(self, mode: str, selected_stocks: List[str] = None) -> Any:
        """Creates a simple config object."""
        class Config:
            def __init__(self, mode, selected_stocks):
                self.mode = mode
                self.selected_stocks = selected_stocks or [
                    "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK", 
                    "HINDUNILVR", "ITC", "SBIN", "BHARTIARTL", "KOTAKBANK",
                    "LT", "ASIANPAINT", "MARUTI", "HCLTECH", "AXISBANK"
                ]
                self.timeframes = ["1min", "3min", "5min", "15min"]
                self.initial_balance = 1000000
        return Config(mode, selected_stocks)

    async def start(self):
        """Main startup sequence for the trading system."""
        logger.info(f"[START] Starting Simple Trading System in {self.mode} mode...")
        logger.info(f"[INFO] Session ID: {self.session_id}")
        logger.info(f"[INFO] Selected stocks: {', '.join(self.config.selected_stocks)}")
        
        await self.event_bus.start_processor()

        # 1. Initialize Market Data Agent
        self.agents['market_data'] = CleanMarketDataAgent(self.event_bus, self.config, self.session_id)
        await self.agents['market_data'].startup()

        # 2. Initialize Signal Generation Agent
        self.agents['signal_generation'] = CleanSignalAgent(self.event_bus, self.config, self.session_id)
        if not await self.agents['signal_generation'].startup():
            raise Exception("Failed to start signal generation agent")

        # 3. Initialize Execution Agent (if available)
        try:
            self.agents['execution'] = ModernExecutionAgent(self.event_bus, self.config, self.session_id)
            if not await self.agents['execution'].start():
                logger.warning("[WARN] Execution agent failed to start, continuing without it")
                del self.agents['execution']
        except Exception as e:
            logger.warning(f"[WARN] Could not initialize execution agent: {e}")

        # 4. Broadcast the universe to all agents
        await self.event_bus.publish("BROADCAST_UNIVERSE", {'universe': self.config.selected_stocks}, source="TradingSystem")

        self.running = True
        logger.info("[SUCCESS] All agents started. System is now in live trading mode.")

        # Main loop for health checks with proper interrupt handling
        try:
            while self.running:
                await asyncio.sleep(60)  # Health check every minute
                await self._check_agent_health()
        except KeyboardInterrupt:
            logger.info("[INTERRUPT] KeyboardInterrupt received, initiating graceful shutdown...")
            self.running = False
            await self.stop()
        except Exception as e:
            logger.error(f"[ERROR] Unexpected error in main loop: {e}")
            self.running = False
            await self.stop()

    async def _check_agent_health(self):
        """Check health of all agents"""
        try:
            health_statuses = await asyncio.gather(
                *(agent.health_check() for agent in self.agents.values()), 
                return_exceptions=True
            )
            for status in health_statuses:
                if isinstance(status, Exception):
                    logger.warning(f"[HEALTH] Health check failed: {status}")
                elif not status.get('healthy', False):
                    logger.warning(f"[HEALTH] Agent {status.get('agent_name', 'unknown')} is unhealthy")
        except Exception as e:
            logger.error(f"[HEALTH] Health check error: {e}")

    async def stop(self):
        """Stop the trading system"""
        logger.info("[STOP] Stopping Simple Trading System...")
        self.running = False
        
        # Stop all agents
        stop_tasks = []
        for agent_name, agent in self.agents.items():
            try:
                if hasattr(agent, 'shutdown'):
                    stop_tasks.append(agent.shutdown())
                elif hasattr(agent, 'stop'):
                    stop_tasks.append(agent.stop())
                logger.info(f"[STOP] Stopping {agent_name} agent...")
            except Exception as e:
                logger.error(f"[ERROR] Failed to stop {agent_name}: {e}")
        
        if stop_tasks:
            await asyncio.gather(*stop_tasks, return_exceptions=True)
        
        await self.event_bus.stop_processor()
        logger.info("[SUCCESS] Simple Trading System stopped.")

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        if sys.platform != "win32":
            try:
                loop = asyncio.get_running_loop()
                for sig in (signal.SIGINT, signal.SIGTERM):
                    loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(self._signal_handler(s)))
                logger.info("[SIGNAL] Unix signal handlers registered successfully")
            except Exception as e:
                logger.warning(f"[SIGNAL] Failed to register Unix signal handlers: {e}")
        else:
            logger.info("[SIGNAL] Windows platform detected - KeyboardInterrupt will be handled in main loop")

    async def _signal_handler(self, sig):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {sig.name}, initiating graceful shutdown...")
        self.running = False
        await self.stop()

async def main():
    parser = argparse.ArgumentParser(description='Simple Trading System (No ML)')
    parser.add_argument('--mode', choices=['paper', 'live'], default='paper', help='Trading mode')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='Logging level')
    parser.add_argument('--stocks', nargs='+', help='List of stocks to trade (optional)')
    args = parser.parse_args()

    # Setup logging
    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)
    logging.basicConfig(
        level=args.log_level.upper(),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_dir / 'simple_trading_system.log')
        ]
    )

    # Create and start trading system
    trading_system = SimpleTradingSystem(args.mode, args.stocks)
    trading_system._setup_signal_handlers()

    try:
        await trading_system.start()
    except KeyboardInterrupt:
        logger.info("[INTERRUPT] KeyboardInterrupt in main(), initiating shutdown...")
        if trading_system.running:
            await trading_system.stop()
    except Exception as e:
        logger.error(f"[FATAL] System error: {e}", exc_info=True)
        if trading_system.running:
            await trading_system.stop()
    finally:
        if trading_system.running:
            await trading_system.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[EXIT] System shutdown complete.")
    except Exception as e:
        logger.error(f"[FATAL] Unhandled exception: {e}", exc_info=True)
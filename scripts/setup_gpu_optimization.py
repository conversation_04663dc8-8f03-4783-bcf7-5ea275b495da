#!/usr/bin/env python3
"""
🚀 GPU Optimization Setup Script
Automated setup for enhanced GPU optimization in trading system
"""

import subprocess
import sys
import os
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GPUOptimizationSetup:
    """Setup GPU optimization environment"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.requirements_installed = False
        
    def run_setup(self):
        """Run complete GPU optimization setup"""
        print("\n" + "="*80)
        print("🚀 GPU OPTIMIZATION SETUP FOR TRADING SYSTEM")
        print("="*80)
        
        try:
            # Check system requirements
            self._check_system_requirements()
            
            # Install required packages
            self._install_gpu_packages()
            
            # Verify installations
            self._verify_installations()
            
            # Setup configuration
            self._setup_configuration()
            
            # Run initial benchmark
            self._run_initial_benchmark()
            
            print("\n✅ GPU optimization setup completed successfully!")
            print("🚀 Your system is now optimized for maximum GPU performance!")
            
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            print(f"\n❌ Setup failed: {e}")
            sys.exit(1)
    
    def _check_system_requirements(self):
        """Check system requirements"""
        print("\n📋 CHECKING SYSTEM REQUIREMENTS")
        print("-" * 40)
        
        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 8):
            raise RuntimeError(f"Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # Check CUDA availability
        try:
            import torch
            if torch.cuda.is_available():
                device_name = torch.cuda.get_device_name(0)
                cuda_version = torch.version.cuda
                print(f"✅ CUDA {cuda_version} - {device_name}")
            else:
                print("⚠️  CUDA not available - GPU optimization will be limited")
        except ImportError:
            print("⚠️  PyTorch not installed - will install during setup")
        
        # Check available disk space
        disk_usage = os.statvfs(self.project_root)
        free_space_gb = (disk_usage.f_frsize * disk_usage.f_bavail) / (1024**3)
        if free_space_gb < 5:
            raise RuntimeError(f"Insufficient disk space: {free_space_gb:.1f}GB available, 5GB required")
        print(f"✅ Disk space: {free_space_gb:.1f}GB available")
    
    def _install_gpu_packages(self):
        """Install required GPU packages"""
        print("\n📦 INSTALLING GPU PACKAGES")
        print("-" * 40)
        
        # Core packages
        core_packages = [
            "torch>=2.0.0",
            "torchvision",
            "torchaudio",
            "polars>=0.20.0",
            "numpy>=1.21.0",
            "numba>=0.58.0",
            "vectorbt>=0.25.0",
            "optuna>=3.0.0",
            "pyyaml>=6.0",
            "asyncio-throttle",
            "aiofiles"
        ]
        
        # Try to install CuPy (CUDA-specific)
        cupy_packages = [
            "cupy-cuda11x",  # For CUDA 11.x
            "cupy-cuda12x"   # For CUDA 12.x
        ]
        
        # Optional packages for monitoring
        optional_packages = [
            "pynvml",  # NVIDIA management library
            "psutil",  # System monitoring
            "matplotlib",  # Plotting
            "seaborn"  # Enhanced plotting
        ]
        
        # Install core packages
        for package in core_packages:
            self._install_package(package)
        
        # Try to install CuPy
        cupy_installed = False
        for cupy_package in cupy_packages:
            if self._install_package(cupy_package, optional=True):
                cupy_installed = True
                break
        
        if not cupy_installed:
            print("⚠️  CuPy installation failed - some GPU optimizations will be unavailable")
            print("   You can manually install CuPy later with:")
            print("   pip install cupy-cuda11x  # For CUDA 11.x")
            print("   pip install cupy-cuda12x  # For CUDA 12.x")
        
        # Install optional packages
        for package in optional_packages:
            self._install_package(package, optional=True)
        
        self.requirements_installed = True
    
    def _install_package(self, package: str, optional: bool = False) -> bool:
        """Install a single package"""
        try:
            print(f"Installing {package}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                print(f"✅ {package} installed successfully")
                return True
            else:
                if optional:
                    print(f"⚠️  {package} installation failed (optional)")
                    return False
                else:
                    raise RuntimeError(f"Failed to install {package}: {result.stderr}")
                    
        except subprocess.TimeoutExpired:
            if optional:
                print(f"⚠️  {package} installation timed out (optional)")
                return False
            else:
                raise RuntimeError(f"Installation of {package} timed out")
        except Exception as e:
            if optional:
                print(f"⚠️  {package} installation error: {e} (optional)")
                return False
            else:
                raise RuntimeError(f"Failed to install {package}: {e}")
    
    def _verify_installations(self):
        """Verify package installations"""
        print("\n🔍 VERIFYING INSTALLATIONS")
        print("-" * 40)
        
        # Test imports
        test_imports = [
            ("torch", "PyTorch"),
            ("polars", "Polars"),
            ("numpy", "NumPy"),
            ("numba", "Numba"),
            ("vectorbt", "VectorBT"),
            ("optuna", "Optuna"),
            ("yaml", "PyYAML")
        ]
        
        for module, name in test_imports:
            try:
                __import__(module)
                print(f"✅ {name}")
            except ImportError as e:
                raise RuntimeError(f"Failed to import {name}: {e}")
        
        # Test CuPy (optional)
        try:
            import cupy
            print("✅ CuPy (GPU arrays)")
        except ImportError:
            print("⚠️  CuPy not available (GPU arrays disabled)")
        
        # Test CUDA functionality
        try:
            import torch
            if torch.cuda.is_available():
                # Test basic CUDA operation
                x = torch.randn(100, 100, device='cuda')
                y = torch.matmul(x, x.T)
                torch.cuda.synchronize()
                print("✅ CUDA operations")
            else:
                print("⚠️  CUDA not available")
        except Exception as e:
            print(f"⚠️  CUDA test failed: {e}")
    
    def _setup_configuration(self):
        """Setup configuration files"""
        print("\n⚙️  SETTING UP CONFIGURATION")
        print("-" * 40)
        
        # Check if enhanced config exists
        enhanced_config_path = self.project_root / "config" / "enhanced_cuda_optimization_config.yaml"
        if enhanced_config_path.exists():
            print("✅ Enhanced CUDA configuration already exists")
        else:
            print("⚠️  Enhanced CUDA configuration not found")
            print("   Please ensure enhanced_cuda_optimization_config.yaml is in config/")
        
        # Create logs directory
        logs_dir = self.project_root / "logs"
        logs_dir.mkdir(exist_ok=True)
        print("✅ Logs directory created")
        
        # Create data directories
        data_dirs = ["data/features", "data/backtest", "data/models"]
        for data_dir in data_dirs:
            dir_path = self.project_root / data_dir
            dir_path.mkdir(parents=True, exist_ok=True)
        print("✅ Data directories created")
    
    def _run_initial_benchmark(self):
        """Run initial GPU benchmark"""
        print("\n🏃 RUNNING INITIAL BENCHMARK")
        print("-" * 40)
        
        try:
            # Import and run benchmark
            benchmark_script = self.project_root / "scripts" / "gpu_benchmark.py"
            if benchmark_script.exists():
                print("Running GPU benchmark...")
                result = subprocess.run(
                    [sys.executable, str(benchmark_script)],
                    cwd=str(self.project_root),
                    timeout=120  # 2 minute timeout
                )
                
                if result.returncode == 0:
                    print("✅ Benchmark completed successfully")
                else:
                    print("⚠️  Benchmark completed with warnings")
            else:
                print("⚠️  Benchmark script not found, skipping")
                
        except subprocess.TimeoutExpired:
            print("⚠️  Benchmark timed out, but setup is complete")
        except Exception as e:
            print(f"⚠️  Benchmark failed: {e}, but setup is complete")
    
    def print_usage_instructions(self):
        """Print usage instructions"""
        print("\n" + "="*80)
        print("📖 USAGE INSTRUCTIONS")
        print("="*80)
        
        print("\n🚀 To run with GPU optimization:")
        print("   python main.py --agent strategy_evolution --optimize_gpu")
        print("   python main.py --agent backtesting --optimize_gpu")
        
        print("\n📊 To run GPU benchmark:")
        print("   python scripts/gpu_benchmark.py")
        
        print("\n⚙️  Configuration files:")
        print("   config/enhanced_cuda_optimization_config.yaml - Main GPU config")
        print("   config/cuda_optimization_config.yaml - Original config")
        
        print("\n🔧 To customize GPU settings:")
        print("   Edit enhanced_cuda_optimization_config.yaml")
        print("   Adjust batch_size, strategies_per_batch, memory_fraction")
        
        print("\n📈 Performance monitoring:")
        print("   GPU performance is monitored automatically")
        print("   Check logs/enhanced_strategy_evolution.log for details")
        
        print("\n" + "="*80)

def main():
    """Main setup function"""
    setup = GPUOptimizationSetup()
    
    try:
        setup.run_setup()
        setup.print_usage_instructions()
    except KeyboardInterrupt:
        print("\n\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
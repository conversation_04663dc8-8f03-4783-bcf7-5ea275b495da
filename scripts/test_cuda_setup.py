#!/usr/bin/env python3
"""
🚀 CUDA Setup Test Script
Tests CUDA installation and optimization for backtesting
"""

import sys
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pytorch_cuda():
    """Test PyTorch CUDA setup"""
    logger.info("🔍 Testing PyTorch CUDA...")
    
    try:
        import torch
        
        logger.info(f"✅ PyTorch version: {torch.__version__}")
        logger.info(f"✅ CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            logger.info(f"✅ CUDA version: {torch.version.cuda}")
            logger.info(f"✅ GPU count: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                logger.info(f"✅ GPU {i}: {props.name}")
                logger.info(f"   Memory: {props.total_memory / 1024**3:.1f} GB")
                logger.info(f"   Compute: {props.major}.{props.minor}")
            
            # Test basic operations
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.matmul(x, y)
            logger.info("✅ Basic CUDA operations working")
            
            return True
        else:
            logger.warning("⚠️ CUDA not available in PyTorch")
            return False
            
    except ImportError:
        logger.error("❌ PyTorch not installed")
        return False
    except Exception as e:
        logger.error(f"❌ PyTorch CUDA test failed: {e}")
        return False

def test_numba_cuda():
    """Test Numba CUDA setup"""
    logger.info("🔍 Testing Numba CUDA...")
    
    try:
        from numba import cuda, jit
        import numpy as np
        
        # Test CUDA detection
        cuda.detect()
        logger.info("✅ Numba CUDA detected")
        
        # Get device info
        device = cuda.get_current_device()
        logger.info(f"✅ Device: {device.name.decode()}")
        
        # Test memory info
        meminfo = cuda.current_context().get_memory_info()
        total_gb = meminfo[1] / 1024**3
        free_gb = meminfo[0] / 1024**3
        logger.info(f"✅ Memory: {free_gb:.1f} GB free / {total_gb:.1f} GB total")
        
        # Test basic kernel
        @cuda.jit
        def test_kernel(arr):
            idx = cuda.grid(1)
            if idx < arr.size:
                arr[idx] = idx * 2
        
        # Test execution
        arr = np.zeros(1000, dtype=np.float32)
        d_arr = cuda.to_device(arr)
        
        threads_per_block = 256
        blocks_per_grid = (arr.size + threads_per_block - 1) // threads_per_block
        
        test_kernel[blocks_per_grid, threads_per_block](d_arr)
        result = d_arr.copy_to_host()
        
        if result[10] == 20:  # Check if kernel worked
            logger.info("✅ Numba CUDA kernel execution working")
            return True
        else:
            logger.error("❌ Numba CUDA kernel execution failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Numba CUDA test failed: {e}")
        return False

def test_polars_optimization():
    """Test Polars optimization"""
    logger.info("🔍 Testing Polars optimization...")
    
    try:
        import polars as pl
        import numpy as np
        
        logger.info(f"✅ Polars version: {pl.__version__}")
        
        # Test large dataset operations
        size = 100000
        data = {
            'a': np.random.randn(size),
            'b': np.random.randn(size),
            'c': np.random.randn(size)
        }
        
        df = pl.DataFrame(data)
        
        # Test operations
        result = df.with_columns([
            (pl.col('a') * pl.col('b')).alias('ab'),
            (pl.col('a') + pl.col('c')).alias('ac')
        ]).filter(pl.col('ab') > 0).select(['ab', 'ac']).mean()
        
        logger.info("✅ Polars operations working")
        logger.info(f"✅ Result shape: {result.shape}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Polars test failed: {e}")
        return False

def test_vectorbt():
    """Test VectorBT GPU acceleration"""
    logger.info("🔍 Testing VectorBT...")
    
    try:
        import vectorbt as vbt
        import numpy as np
        
        logger.info(f"✅ VectorBT version: {vbt.__version__}")
        
        # Test basic portfolio simulation
        size = 10000
        prices = np.random.randn(size).cumsum() + 100
        entries = np.random.choice([True, False], size, p=[0.01, 0.99])
        exits = np.random.choice([True, False], size, p=[0.01, 0.99])
        
        pf = vbt.Portfolio.from_signals(
            prices,
            entries,
            exits,
            init_cash=10000,
            fees=0.001
        )
        
        stats = pf.stats()
        logger.info("✅ VectorBT portfolio simulation working")
        logger.info(f"✅ Total return: {stats['Total Return [%]']:.2f}%")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ VectorBT test failed: {e}")
        return False

def test_cuda_optimizer():
    """Test our CUDA optimizer"""
    logger.info("🔍 Testing CUDA Optimizer...")
    
    try:
        from utils.cuda_optimizer import get_cuda_optimizer, optimize_cuda_for_backtesting
        
        optimizer = get_cuda_optimizer()
        logger.info(f"✅ CUDA Optimizer initialized")
        logger.info(f"✅ CUDA available: {optimizer.cuda_available}")
        
        if optimizer.cuda_available:
            memory_info = optimizer.get_memory_info()
            logger.info(f"✅ GPU: {memory_info.get('device_name', 'Unknown')}")
            logger.info(f"✅ Memory: {memory_info.get('memory_total', 0):.1f} GB")
        
        # Test optimization
        optimizations = optimize_cuda_for_backtesting()
        logger.info(f"✅ Optimizations applied: {optimizations.get('cuda_enabled', False)}")
        
        # Test batch size calculation
        batch_size = optimizer.get_optimal_batch_size(50000)
        logger.info(f"✅ Optimal batch size for 50k items: {batch_size}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ CUDA Optimizer test failed: {e}")
        return False

def test_gpu_memory_manager():
    """Test GPU memory manager"""
    logger.info("🔍 Testing GPU Memory Manager...")
    
    try:
        from utils.gpu_memory_manager import get_gpu_memory_manager, cleanup_gpu_memory
        
        manager = get_gpu_memory_manager()
        logger.info("✅ GPU Memory Manager initialized")
        
        # Test memory stats
        stats = manager.get_memory_stats()
        if stats.get('cuda_available'):
            logger.info(f"✅ GPU Memory: {stats.get('total_gb', 0):.1f} GB total")
            logger.info(f"✅ Utilization: {stats.get('utilization_pct', 0):.1f}%")
        
        # Test cleanup
        cleanup_result = cleanup_gpu_memory()
        logger.info(f"✅ Memory cleanup: {cleanup_result.get('cleaned', False)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ GPU Memory Manager test failed: {e}")
        return False

def main():
    """Run all CUDA tests"""
    logger.info("🚀 Starting CUDA Setup Tests")
    logger.info("=" * 50)
    
    tests = [
        ("PyTorch CUDA", test_pytorch_cuda),
        ("Numba CUDA", test_numba_cuda),
        ("Polars Optimization", test_polars_optimization),
        ("VectorBT", test_vectorbt),
        ("CUDA Optimizer", test_cuda_optimizer),
        ("GPU Memory Manager", test_gpu_memory_manager)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("🏁 Test Results Summary")
    logger.info("="*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! CUDA setup is optimal for backtesting.")
    elif passed >= total * 0.7:
        logger.info("⚡ Most tests passed. CUDA acceleration should work well.")
    else:
        logger.warning("⚠️ Several tests failed. Consider CPU-only mode or check CUDA installation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
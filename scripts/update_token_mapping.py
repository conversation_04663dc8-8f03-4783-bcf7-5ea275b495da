#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to download the latest instrument master from Angel One and update token mapping.
This ensures we have the most current tokens for historical data download.
"""

import json
import requests
import sys
import logging
from pathlib import Path
from typing import Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def download_instrument_master() -> Dict[str, Any]:
    """Download the latest instrument master from Angel One"""
    url = "https://margincalculator.angelone.in/OpenAPI_File/files/OpenAPIScripMaster.json"
    
    try:
        logger.info("Downloading latest instrument master from Angel One...")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"Downloaded {len(data)} instruments")
        return data
        
    except Exception as e:
        logger.error(f"Failed to download instrument master: {e}")
        return []

def create_token_mapping(instruments: list) -> Dict[str, str]:
    """Create symbol to token mapping from instrument data"""
    mapping = {}
    
    for instrument in instruments:
        try:
            token = instrument.get('token', '')
            symbol = instrument.get('symbol', '')
            name = instrument.get('name', '')
            exch_seg = instrument.get('exch_seg', '')
            instrumenttype = instrument.get('instrumenttype', '')
            
            if not token or not symbol:
                continue
            
            # Create multiple key variations for better lookup
            keys_to_add = []
            
            # For equity instruments
            if exch_seg == 'NSE' and (instrumenttype == '' or 'EQ' in symbol):
                # Standard equity format
                if symbol.endswith('-EQ'):
                    base_symbol = symbol.replace('-EQ', '')
                    keys_to_add.extend([
                        f"{base_symbol}-EQ_NSE",
                        f"{base_symbol}_NSE",
                        base_symbol
                    ])
                else:
                    keys_to_add.extend([
                        f"{symbol}-EQ_NSE",
                        f"{symbol}_NSE",
                        symbol
                    ])
            
            # For other instruments, use as-is
            keys_to_add.extend([
                f"{symbol}_NSE",
                symbol
            ])
            
            # Add all key variations
            for key in keys_to_add:
                if key not in mapping:  # Don't overwrite existing mappings
                    mapping[key] = token
                    
        except Exception as e:
            logger.warning(f"Error processing instrument {instrument}: {e}")
            continue
    
    logger.info(f"Created {len(mapping)} token mappings")
    return mapping

def save_token_mapping(mapping: Dict[str, str], output_file: Path):
    """Save token mapping to JSON file"""
    try:
        output_data = {
            "symbol_to_token": mapping,
            "metadata": {
                "source": "Angel One OpenAPI ScripMaster",
                "url": "https://margincalculator.angelone.in/OpenAPI_File/files/OpenAPIScripMaster.json",
                "total_mappings": len(mapping)
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        logger.info(f"Saved {len(mapping)} token mappings to {output_file}")
        
    except Exception as e:
        logger.error(f"Failed to save token mapping: {e}")

def main():
    """Main function"""
    project_root = Path(__file__).parent.parent
    output_file = project_root / 'token_mapping_new.json'
    
    # Download latest instrument master
    instruments = download_instrument_master()
    if not instruments:
        logger.error("Failed to download instrument master")
        return 1
    
    # Create token mapping
    mapping = create_token_mapping(instruments)
    if not mapping:
        logger.error("Failed to create token mapping")
        return 1
    
    # Save to file
    save_token_mapping(mapping, output_file)
    
    # Show some sample mappings
    logger.info("\nSample token mappings:")
    sample_symbols = ['RELIANCE', 'TCS', 'INFY', 'SBIN', 'ICICIBANK']
    for symbol in sample_symbols:
        keys_to_check = [
            f"{symbol}-EQ_NSE",
            f"{symbol}_NSE", 
            symbol
        ]
        
        found_token = None
        found_key = None
        for key in keys_to_check:
            if key in mapping:
                found_token = mapping[key]
                found_key = key
                break
        
        if found_token:
            logger.info(f"  {symbol}: {found_token} (via {found_key})")
        else:
            logger.warning(f"  {symbol}: NOT FOUND")
    
    logger.info(f"\nToken mapping saved to: {output_file}")
    logger.info("To use this mapping, replace the existing token_mapping.json file")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

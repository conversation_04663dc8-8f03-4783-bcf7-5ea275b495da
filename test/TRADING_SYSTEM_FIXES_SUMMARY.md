# Trading System Issues Analysis and Fixes - UPDATED

## Issues Identified and Fixed

### 1. **66 Active Orders vs 50 Trade Limit Issue** ✅ FIXED

**Problem**: System was showing 66 active orders when the limit is 50 trades per day.

**Root Cause**: 
- System was counting all orders (pending, active, rejected) instead of only completed trades
- Rejected orders were staying in active_orders instead of being moved to completed_orders

**Fixes Applied**:
- Modified `can_place_trade()` in `utils/paper_trading.py` to count only `EXECUTED` trades
- Updated `get_account_summary()` to count only completed trades
- Fixed `ModernExecutionAgent` to move rejected orders to completed_orders
- Updated debug logging to show "completed trades" instead of "all trades"

### 2. **Position Size Calculation Error** ✅ FIXED

**Problem**: AMBUJACEM order of Rs.99,666.87 exceeded Rs.50,000 limit but was still being attempted.

**Root Cause**: 
- Risk management agent wasn't properly checking the PAPER_TRADING_MAX_POSITION_SIZE environment variable
- Position sizing logic wasn't applying the more restrictive of the two limits (portfolio % vs absolute value)

**Fixes Applied**:
- Enhanced `_calculate_position_size()` in `RiskManagementAgent` to check both portfolio percentage and absolute value limits
- Added comprehensive debug logging for position size calculations
- Applied the more restrictive limit between portfolio-based and value-based limits

### 3. **Virtual Account Balance Logging** ✅ ADDED

**Problem**: No current virtual account balance logging in monitoring loop.

**Solution**: Added comprehensive virtual account logging to monitoring system showing:
- Current balance, Available margin, Used margin, Total PnL
- Today's completed trades count, Active positions, Return percentage

### 4. Order Statistics Inconsistency
**Problem**: 
- `orders_placed`: 0 (never incremented)
- `orders_cancelled`: 165 (high cancellation rate)
- `orders_filled`: 0 (no successful fills)
- `active_orders`: 15, `completed_orders`: 165, `active_positions`: 3

**Root Cause**: 
- `orders_placed` counter was only incremented in `_execute_order()` but orders were being cancelled before execution
- Orders were being auto-cancelled after 5 minutes, which was too aggressive

**Fixes Applied**:
1. **Fixed Order Counting Logic** (`agents/modern_execution_agent.py`):
   - Moved `orders_placed` increment to when order is created (line 217)
   - Removed duplicate increment from `_execute_order()` method

2. **Increased Order Timeout** (`agents/modern_execution_agent.py`):
   - Changed order cancellation timeout from 5 minutes to 30 minutes (line 449)
   - Added warning log when cancelling stale orders

### 2. Health Check Error
**Problem**: 
- Error: `'agent_name'` KeyError in health check
- Health check was failing when agent status didn't contain expected keys

**Root Cause**: 
- Health check method didn't handle exceptions or malformed status dictionaries
- Missing error handling for edge cases

**Fixes Applied**:
1. **Enhanced Health Check Robustness** (`scripts/run_clean_trading_system.py`):
   - Added `return_exceptions=True` to `asyncio.gather()` (line 829)
   - Added exception handling for health check failures
   - Added validation for status dictionary format
   - Used `status.get('agent_name', 'Unknown')` for safe key access

### 3. CleanMarketDataAgent Inactivity
**Problem**: 
- Agent showing as unhealthy: `inactive_for_3420s` (57 minutes)
- Agent wasn't updating its activity status regularly

**Root Cause**: 
- Agent wasn't calling `increment_message_count()` in event handlers
- No periodic heartbeat to maintain activity status

**Fixes Applied**:
1. **Added Activity Tracking** (`agents/clean_market_data_agent.py`):
   - Added `increment_message_count()` calls in `_handle_data_request()` (line 440)
   - Added `increment_message_count()` calls in `_handle_universe_update()` (line 501)

2. **Added Heartbeat Loop** (`agents/clean_market_data_agent.py`):
   - Created `_heartbeat_loop()` method (line 202) that runs every 60 seconds
   - Heartbeat updates activity status to prevent "inactive" warnings
   - Added heartbeat task to `start()` method (line 153)
   - Added heartbeat task cancellation to `stop()` method (line 641)

## Expected Results After Fixes

### Order Statistics Should Show:
- `orders_placed` > 0 (properly incremented when orders are created)
- Reduced `orders_cancelled` rate (30-minute timeout instead of 5-minute)
- Better `orders_filled` rate (orders have more time to execute)
- Consistent relationship between placed, filled, and cancelled orders

### Health Check Should Show:
- No more `'agent_name'` KeyError exceptions
- Proper error handling for malformed health status
- More robust health monitoring

### CleanMarketDataAgent Should Show:
- `healthy: true` status
- Regular activity updates from heartbeat
- No more "inactive_for_XXXs" warnings

## Testing Recommendations

1. **Monitor Order Flow**:
   ```bash
   python scripts\run_clean_trading_system_with_monitoring.py --mode paper --log-level INFO
   ```
   - Watch for proper `orders_placed` increments
   - Verify reduced cancellation rate
   - Check for successful order fills

2. **Verify Health Status**:
   - Confirm no more health check exceptions
   - Verify CleanMarketDataAgent shows as healthy
   - Monitor agent activity status

3. **Long-term Monitoring**:
   - Run system for extended periods (>1 hour)
   - Verify agents remain healthy over time
   - Check order statistics consistency

## Files Modified

1. `agents/modern_execution_agent.py`
   - Fixed order counting logic
   - Increased order timeout from 5 to 30 minutes

2. `scripts/run_clean_trading_system.py`
   - Enhanced health check error handling
   - Added exception handling and validation

3. `agents/clean_market_data_agent.py`
   - Added activity tracking to event handlers
   - Implemented heartbeat loop for continuous activity
   - Updated start/stop methods for heartbeat management

## Notes

- All fixes maintain backward compatibility
- No breaking changes to existing APIs
- Improved logging for better debugging
- Enhanced error handling throughout the system
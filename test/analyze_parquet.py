#!/usr/bin/env python3
"""
Analyze the historical_5min.parquet file to check row count and data structure
"""

import polars as pl
import pandas as pd
import os
import sys
from pathlib import Path

# Add parent directory to path to access the data folder
sys.path.append('..')

def analyze_parquet_file():
    """Analyze the parquet file comprehensively"""
    
    parquet_path = "data/historical_5min.parquet"
    
    print("=" * 80)
    print("PARQUET FILE ANALYSIS")
    print("=" * 80)
    
    # Check if file exists
    if not os.path.exists(parquet_path):
        print(f"❌ File not found: {parquet_path}")
        return
    
    # Get file size
    file_size = os.path.getsize(parquet_path)
    file_size_mb = file_size / (1024 * 1024)
    file_size_gb = file_size_mb / 1024
    
    print(f"📁 File: {parquet_path}")
    print(f"📊 File size: {file_size_gb:.2f}GB ({file_size_mb:.1f}MB)")
    
    try:
        # Read with polars for efficiency
        print("\n🔍 Reading parquet file with Polars...")
        df = pl.read_parquet(parquet_path)
        
        # Basic info
        print(f"\n📈 BASIC INFORMATION:")
        print(f"   Total rows: {len(df):,}")
        print(f"   Total columns: {len(df.columns)}")
        print(f"   Memory usage: {df.estimated_size('mb'):.1f}MB")
        
        # Column information
        print(f"\n📋 COLUMN INFORMATION:")
        schema = df.schema
        for i, (col_name, dtype) in enumerate(schema.items(), 1):
            print(f"   {i:2d}. {col_name:<20} : {dtype}")
        
        # Data range analysis
        print(f"\n📅 DATA RANGE ANALYSIS:")
        
        # Check for datetime columns
        datetime_cols = [col for col, dtype in schema.items() if 'datetime' in col.lower() or dtype in [pl.Datetime, pl.Date]]
        
        if datetime_cols:
            for col in datetime_cols:
                try:
                    min_date = df[col].min()
                    max_date = df[col].max()
                    print(f"   {col}: {min_date} to {max_date}")
                except:
                    print(f"   {col}: Could not determine range")
        else:
            print("   No datetime columns detected")
        
        # Symbol analysis (if exists)
        if 'symbol' in df.columns:
            print(f"\n🏷️  SYMBOL ANALYSIS:")
            unique_symbols = df['symbol'].n_unique()
            print(f"   Unique symbols: {unique_symbols}")
            
            # Top symbols by row count
            symbol_counts = df.group_by('symbol').agg(pl.count().alias('count')).sort('count', descending=True)
            print(f"   Top 10 symbols by row count:")
            for row in symbol_counts.head(10).iter_rows():
                symbol, count = row
                print(f"     {symbol}: {count:,} rows")
        
        # Timeframe analysis (if exists)
        if 'timeframe' in df.columns:
            print(f"\n⏰ TIMEFRAME ANALYSIS:")
            timeframe_counts = df.group_by('timeframe').agg(pl.count().alias('count')).sort('count', descending=True)
            print(f"   Timeframes:")
            for row in timeframe_counts.iter_rows():
                timeframe, count = row
                print(f"     {timeframe}: {count:,} rows")
        
        # Sample data
        print(f"\n📋 SAMPLE DATA (first 5 rows):")
        sample_df = df.head(5)
        
        # Convert to pandas for better display
        sample_pd = sample_df.to_pandas()
        print(sample_pd.to_string(index=False))
        
        # Data quality checks
        print(f"\n🔍 DATA QUALITY CHECKS:")
        
        # Check for null values
        null_counts = df.null_count()
        has_nulls = False
        for col in df.columns:
            null_count = null_counts[col][0]
            if null_count > 0:
                null_pct = (null_count / len(df)) * 100
                print(f"   {col}: {null_count:,} nulls ({null_pct:.2f}%)")
                has_nulls = True
        
        if not has_nulls:
            print("   ✅ No null values found")
        
        # Check for duplicates (if reasonable size)
        if len(df) < 10_000_000:  # Only for smaller datasets
            duplicate_count = len(df) - df.n_unique()
            if duplicate_count > 0:
                print(f"   ⚠️  Duplicate rows: {duplicate_count:,}")
            else:
                print("   ✅ No duplicate rows found")
        else:
            print("   ⏭️  Skipping duplicate check (dataset too large)")
        
        # Numeric column statistics
        numeric_cols = [col for col, dtype in schema.items() if dtype in [pl.Float32, pl.Float64, pl.Int8, pl.Int16, pl.Int32, pl.Int64, pl.UInt8, pl.UInt16, pl.UInt32, pl.UInt64]]
        
        if numeric_cols:
            print(f"\n📊 NUMERIC STATISTICS:")
            stats_df = df.select(numeric_cols).describe()
            stats_pd = stats_df.to_pandas()
            print(stats_pd.to_string())
        
        # Estimate original CSV size
        print(f"\n💾 COMPRESSION ANALYSIS:")
        
        # Rough estimate: parquet is typically 5-10x smaller than CSV
        estimated_csv_size_gb = file_size_gb * 7  # Conservative estimate
        compression_ratio = estimated_csv_size_gb / file_size_gb if file_size_gb > 0 else 0
        
        print(f"   Parquet size: {file_size_gb:.2f}GB")
        print(f"   Estimated original CSV size: {estimated_csv_size_gb:.2f}GB")
        print(f"   Estimated compression ratio: {compression_ratio:.1f}x")
        print(f"   Space saved: {estimated_csv_size_gb - file_size_gb:.2f}GB")
        
        # Performance metrics
        print(f"\n⚡ PERFORMANCE METRICS:")
        
        # Estimate rows per MB
        rows_per_mb = len(df) / file_size_mb if file_size_mb > 0 else 0
        print(f"   Rows per MB: {rows_per_mb:,.0f}")
        
        # Estimate processing time for different operations
        if len(df) > 0:
            # Simple read time estimate (based on typical SSD speeds)
            read_time_estimate = file_size_mb / 500  # ~500 MB/s for good SSD
            print(f"   Estimated read time: {read_time_estimate:.1f} seconds")
        
        print(f"\n✅ Analysis completed successfully!")
        
        return {
            'rows': len(df),
            'columns': len(df.columns),
            'file_size_gb': file_size_gb,
            'file_size_mb': file_size_mb
        }
        
    except Exception as e:
        print(f"❌ Error analyzing parquet file: {e}")
        return None

def compare_with_original_csv():
    """Compare with original CSV if it exists"""
    
    original_csv = r"C:\Users\<USER>\Documents\Intraday-AI\smartapi_data.csv"
    
    print(f"\n" + "=" * 80)
    print("COMPARISON WITH ORIGINAL CSV")
    print("=" * 80)
    
    if not os.path.exists(original_csv):
        print(f"❌ Original CSV not found: {original_csv}")
        return
    
    # Get CSV file size
    csv_size = os.path.getsize(original_csv)
    csv_size_gb = csv_size / (1024**3)
    
    print(f"📁 Original CSV: {original_csv}")
    print(f"📊 CSV size: {csv_size_gb:.2f}GB")
    
    # Try to estimate CSV row count (without loading the entire file)
    try:
        print(f"🔍 Estimating CSV row count...")
        
        # Sample first few lines to estimate
        with open(original_csv, 'r', encoding='utf-8') as f:
            # Skip header
            header = f.readline()
            
            # Sample 1000 lines
            total_chars = len(header)
            line_count = 1
            
            for i, line in enumerate(f):
                if i >= 1000:
                    break
                total_chars += len(line)
                line_count += 1
            
            if line_count > 1:
                avg_line_length = total_chars / line_count
                estimated_csv_rows = int(csv_size / avg_line_length)
                print(f"📈 Estimated CSV rows: {estimated_csv_rows:,}")
                
                # Compare with parquet
                parquet_path = "data/historical_5min.parquet"
                if os.path.exists(parquet_path):
                    parquet_info = analyze_parquet_file()
                    if parquet_info:
                        print(f"\n📊 COMPARISON:")
                        print(f"   CSV rows (estimated): {estimated_csv_rows:,}")
                        print(f"   Parquet rows (actual): {parquet_info['rows']:,}")
                        
                        if estimated_csv_rows > 0:
                            conversion_completeness = (parquet_info['rows'] / estimated_csv_rows) * 100
                            print(f"   Conversion completeness: {conversion_completeness:.1f}%")
                            
                            if conversion_completeness < 95:
                                print(f"   ⚠️  Warning: Conversion may be incomplete!")
                            else:
                                print(f"   ✅ Conversion appears complete")
                
    except Exception as e:
        print(f"❌ Error estimating CSV size: {e}")

def main():
    """Main analysis function"""
    
    # Create test directory if it doesn't exist
    test_dir = Path(".")
    test_dir.mkdir(exist_ok=True)
    
    # Analyze parquet file
    result = analyze_parquet_file()
    
    # Compare with original if available
    compare_with_original_csv()
    
    return result

if __name__ == "__main__":
    main()

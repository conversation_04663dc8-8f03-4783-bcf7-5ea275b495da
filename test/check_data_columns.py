#!/usr/bin/env python3
"""
Check what columns we actually have in our data
"""

import pandas as pd
import sys

def check_data_columns():
    """Check actual columns in our feature data"""
    print("🔍 Checking actual data columns...")
    
    try:
        # Load a small sample to check columns
        df = pd.read_csv("data/features/features_15min.csv", nrows=10)
        print(f"✅ Loaded sample data: {len(df)} rows, {len(df.columns)} columns")
        
        print(f"\n📊 Available columns ({len(df.columns)} total):")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # Check for specific columns our strategies need
        required_columns = [
            'Close', 'High', 'Low', 'Open', 'Volume',
            'RSI_14', 'RSI_5', 'MACD', 'MACD_signal',
            'EMA_5', 'EMA_10', 'EMA_13', 'EMA_20', 'EMA_21', 'EMA_30', 'EMA_50', 'EMA_100',
            'SMA_20', 'stoch_k', 'stoch_d', 'CCI', 'ADX', 'MFI',
            'BB_upper', 'BB_lower', 'ATR', 'VWAP', 'SuperTrend',
            'Donchian_high', 'Donchian_low', 'Pivot',
            'CPR_Top', 'CPR_Bottom', 'Support', 'Resistance'
        ]
        
        print(f"\n🔍 Checking required columns:")
        missing_columns = []
        available_columns = []
        
        for col in required_columns:
            if col in df.columns:
                available_columns.append(col)
                print(f"  ✅ {col}")
            else:
                missing_columns.append(col)
                print(f"  ❌ {col} - MISSING")
        
        print(f"\n📈 Summary:")
        print(f"  ✅ Available: {len(available_columns)}/{len(required_columns)} ({len(available_columns)/len(required_columns)*100:.1f}%)")
        print(f"  ❌ Missing: {len(missing_columns)} columns")
        
        if missing_columns:
            print(f"\n🚫 Missing columns:")
            for col in missing_columns:
                print(f"    - {col}")
        
        # Show sample data
        print(f"\n📋 Sample data (first 3 rows):")
        print(df.head(3).to_string())
        
        return df.columns.tolist(), available_columns, missing_columns
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return [], [], []

def suggest_column_mapping():
    """Suggest column name mappings"""
    print(f"\n💡 Suggested fixes:")
    print(f"1. Check if CPR_Top/CPR_Bottom are named differently (e.g., CPR_top, cpr_top)")
    print(f"2. Verify EMA_21 exists or use EMA_20 instead")
    print(f"3. Check case sensitivity (Close vs close)")
    print(f"4. Generate missing indicators if needed")

def main():
    """Main function"""
    print("🚀 Data Column Checker")
    print("=" * 50)
    
    all_cols, available, missing = check_data_columns()
    
    if missing:
        suggest_column_mapping()
    
    print(f"\n🎉 Check complete!")

if __name__ == "__main__":
    main()

"""
Pytest configuration and shared fixtures for enhanced backtesting tests
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil
from typing import Dict, List, Any

# Add the parent directory to the path so we can import the backtesting module
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.enhanced_backtesting_polars import (
    BacktestConfig, BacktestMode, RiskModel, MarketRegime,
    MultiStrategyBacktester, SmartBacktestEngine, AdvancedMetricsCalculator,
    CapitalRiskManager, MarketRegimeAnalyzer, ScenarioTester,
    ParameterOptimizer, BacktestLogger, BacktestVisualizer,
    SignalAnalyzer, LLMResultsExplainer
)

@pytest.fixture
def sample_market_data():
    """Generate sample market data for testing"""
    np.random.seed(42)  # For reproducible tests
    
    dates = [datetime(2023, 1, 1) + timedelta(days=i) for i in range(100)]
    
    # Generate realistic OHLCV data
    base_price = 100
    prices = []
    volumes = []
    
    for i in range(100):
        # Random walk with slight upward bias
        change = np.random.normal(0.001, 0.02)  # 0.1% daily drift, 2% volatility
        base_price *= (1 + change)
        
        # OHLC around close price
        close = base_price
        open_price = close * (1 + np.random.normal(0, 0.005))
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.randint(10000, 100000)
        
        prices.append([open_price, high, low, close])
        volumes.append(volume)
    
    return pl.DataFrame({
        'timestamp': dates,
        'open': [p[0] for p in prices],
        'high': [p[1] for p in prices],
        'low': [p[2] for p in prices],
        'close': [p[3] for p in prices],
        'volume': volumes
    })

@pytest.fixture
def sample_strategy():
    """Sample trading strategy for testing"""
    return {
        'name': 'Test SMA Crossover',
        'description': 'Simple moving average crossover strategy',
        'long': 'sma(20) > sma(50)',
        'short': 'sma(20) < sma(50)',
        'exit_long': 'sma(20) < sma(50)',
        'exit_short': 'sma(20) > sma(50)',
        'parameters': {
            'sma_fast': 20,
            'sma_slow': 50
        }
    }

@pytest.fixture
def sample_trades():
    """Generate sample trade data for testing"""
    np.random.seed(42)
    
    trades = []
    for i in range(20):
        pnl_pct = np.random.normal(0.5, 3.0)  # Average 0.5% return, 3% std
        trades.append({
            'entry_time': datetime(2023, 1, 1) + timedelta(days=i*3),
            'exit_time': datetime(2023, 1, 1) + timedelta(days=i*3+1),
            'entry_price': 100 + np.random.normal(0, 5),
            'exit_price': 100 + np.random.normal(0, 5),
            'pnl': pnl_pct * 1000,  # Assuming $1000 position size
            'pnl_pct': pnl_pct,
            'side': 'long' if np.random.random() > 0.5 else 'short',
            'quantity': 10
        })
    
    return trades

@pytest.fixture
def sample_backtest_config():
    """Sample backtest configuration"""
    return BacktestConfig(
        mode=BacktestMode.DETERMINISTIC,
        risk_model=RiskModel.FIXED_FRACTIONAL,
        initial_capital=100000,
        position_size=0.02,
        monte_carlo_runs=100
    )

@pytest.fixture
def sample_metrics():
    """Sample performance metrics for testing"""
    return {
        'total_return': 15.5,
        'total_trades': 20,
        'win_rate': 0.65,
        'profit_factor': 1.8,
        'sharpe_ratio': 1.2,
        'sortino_ratio': 1.5,
        'max_drawdown': 0.08,
        'volatility': 0.15,
        'var_95': 0.03,
        'cvar_95': 0.045,
        'calmar_ratio': 1.94,
        'average_win': 2.1,
        'average_loss': -1.2,
        'largest_win': 5.8,
        'largest_loss': -3.2
    }

@pytest.fixture
def temp_directory():
    """Create temporary directory for testing file operations"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)

@pytest.fixture
def multiple_strategies():
    """Multiple strategies for portfolio testing"""
    return [
        {
            'name': 'Strategy A',
            'description': 'Momentum strategy',
            'long': 'rsi(14) < 30',
            'short': 'rsi(14) > 70',
            'weight': 0.4
        },
        {
            'name': 'Strategy B', 
            'description': 'Mean reversion strategy',
            'long': 'sma(20) > sma(50)',
            'short': 'sma(20) < sma(50)',
            'weight': 0.6
        }
    ]

@pytest.fixture
def sample_backtest_results(sample_trades, sample_metrics):
    """Complete backtest results for testing"""
    return {
        'trades': sample_trades,
        'metrics': sample_metrics,
        'equity_curve': [100000 + i*1000 for i in range(len(sample_trades))],
        'drawdown_series': [max(0, i*0.01) for i in range(len(sample_trades))],
        'timestamps': [datetime(2023, 1, 1) + timedelta(days=i) for i in range(len(sample_trades))]
    }

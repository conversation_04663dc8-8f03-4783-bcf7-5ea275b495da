#!/usr/bin/env python3
"""
Debug script to analyze RSI_Reversal strategy issue
"""

import polars as pl
import yaml
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_and_analyze_data():
    """Load sample data and analyze RSI_Reversal strategy conditions"""
    
    # Load the 360ONE 15min data that was used in the test
    data_file = "c:/Users/<USER>/Documents/Equity/data/features/features_360ONE_15min.parquet"
    
    try:
        df = pl.read_parquet(data_file)
        logger.info(f"Loaded data: {len(df)} rows")
        logger.info(f"Columns: {df.columns}")
        
        # Check for required columns for RSI_Reversal strategy
        required_cols = ['rsi_14', 'close', 'ema_10']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            logger.error(f"Missing required columns: {missing_cols}")
            return
        
        # Show basic statistics for required columns
        for col in required_cols:
            logger.info(f"\n{col} statistics:")
            logger.info(f"  Min: {df[col].min()}")
            logger.info(f"  Max: {df[col].max()}")
            logger.info(f"  Mean: {df[col].mean()}")
            logger.info(f"  Null count: {df[col].is_null().sum()}")
            logger.info(f"  Sample values: {df[col].head(10).to_list()}")
        
        # Test RSI_Reversal long condition: rsi_14 < 30 and close > ema_10
        long_condition = (df['rsi_14'] < 30) & (df['close'] > df['ema_10'])
        long_signals = long_condition.sum()
        logger.info(f"\nRSI_Reversal LONG signals: {long_signals}")
        
        if long_signals > 0:
            # Show some examples where condition is met
            long_examples = df.filter(long_condition).head(5)
            logger.info("Long signal examples:")
            for i, row in enumerate(long_examples.iter_rows(named=True)):
                logger.info(f"  {i+1}. RSI: {row['rsi_14']:.2f}, Close: {row['close']:.2f}, EMA10: {row['ema_10']:.2f}")
        
        # Test RSI_Reversal short condition: rsi_14 > 70 and close < ema_10
        short_condition = (df['rsi_14'] > 70) & (df['close'] < df['ema_10'])
        short_signals = short_condition.sum()
        logger.info(f"\nRSI_Reversal SHORT signals: {short_signals}")
        
        if short_signals > 0:
            # Show some examples where condition is met
            short_examples = df.filter(short_condition).head(5)
            logger.info("Short signal examples:")
            for i, row in enumerate(short_examples.iter_rows(named=True)):
                logger.info(f"  {i+1}. RSI: {row['rsi_14']:.2f}, Close: {row['close']:.2f}, EMA10: {row['ema_10']:.2f}")
        
        # Check RSI distribution
        rsi_below_30 = (df['rsi_14'] < 30).sum()
        rsi_above_70 = (df['rsi_14'] > 70).sum()
        logger.info(f"\nRSI distribution:")
        logger.info(f"  RSI < 30: {rsi_below_30} ({rsi_below_30/len(df)*100:.2f}%)")
        logger.info(f"  RSI > 70: {rsi_above_70} ({rsi_above_70/len(df)*100:.2f}%)")
        
        # Check close vs ema_10 relationship
        close_above_ema = (df['close'] > df['ema_10']).sum()
        close_below_ema = (df['close'] < df['ema_10']).sum()
        logger.info(f"\nClose vs EMA10:")
        logger.info(f"  Close > EMA10: {close_above_ema} ({close_above_ema/len(df)*100:.2f}%)")
        logger.info(f"  Close < EMA10: {close_below_ema} ({close_below_ema/len(df)*100:.2f}%)")
        
        # Check for data quality issues
        logger.info(f"\nData quality check:")
        for col in required_cols:
            inf_count = df[col].is_infinite().sum()
            if inf_count > 0:
                logger.warning(f"  {col} has {inf_count} infinite values")
            
            # Check for very extreme values
            if col == 'rsi_14':
                invalid_rsi = ((df[col] < 0) | (df[col] > 100)).sum()
                if invalid_rsi > 0:
                    logger.warning(f"  {col} has {invalid_rsi} values outside 0-100 range")
        
        return df
        
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        return None

def test_signal_generation():
    """Test the signal generation logic from the backtesting module"""
    
    # Load strategies
    strategies_file = "c:/Users/<USER>/Documents/Equity/config/strategies.yaml"
    try:
        with open(strategies_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        strategies = data.get('strategies', [])
        
        # Find RSI_Reversal strategy
        rsi_strategy = None
        for strategy in strategies:
            if strategy.get('name') == 'RSI_Reversal':
                rsi_strategy = strategy
                break
        
        if not rsi_strategy:
            logger.error("RSI_Reversal strategy not found in config")
            return
        
        logger.info(f"RSI_Reversal strategy config:")
        logger.info(f"  Long: {rsi_strategy.get('long')}")
        logger.info(f"  Short: {rsi_strategy.get('short')}")
        
        # Load data
        df = load_and_analyze_data()
        if df is None:
            return
        
        # Test manual signal generation (simplified version of the backtesting logic)
        logger.info("\nTesting manual signal generation...")
        
        # Long signals: rsi_14 < 30 and close > ema_10
        long_expr = (pl.col("rsi_14") < 30) & (pl.col("close") > pl.col("ema_10"))
        long_signals = df.select(long_expr.alias("long_signal")).to_series()
        logger.info(f"Manual long signals: {long_signals.sum()}")
        
        # Short signals: rsi_14 > 70 and close < ema_10
        short_expr = (pl.col("rsi_14") > 70) & (pl.col("close") < pl.col("ema_10"))
        short_signals = df.select(short_expr.alias("short_signal")).to_series()
        logger.info(f"Manual short signals: {short_signals.sum()}")
        
        # Check if we have any signals at all
        total_signals = long_signals.sum() + short_signals.sum()
        logger.info(f"Total signals: {total_signals}")
        
        if total_signals == 0:
            logger.warning("No signals generated - this explains why vectorbt has no trades!")
            
            # Let's try relaxed conditions to see if we can get any signals
            logger.info("\nTrying relaxed conditions...")
            
            # More relaxed long: rsi_14 < 35 and close > ema_10
            relaxed_long = (pl.col("rsi_14") < 35) & (pl.col("close") > pl.col("ema_10"))
            relaxed_long_signals = df.select(relaxed_long.alias("relaxed_long")).to_series()
            logger.info(f"Relaxed long signals (RSI < 35): {relaxed_long_signals.sum()}")
            
            # More relaxed short: rsi_14 > 65 and close < ema_10
            relaxed_short = (pl.col("rsi_14") > 65) & (pl.col("close") < pl.col("ema_10"))
            relaxed_short_signals = df.select(relaxed_short.alias("relaxed_short")).to_series()
            logger.info(f"Relaxed short signals (RSI > 65): {relaxed_short_signals.sum()}")
        
    except Exception as e:
        logger.error(f"Error in signal generation test: {e}")

if __name__ == "__main__":
    logger.info("Starting RSI_Reversal debug analysis...")
    test_signal_generation()
    logger.info("Debug analysis completed.")
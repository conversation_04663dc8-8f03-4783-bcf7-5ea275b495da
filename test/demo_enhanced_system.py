#!/usr/bin/env python3
"""
🚀 Enhanced Trading System Demo
Comprehensive demonstration of all system enhancements

Features:
- Enhanced ML models with signal enhancement
- Advanced technical indicators (RSI, MACD, Bollinger Bands, Ichimoku, etc.)
- NSE 500 stock universe with sector mapping
- Real-time WebSocket integration with performance monitoring
"""

import os
import sys
import asyncio
import logging
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import enhanced components
from agents.enhanced_signal_agent import EnhancedSignalAgent
from utils.advanced_indicators import AdvancedIndicators
from utils.nse_500_universe import NSE500Universe
from utils.enhanced_websocket_manager import EnhancedWebSocketManager
from scripts.initialize_nse_500_universe import NSE500Initializer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedTradingSystemDemo:
    """
    Comprehensive demo of the enhanced trading system
    """
    
    def __init__(self):
        """Initialize the demo system"""
        
        self.config = {
            'api_key': 'demo_api_key',
            'username': 'demo_user',
            'websocket': {
                'max_symbols_per_batch': 50,
                'max_reconnection_attempts': 5,
                'reconnection_delay': 5,
                'heartbeat_interval': 30
            }
        }
        
        # Initialize components
        self.signal_agent = None
        self.universe = NSE500Universe()
        self.websocket_manager = EnhancedWebSocketManager(self.config)
        
        # Demo results
        self.demo_results = {}
        
        logger.info("🚀 Enhanced Trading System Demo initialized")
    
    async def run_comprehensive_demo(self) -> Dict[str, Any]:
        """Run comprehensive system demonstration"""
        try:
            print("\n" + "="*80)
            print("🚀 ENHANCED TRADING SYSTEM DEMONSTRATION")
            print("="*80)
            
            # Step 1: Initialize NSE 500 Universe
            print("\n📈 Step 1: Initializing NSE 500 Stock Universe...")
            universe_results = await self._demo_nse_500_universe()
            
            # Step 2: Demonstrate Advanced Technical Indicators
            print("\n📊 Step 2: Demonstrating Advanced Technical Indicators...")
            indicators_results = await self._demo_advanced_indicators()
            
            # Step 3: Showcase Enhanced ML Models
            print("\n🧠 Step 3: Showcasing Enhanced ML Models...")
            ml_results = await self._demo_enhanced_ml_models()
            
            # Step 4: Test WebSocket Integration
            print("\n📡 Step 4: Testing Enhanced WebSocket Integration...")
            websocket_results = await self._demo_websocket_integration()
            
            # Step 5: Integrated System Test
            print("\n🔗 Step 5: Running Integrated System Test...")
            integration_results = await self._demo_system_integration()
            
            # Compile final results
            final_results = {
                "demo_timestamp": datetime.now().isoformat(),
                "universe_demo": universe_results,
                "indicators_demo": indicators_results,
                "ml_models_demo": ml_results,
                "websocket_demo": websocket_results,
                "integration_demo": integration_results,
                "overall_status": "SUCCESS"
            }
            
            # Print summary
            self._print_demo_summary(final_results)
            
            return final_results
            
        except Exception as e:
            logger.error(f"❌ Demo execution failed: {e}")
            return {"error": str(e), "overall_status": "FAILED"}
    
    async def _demo_nse_500_universe(self) -> Dict[str, Any]:
        """Demonstrate NSE 500 universe functionality"""
        try:
            print("   🏢 Initializing comprehensive NSE 500 stock universe...")
            
            # Initialize universe
            initializer = NSE500Initializer()
            success = await initializer.initialize_universe()
            
            if success:
                # Get universe statistics
                stats = initializer.universe.get_universe_stats()
                
                print(f"   ✅ Successfully loaded {stats['total_stocks']} stocks")
                print(f"   📊 Sectors: {stats['sectors']}")
                print(f"   ⭐ Nifty 50: {stats['nifty_50_count']} stocks")
                print(f"   🔵 Large Cap: {stats['large_cap_count']} stocks")
                
                # Test sector filtering
                banking_stocks = initializer.universe.get_stocks_by_sector("Banking")
                it_stocks = initializer.universe.get_stocks_by_sector("Information Technology")
                
                print(f"   🏦 Banking stocks: {len(banking_stocks)}")
                print(f"   💻 IT stocks: {len(it_stocks)}")
                
                return {
                    "status": "SUCCESS",
                    "universe_stats": stats,
                    "sample_sectors": {
                        "Banking": len(banking_stocks),
                        "Information Technology": len(it_stocks)
                    }
                }
            else:
                return {"status": "FAILED", "error": "Universe initialization failed"}
                
        except Exception as e:
            logger.error(f"❌ Universe demo failed: {e}")
            return {"status": "FAILED", "error": str(e)}
    
    async def _demo_advanced_indicators(self) -> Dict[str, Any]:
        """Demonstrate advanced technical indicators"""
        try:
            print("   📊 Calculating advanced technical indicators...")
            
            # Create sample OHLCV data
            import polars as pl
            import numpy as np
            
            # Generate realistic sample data
            dates = pl.date_range(
                start=datetime(2024, 1, 1),
                end=datetime(2024, 7, 22),
                interval="1d"
            )
            
            n_days = len(dates)
            base_price = 2500.0
            
            # Generate realistic price data with trend and volatility
            price_changes = np.random.normal(0, 0.02, n_days)  # 2% daily volatility
            prices = [base_price]
            
            for change in price_changes[1:]:
                new_price = prices[-1] * (1 + change)
                prices.append(max(new_price, 1.0))  # Ensure positive prices
            
            # Create OHLCV data
            sample_data = pl.DataFrame({
                'timestamp': dates,
                'open': [p * (1 + np.random.uniform(-0.01, 0.01)) for p in prices],
                'high': [p * (1 + np.random.uniform(0, 0.03)) for p in prices],
                'low': [p * (1 - np.random.uniform(0, 0.03)) for p in prices],
                'close': prices,
                'volume': [np.random.randint(100000, 1000000) for _ in range(n_days)]
            })
            
            # Calculate all advanced indicators
            enhanced_data = AdvancedIndicators.calculate_all_advanced_indicators(sample_data)
            
            # Check which indicators were calculated
            indicator_columns = [col for col in enhanced_data.columns 
                               if col not in ['timestamp', 'open', 'high', 'low', 'close', 'volume']]
            
            print(f"   ✅ Calculated {len(indicator_columns)} advanced indicators")
            print(f"   📈 Indicators: {', '.join(indicator_columns[:10])}...")
            
            # Sample some indicator values
            latest_row = enhanced_data.tail(1).to_dicts()[0]
            sample_indicators = {
                'ichimoku_tenkan': latest_row.get('ichimoku_tenkan'),
                'bb_width': latest_row.get('bb_width'),
                'fisher_transform': latest_row.get('fisher_transform'),
                'pivot_point': latest_row.get('pivot_point')
            }
            
            print(f"   📊 Sample values: {sample_indicators}")
            
            return {
                "status": "SUCCESS",
                "indicators_calculated": len(indicator_columns),
                "indicator_list": indicator_columns,
                "sample_values": sample_indicators
            }
            
        except Exception as e:
            logger.error(f"❌ Indicators demo failed: {e}")
            return {"status": "FAILED", "error": str(e)}
    
    async def _demo_enhanced_ml_models(self) -> Dict[str, Any]:
        """Demonstrate enhanced ML models"""
        try:
            print("   🧠 Initializing enhanced ML models...")
            
            # Initialize enhanced signal agent
            self.signal_agent = EnhancedSignalAgent()
            success = await self.signal_agent.initialize()
            
            if not success:
                return {"status": "FAILED", "error": "Signal agent initialization failed"}
            
            print("   ✅ Enhanced signal agent initialized")
            
            # Create sample signals for enhancement
            import numpy as np
            
            sample_signals = {
                "5min": np.random.normal(0, 1, 100),
                "15min": np.random.normal(0, 1, 100),
                "1h": np.random.normal(0, 1, 100)
            }
            
            # Enhance signals
            enhancement_results = await self.signal_agent.enhance_signals(sample_signals)
            
            if "error" not in enhancement_results:
                print("   ✅ Signal enhancement completed")
                print(f"   📊 Enhancement methods: {enhancement_results.get('enhancement_metadata', {}).get('methods_applied', [])}")
                print(f"   🎯 Signal quality score: {enhancement_results.get('enhancement_metadata', {}).get('signal_quality_score', 0):.3f}")
                
                return {
                    "status": "SUCCESS",
                    "enhancement_methods": enhancement_results.get('enhancement_metadata', {}).get('methods_applied', []),
                    "quality_score": enhancement_results.get('enhancement_metadata', {}).get('signal_quality_score', 0)
                }
            else:
                return {"status": "FAILED", "error": enhancement_results["error"]}
                
        except Exception as e:
            logger.error(f"❌ ML models demo failed: {e}")
            return {"status": "FAILED", "error": str(e)}
    
    async def _demo_websocket_integration(self) -> Dict[str, Any]:
        """Demonstrate WebSocket integration"""
        try:
            print("   📡 Testing enhanced WebSocket integration...")
            
            # Get sample symbols from universe
            if self.universe.stocks:
                sample_symbols = []
                for symbol, stock in list(self.universe.stocks.items())[:10]:
                    sample_symbols.append({
                        'symbol': stock.symbol,
                        'token': stock.token,
                        'exchange': stock.exchange
                    })
            else:
                # Fallback sample symbols
                sample_symbols = [
                    {'symbol': 'RELIANCE', 'token': '2885', 'exchange': 'NSE'},
                    {'symbol': 'TCS', 'token': '11536', 'exchange': 'NSE'},
                    {'symbol': 'HDFCBANK', 'token': '1333', 'exchange': 'NSE'}
                ]
            
            # Add symbols to WebSocket manager
            self.websocket_manager.add_symbols(sample_symbols)
            
            print(f"   ✅ Added {len(sample_symbols)} symbols for streaming")
            print(f"   📦 Created {len(self.websocket_manager.subscription_batches)} subscription batches")
            
            # Test authentication (mock)
            auth_success = await self.websocket_manager.authenticate('mock_token', 'mock_feed')
            
            if auth_success:
                print("   ✅ WebSocket authentication successful")
            
            # Get connection metrics
            metrics = self.websocket_manager.get_connection_metrics()
            quality_metrics = self.websocket_manager.get_quality_metrics()
            
            return {
                "status": "SUCCESS",
                "symbols_added": len(sample_symbols),
                "subscription_batches": len(self.websocket_manager.subscription_batches),
                "authentication": "SUCCESS" if auth_success else "FAILED",
                "connection_metrics": {
                    "total_messages": metrics.total_messages,
                    "error_count": metrics.error_count
                },
                "quality_metrics": quality_metrics
            }
            
        except Exception as e:
            logger.error(f"❌ WebSocket demo failed: {e}")
            return {"status": "FAILED", "error": str(e)}
    
    async def _demo_system_integration(self) -> Dict[str, Any]:
        """Demonstrate integrated system functionality"""
        try:
            print("   🔗 Testing integrated system functionality...")
            
            integration_tests = {
                "universe_loaded": len(self.universe.stocks) > 0,
                "signal_agent_ready": self.signal_agent is not None and self.signal_agent.is_initialized,
                "websocket_configured": len(self.websocket_manager.subscribed_symbols) > 0,
                "indicators_available": True  # AdvancedIndicators is stateless
            }
            
            all_tests_passed = all(integration_tests.values())
            
            print(f"   📊 Integration test results:")
            for test_name, result in integration_tests.items():
                status = "✅" if result else "❌"
                print(f"      {status} {test_name}: {'PASS' if result else 'FAIL'}")
            
            if all_tests_passed:
                print("   🎉 All integration tests passed!")
            else:
                print("   ⚠️ Some integration tests failed")
            
            return {
                "status": "SUCCESS" if all_tests_passed else "PARTIAL",
                "test_results": integration_tests,
                "overall_integration": "PASS" if all_tests_passed else "FAIL"
            }
            
        except Exception as e:
            logger.error(f"❌ Integration demo failed: {e}")
            return {"status": "FAILED", "error": str(e)}
    
    def _print_demo_summary(self, results: Dict[str, Any]):
        """Print comprehensive demo summary"""
        try:
            print("\n" + "="*80)
            print("📋 ENHANCED TRADING SYSTEM DEMO SUMMARY")
            print("="*80)
            
            # Overall status
            overall_status = results.get("overall_status", "UNKNOWN")
            status_emoji = "✅" if overall_status == "SUCCESS" else "❌"
            print(f"{status_emoji} Overall Status: {overall_status}")
            
            # Component summaries
            components = [
                ("NSE 500 Universe", results.get("universe_demo", {})),
                ("Advanced Indicators", results.get("indicators_demo", {})),
                ("Enhanced ML Models", results.get("ml_models_demo", {})),
                ("WebSocket Integration", results.get("websocket_demo", {})),
                ("System Integration", results.get("integration_demo", {}))
            ]
            
            print("\n📊 Component Status:")
            for component_name, component_results in components:
                status = component_results.get("status", "UNKNOWN")
                emoji = "✅" if status == "SUCCESS" else "⚠️" if status == "PARTIAL" else "❌"
                print(f"   {emoji} {component_name}: {status}")
            
            # Key metrics
            print("\n📈 Key Metrics:")
            universe_stats = results.get("universe_demo", {}).get("universe_stats", {})
            if universe_stats:
                print(f"   🏢 Total Stocks: {universe_stats.get('total_stocks', 0)}")
                print(f"   📊 Sectors: {universe_stats.get('sectors', 0)}")
                print(f"   ⭐ Nifty 50: {universe_stats.get('nifty_50_count', 0)}")
            
            indicators_count = results.get("indicators_demo", {}).get("indicators_calculated", 0)
            print(f"   📊 Technical Indicators: {indicators_count}")
            
            websocket_symbols = results.get("websocket_demo", {}).get("symbols_added", 0)
            print(f"   📡 WebSocket Symbols: {websocket_symbols}")
            
            print("\n🎯 Demo completed successfully!")
            print("📁 Check 'reports/' and 'data/exports/' for detailed outputs")
            print("="*80)
            
        except Exception as e:
            logger.error(f"❌ Summary printing failed: {e}")

async def main():
    """Main demo execution"""
    try:
        # Create and run demo
        demo = EnhancedTradingSystemDemo()
        results = await demo.run_comprehensive_demo()
        
        # Save results
        results_dir = Path("reports/demo")
        results_dir.mkdir(parents=True, exist_ok=True)
        
        with open(results_dir / "demo_results.json", 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Demo results saved to: {results_dir / 'demo_results.json'}")
        
        return 0 if results.get("overall_status") == "SUCCESS" else 1
        
    except Exception as e:
        logger.error(f"❌ Demo execution failed: {e}")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

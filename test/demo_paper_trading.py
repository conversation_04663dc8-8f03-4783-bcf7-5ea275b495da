#!/usr/bin/env python3
"""
Paper Trading Demo Script
Demonstrates the paper trading functionality with virtual account management
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set environment for paper trading
os.environ['TRADING_MODE'] = 'paper'
os.environ['PAPER_TRADING_INITIAL_BALANCE'] = '100000'
os.environ['PAPER_TRADING_MAX_TRADES_PER_DAY'] = '5'

# Import modules
try:
    from utils.paper_trading import VirtualAccount
    from agents.execution_agent import ExecutionAgent, SignalPayload
    from main import TradingSystemOrchestrator
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PaperTradingDemo:
    """Demo class for paper trading functionality"""
    
    def __init__(self):
        """Initialize demo"""
        self.config = {
            'paper_trading': {
                'initial_balance': 100000,
                'max_trades_per_day': 5,
                'commission_rate': 0.0003,
                'max_position_size': 20000,
                'max_daily_loss': 5000,
                'margin_multiplier': 3.5
            }
        }
        
        self.virtual_account = None
        self.execution_agent = None
    
    async def initialize(self):
        """Initialize components"""
        try:
            logger.info("🚀 Initializing Paper Trading Demo...")
            
            # Initialize virtual account
            self.virtual_account = VirtualAccount(self.config)
            logger.info(f"✅ Virtual Account initialized with ₹{self.virtual_account.initial_balance:,.2f}")
            
            # Initialize execution agent
            self.execution_agent = ExecutionAgent(
                trading_mode="paper",
                trading_config=self.config
            )
            
            init_success = await self.execution_agent.initialize()
            if init_success:
                logger.info("✅ Execution Agent initialized in paper mode")
            else:
                logger.error("❌ Failed to initialize Execution Agent")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing demo: {e}")
            return False
    
    def display_account_summary(self):
        """Display account summary"""
        try:
            summary = self.virtual_account.get_account_summary()
            positions = self.virtual_account.get_positions_summary()
            
            print("\n" + "="*60)
            print("📊 VIRTUAL ACCOUNT SUMMARY")
            print("="*60)
            print(f"💰 Current Balance:     ₹{summary.get('current_balance', 0):,.2f}")
            print(f"💳 Available Margin:    ₹{summary.get('available_margin', 0):,.2f}")
            print(f"📈 Total P&L:           ₹{summary.get('total_pnl', 0):,.2f}")
            print(f"📊 Return %:            {summary.get('return_percent', 0):.2f}%")
            print(f"🔢 Total Trades:        {summary.get('total_trades', 0)}")
            print(f"📍 Active Positions:    {summary.get('active_positions', 0)}")
            print(f"📅 Today's Trades:      {summary.get('today_trades', 0)}/{summary.get('today_trades', 0) + summary.get('trades_remaining_today', 0)}")
            
            if positions:
                print(f"\n📍 ACTIVE POSITIONS:")
                print("-" * 60)
                for pos in positions:
                    pnl_color = "🟢" if pos['unrealized_pnl'] >= 0 else "🔴"
                    print(f"{pnl_color} {pos['symbol']}: {pos['quantity']} @ ₹{pos['average_price']:.2f}")
                    print(f"   Current: ₹{pos['current_price']:.2f} | P&L: ₹{pos['unrealized_pnl']:.2f} ({pos['pnl_percent']:.2f}%)")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"❌ Error displaying account summary: {e}")
    
    async def demo_basic_trading(self):
        """Demonstrate basic trading operations"""
        try:
            logger.info("\n🎯 Demo: Basic Trading Operations")
            
            # Demo trades
            demo_trades = [
                {
                    'symbol': 'RELIANCE-EQ',
                    'action': 'BUY',
                    'quantity': 10,
                    'price': 2500.0,
                    'sl_price': 2400.0,
                    'target_price': 2600.0
                },
                {
                    'symbol': 'TCS-EQ',
                    'action': 'BUY',
                    'quantity': 5,
                    'price': 3500.0,
                    'sl_price': 3400.0,
                    'target_price': 3650.0
                },
                {
                    'symbol': 'HDFCBANK-EQ',
                    'action': 'BUY',
                    'quantity': 8,
                    'price': 1500.0,
                    'sl_price': 1450.0,
                    'target_price': 1580.0
                }
            ]
            
            for i, trade_info in enumerate(demo_trades):
                logger.info(f"\n📈 Executing Trade {i+1}: {trade_info['action']} {trade_info['quantity']} {trade_info['symbol']}")
                
                # Create signal
                signal = SignalPayload(
                    symbol=trade_info['symbol'],
                    exchange='NSE',
                    symbol_token=f'token_{i+1}',
                    action=trade_info['action'],
                    entry_price=trade_info['price'],
                    sl_price=trade_info['sl_price'],
                    target_price=trade_info['target_price'],
                    quantity=trade_info['quantity'],
                    strategy_name='demo_strategy',
                    signal_id=f'demo_{i+1:03d}'
                )
                
                # Execute trade
                success, message, trade_execution = await self.execution_agent.process_signal(signal)
                
                if success:
                    logger.info(f"✅ Trade executed successfully: {message}")
                else:
                    logger.error(f"❌ Trade failed: {message}")
                
                # Show account summary after each trade
                self.display_account_summary()
                
                # Small delay for demo effect
                await asyncio.sleep(1)
            
        except Exception as e:
            logger.error(f"❌ Error in basic trading demo: {e}")
    
    async def demo_profit_taking(self):
        """Demonstrate profit taking by selling positions"""
        try:
            logger.info("\n💰 Demo: Profit Taking")
            
            # Get current positions
            positions = self.virtual_account.get_positions_summary()
            
            if not positions:
                logger.warning("⚠️ No positions to sell")
                return
            
            # Sell first position at a profit
            first_position = positions[0]
            profit_price = first_position['average_price'] * 1.05  # 5% profit
            
            logger.info(f"📤 Selling {first_position['symbol']} at ₹{profit_price:.2f} (5% profit)")
            
            # Create sell signal
            sell_signal = SignalPayload(
                symbol=first_position['symbol'],
                exchange='NSE',
                symbol_token='sell_token',
                action='SELL',
                entry_price=profit_price,
                sl_price=0,
                target_price=0,
                quantity=first_position['quantity'],
                strategy_name='profit_taking',
                signal_id='sell_001'
            )
            
            # Execute sell trade
            success, message, trade_execution = await self.execution_agent.process_signal(sell_signal)
            
            if success:
                logger.info(f"✅ Profit taking successful: {message}")
            else:
                logger.error(f"❌ Profit taking failed: {message}")
            
            # Show updated account summary
            self.display_account_summary()
            
        except Exception as e:
            logger.error(f"❌ Error in profit taking demo: {e}")
    
    async def demo_risk_limits(self):
        """Demonstrate risk limits enforcement"""
        try:
            logger.info("\n🛡️ Demo: Risk Limits Enforcement")
            
            # Try to place a trade that exceeds position size limit
            logger.info("🧪 Testing position size limit...")
            
            large_trade_signal = SignalPayload(
                symbol='LARGECAP-EQ',
                exchange='NSE',
                symbol_token='large_token',
                action='BUY',
                entry_price=1000.0,
                sl_price=950.0,
                target_price=1100.0,
                quantity=50,  # ₹50,000 trade value (exceeds ₹20,000 limit)
                strategy_name='risk_test',
                signal_id='risk_001'
            )
            
            success, message, _ = await self.execution_agent.process_signal(large_trade_signal)
            
            if not success:
                logger.info(f"✅ Risk limit enforced: {message}")
            else:
                logger.warning(f"⚠️ Risk limit not enforced - this shouldn't happen")
            
            # Try to exceed daily trade limit
            logger.info("\n🧪 Testing daily trade limit...")
            
            current_summary = self.virtual_account.get_account_summary()
            remaining_trades = current_summary.get('trades_remaining_today', 0)
            
            logger.info(f"📊 Remaining trades today: {remaining_trades}")
            
            # Try to place more trades than allowed
            for i in range(remaining_trades + 2):  # Try to exceed limit
                test_signal = SignalPayload(
                    symbol=f'TEST{i}-EQ',
                    exchange='NSE',
                    symbol_token=f'test_token_{i}',
                    action='BUY',
                    entry_price=100.0,
                    sl_price=95.0,
                    target_price=110.0,
                    quantity=1,
                    strategy_name='limit_test',
                    signal_id=f'limit_{i:03d}'
                )
                
                success, message, _ = await self.execution_agent.process_signal(test_signal)
                
                if success:
                    logger.info(f"✅ Trade {i+1} executed")
                else:
                    logger.info(f"🛡️ Trade {i+1} blocked: {message}")
                    break
            
        except Exception as e:
            logger.error(f"❌ Error in risk limits demo: {e}")
    
    async def demo_account_reset(self):
        """Demonstrate account reset functionality"""
        try:
            logger.info("\n🔄 Demo: Account Reset")
            
            # Show current account state
            logger.info("📊 Account state before reset:")
            self.display_account_summary()
            
            # Reset account
            logger.info("🔄 Resetting virtual account...")
            reset_success = await self.execution_agent.reset_paper_account()
            
            if reset_success:
                logger.info("✅ Account reset successful")
            else:
                logger.error("❌ Account reset failed")
            
            # Show account state after reset
            logger.info("📊 Account state after reset:")
            self.display_account_summary()
            
        except Exception as e:
            logger.error(f"❌ Error in account reset demo: {e}")
    
    async def run_full_demo(self):
        """Run the complete demo"""
        try:
            print("\n" + "="*80)
            print("🎯 PAPER TRADING SYSTEM DEMO")
            print("="*80)
            print("This demo showcases the paper trading functionality including:")
            print("• Virtual account management with realistic commissions")
            print("• Trade execution simulation")
            print("• Risk limits enforcement")
            print("• P&L tracking and position management")
            print("• Account reset functionality")
            print("="*80)
            
            # Initialize
            if not await self.initialize():
                logger.error("❌ Demo initialization failed")
                return False
            
            # Show initial account state
            logger.info("📊 Initial Account State:")
            self.display_account_summary()
            
            # Run demo sections
            await self.demo_basic_trading()
            await self.demo_profit_taking()
            await self.demo_risk_limits()
            await self.demo_account_reset()
            
            print("\n" + "="*80)
            print("🎉 DEMO COMPLETED SUCCESSFULLY!")
            print("="*80)
            print("The paper trading system is ready for use. You can:")
            print("• Switch between paper and real trading using TRADING_MODE environment variable")
            print("• Use the execution agent for automated trading")
            print("• Monitor account status and performance")
            print("• Test strategies risk-free with virtual money")
            print("="*80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error running demo: {e}")
            return False

async def main():
    """Main function"""
    demo = PaperTradingDemo()
    success = await demo.run_full_demo()
    
    if success:
        print("\n✅ Paper trading demo completed successfully!")
        return 0
    else:
        print("\n❌ Demo failed. Check logs for details.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

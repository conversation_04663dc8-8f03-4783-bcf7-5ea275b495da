#!/usr/bin/env python3
"""
Direct test of data loading without the full agent
"""

import polars as pl
import os
from datetime import datetime
from collections import deque

def test_data_loading():
    """Test data loading directly"""
    print("🔍 Direct Data Loading Test")
    
    # Check file existence
    live_5min_path = "data/live/live_5min.parquet"
    historical_5min_path = "data/historical/historical_5min.parquet"
    
    print(f"Live data exists: {os.path.exists(live_5min_path)}")
    print(f"Historical data exists: {os.path.exists(historical_5min_path)}")
    
    df_5min = None
    
    # Try live data first
    if os.path.exists(live_5min_path):
        try:
            print(f"Loading live data from {live_5min_path}")
            df_5min = pl.read_parquet(live_5min_path)
            print(f"Live data loaded: {len(df_5min)} records")
            print(f"Live data columns: {df_5min.columns}")
            if len(df_5min) > 0:
                print(f"Live data sample:\n{df_5min.head()}")
        except Exception as e:
            print(f"Failed to load live data: {e}")
            df_5min = None
    
    # Try historical data
    if df_5min is None or len(df_5min) < 100:
        if os.path.exists(historical_5min_path):
            try:
                print(f"Loading historical data from {historical_5min_path}")
                df_historical = pl.read_parquet(historical_5min_path)
                print(f"Historical data loaded: {len(df_historical)} records")
                print(f"Historical data columns: {df_historical.columns}")
                
                # Preprocess historical data
                if 'timestamp' not in df_historical.columns and 'date' in df_historical.columns and 'time' in df_historical.columns:
                    print("Converting date/time to timestamp...")
                    df_historical = df_historical.with_columns([
                        pl.concat_str([
                            pl.col('date'),
                            pl.lit(' '),
                            pl.col('time')
                        ]).str.strptime(pl.Datetime, format='%d-%m-%Y %H:%M:%S').alias('timestamp')
                    ])
                    df_historical = df_historical.drop(['date', 'time'])
                    print("Timestamp conversion completed")
                
                # Convert timestamp to string format
                if df_historical['timestamp'].dtype != pl.Utf8:
                    df_historical = df_historical.with_columns([
                        pl.col('timestamp').dt.strftime('%Y-%m-%d %H:%M:%S').alias('timestamp')
                    ])
                    print("Timestamp converted to string format")
                
                df_5min = df_historical
                print(f"Final data: {len(df_5min)} records")
                print(f"Final columns: {df_5min.columns}")
                print(f"Sample data:\n{df_5min.head()}")
                
                # Test symbol extraction
                symbols = df_5min['symbol'].unique().to_list()
                print(f"Unique symbols: {len(symbols)}")
                print(f"First 10 symbols: {symbols[:10]}")
                
                # Test data for specific symbols
                test_symbols = ['INFY', 'RELIANCE', 'HDFCBANK']
                for symbol in test_symbols:
                    symbol_data = df_5min.filter(pl.col('symbol') == symbol)
                    print(f"{symbol}: {len(symbol_data)} records")
                    if len(symbol_data) > 0:
                        print(f"  Latest: {symbol_data.tail(1)}")
                
            except Exception as e:
                print(f"Failed to load historical data: {e}")
                import traceback
                traceback.print_exc()
    
    return df_5min

if __name__ == "__main__":
    test_data_loading()

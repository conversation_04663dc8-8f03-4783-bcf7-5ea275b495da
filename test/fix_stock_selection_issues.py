#!/usr/bin/env python3
"""
Fix script to address stock selection and volume data issues
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from utils.nse_500_universe import NSE500Universe
from agents.market_monitoring_agent import MarketMonitoringAgent

logger = logging.getLogger(__name__)

class StockSelectionFixer:
    """Fixes for stock selection and volume data issues"""
    
    def __init__(self):
        self.universe = NSE500Universe()
        
    async def fix_stock_selection(self):
        """Fix stock selection to use Nifty500 instead of Nifty50"""
        try:
            print("🔧 Fixing stock selection issues...")
            
            # Load NSE 500 universe
            success = self.universe.load_nse_500_universe()
            if not success:
                print("❌ Failed to load NSE 500 universe")
                return False
                
            stats = self.universe.get_universe_stats()
            print(f"✅ Loaded {stats['total_stocks']} stocks from NSE 500")
            print(f"📊 Available sectors: {stats['sectors']}")
            print(f"⭐ Nifty 50 stocks: {stats['nifty_50_count']}")
            print(f"🔵 Large Cap: {stats['large_cap_count']}")
            print(f"📈 Mid Cap: {stats['mid_cap_count']}")
            
            # Get all Nifty500 stocks
            all_stocks = self.universe.get_all_stocks()
            nifty500_stocks = [s for s in all_stocks if s.nifty_500]
            
            print(f"🎯 Found {len(nifty500_stocks)} Nifty 500 stocks")
            
            # Save the Nifty500 list for use in trading
            import json
            nifty500_list = [
                {
                    "symbol": stock.symbol,
                    "token": stock.token,
                    "exchange": stock.exchange,
                    "sector": stock.sector,
                    "market_cap": stock.market_cap
                }
                for stock in nifty500_stocks
            ]
            
            with open("config/nifty500_stocks.json", "w") as f:
                json.dump(nifty500_list, f, indent=2)
                
            print("✅ Nifty500 stock list saved to config/nifty500_stocks.json")
            
            return True
            
        except Exception as e:
            print(f"❌ Error fixing stock selection: {e}")
            return False
    
    async def fix_volume_data(self):
        """Fix volume data fetching from WebSocket"""
        try:
            print("🔧 Fixing volume data issues...")
            
            # Create enhanced WebSocket configuration
            config = {
                "volume_fields": [
                    "volume_traded",
                    "volume_traded_today", 
                    "volume",
                    "v",
                    "total_traded_volume"
                ],
                "price_fields": [
                    "last_traded_price",
                    "ltp",
                    "lp"
                ],
                "fallback_volume_calculation": True
            }
            
            with open("config/websocket_volume_config.json", "w") as f:
                import json
                json.dump(config, f, indent=2)
                
            print("✅ WebSocket volume configuration saved")
            return True
            
        except Exception as e:
            print(f"❌ Error fixing volume data: {e}")
            return False
    
    async def fix_market_monitoring_config(self):
        """Fix market monitoring agent to use Nifty500"""
        try:
            print("🔧 Fixing market monitoring configuration...")
            
            # Update market monitoring config to use Nifty500
            import yaml
            
            config_path = "config/market_monitoring_config.yaml"
            if Path(config_path).exists():
                with open(config_path, 'r') as f:
                    config = yaml.safe_load(f)
                
                # Update symbols to use Nifty500
                config['market_data']['symbols_source'] = 'nifty500'
                config['market_data']['max_symbols'] = 500
                config['market_data']['min_symbols'] = 100
                
                with open(config_path, 'w') as f:
                    yaml.dump(config, f, indent=2)
                    
                print("✅ Market monitoring config updated")
            else:
                print("⚠️ Market monitoring config not found, creating new one")
                
            return True
            
        except Exception as e:
            print(f"❌ Error fixing market monitoring config: {e}")
            return False

async def main():
    """Main execution"""
    try:
        print("🚀 Starting stock selection fixes...")
        
        fixer = StockSelectionFixer()
        
        # Fix stock selection
        await fixer.fix_stock_selection()
        
        # Fix volume data
        await fixer.fix_volume_data()
        
        # Fix market monitoring config
        await fixer.fix_market_monitoring_config()
        
        print("✅ All fixes completed successfully!")
        
    except Exception as e:
        print(f"❌ Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())

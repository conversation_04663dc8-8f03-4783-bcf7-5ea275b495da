#!/usr/bin/env python3
"""
Fix Unicode Issues Script
Replaces emoji characters with ASCII alternatives to fix Windows cp1252 codec errors
"""

import os
import re
from pathlib import Path

# Emoji to ASCII mapping
EMOJI_REPLACEMENTS = {
    '🚀': '[INIT]',
    '🎯': '[TARGET]',
    '❌': '[ERROR]',
    '⚠️': '[WARN]',
    '📡': '[SIGNAL]',
    '🔄': '[PROCESS]',
    '✅': '[SUCCESS]',
    '🏥': '[HEALTH]',
    '💻': '[SYSTEM]',
    '🔧': '[CONFIG]',
    '📊': '[STATUS]',
    '⏰': '[TIME]',
    '🕐': '[UPTIME]',
    '🤖': '[AGENT]',
    '📋': '[LIST]',
    '🔍': '[DEBUG]',
    '👋': '[EXIT]',
    '🛡️': '[SECURITY]',
    '📈': '[METRICS]',
    '⚡': '[FAST]',
    '🛠️': '[TOOLS]',
    '🏃': '[RUNNING]',
    '💡': '[INFO]',
    '📢': '[NOTIFY]',
    '🎬': '[DEMO]',
    '🏭': '[PROD]',
    '🛑': '[STOP]',
    '📬': '[MESSAGE]',
    '🔐': '[SECURE]',
    '💬': '[COMM]',
    '🔄': '[WORKFLOW]',
    '💰': '[MONEY]',
    '₹': 'Rs.',
    '📱': '[MOBILE]',
    '📁': '[FOLDER]',
    '💓': '[HEALTH]',
    '🔌': '[CONNECT]',
}

def fix_file_unicode(file_path: Path):
    """Fix unicode issues in a single file"""
    try:
        # Read file with UTF-8 encoding
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace emojis
        original_content = content
        for emoji, replacement in EMOJI_REPLACEMENTS.items():
            content = content.replace(emoji, replacement)
        
        # Only write if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix unicode issues"""
    print("Fixing Unicode Issues in Trading System...")
    
    # Files to fix
    files_to_fix = [
        'main.py',
        'agents/execution_agent.py',
        'agents/run_execution_agent.py',
        'agents/market_monitoring_agent.py',
        'agents/run_market_monitoring.py',
        'agents/signal_generation_agent.py',
        'agents/risk_agent.py',
        'agents/ai_training_agent.py',
        'utils/config_loader.py',
        'utils/gpu_optimizer.py',
    ]
    
    # Additional patterns to search for Python files
    python_files = []
    for pattern in ['agents/*.py', 'utils/*.py', 'scripts/*.py']:
        python_files.extend(Path('.').glob(pattern))
    
    # Combine all files
    all_files = set()
    for file_path in files_to_fix:
        if Path(file_path).exists():
            all_files.add(Path(file_path))
    
    for file_path in python_files:
        if file_path.is_file():
            all_files.add(file_path)
    
    # Fix each file
    fixed_count = 0
    for file_path in sorted(all_files):
        if fix_file_unicode(file_path):
            fixed_count += 1
    
    print(f"\nFixed {fixed_count} files with unicode issues")
    print("Unicode fix completed!")

if __name__ == "__main__":
    main()

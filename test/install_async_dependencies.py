#!/usr/bin/env python3
"""
Installation script for async/concurrent processing dependencies
Compatible with cuDF and cuPy for GPU acceleration
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Success")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"Error: {e.stderr}")
        return False

def check_gpu():
    """Check if NVIDIA GPU is available"""
    try:
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected")
            return True
        else:
            print("⚠️  No NVIDIA GPU detected")
            return False
    except:
        print("⚠️  nvidia-smi not found - GPU may not be available")
        return False

def main():
    print("🚀 Installing async/concurrent processing dependencies for cuDF/cuPy")
    print("=" * 60)
    
    # Check GPU availability
    gpu_available = check_gpu()
    
    # Core async dependencies
    dependencies = [
        ("pip install nest-asyncio", "Installing nest-asyncio for nested event loops"),
        ("pip install asyncio-throttle", "Installing asyncio throttling utilities"),
    ]
    
    # GPU-specific dependencies
    if gpu_available:
        gpu_dependencies = [
            ("pip install dask-cudf", "Installing Dask-cuDF for distributed GPU processing"),
            ("pip install dask-cuda", "Installing Dask-CUDA for multi-GPU support"),
            ("pip install ucx-py", "Installing UCX-Py for high-performance communication"),
        ]
        dependencies.extend(gpu_dependencies)
    else:
        print("⚠️  Skipping GPU-specific dependencies (no GPU detected)")
    
    # Optional performance dependencies
    optional_dependencies = [
        ("pip install uvloop", "Installing uvloop for faster event loops (Linux/macOS only)"),
        ("pip install aiodns", "Installing aiodns for async DNS resolution"),
    ]
    
    # Install core dependencies
    print("\n📦 Installing core dependencies...")
    success_count = 0
    for command, description in dependencies:
        if run_command(command, description):
            success_count += 1
    
    # Install optional dependencies (don't fail if these don't work)
    print("\n📦 Installing optional performance dependencies...")
    for command, description in optional_dependencies:
        run_command(command, description)  # Don't count failures
    
    # Summary
    print("\n" + "=" * 60)
    print(f"✅ Installation complete: {success_count}/{len(dependencies)} core dependencies installed")
    
    if gpu_available:
        print("\n🔧 GPU Configuration Tips:")
        print("1. Ensure CUDA toolkit is installed and compatible with your GPU")
        print("2. Set CUDA_VISIBLE_DEVICES to control which GPUs to use")
        print("3. Monitor GPU memory usage with nvidia-smi")
        print("4. Adjust CONCURRENT_STRATEGIES based on your GPU memory")
    
    print("\n🚀 Configuration recommendations:")
    print("- CONCURRENT_STRATEGIES = 5-10 (adjust based on GPU memory)")
    print("- MAX_WORKERS = 8-16 (adjust based on CPU cores)")
    print("- CHUNKSIZE = 200K-500K (adjust based on available RAM/VRAM)")
    
    print("\n📊 Performance monitoring:")
    print("- Use nvidia-smi to monitor GPU utilization")
    print("- Use htop/top to monitor CPU and memory usage")
    print("- Monitor async task completion rates in logs")

if __name__ == "__main__":
    main()

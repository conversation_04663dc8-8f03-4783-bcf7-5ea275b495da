#!/usr/bin/env python3
"""
Comprehensive test runner for Enhanced Backtesting System
Runs all unit tests for the 10 advanced features
"""

import pytest
import sys
import os
from pathlib import Path
import argparse
import time
from datetime import datetime

# Add the parent directory to the path so we can import the main module
sys.path.insert(0, str(Path(__file__).parent.parent))

def run_feature_tests(feature_name=None, verbose=False, coverage=False):
    """
    Run tests for specific feature or all features
    
    Args:
        feature_name: Name of specific feature to test (optional)
        verbose: Enable verbose output
        coverage: Enable coverage reporting
    """
    
    # Test file mapping
    test_files = {
        'multi_strategy': 'test_multi_strategy_backtesting.py',
        'smart_modes': 'test_smart_backtesting_modes.py', 
        'metrics': 'test_performance_metrics.py',
        'risk_modeling': 'test_capital_risk_modeling.py',
        'scenario_testing': 'test_scenario_testing.py',
        'optimization': 'test_parameter_optimization.py',
        'logging': 'test_result_logging.py',
        'visualization': 'test_visualization_debugging.py',
        'signal_debugging': 'test_signal_debugging.py',
        'llm_explanations': 'test_llm_explanations.py'
    }
    
    # Build pytest arguments
    pytest_args = []
    
    if feature_name:
        if feature_name not in test_files:
            print(f"Error: Unknown feature '{feature_name}'")
            print(f"Available features: {', '.join(test_files.keys())}")
            return False
        
        test_file = test_files[feature_name]
        pytest_args.append(f"test/{test_file}")
        print(f"Running tests for feature: {feature_name}")
    else:
        pytest_args.append("test/")
        print("Running all feature tests...")
    
    # Add verbose flag
    if verbose:
        pytest_args.append("-v")
    
    # Add coverage reporting
    if coverage:
        pytest_args.extend([
            "--cov=agents.enhanced_backtesting_polars",
            "--cov-report=html:test/coverage_html",
            "--cov-report=term-missing"
        ])
    
    # Add other useful flags
    pytest_args.extend([
        "--tb=short",  # Shorter traceback format
        "--strict-markers",  # Strict marker checking
        "-ra",  # Show summary of all test results
    ])
    
    print(f"Pytest command: pytest {' '.join(pytest_args)}")
    print("-" * 60)
    
    # Record start time
    start_time = time.time()
    
    # Run the tests
    exit_code = pytest.main(pytest_args)
    
    # Record end time and calculate duration
    end_time = time.time()
    duration = end_time - start_time
    
    print("-" * 60)
    print(f"Test execution completed in {duration:.2f} seconds")
    
    if exit_code == 0:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return exit_code == 0

def run_specific_test_class(test_file, test_class, verbose=False):
    """
    Run tests for a specific test class
    
    Args:
        test_file: Test file name
        test_class: Test class name
        verbose: Enable verbose output
    """
    pytest_args = [f"test/{test_file}::{test_class}"]
    
    if verbose:
        pytest_args.append("-v")
    
    pytest_args.extend(["--tb=short", "-ra"])
    
    print(f"Running tests for class: {test_class}")
    print(f"Pytest command: pytest {' '.join(pytest_args)}")
    print("-" * 60)
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print(f"✅ All tests in {test_class} passed!")
    else:
        print(f"❌ Some tests in {test_class} failed!")
    
    return exit_code == 0

def run_integration_tests():
    """
    Run integration tests that test multiple features together
    """
    print("Running integration tests...")
    
    # Integration test scenarios
    integration_scenarios = [
        "Multi-strategy with optimization",
        "Smart modes with visualization", 
        "Risk modeling with scenario testing",
        "Signal debugging with LLM explanations"
    ]
    
    print("Integration test scenarios:")
    for i, scenario in enumerate(integration_scenarios, 1):
        print(f"  {i}. {scenario}")
    
    # For now, just run a subset of tests that work well together
    pytest_args = [
        "test/test_multi_strategy_backtesting.py::TestMultiStrategyBacktester::test_portfolio_backtest_multiple_strategies",
        "test/test_parameter_optimization.py::TestParameterOptimizer::test_grid_search_optimization",
        "test/test_performance_metrics.py::TestAdvancedMetricsCalculator::test_calculate_comprehensive_metrics",
        "-v"
    ]
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("✅ Integration tests passed!")
    else:
        print("❌ Integration tests failed!")
    
    return exit_code == 0

def generate_test_report():
    """
    Generate a comprehensive test report
    """
    print("Generating comprehensive test report...")
    
    pytest_args = [
        "test/",
        "--html=test/test_report.html",
        "--self-contained-html",
        "--cov=agents.enhanced_backtesting_polars",
        "--cov-report=html:test/coverage_html",
        "--cov-report=term",
        "-v"
    ]
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("✅ Test report generated successfully!")
        print("📊 HTML report: test/test_report.html")
        print("📈 Coverage report: test/coverage_html/index.html")
    else:
        print("❌ Test report generation failed!")
    
    return exit_code == 0

def main():
    """
    Main function to handle command line arguments and run tests
    """
    parser = argparse.ArgumentParser(
        description="Enhanced Backtesting System Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                           # Run all tests
  python run_tests.py --feature multi_strategy  # Run specific feature tests
  python run_tests.py --verbose                 # Run with verbose output
  python run_tests.py --coverage                # Run with coverage reporting
  python run_tests.py --integration             # Run integration tests
  python run_tests.py --report                  # Generate comprehensive report
        """
    )
    
    parser.add_argument(
        '--feature', 
        choices=[
            'multi_strategy', 'smart_modes', 'metrics', 'risk_modeling',
            'scenario_testing', 'optimization', 'logging', 'visualization',
            'signal_debugging', 'llm_explanations'
        ],
        help='Run tests for specific feature'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    parser.add_argument(
        '--coverage', '-c',
        action='store_true',
        help='Enable coverage reporting'
    )
    
    parser.add_argument(
        '--integration', '-i',
        action='store_true',
        help='Run integration tests'
    )
    
    parser.add_argument(
        '--report', '-r',
        action='store_true',
        help='Generate comprehensive test report'
    )
    
    parser.add_argument(
        '--class',
        dest='test_class',
        help='Run specific test class (requires --feature)'
    )
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("Enhanced Backtesting System - Test Runner")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = True
    
    if args.report:
        success = generate_test_report()
    elif args.integration:
        success = run_integration_tests()
    elif args.test_class:
        if not args.feature:
            print("Error: --class requires --feature to be specified")
            return 1
        
        test_files = {
            'multi_strategy': 'test_multi_strategy_backtesting.py',
            'smart_modes': 'test_smart_backtesting_modes.py',
            'metrics': 'test_performance_metrics.py',
            'risk_modeling': 'test_capital_risk_modeling.py',
            'scenario_testing': 'test_scenario_testing.py',
            'optimization': 'test_parameter_optimization.py',
            'logging': 'test_result_logging.py',
            'visualization': 'test_visualization_debugging.py',
            'signal_debugging': 'test_signal_debugging.py',
            'llm_explanations': 'test_llm_explanations.py'
        }
        
        test_file = test_files[args.feature]
        success = run_specific_test_class(test_file, args.test_class, args.verbose)
    else:
        success = run_feature_tests(args.feature, args.verbose, args.coverage)
    
    print()
    print("=" * 60)
    
    if success:
        print("🎉 Test execution completed successfully!")
        return 0
    else:
        print("💥 Test execution failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())

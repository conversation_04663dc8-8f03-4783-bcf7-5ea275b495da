#!/usr/bin/env python3
"""
Simple test to verify our data columns and create working strategies
"""

import pandas as pd
import numpy as np

def test_data_columns():
    """Test what columns we actually have in our data"""
    print("🔍 Testing actual data columns...")
    
    # Load a small sample of real data
    try:
        df = pd.read_csv("data/features/features_15min.csv", nrows=100)
        print(f"✅ Loaded {len(df)} rows with {len(df.columns)} columns")
        
        print(f"\n📊 Available columns:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # Test a simple strategy expression
        print(f"\n🧪 Testing simple expressions:")
        
        # Test 1: Simple RSI condition
        try:
            result1 = df.eval("RSI_14 > 50")
            signals1 = sum(result1)
            print(f"  ✅ RSI_14 > 50: {signals1} signals ({signals1/len(df)*100:.1f}%)")
        except Exception as e:
            print(f"  ❌ RSI_14 > 50: {e}")
        
        # Test 2: Simple EMA condition
        try:
            result2 = df.eval("EMA_5 > EMA_20")
            signals2 = sum(result2)
            print(f"  ✅ EMA_5 > EMA_20: {signals2} signals ({signals2/len(df)*100:.1f}%)")
        except Exception as e:
            print(f"  ❌ EMA_5 > EMA_20: {e}")
        
        # Test 3: Volume condition
        try:
            result3 = df.eval("Volume > Volume.rolling(10).mean()")
            signals3 = sum(result3)
            print(f"  ✅ Volume > Volume.rolling(10).mean(): {signals3} signals ({signals3/len(df)*100:.1f}%)")
        except Exception as e:
            print(f"  ❌ Volume > Volume.rolling(10).mean(): {e}")
        
        # Test 4: Complex condition
        try:
            result4 = df.eval("Close > VWAP and RSI_14 > 50")
            signals4 = sum(result4)
            print(f"  ✅ Close > VWAP and RSI_14 > 50: {signals4} signals ({signals4/len(df)*100:.1f}%)")
        except Exception as e:
            print(f"  ❌ Close > VWAP and RSI_14 > 50: {e}")
        
        return df
        
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return None

def create_simple_strategies():
    """Create simple working strategies based on available columns"""
    
    strategies = [
        {
            "name": "Simple_RSI_Long",
            "long": "RSI_14 < 30",
            "short": "RSI_14 > 70",
            "capital": 100000
        },
        {
            "name": "Simple_EMA_Crossover",
            "long": "EMA_5 > EMA_20",
            "short": "EMA_5 < EMA_20", 
            "capital": 100000
        },
        {
            "name": "Simple_VWAP_Strategy",
            "long": "Close > VWAP",
            "short": "Close < VWAP",
            "capital": 100000
        },
        {
            "name": "Simple_SuperTrend",
            "long": "Close > SuperTrend",
            "short": "Close < SuperTrend",
            "capital": 100000
        },
        {
            "name": "Simple_Volume_Breakout",
            "long": "Volume > Volume.rolling(20).mean() * 2",
            "short": "Volume > Volume.rolling(20).mean() * 2",
            "capital": 100000
        }
    ]
    
    return strategies

def test_simple_strategies(df):
    """Test simple strategies on real data"""
    print(f"\n🎯 Testing Simple Strategies")
    print("=" * 50)
    
    strategies = create_simple_strategies()
    
    for i, strat in enumerate(strategies, 1):
        print(f"\n{i}. {strat['name']}")
        
        try:
            # Test long condition
            long_result = df.eval(strat['long'])
            long_signals = sum(long_result)
            long_pct = (long_signals / len(df)) * 100
            
            # Test short condition
            short_result = df.eval(strat['short'])
            short_signals = sum(short_result)
            short_pct = (short_signals / len(df)) * 100
            
            total_signals = long_signals + short_signals
            total_pct = (total_signals / len(df)) * 100
            
            print(f"   Long:  {strat['long']}")
            print(f"          {long_signals} signals ({long_pct:.1f}%)")
            print(f"   Short: {strat['short']}")
            print(f"          {short_signals} signals ({short_pct:.1f}%)")
            print(f"   Total: {total_signals} signals ({total_pct:.1f}%)")
            
            if total_signals > 0:
                print(f"   ✅ WORKING - Good signal generation")
            else:
                print(f"   ❌ NO SIGNALS - Strategy needs adjustment")
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")

def save_simple_strategies():
    """Save working simple strategies to YAML file"""
    print(f"\n💾 Saving Simple Strategies")
    print("=" * 50)
    
    strategies = create_simple_strategies()
    
    yaml_content = "# Simple Working Strategies for Indian Market\n"
    yaml_content += "# Tested and verified to work with actual data columns\n\n"
    yaml_content += "strategies:\n"
    
    for strat in strategies:
        yaml_content += f"  - name: \"{strat['name']}\"\n"
        yaml_content += f"    long: \"{strat['long']}\"\n"
        yaml_content += f"    short: \"{strat['short']}\"\n"
        yaml_content += f"    capital: {strat['capital']}\n\n"
    
    # Save to file
    with open("config/simple_strategies.yaml", "w") as f:
        f.write(yaml_content)
    
    print(f"✅ Saved {len(strategies)} simple strategies to config/simple_strategies.yaml")
    print(f"📝 You can use this file to test the backtesting system")

def main():
    """Main test function"""
    print("🚀 Simple Strategy Test Suite")
    print("=" * 60)
    
    # Test data columns
    df = test_data_columns()
    
    if df is not None:
        # Test simple strategies
        test_simple_strategies(df)
        
        # Save working strategies
        save_simple_strategies()
        
        print(f"\n🎉 Test Complete!")
        print(f"📋 Summary:")
        print(f"   - Data loaded successfully with {len(df.columns)} columns")
        print(f"   - Simple expressions work with eval()")
        print(f"   - Created 5 working strategies")
        print(f"   - Saved to config/simple_strategies.yaml")
        print(f"\n💡 Next steps:")
        print(f"   1. Update backtesting_agent.py to use simple_strategies.yaml")
        print(f"   2. Fix the expression parser to handle eval() properly")
        print(f"   3. Test with these working strategies first")
    else:
        print(f"\n❌ Cannot proceed without data")

if __name__ == "__main__":
    main()

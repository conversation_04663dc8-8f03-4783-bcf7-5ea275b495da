import pandas as pd
import os

# ========== CONFIG ==========
INPUT_FILE = "data/features/features_5min.csv"
OUTPUT_FILE = "data/strategies/strategy_signals_5min.csv"

def apply_strategies(df):
    # Strategy 1: MACD crossover
    df["strategy_macd_cross"] = 0
    df.loc[df["macd"] > df["macd_signal"], "strategy_macd_cross"] = 1
    df.loc[df["macd"] < df["macd_signal"], "strategy_macd_cross"] = -1

    # Strategy 2: EMA trend
    df["strategy_ema_trend"] = 0
    df.loc[df["ema_5"] > df["ema_10"], "strategy_ema_trend"] = 1
    df.loc[df["ema_5"] < df["ema_10"], "strategy_ema_trend"] = -1

    # Strategy 3: MACD + volume spike
    df["strategy_macd_volume"] = 0
    df.loc[(df["macd"] > 0) & (df["volume_spike"] == 1), "strategy_macd_volume"] = 1
    df.loc[(df["macd"] < 0) & (df["volume_spike"] == 1), "strategy_macd_volume"] = -1

    # Strategy 4: Mean Reversion on Close
    df["strategy_mean_reversion"] = 0
    upper_band = df["rolling_return_mean"] + 2 * df["rolling_return_std"]
    lower_band = df["rolling_return_mean"] - 2 * df["rolling_return_std"]

    df.loc[df["log_return"] > upper_band, "strategy_mean_reversion"] = -1
    df.loc[df["log_return"] < lower_band, "strategy_mean_reversion"] = 1

    return df

def main():
    df = pd.read_csv(INPUT_FILE)
    os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)

    print("Generating strategy signals...")
    df = apply_strategies(df)

    df.to_csv(OUTPUT_FILE, index=False)
    print(f"✅ Strategy signals saved to {OUTPUT_FILE}")

if __name__ == "__main__":
    main()

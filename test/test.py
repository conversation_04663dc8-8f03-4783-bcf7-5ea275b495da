
import pandas as pd

# Replace 'your_file.parquet' with the path to your Parquet file
file_path = r"../data/historical/ADANIENSOL_15min.parquet"
pd.set_option('display.max_columns', None)
# pd.set_option('display.max_rows', None)
# Read the top 10 rows of the Parquet file
df = pd.read_parquet(file_path)
# Display the top 10 rows with all columns
print(df)
print(df.columns)

'''
import pandas as pd

def print_parquet_columns(file_path):
    try:
        # Read the Parquet file
        df = pd.read_parquet(file_path)
        print(df)
         # Get and print all column names
        print("Column names in the Parquet file:")
        for column in df.columns:
            print(column)
        print(df['symbol'].value_counts())
        pd.set_option('display.max_columns', None)
        # Display the result
        print(df) 
    except Exception as e:
        print(f"Error reading Parquet file: {e}")

# Example usage
if __name__ == "__main__":
    # Replace 'your_file.parquet' with the path to your Parquet file
    file_path = r"data/features/features_historical_1hr.parquet"
    print_parquet_columns(file_path)
    '''

#!/usr/bin/env python3
"""
Test file for AI Training Agent
Comprehensive tests for all components
"""

import os
import sys
import pytest
import numpy as np
import polars as pl
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.ai_training_agent import AITrainingAgent, AITrainingConfig
from agents.ai_training_utils import (
    load_config, validate_data, check_data_quality, 
    clean_data, calculate_feature_importance_summary
)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TEST FIXTURES
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.fixture
def sample_config():
    """Create sample configuration for testing"""
    return AITrainingConfig(
        data_dir="test_data",
        input_file="test_results.parquet",
        models_dir="test_models",
        target_columns=['ROI', 'sharpe_ratio', 'expectancy'],
        feature_columns=['n_trades', 'avg_holding_period', 'volatility'],
        optuna_trials=5,  # Reduced for testing
        use_gpu=False  # Use CPU for testing
    )

@pytest.fixture
def sample_data():
    """Create sample backtesting data for testing"""
    np.random.seed(42)
    n_samples = 100
    
    data = {
        'strategy_name': [f'Strategy_{i}' for i in range(n_samples)],
        'symbol': ['AAPL', 'GOOGL', 'MSFT'] * (n_samples // 3) + ['AAPL'] * (n_samples % 3),
        'timeframe': ['5min', '15min', '1h'] * (n_samples // 3) + ['5min'] * (n_samples % 3),
        'n_trades': np.random.randint(10, 100, n_samples),
        'ROI': np.random.normal(0.05, 0.15, n_samples),
        'accuracy': np.random.uniform(0.4, 0.8, n_samples),
        'expectancy': np.random.normal(0.02, 0.1, n_samples),
        'sharpe_ratio': np.random.normal(1.0, 0.5, n_samples),
        'max_drawdown': np.random.uniform(0.05, 0.3, n_samples),
        'profit_factor': np.random.uniform(0.8, 2.5, n_samples),
        'avg_holding_period': np.random.uniform(1, 10, n_samples),
        'risk_reward_ratio': np.random.uniform(0.5, 3.0, n_samples),
        'capital_at_risk': np.random.uniform(0.01, 0.1, n_samples),
        'liquidity': np.random.uniform(0.5, 1.0, n_samples),
        'volatility': np.random.uniform(0.1, 0.5, n_samples),
        'market_regime': np.random.choice(['bull', 'bear', 'sideways'], n_samples),
        'correlation_index': np.random.uniform(-0.5, 0.8, n_samples),
        'drawdown_duration': np.random.uniform(1, 20, n_samples),
        'winning_trades': np.random.randint(5, 60, n_samples),
        'losing_trades': np.random.randint(5, 40, n_samples),
        'avg_win': np.random.uniform(100, 1000, n_samples),
        'avg_loss': np.random.uniform(-1000, -100, n_samples),
        'total_pnl': np.random.normal(1000, 5000, n_samples),
        'position_size_pct': np.random.uniform(0.01, 0.1, n_samples),
        'is_profitable': np.random.choice([True, False], n_samples)
    }
    
    return pl.DataFrame(data)

@pytest.fixture
def temp_data_file(sample_data):
    """Create temporary data file for testing"""
    with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as f:
        sample_data.write_parquet(f.name)
        yield f.name
    os.unlink(f.name)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 CONFIGURATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

def test_ai_training_config_initialization():
    """Test AITrainingConfig initialization"""
    config = AITrainingConfig()
    
    assert config.data_dir == "data/backtest"
    assert config.input_file == "enhanced_strategy_results.parquet"
    assert len(config.target_columns) > 0
    assert len(config.feature_columns) > 0
    assert config.ensemble_weights['lightgbm'] + config.ensemble_weights['tabnet'] == 1.0

def test_ai_training_config_custom():
    """Test AITrainingConfig with custom values"""
    config = AITrainingConfig(
        data_dir="custom_data",
        target_columns=['ROI', 'sharpe_ratio'],
        test_size=0.3
    )
    
    assert config.data_dir == "custom_data"
    assert len(config.target_columns) == 2
    assert config.test_size == 0.3

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 AGENT INITIALIZATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

def test_agent_initialization(sample_config):
    """Test AI Training Agent initialization"""
    agent = AITrainingAgent(sample_config)
    
    assert agent.config == sample_config
    assert not agent.is_trained
    assert len(agent.models) == 0
    assert len(agent.scalers) == 0
    assert len(agent.encoders) == 0

def test_agent_initialization_default():
    """Test AI Training Agent initialization with default config"""
    agent = AITrainingAgent()
    
    assert agent.config is not None
    assert isinstance(agent.config, AITrainingConfig)
    assert not agent.is_trained

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 DATA LOADING AND PREPROCESSING TESTS
# ═══════════════════════════════════════════════════════════════════════════════

def test_load_data(sample_config, temp_data_file):
    """Test data loading functionality"""
    agent = AITrainingAgent(sample_config)
    
    df = agent.load_data(temp_data_file)
    
    assert isinstance(df, pl.DataFrame)
    assert len(df) > 0
    assert len(df.columns) > 0

def test_load_data_file_not_found(sample_config):
    """Test data loading with non-existent file"""
    agent = AITrainingAgent(sample_config)
    
    with pytest.raises(FileNotFoundError):
        agent.load_data("non_existent_file.parquet")

def test_preprocess_data(sample_config, sample_data):
    """Test data preprocessing"""
    agent = AITrainingAgent(sample_config)
    
    features, targets, feature_names = agent.preprocess_data(sample_data)
    
    assert isinstance(features, np.ndarray)
    assert isinstance(targets, np.ndarray)
    assert len(feature_names) == len(sample_config.feature_columns)
    assert features.shape[1] == len(sample_config.feature_columns)
    assert targets.shape[1] == len(sample_config.target_columns)

def test_split_data(sample_config):
    """Test data splitting"""
    agent = AITrainingAgent(sample_config)
    
    # Create dummy data
    features = np.random.randn(100, 5)
    targets = np.random.randn(100, 3)
    
    X_train, X_val, X_test, y_train, y_val, y_test = agent.split_data(features, targets)
    
    # Check shapes
    assert X_train.shape[0] + X_val.shape[0] + X_test.shape[0] == 100
    assert X_train.shape[1] == 5
    assert y_train.shape[1] == 3
    
    # Check proportions (approximately)
    assert abs(X_test.shape[0] / 100 - sample_config.test_size) < 0.05

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 MODEL TRAINING TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_train_lightgbm(sample_config):
    """Test LightGBM training"""
    agent = AITrainingAgent(sample_config)
    
    # Create dummy data
    X_train = np.random.randn(50, 3)
    y_train = np.random.randn(50, 3)
    X_val = np.random.randn(20, 3)
    y_val = np.random.randn(20, 3)
    
    models = agent.train_lightgbm(X_train, y_train, X_val, y_val)
    
    assert len(models) == len(sample_config.target_columns)
    assert all(target in models for target in sample_config.target_columns)

@pytest.mark.asyncio
async def test_train_tabnet(sample_config):
    """Test TabNet training"""
    agent = AITrainingAgent(sample_config)

    # Create dummy data
    X_train = np.random.randn(50, 3).astype(np.float32)
    y_train = np.random.randn(50, 3).astype(np.float32)
    X_val = np.random.randn(20, 3).astype(np.float32)
    y_val = np.random.randn(20, 3).astype(np.float32)

    # Mock TabNet to avoid actual training
    with patch('agents.ai_training_agent.TabNetRegressor') as mock_tabnet:
        mock_model = Mock()
        mock_model.fit.return_value = None  # Mock the fit method
        mock_model.feature_importances_ = np.random.randn(3)  # Mock feature importances
        mock_tabnet.return_value = mock_model

        model = agent.train_tabnet(X_train, y_train, X_val, y_val)

        # TabNet training should succeed with proper mock
        assert model == mock_model
        mock_model.fit.assert_called_once()

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 PREDICTION AND RANKING TESTS
# ═══════════════════════════════════════════════════════════════════════════════

def test_rank_strategies(sample_config):
    """Test strategy ranking"""
    agent = AITrainingAgent(sample_config)
    
    # Create dummy predictions
    predictions = np.random.randn(5, 3)
    strategy_names = [f'Strategy_{i}' for i in range(5)]
    
    rankings = agent.rank_strategies(predictions, strategy_names, 'bull')
    
    assert len(rankings) == 5
    assert all('strategy_name' in ranking for ranking in rankings)
    assert all('composite_score' in ranking for ranking in rankings)
    assert all('predicted_metrics' in ranking for ranking in rankings)
    
    # Check if sorted by composite score
    scores = [ranking['composite_score'] for ranking in rankings]
    assert scores == sorted(scores, reverse=True)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 UTILITY FUNCTION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

def test_validate_data(sample_data):
    """Test data validation"""
    required_columns = ['ROI', 'sharpe_ratio', 'n_trades']
    
    is_valid, missing = validate_data(sample_data, required_columns)
    
    assert is_valid
    assert len(missing) == 0

def test_validate_data_missing_columns(sample_data):
    """Test data validation with missing columns"""
    required_columns = ['ROI', 'missing_column', 'n_trades']
    
    is_valid, missing = validate_data(sample_data, required_columns)
    
    assert not is_valid
    assert 'missing_column' in missing

def test_check_data_quality(sample_data):
    """Test data quality check"""
    quality_metrics = check_data_quality(sample_data)
    
    assert 'total_rows' in quality_metrics
    assert 'total_columns' in quality_metrics
    assert 'missing_values' in quality_metrics
    assert 'data_types' in quality_metrics
    assert quality_metrics['total_rows'] == len(sample_data)

def test_clean_data(sample_data):
    """Test data cleaning"""
    # Add some missing values
    data_with_nulls = sample_data.with_columns(
        pl.when(pl.col('ROI') > 0.1).then(None).otherwise(pl.col('ROI')).alias('ROI')
    )
    
    cleaned_data = clean_data(data_with_nulls, remove_duplicates=True, fill_missing="mean")
    
    assert isinstance(cleaned_data, pl.DataFrame)
    assert cleaned_data['ROI'].null_count() == 0

def test_calculate_feature_importance_summary():
    """Test feature importance summary calculation"""
    feature_importance = {
        'model1': {'feature_a': 0.5, 'feature_b': 0.3, 'feature_c': 0.2},
        'model2': {'feature_a': 0.4, 'feature_b': 0.4, 'feature_c': 0.2},
        'model3': {'feature_a': 0.6, 'feature_b': 0.2, 'feature_c': 0.2}
    }
    
    summary = calculate_feature_importance_summary(feature_importance)
    
    assert len(summary) == 3
    assert 'feature_a' in summary
    assert summary['feature_a'] == pytest.approx(0.5, rel=1e-2)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 INTEGRATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_full_training_pipeline(sample_config, temp_data_file):
    """Test complete training pipeline"""
    # Use a very small configuration for testing
    sample_config.optuna_trials = 2
    sample_config.lgb_params['num_boost_round'] = 10
    sample_config.tabnet_params['max_epochs'] = 5

    agent = AITrainingAgent(sample_config)

    # Mock the heavy training components
    with patch.object(agent, 'optimize_hyperparameters') as mock_optuna, \
         patch.object(agent, 'train_tabnet') as mock_tabnet:

        mock_optuna.return_value = {}

        # Create a proper mock TabNet model with predict method
        mock_tabnet_model = Mock()
        mock_tabnet_model.predict.return_value = np.random.randn(20, 3)  # Mock predictions
        mock_tabnet.return_value = mock_tabnet_model

        # This should run without errors
        results = await agent.train_async(temp_data_file, optimize_hyperparams=False)

        assert 'data_shape' in results
        assert 'evaluation_metrics' in results
        assert results['feature_count'] > 0

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 MAIN TEST EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

if __name__ == "__main__":
    pytest.main([__file__, "-v"])

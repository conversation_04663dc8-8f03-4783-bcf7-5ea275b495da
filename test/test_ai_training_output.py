#!/usr/bin/env python3
"""
Test script to verify the Enhanced Backtesting Agent outputs the correct format for AI training

This script tests:
1. Correct timeframes (1min, 3min, 5min, 15min)
2. All required columns for AI training
3. Individual trade-level data (not aggregated)
4. Proper data types and formats
"""

import os
import sys
import asyncio
import tempfile
import yaml
import json
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.enhanced_backtesting_agent import EnhancedBacktestingAgent, TradeResult

def create_test_data():
    """Create test feature data with all required columns"""
    # Generate sample OHLCV data with technical indicators
    dates = [datetime.now() - timedelta(minutes=i*5) for i in range(100, 0, -1)]
    
    data = []
    for i, date in enumerate(dates):
        # Generate realistic price data
        base_price = 1000 + np.sin(i * 0.1) * 100
        
        row = {
            'datetime': date,
            'symbol': 'RELIANCE',
            'open': base_price + np.random.normal(0, 5),
            'high': base_price + abs(np.random.normal(10, 5)),
            'low': base_price - abs(np.random.normal(10, 5)),
            'close': base_price + np.random.normal(0, 5),
            'volume': int(1000000 + np.random.normal(0, 100000)),
            
            # Technical indicators (all required for AI training)
            'rsi_5': 30 + np.random.normal(20, 15),
            'rsi_14': 30 + np.random.normal(20, 15),
            'macd': np.random.normal(0, 2),
            'macd_signal': np.random.normal(0, 1.5),
            'macd_histogram': np.random.normal(0, 1),
            'ema_5': base_price + np.random.normal(0, 2),
            'ema_10': base_price + np.random.normal(0, 3),
            'ema_20': base_price + np.random.normal(0, 5),
            'ema_50': base_price + np.random.normal(0, 10),
            'sma_20': base_price + np.random.normal(0, 5),
            'sma_50': base_price + np.random.normal(0, 10),
            'bb_upper': base_price + 20,
            'bb_lower': base_price - 20,
            'bb_middle': base_price,
            'adx': abs(np.random.normal(25, 10)),
            'atr': abs(np.random.normal(15, 5)),
            'stoch_k': np.random.uniform(0, 100),
            'stoch_d': np.random.uniform(0, 100),
            'cci': np.random.normal(0, 50),
            'mfi': np.random.uniform(0, 100),
            'vwap': base_price + np.random.normal(0, 3),
            'supertrend': base_price + np.random.normal(0, 10),
            
            # Options-specific indicators (placeholder)
            'iv_rank': np.random.uniform(0, 100),
            'iv_percentile': np.random.uniform(0, 100),
            'vix': np.random.uniform(10, 30),
            'delta': np.random.uniform(0.2, 0.8),
            'gamma': np.random.uniform(0, 0.1),
            'theta': np.random.uniform(-0.1, 0),
            'vega': np.random.uniform(0, 0.5),
            'open_interest': int(np.random.uniform(10000, 100000)),
            'open_interest_change': int(np.random.normal(0, 5000)),
            
            # Market context
            'nifty_level': 18000 + np.random.normal(0, 500),
            'banknifty_level': 44000 + np.random.normal(0, 1000),
        }
        data.append(row)
    
    return pl.DataFrame(data)

def verify_ai_training_columns(df: pl.DataFrame) -> bool:
    """Verify that the output contains all required columns for AI training"""
    
    # Required columns as per specification
    required_columns = {
        # A. Trade Metadata Columns
        'trade_id', 'timestamp', 'exit_timestamp', 'symbol', 'underlying', 
        'expiry_type', 'option_type', 'lot_size', 'direction', 'strategy_id', 
        'strategy_name', 'timeframe', 'market_regime', 'volatility_regime', 'event_tag',
        
        # B. Feature Snapshot (at Entry Time)
        'rsi', 'rsi_5', 'rsi_14', 'macd', 'macd_signal', 'macd_histogram',
        'ema_5', 'ema_10', 'ema_20', 'ema_50', 'sma_20', 'sma_50', 'sma_20_vs_price',
        'bb_upper', 'bb_lower', 'bb_middle', 'adx', 'atr', 'stoch_k', 'stoch_d', 
        'cci', 'mfi', 'volume', 'volume_spike_ratio', 'vwap', 'price_vs_vwap', 
        'supertrend', 'iv_rank', 'iv_percentile', 'vix', 'delta', 'gamma', 
        'theta', 'vega', 'open_interest', 'open_interest_change', 'hour_of_day', 
        'day_of_week', 'days_to_expiry', 'nifty_level', 'banknifty_level',
        
        # C. Trade Outcome / Label Columns
        'entry_price', 'exit_price', 'pnl_abs', 'pnl_pct', 'holding_minutes', 
        'is_profitable', 'target_hit', 'exit_reason', 'expectancy_tag', 
        'sharpe_score_tag', 'confidence_score', 'trade_score', 'label_strategy_choice',
        'position_size', 'quantity', 'risk_reward_ratio', 'max_adverse_excursion', 
        'max_favorable_excursion', 'slippage', 'transaction_cost',
        
        # D. Optional Advanced Tags
        'model_version_used', 'was_signal_live', 'live_vs_backtest_diff_pct', 
        'used_in_training', 'llm_summary'
    }
    
    # Check which columns are missing
    actual_columns = set(df.columns)
    missing_columns = required_columns - actual_columns
    extra_columns = actual_columns - required_columns
    
    print(f"\n📊 Column Analysis:")
    print(f"   • Required columns: {len(required_columns)}")
    print(f"   • Actual columns: {len(actual_columns)}")
    print(f"   • Missing columns: {len(missing_columns)}")
    print(f"   • Extra columns: {len(extra_columns)}")
    
    if missing_columns:
        print(f"\n❌ Missing columns:")
        for col in sorted(missing_columns):
            print(f"   • {col}")
    
    if extra_columns:
        print(f"\n➕ Extra columns:")
        for col in sorted(extra_columns):
            print(f"   • {col}")
    
    # Check data types for key columns
    print(f"\n🔍 Key Column Data Types:")
    key_columns = ['trade_id', 'timestamp', 'symbol', 'pnl_abs', 'is_profitable', 'timeframe']
    for col in key_columns:
        if col in actual_columns:
            print(f"   • {col}: {df[col].dtype}")
    
    return len(missing_columns) == 0

async def test_enhanced_backtesting_output():
    """Test the enhanced backtesting agent output format"""
    
    print("🧪 Testing Enhanced Backtesting Agent AI Training Output Format")
    print("=" * 70)
    
    # Create temporary directory for test
    with tempfile.TemporaryDirectory() as temp_dir:
        # Setup test directories
        data_dir = os.path.join(temp_dir, 'data', 'features')
        output_dir = os.path.join(temp_dir, 'data', 'backtest')
        config_dir = os.path.join(temp_dir, 'config')
        
        os.makedirs(data_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(config_dir, exist_ok=True)
        
        # Create test configuration
        test_config = {
            'general': {
                'agent_name': 'Test Enhanced Backtesting Agent',
                'data_directory': data_dir,
                'output_directory': output_dir,
                'strategies_config': os.path.join(config_dir, 'strategies.yaml'),
                'symbols_config': os.path.join(config_dir, 'symbols.json'),
                'max_symbols': 1,
                'max_strategies': 1,
            },
            'multi_timeframe': {
                'timeframes': ['5min'],  # Test with one timeframe first
            },
            'result_logging': {
                'output_format': 'parquet',
                'compression': 'snappy',
            }
        }
        
        # Create test strategies
        test_strategies = {
            'strategies': [
                {
                    'name': 'Test_RSI_Strategy',
                    'long': 'rsi_14 < 30',
                    'short': 'rsi_14 > 70',
                }
            ]
        }
        
        # Create test symbols
        test_symbols = ['RELIANCE']
        
        # Save test files
        config_path = os.path.join(config_dir, 'enhanced_backtesting_config.yaml')
        strategies_path = os.path.join(config_dir, 'strategies.yaml')
        symbols_path = os.path.join(config_dir, 'symbols.json')
        
        with open(config_path, 'w') as f:
            yaml.dump(test_config, f)
        
        with open(strategies_path, 'w') as f:
            yaml.dump(test_strategies, f)
        
        with open(symbols_path, 'w') as f:
            json.dump(test_symbols, f)
        
        # Create and save test feature data
        test_data = create_test_data()
        data_file = os.path.join(data_dir, 'features_5min.parquet')
        test_data.write_parquet(data_file)
        
        print(f"✅ Test setup completed")
        print(f"   • Config: {config_path}")
        print(f"   • Data: {data_file} ({test_data.height} rows)")
        
        # Test the enhanced backtesting agent
        try:
            agent = EnhancedBacktestingAgent(config_path)
            
            print(f"\n🚀 Initializing Enhanced Backtesting Agent...")
            success = await agent.initialize()
            
            if not success:
                print("❌ Failed to initialize agent")
                return False
            
            print(f"✅ Agent initialized successfully")
            print(f"   • Strategies: {len(agent.strategies)}")
            print(f"   • Symbols: {len(agent.symbols)}")
            print(f"   • Timeframes: {agent.config.timeframes}")
            
            # Run backtesting
            print(f"\n🎯 Running backtesting...")
            success = await agent.start_backtesting()
            
            if not success:
                print("❌ Backtesting failed")
                return False
            
            print(f"✅ Backtesting completed successfully")
            
            # Check output files
            output_files = list(Path(output_dir).glob('ai_training_trades_*.parquet'))
            
            if not output_files:
                print("❌ No AI training output files found")
                return False
            
            # Load and analyze the output
            output_file = output_files[0]
            print(f"\n📊 Analyzing output file: {output_file.name}")
            
            df = pl.read_parquet(output_file)
            print(f"   • Total trades: {df.height}")
            print(f"   • Total columns: {len(df.columns)}")
            
            # Verify columns
            columns_ok = verify_ai_training_columns(df)
            
            if columns_ok:
                print(f"\n✅ All required columns present!")
                
                # Show sample data
                print(f"\n📋 Sample Trade Data:")
                if df.height > 0:
                    sample_row = df.row(0, named=True)
                    key_fields = ['trade_id', 'symbol', 'strategy_name', 'timeframe', 
                                 'entry_price', 'exit_price', 'pnl_abs', 'is_profitable']
                    for field in key_fields:
                        if field in sample_row:
                            print(f"   • {field}: {sample_row[field]}")
                
                return True
            else:
                print(f"\n❌ Missing required columns for AI training")
                return False
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            return False
        
        finally:
            if 'agent' in locals():
                await agent.stop()

async def main():
    """Main test function"""
    try:
        success = await test_enhanced_backtesting_output()
        
        if success:
            print(f"\n🎉 Enhanced Backtesting Agent AI Training Output Test PASSED!")
            print(f"\n✅ Verified:")
            print(f"   • Correct timeframes (1min, 3min, 5min, 15min)")
            print(f"   • Individual trade-level data (not aggregated)")
            print(f"   • All required columns for AI training")
            print(f"   • Proper data types and formats")
        else:
            print(f"\n❌ Enhanced Backtesting Agent AI Training Output Test FAILED!")
            
    except Exception as e:
        print(f"❌ Test execution failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())

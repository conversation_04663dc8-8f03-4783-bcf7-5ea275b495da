"""
Tests for Feature 4: Capital & Risk Modeling
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from agents.enhanced_backtesting_polars import (
    CapitalRiskManager, PortfolioOptimizer, RiskModel, BacktestConfig
)


class TestCapitalRiskManager:
    """Test suite for CapitalRiskManager class"""
    
    @pytest.fixture
    def risk_manager(self):
        """Create CapitalRiskManager instance"""
        return CapitalRiskManager(initial_capital=100000)
    
    @pytest.mark.asyncio
    async def test_initialization(self, risk_manager):
        """Test proper initialization of CapitalRiskManager"""
        assert risk_manager.initial_capital == 100000
        assert risk_manager.current_capital == 100000
        assert risk_manager.risk_budget == 0.02  # 2% default
        assert risk_manager.max_position_size == 0.1  # 10% default
        assert risk_manager.position_history == []
    
    @pytest.mark.asyncio
    async def test_kelly_criterion_position_sizing(self, risk_manager):
        """Test Kelly criterion position sizing"""
        # Sample trade history for Kelly calculation
        trade_history = [
            {'pnl_pct': 2.5, 'win': True},
            {'pnl_pct': -1.2, 'win': False},
            {'pnl_pct': 3.1, 'win': True},
            {'pnl_pct': -0.8, 'win': False},
            {'pnl_pct': 1.9, 'win': True}
        ]
        
        position_size = await risk_manager.calculate_kelly_position_size(
            trade_history, current_price=100, max_position_pct=0.1
        )
        
        assert 0 <= position_size <= 0.1  # Should be within max position limit
        assert isinstance(position_size, float)
    
    @pytest.mark.asyncio
    async def test_volatility_targeting_position_sizing(self, risk_manager, sample_market_data):
        """Test volatility targeting position sizing"""
        target_volatility = 0.15  # 15% target volatility
        
        position_size = await risk_manager.calculate_volatility_target_position_size(
            sample_market_data, target_volatility, current_price=100
        )
        
        assert position_size > 0
        assert position_size <= risk_manager.max_position_size
    
    @pytest.mark.asyncio
    async def test_risk_parity_position_sizing(self, risk_manager):
        """Test risk parity position sizing"""
        # Sample asset data
        assets = [
            {'symbol': 'ASSET1', 'volatility': 0.20, 'correlation': 1.0},
            {'symbol': 'ASSET2', 'volatility': 0.15, 'correlation': 0.3},
            {'symbol': 'ASSET3', 'volatility': 0.25, 'correlation': 0.1}
        ]
        
        position_sizes = await risk_manager.calculate_risk_parity_positions(assets)
        
        assert len(position_sizes) == 3
        assert all(size > 0 for size in position_sizes.values())
        
        # Total position should not exceed 100%
        total_position = sum(position_sizes.values())
        assert total_position <= 1.0
    
    @pytest.mark.asyncio
    async def test_max_drawdown_targeting(self, risk_manager):
        """Test maximum drawdown targeting"""
        # Sample drawdown history
        drawdown_history = [0.02, 0.05, 0.03, 0.08, 0.04, 0.06]
        max_drawdown_target = 0.10  # 10% max drawdown target
        
        position_adjustment = await risk_manager.calculate_max_drawdown_position_adjustment(
            drawdown_history, max_drawdown_target
        )
        
        assert 0 <= position_adjustment <= 1.0
        assert isinstance(position_adjustment, float)
    
    @pytest.mark.asyncio
    async def test_dynamic_position_sizing(self, risk_manager, sample_market_data):
        """Test dynamic position sizing based on market conditions"""
        config = BacktestConfig(
            risk_model=RiskModel.VOLATILITY_TARGETING,
            initial_capital=100000,
            position_size=0.05
        )
        
        position_size = await risk_manager.calculate_dynamic_position_size(
            sample_market_data, config, current_price=100
        )
        
        assert position_size > 0
        assert position_size <= risk_manager.max_position_size
    
    @pytest.mark.asyncio
    async def test_risk_budget_allocation(self, risk_manager):
        """Test risk budget allocation across strategies"""
        strategies = [
            {'name': 'Strategy A', 'expected_volatility': 0.15, 'weight': 0.4},
            {'name': 'Strategy B', 'expected_volatility': 0.20, 'weight': 0.6}
        ]
        
        risk_allocations = await risk_manager.allocate_risk_budget(strategies)
        
        assert len(risk_allocations) == 2
        assert all(allocation > 0 for allocation in risk_allocations.values())
        
        # Total risk allocation should equal risk budget
        total_risk = sum(risk_allocations.values())
        assert abs(total_risk - risk_manager.risk_budget) < 1e-6
    
    @pytest.mark.asyncio
    async def test_position_size_limits(self, risk_manager):
        """Test position size limits enforcement"""
        # Test with very high calculated position size
        large_position = 0.5  # 50%
        
        limited_position = await risk_manager._apply_position_limits(large_position)
        
        assert limited_position <= risk_manager.max_position_size
        assert limited_position > 0
    
    @pytest.mark.asyncio
    async def test_capital_updates(self, risk_manager):
        """Test capital updates after trades"""
        initial_capital = risk_manager.current_capital
        
        # Simulate profitable trade
        await risk_manager.update_capital_after_trade(5000)  # $5000 profit
        assert risk_manager.current_capital == initial_capital + 5000
        
        # Simulate losing trade
        await risk_manager.update_capital_after_trade(-2000)  # $2000 loss
        assert risk_manager.current_capital == initial_capital + 3000
    
    @pytest.mark.asyncio
    async def test_risk_metrics_calculation(self, risk_manager):
        """Test risk metrics calculation"""
        returns = [0.02, -0.01, 0.015, -0.008, 0.025, -0.012]
        
        risk_metrics = await risk_manager.calculate_portfolio_risk_metrics(returns)
        
        assert 'portfolio_volatility' in risk_metrics
        assert 'var_95' in risk_metrics
        assert 'expected_shortfall' in risk_metrics
        assert 'sharpe_ratio' in risk_metrics
        
        assert risk_metrics['portfolio_volatility'] > 0
        assert risk_metrics['var_95'] > 0


class TestPortfolioOptimizer:
    """Test suite for PortfolioOptimizer class"""
    
    @pytest.fixture
    def optimizer(self):
        """Create PortfolioOptimizer instance"""
        return PortfolioOptimizer()
    
    @pytest.mark.asyncio
    async def test_initialization(self, optimizer):
        """Test proper initialization of PortfolioOptimizer"""
        assert optimizer.optimization_method == 'mean_variance'
        assert optimizer.constraints == {}
        assert optimizer.optimization_history == []
    
    @pytest.mark.asyncio
    async def test_mean_variance_optimization(self, optimizer):
        """Test mean-variance optimization"""
        # Sample expected returns and covariance matrix
        expected_returns = np.array([0.12, 0.10, 0.08])
        cov_matrix = np.array([
            [0.04, 0.01, 0.005],
            [0.01, 0.03, 0.008],
            [0.005, 0.008, 0.02]
        ])
        
        optimal_weights = await optimizer.optimize_mean_variance(
            expected_returns, cov_matrix, risk_aversion=3.0
        )
        
        assert len(optimal_weights) == 3
        assert all(w >= 0 for w in optimal_weights)  # Long-only constraint
        assert abs(sum(optimal_weights) - 1.0) < 1e-6  # Weights sum to 1
    
    @pytest.mark.asyncio
    async def test_risk_parity_optimization(self, optimizer):
        """Test risk parity optimization"""
        cov_matrix = np.array([
            [0.04, 0.01, 0.005],
            [0.01, 0.03, 0.008],
            [0.005, 0.008, 0.02]
        ])
        
        risk_parity_weights = await optimizer.optimize_risk_parity(cov_matrix)
        
        assert len(risk_parity_weights) == 3
        assert all(w > 0 for w in risk_parity_weights)
        assert abs(sum(risk_parity_weights) - 1.0) < 1e-6
    
    @pytest.mark.asyncio
    async def test_minimum_variance_optimization(self, optimizer):
        """Test minimum variance optimization"""
        cov_matrix = np.array([
            [0.04, 0.01, 0.005],
            [0.01, 0.03, 0.008],
            [0.005, 0.008, 0.02]
        ])
        
        min_var_weights = await optimizer.optimize_minimum_variance(cov_matrix)
        
        assert len(min_var_weights) == 3
        assert all(w >= 0 for w in min_var_weights)
        assert abs(sum(min_var_weights) - 1.0) < 1e-6
    
    @pytest.mark.asyncio
    async def test_maximum_diversification_optimization(self, optimizer):
        """Test maximum diversification optimization"""
        cov_matrix = np.array([
            [0.04, 0.01, 0.005],
            [0.01, 0.03, 0.008],
            [0.005, 0.008, 0.02]
        ])
        
        max_div_weights = await optimizer.optimize_maximum_diversification(cov_matrix)
        
        assert len(max_div_weights) == 3
        assert all(w >= 0 for w in max_div_weights)
        assert abs(sum(max_div_weights) - 1.0) < 1e-6
    
    @pytest.mark.asyncio
    async def test_black_litterman_optimization(self, optimizer):
        """Test Black-Litterman optimization"""
        # Market cap weights (prior)
        market_weights = np.array([0.5, 0.3, 0.2])
        
        # Sample covariance matrix
        cov_matrix = np.array([
            [0.04, 0.01, 0.005],
            [0.01, 0.03, 0.008],
            [0.005, 0.008, 0.02]
        ])
        
        # Investor views
        views = {
            'P': np.array([[1, 0, 0], [0, 1, -1]]),  # View matrix
            'Q': np.array([0.05, 0.02]),  # Expected excess returns
            'omega': np.array([[0.001, 0], [0, 0.002]])  # Uncertainty matrix
        }
        
        bl_weights = await optimizer.optimize_black_litterman(
            market_weights, cov_matrix, views, risk_aversion=3.0
        )
        
        assert len(bl_weights) == 3
        assert all(w >= 0 for w in bl_weights)
        assert abs(sum(bl_weights) - 1.0) < 1e-6
    
    @pytest.mark.asyncio
    async def test_constraints_application(self, optimizer):
        """Test application of optimization constraints"""
        constraints = {
            'min_weight': 0.05,  # Minimum 5% allocation
            'max_weight': 0.6,   # Maximum 60% allocation
            'sector_limits': {'tech': 0.4, 'finance': 0.3}
        }
        
        optimizer.set_constraints(constraints)
        
        assert optimizer.constraints == constraints
    
    @pytest.mark.asyncio
    async def test_portfolio_performance_calculation(self, optimizer):
        """Test portfolio performance calculation"""
        weights = np.array([0.4, 0.3, 0.3])
        expected_returns = np.array([0.12, 0.10, 0.08])
        cov_matrix = np.array([
            [0.04, 0.01, 0.005],
            [0.01, 0.03, 0.008],
            [0.005, 0.008, 0.02]
        ])
        
        performance = await optimizer.calculate_portfolio_performance(
            weights, expected_returns, cov_matrix
        )
        
        assert 'expected_return' in performance
        assert 'volatility' in performance
        assert 'sharpe_ratio' in performance
        
        assert performance['expected_return'] > 0
        assert performance['volatility'] > 0
    
    @pytest.mark.asyncio
    async def test_efficient_frontier_calculation(self, optimizer):
        """Test efficient frontier calculation"""
        expected_returns = np.array([0.12, 0.10, 0.08])
        cov_matrix = np.array([
            [0.04, 0.01, 0.005],
            [0.01, 0.03, 0.008],
            [0.005, 0.008, 0.02]
        ])
        
        efficient_frontier = await optimizer.calculate_efficient_frontier(
            expected_returns, cov_matrix, num_points=10
        )
        
        assert len(efficient_frontier) == 10
        assert all('return' in point and 'volatility' in point for point in efficient_frontier)
        
        # Returns should be increasing along the frontier
        returns = [point['return'] for point in efficient_frontier]
        assert all(returns[i] <= returns[i+1] for i in range(len(returns)-1))
    
    @pytest.mark.asyncio
    async def test_rebalancing_optimization(self, optimizer):
        """Test portfolio rebalancing optimization"""
        current_weights = np.array([0.5, 0.3, 0.2])
        target_weights = np.array([0.4, 0.4, 0.2])
        transaction_costs = 0.001  # 0.1% transaction cost
        
        rebalancing_plan = await optimizer.optimize_rebalancing(
            current_weights, target_weights, transaction_costs
        )
        
        assert 'trades' in rebalancing_plan
        assert 'total_cost' in rebalancing_plan
        assert 'net_benefit' in rebalancing_plan
        
        assert rebalancing_plan['total_cost'] >= 0

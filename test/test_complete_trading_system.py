#!/usr/bin/env python3
"""
Complete Trading System Test
Tests both paper trading and real margin calculation functionality
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompleteTradingSystemTest:
    """Complete test suite for the trading system"""
    
    def __init__(self):
        """Initialize test suite"""
        self.paper_trading_config = {
            'paper_trading': {
                'initial_balance': 100000,
                'max_trades_per_day': 5,
                'commission_rate': 0.0003,
                'max_position_size': 20000,
                'max_daily_loss': 5000,
                'margin_multiplier': 3.5
            }
        }
        
        # Check if real trading credentials are available
        self.has_real_credentials = all([
            os.getenv('SMARTAPI_API_KEY'),
            os.getenv('SMARTAPI_USERNAME'),
            os.getenv('SMARTAPI_PASSWORD'),
            os.getenv('SMARTAPI_TOTP_TOKEN')
        ])
    
    async def test_paper_trading_system(self) -> bool:
        """Test paper trading functionality"""
        try:
            print("\n" + "="*80)
            print("TESTING: Paper Trading System")
            print("="*80)
            
            # Import paper trading modules
            from utils.paper_trading import VirtualAccount
            
            # Test virtual account
            virtual_account = VirtualAccount(self.paper_trading_config)
            
            # Test basic functionality
            print(f"Initial Balance: Rs.{virtual_account.initial_balance:,.2f}")
            
            # Test trade execution
            success, message, trade = await virtual_account.execute_trade(
                symbol="RELIANCE-EQ",
                exchange="NSE",
                quantity=5,
                price=2500.0,
                transaction_type="BUY",
                product_type="MIS",
                strategy_name="test_strategy"
            )
            
            if success:
                print(f"SUCCESS: Paper trade executed - {trade.trade_id}")
                print(f"  Trade Value: Rs.{trade.quantity * trade.price:,.2f}")
                print(f"  Total Charges: Rs.{trade.total_charges:.2f}")
                print(f"  Net Amount: Rs.{trade.net_amount:.2f}")
                
                # Test account summary
                summary = virtual_account.get_account_summary()
                print(f"  Account Balance: Rs.{summary.get('current_balance', 0):,.2f}")
                print(f"  Active Positions: {summary.get('active_positions', 0)}")
                
                return True
            else:
                print(f"FAILED: Paper trade failed - {message}")
                return False
                
        except Exception as e:
            print(f"FAILED: Paper trading test error - {e}")
            return False
    
    async def test_real_margin_calculation(self) -> bool:
        """Test real margin calculation with SmartAPI"""
        try:
            print("\n" + "="*80)
            print("TESTING: Real Margin Calculation")
            print("="*80)
            
            if not self.has_real_credentials:
                print("SKIPPED: Real trading credentials not available")
                print("To test real margin calculation, set these environment variables:")
                print("  SMARTAPI_API_KEY")
                print("  SMARTAPI_USERNAME") 
                print("  SMARTAPI_PASSWORD")
                print("  SMARTAPI_TOTP_TOKEN")
                return True  # Skip but don't fail
            
            # Import Angel API
            from utils.angel_api import AngelOneAPIClient
            
            # Create config for real API
            real_config = {
                'angel_one_api': {
                    'api_key': os.getenv('SMARTAPI_API_KEY'),
                    'username': os.getenv('SMARTAPI_USERNAME'),
                    'password': os.getenv('SMARTAPI_PASSWORD'),
                    'totp_token': os.getenv('SMARTAPI_TOTP_TOKEN'),
                    'timeout': 10,
                    'max_retries': 3,
                    'retry_delay': 2,
                    'requests_per_second': 10
                }
            }
            
            # Initialize Angel API client
            angel_api = AngelOneAPIClient(real_config)
            auth_success = await angel_api.authenticate()
            
            if not auth_success:
                print("FAILED: Could not authenticate with Angel One API")
                return False
            
            print("SUCCESS: Authenticated with Angel One API")
            
            # Test margin calculation for small trades
            test_cases = [
                {
                    'symbol': 'RELIANCE-EQ',
                    'exchange': 'NSE',
                    'quantity': 1,
                    'price': 2500.0,
                    'transaction_type': 'BUY',
                    'product_type': 'MIS'
                },
                {
                    'symbol': 'TCS-EQ',
                    'exchange': 'NSE',
                    'quantity': 1,
                    'price': 3500.0,
                    'transaction_type': 'BUY',
                    'product_type': 'MIS'
                }
            ]
            
            successful_tests = 0
            
            for i, test_case in enumerate(test_cases):
                print(f"\nTest {i+1}: {test_case['symbol']}")
                
                margin_req = await angel_api.get_margin_requirement(**test_case)
                
                if margin_req and not margin_req.error_message:
                    print(f"SUCCESS: Margin calculation for {test_case['symbol']}")
                    print(f"  Quantity: {margin_req.quantity}")
                    print(f"  Price: Rs.{margin_req.price:,.2f}")
                    print(f"  Margin Required: Rs.{margin_req.margin_required:,.2f}")
                    print(f"  Available Margin: Rs.{margin_req.available_margin:,.2f}")
                    print(f"  Trade Allowed: {margin_req.is_allowed}")
                    print(f"  Utilization: {margin_req.limit_used_percent:.2f}%")
                    successful_tests += 1
                else:
                    error_msg = margin_req.error_message if margin_req else "Unknown error"
                    print(f"FAILED: Margin calculation for {test_case['symbol']} - {error_msg}")
            
            # Close API session
            await angel_api.close_session()
            
            if successful_tests > 0:
                print(f"\nSUCCESS: Real margin calculation test passed ({successful_tests}/{len(test_cases)} successful)")
                return True
            else:
                print("\nFAILED: All margin calculations failed")
                return False
                
        except Exception as e:
            print(f"FAILED: Real margin calculation test error - {e}")
            return False
    
    async def test_execution_agent_integration(self) -> bool:
        """Test execution agent with both paper and real modes"""
        try:
            print("\n" + "="*80)
            print("TESTING: Execution Agent Integration")
            print("="*80)
            
            # Import execution agent
            from agents.execution_agent import ExecutionAgent, SignalPayload
            
            # Test paper mode
            print("\nTesting Paper Mode:")
            paper_agent = ExecutionAgent(
                trading_mode="paper",
                trading_config=self.paper_trading_config
            )
            
            init_success = await paper_agent.initialize()
            if not init_success:
                print("FAILED: Could not initialize paper trading execution agent")
                return False
            
            print("SUCCESS: Paper trading execution agent initialized")
            
            # Test signal processing in paper mode
            test_signal = SignalPayload(
                symbol="RELIANCE-EQ",
                exchange="NSE",
                symbol_token="2885",
                action="BUY",
                entry_price=2500.0,
                sl_price=2400.0,
                target_price=2600.0,
                quantity=2,
                strategy_name="test_strategy",
                signal_id="test_001"
            )
            
            success, message, trade_execution = await paper_agent.process_signal(test_signal)
            if success:
                print("SUCCESS: Paper trading signal processed")
                print(f"  Trade ID: {trade_execution.entry_order.order_id if trade_execution.entry_order else 'N/A'}")
            else:
                print(f"FAILED: Paper trading signal failed - {message}")
                return False
            
            # Test real mode if credentials available
            if self.has_real_credentials:
                print("\nTesting Real Mode (Validation Only):")
                real_agent = ExecutionAgent(
                    trading_mode="real",
                    trading_config={}
                )
                
                init_success = await real_agent.initialize()
                if init_success:
                    print("SUCCESS: Real trading execution agent initialized")
                    
                    # Test margin validation (without actually placing orders)
                    margin_valid, margin_msg = await real_agent._validate_margin_requirement(test_signal)
                    if margin_valid:
                        print("SUCCESS: Real trading margin validation passed")
                    else:
                        print(f"INFO: Real trading margin validation result - {margin_msg}")
                else:
                    print("INFO: Could not initialize real trading (expected without full setup)")
            else:
                print("\nSKIPPED: Real mode test (credentials not available)")
            
            return True
            
        except Exception as e:
            print(f"FAILED: Execution agent integration test error - {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """Run all tests"""
        print("\n" + "="*100)
        print("COMPLETE TRADING SYSTEM TEST SUITE")
        print("="*100)
        print("Testing paper trading, margin calculation, and execution agent integration")
        print("="*100)
        
        tests = [
            ("Paper Trading System", self.test_paper_trading_system),
            ("Real Margin Calculation", self.test_real_margin_calculation),
            ("Execution Agent Integration", self.test_execution_agent_integration)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'-'*80}")
            print(f"Running: {test_name}")
            print(f"{'-'*80}")
            
            try:
                result = await test_func()
                
                if result:
                    print(f"RESULT: {test_name} - PASSED")
                    passed_tests += 1
                else:
                    print(f"RESULT: {test_name} - FAILED")
                    
            except Exception as e:
                print(f"RESULT: {test_name} - ERROR: {e}")
        
        # Summary
        print(f"\n{'='*100}")
        print(f"TEST SUMMARY")
        print(f"{'='*100}")
        print(f"Passed: {passed_tests}/{total_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("RESULT: All tests passed!")
            print("\nThe complete trading system is working correctly!")
            print("Features verified:")
            print("- Paper trading with virtual account management")
            print("- Real margin calculation using SmartAPI")
            print("- Execution agent integration for both modes")
            print("- Risk management and validation")
            return True
        else:
            print(f"RESULT: {total_tests - passed_tests} test(s) failed")
            return False

async def main():
    """Main function"""
    test_suite = CompleteTradingSystemTest()
    success = await test_suite.run_all_tests()
    
    if success:
        print("\nSUCCESS: Complete trading system test passed!")
        return 0
    else:
        print("\nFAILED: Some tests failed. Check output for details.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
🧪 Test Suite for Enhanced AI Training Agent
Comprehensive tests for multi-task learning capabilities
"""

import pytest
import asyncio
import numpy as np
import pandas as pd
import polars as pl
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, patch, AsyncMock

# Import the enhanced AI Training Agent
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents.ai_training_agent import AITrainingAgent, AITrainingConfig

class TestEnhancedAITrainingAgent:
    """Test suite for Enhanced AI Training Agent"""
    
    @pytest.fixture
    def sample_config(self):
        """Create sample configuration for testing"""
        config = AITrainingConfig()
        config.data_dir = "test_data"
        config.models_dir = "test_models"
        config.registry_dir = "test_models/registry"
        config.optuna_trials = 5  # Reduced for testing
        config.cv_folds = 2  # Reduced for testing
        return config
    
    @pytest.fixture
    def sample_data(self):
        """Create sample training data"""
        np.random.seed(42)
        n_samples = 1000
        
        # Create sample features
        data = {
            # Base features
            'n_trades': np.random.randint(10, 100, n_samples),
            'avg_holding_period': np.random.uniform(0.5, 6.0, n_samples),
            'capital_at_risk': np.random.uniform(0.01, 0.1, n_samples),
            'liquidity': np.random.uniform(0.5, 1.0, n_samples),
            'volatility': np.random.uniform(0.1, 0.5, n_samples),
            'correlation_index': np.random.uniform(-1.0, 1.0, n_samples),
            
            # Technical indicators
            'sma_20': np.random.uniform(100, 200, n_samples),
            'rsi_14': np.random.uniform(20, 80, n_samples),
            'macd_line': np.random.uniform(-5, 5, n_samples),
            
            # Option features
            'delta': np.random.uniform(-1, 1, n_samples),
            'gamma': np.random.uniform(0, 0.1, n_samples),
            'theta': np.random.uniform(-0.1, 0, n_samples),
            'vega': np.random.uniform(0, 0.5, n_samples),
            'implied_volatility': np.random.uniform(0.1, 0.8, n_samples),
            
            # Time features
            'hour_of_day': np.random.randint(9, 16, n_samples),
            'days_to_expiry': np.random.randint(1, 30, n_samples),
            
            # Targets for multi-task learning
            'trade_direction': np.random.choice(['buy_call', 'buy_put', 'no_trade'], n_samples),
            'is_profitable': np.random.choice([0, 1], n_samples),
            'signal_confidence': np.random.uniform(0, 1, n_samples),
            'expected_roi': np.random.uniform(-0.1, 0.3, n_samples),
            'best_strategy_id': np.random.randint(0, 25, n_samples),
            'market_regime': np.random.choice(['trending_up', 'trending_down', 'sideways', 'volatile'], n_samples),
            
            # Legacy targets
            'ROI': np.random.uniform(-0.2, 0.5, n_samples),
            'sharpe_ratio': np.random.uniform(-2, 3, n_samples),
            'expectancy': np.random.uniform(-0.1, 0.2, n_samples),
        }
        
        return pl.DataFrame(data)
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    def test_config_initialization(self, sample_config):
        """Test configuration initialization"""
        agent = AITrainingAgent(sample_config)
        
        assert agent.config == sample_config
        assert isinstance(agent.models, dict)
        assert isinstance(agent.task_models, dict)
        assert isinstance(agent.ensemble_models, dict)
        assert isinstance(agent.meta_models, dict)
    
    def test_config_defaults(self):
        """Test default configuration values"""
        config = AITrainingConfig()
        
        # Check multi-task objectives
        assert "trade_direction" in config.multi_task_objectives
        assert "profitability" in config.multi_task_objectives
        assert "signal_confidence" in config.multi_task_objectives
        assert "expected_roi" in config.multi_task_objectives
        assert "strategy_selection" in config.multi_task_objectives
        assert "regime_classification" in config.multi_task_objectives
        
        # Check feature categories
        assert len(config.technical_indicators) > 0
        assert len(config.option_features) > 0
        assert len(config.time_features) > 0
        assert len(config.regime_features) > 0
        assert len(config.strategy_features) > 0
        
        # Check enabled models
        assert "lightgbm" in config.enabled_models
        assert "xgboost" in config.enabled_models
        assert "tabnet" in config.enabled_models
    
    @pytest.mark.asyncio
    async def test_prepare_multi_task_data(self, sample_config, sample_data):
        """Test multi-task data preparation"""
        agent = AITrainingAgent(sample_config)
        
        task_data = await agent.prepare_multi_task_data(sample_data)
        
        assert isinstance(task_data, dict)
        assert len(task_data) > 0
        
        # Check that each task has required components
        for task_name, data_dict in task_data.items():
            assert "X" in data_dict
            assert "y" in data_dict
            assert "task_info" in data_dict
            assert "feature_names" in data_dict
            assert "n_samples" in data_dict
            
            # Check data types
            assert isinstance(data_dict["X"], pl.DataFrame)
            assert isinstance(data_dict["y"], pl.DataFrame)
            assert isinstance(data_dict["task_info"], dict)
            assert isinstance(data_dict["feature_names"], list)
            assert isinstance(data_dict["n_samples"], int)
    
    def test_get_task_features(self, sample_config, sample_data):
        """Test task-specific feature selection"""
        agent = AITrainingAgent(sample_config)
        
        available_columns = sample_data.columns
        
        # Test different tasks
        trade_features = agent._get_task_features("trade_direction", available_columns)
        regime_features = agent._get_task_features("regime_classification", available_columns)
        strategy_features = agent._get_task_features("strategy_selection", available_columns)
        
        assert len(trade_features) > 0
        assert len(regime_features) > 0
        assert len(strategy_features) > 0
        
        # Check that all features exist in available columns
        for feature in trade_features:
            assert feature in available_columns
    
    def test_prepare_classification_targets(self, sample_config, sample_data):
        """Test classification target preparation"""
        agent = AITrainingAgent(sample_config)
        
        # Test trade direction classification
        task_config = agent.config.multi_task_objectives["trade_direction"]
        y_data = sample_data.select("trade_direction")
        
        prepared_y = agent._prepare_classification_targets(y_data, task_config)
        
        assert isinstance(prepared_y, pl.DataFrame)
        assert prepared_y.height > 0
        
        # Check that values are properly encoded
        unique_values = prepared_y.select(pl.col("trade_direction").unique()).to_pandas().iloc[:, 0].tolist()
        assert all(isinstance(val, (int, np.integer)) for val in unique_values)
    
    def test_create_cv_splits(self, sample_config, sample_data):
        """Test cross-validation split creation"""
        agent = AITrainingAgent(sample_config)
        
        X = sample_data.select(["sma_20", "rsi_14", "delta"]).to_pandas()
        y = sample_data.select("is_profitable").to_pandas().iloc[:, 0]
        
        # Test time series splits
        agent.config.cv_strategy = "time_series"
        splits = agent._create_cv_splits(X, y, "classification")
        
        assert len(splits) == agent.config.cv_folds
        
        for train_idx, val_idx in splits:
            assert len(train_idx) > 0
            assert len(val_idx) > 0
            assert len(set(train_idx) & set(val_idx)) == 0  # No overlap
    
    def test_create_model(self, sample_config):
        """Test model creation for different types"""
        agent = AITrainingAgent(sample_config)
        
        # Test LightGBM
        lgb_reg = agent._create_model("lightgbm", "regression")
        lgb_clf = agent._create_model("lightgbm", "classification")
        
        assert lgb_reg is not None
        assert lgb_clf is not None
        
        # Test XGBoost
        xgb_reg = agent._create_model("xgboost", "regression")
        xgb_clf = agent._create_model("xgboost", "classification")
        
        assert xgb_reg is not None
        assert xgb_clf is not None
        
        # Test TabNet
        tabnet_reg = agent._create_model("tabnet", "regression")
        tabnet_clf = agent._create_model("tabnet", "classification")
        
        assert tabnet_reg is not None
        assert tabnet_clf is not None
    
    def test_ensemble_model_creation(self, sample_config):
        """Test ensemble model creation"""
        agent = AITrainingAgent(sample_config)
        
        # Create mock models
        mock_models = {
            "lightgbm": Mock(),
            "xgboost": Mock()
        }
        
        # Configure mock predictions
        mock_models["lightgbm"].predict.return_value = np.array([0.5, 0.6, 0.7])
        mock_models["xgboost"].predict.return_value = np.array([0.4, 0.5, 0.8])
        
        task_info = {"type": "regression"}
        ensemble = agent._create_ensemble_model(mock_models, task_info)
        
        assert ensemble is not None
        assert hasattr(ensemble, 'predict')
        
        # Test prediction
        X_test = pd.DataFrame({'feature1': [1, 2, 3], 'feature2': [4, 5, 6]})
        predictions = ensemble.predict(X_test)
        
        assert predictions is not None
        assert len(predictions) == 3
    
    @pytest.mark.asyncio
    async def test_load_training_data(self, sample_config, sample_data, temp_dir):
        """Test training data loading"""
        agent = AITrainingAgent(sample_config)
        
        # Save sample data to temporary file
        data_file = Path(temp_dir) / "test_data.parquet"
        sample_data.write_parquet(data_file)
        
        # Test loading
        loaded_data = await agent._load_training_data(str(data_file))
        
        assert loaded_data is not None
        assert isinstance(loaded_data, pl.DataFrame)
        assert loaded_data.height == sample_data.height
        assert loaded_data.width == sample_data.width
    
    def test_prepare_prediction_features(self, sample_config):
        """Test feature preparation for prediction"""
        agent = AITrainingAgent(sample_config)
        
        # Sample feature dictionary
        features = {
            'sma_20': 150.0,
            'rsi_14': 65.0,
            'delta': 0.5,
            'gamma': 0.05,
            'hour_of_day': 10,
            'n_trades': 50
        }
        
        # Test feature preparation for trade direction task
        prepared_features = agent._prepare_prediction_features("trade_direction", features)
        
        assert prepared_features is not None
        assert isinstance(prepared_features, pd.DataFrame)
        assert len(prepared_features) == 1  # Single prediction sample
        assert prepared_features.shape[1] > 0  # Has features
    
    @pytest.mark.asyncio
    async def test_directory_setup(self, sample_config, temp_dir):
        """Test directory setup"""
        sample_config.models_dir = str(Path(temp_dir) / "models")
        sample_config.registry_dir = str(Path(temp_dir) / "models" / "registry")
        
        agent = AITrainingAgent(sample_config)
        
        # Check that directories were created
        assert Path(sample_config.models_dir).exists()
        assert Path(sample_config.registry_dir).exists()
        assert Path(sample_config.models_dir, "explainability").exists()
        assert Path(sample_config.models_dir, "versions").exists()
        assert Path(sample_config.models_dir, "meta").exists()

    @pytest.mark.asyncio
    async def test_model_explainability(self, sample_config, sample_data):
        """Test model explainability features"""
        agent = AITrainingAgent(sample_config)

        # Prepare minimal task data
        task_data = await agent.prepare_multi_task_data(sample_data)

        # Mock trained models
        agent.task_models = {
            "trade_direction": {
                "lightgbm": Mock()
            }
        }

        # Mock model attributes
        mock_model = agent.task_models["trade_direction"]["lightgbm"]
        mock_model.feature_importances_ = np.random.random(10)

        # Test explanation generation (will skip SHAP/LIME if not available)
        explanations = await agent.generate_model_explanations(task_data)

        assert isinstance(explanations, dict)

    @pytest.mark.asyncio
    async def test_model_registry(self, sample_config, temp_dir):
        """Test model registry and versioning"""
        sample_config.registry_dir = str(Path(temp_dir) / "registry")
        agent = AITrainingAgent(sample_config)

        # Mock training results
        training_results = {
            "trade_direction": {
                "lightgbm": {"cv_mean": 0.75, "cv_std": 0.05}
            }
        }

        # Mock models
        agent.task_models = {
            "trade_direction": {
                "lightgbm": Mock()
            }
        }

        # Test model saving
        registry_entries = await agent.save_models_to_registry(training_results, {})

        assert isinstance(registry_entries, dict)
        assert Path(sample_config.registry_dir).exists()

    @pytest.mark.asyncio
    async def test_live_prediction(self, sample_config):
        """Test live prediction capability"""
        agent = AITrainingAgent(sample_config)

        # Mock trained models
        mock_model = Mock()
        mock_model.predict.return_value = np.array([0.7])
        mock_model.predict_proba.return_value = np.array([[0.3, 0.7]])

        agent.task_models = {
            "trade_direction": {
                "lightgbm": mock_model
            }
        }

        # Test prediction
        features = {
            'sma_20': 150.0,
            'rsi_14': 65.0,
            'delta': 0.5
        }

        predictions = await agent.predict_live(features, "trade_direction")

        assert isinstance(predictions, dict)
        assert "trade_direction" in predictions

    @pytest.mark.asyncio
    async def test_llm_insights_generation(self, sample_config):
        """Test LLM insights generation"""
        agent = AITrainingAgent(sample_config)

        # Mock training results
        training_results = {
            "trade_direction": {
                "lightgbm": {"cv_mean": 0.75, "cv_std": 0.05}
            }
        }

        # Mock explanations
        explanations = {
            "global_importance": {
                "rsi_14": 0.25,
                "delta": 0.20,
                "sma_20": 0.15
            }
        }

        # Test insights generation
        insights = await agent.generate_llm_insights(training_results, explanations)

        assert isinstance(insights, dict)
        if insights:  # Only check if insights were generated
            assert "system_summary" in insights or "error" in insights

    def test_meta_learning_features(self, sample_config):
        """Test meta-learning feature creation"""
        agent = AITrainingAgent(sample_config)

        # Test meta-feature creation with mock data
        meta_features = pd.DataFrame({
            'task1_model1_pred': [0.5, 0.6, 0.7],
            'task1_model1_conf': [0.8, 0.9, 0.7],
            'task2_model1_pred': [0.3, 0.4, 0.8]
        })

        # Test strategy selection model creation
        strategy_result = asyncio.run(agent._train_strategy_selection_model(meta_features))

        assert isinstance(strategy_result, dict)
        assert "model" in strategy_result or "error" in strategy_result

    def test_walk_forward_splits(self, sample_config, sample_data):
        """Test walk-forward validation splits"""
        agent = AITrainingAgent(sample_config)
        agent.config.cv_strategy = "walk_forward"

        X = sample_data.select(["sma_20", "rsi_14"]).to_pandas()
        y = sample_data.select("is_profitable").to_pandas().iloc[:, 0]

        splits = agent._create_walk_forward_splits(X, y)

        assert len(splits) > 0

        # Check that splits are properly ordered (no future data in training)
        for train_idx, test_idx in splits:
            assert max(train_idx) < min(test_idx)  # No data leakage

    def test_feature_categorization(self, sample_config):
        """Test feature categorization for different tasks"""
        agent = AITrainingAgent(sample_config)

        # Test that different tasks get appropriate features
        all_columns = (
            agent.config.feature_columns +
            agent.config.technical_indicators +
            agent.config.option_features +
            agent.config.time_features +
            agent.config.regime_features +
            agent.config.strategy_features
        )

        trade_features = agent._get_task_features("trade_direction", all_columns)
        regime_features = agent._get_task_features("regime_classification", all_columns)

        # Trade direction should include option features
        assert any(feat in trade_features for feat in agent.config.option_features)

        # Regime classification should include regime features
        assert any(feat in regime_features for feat in agent.config.regime_features)

    @pytest.mark.asyncio
    async def test_error_handling(self, sample_config):
        """Test error handling in various scenarios"""
        agent = AITrainingAgent(sample_config)

        # Test with empty data
        empty_data = pl.DataFrame()
        task_data = await agent.prepare_multi_task_data(empty_data)
        assert len(task_data) == 0

        # Test prediction with no models
        features = {'test_feature': 1.0}
        predictions = await agent.predict_live(features)
        assert "error" in predictions

        # Test training with invalid data file
        results = await agent.train_enhanced_models("nonexistent_file.parquet")
        assert results["status"] == "error"

class TestFeedbackIntegration:
    """Test feedback integration features"""

    @pytest.fixture
    def feedback_config(self):
        """Create feedback configuration for testing"""
        from agents.ai_training_feedback import FeedbackConfig
        config = FeedbackConfig()
        config.feedback_dir = "test_feedback"
        config.drift_threshold = 0.05
        config.performance_threshold = 0.1
        return config

    @pytest.mark.asyncio
    async def test_feedback_collection(self, feedback_config, temp_dir):
        """Test feedback collection from agents"""
        from agents.ai_training_feedback import AITrainingFeedbackIntegrator

        feedback_config.feedback_dir = str(Path(temp_dir) / "feedback")
        integrator = AITrainingFeedbackIntegrator(feedback_config)

        # Test feedback collection
        feedback_data = {
            "predictions": {"task1": 0.7, "task2": 0.3},
            "actual_outcomes": {"task1": 1, "task2": 0},
            "metadata": {"timestamp": "2024-01-01T10:00:00"}
        }

        success = await integrator.collect_feedback("execution_agent", feedback_data)
        assert success

        # Check feedback summary
        summary = await integrator.get_feedback_summary()
        assert summary["total_feedback"] == 1
        assert "execution_agent" in summary["sources"]

    @pytest.mark.asyncio
    async def test_drift_detection(self, feedback_config):
        """Test concept drift detection"""
        from agents.ai_training_feedback import DriftDetector

        detector = DriftDetector(feedback_config)

        # Test with similar data (no drift)
        predictions1 = np.random.normal(0, 1, 100)
        outcomes1 = np.random.normal(0, 1, 100)

        drift1 = await detector.check_drift(predictions1, outcomes1)
        assert not drift1  # Should not detect drift initially

        # Test with different distribution (potential drift)
        predictions2 = np.random.normal(2, 1, 100)  # Different mean
        outcomes2 = np.random.normal(2, 1, 100)

        # May or may not detect drift depending on threshold
        drift2 = await detector.check_drift(predictions2, outcomes2)
        assert isinstance(drift2, bool)

    @pytest.mark.asyncio
    async def test_performance_monitoring(self, feedback_config):
        """Test performance monitoring"""
        from agents.ai_training_feedback import PerformanceMonitor

        monitor = PerformanceMonitor(feedback_config)

        # Test with good performance
        predictions = [1, 0, 1, 0, 1]
        outcomes = [1, 0, 1, 0, 1]  # Perfect match

        perf_drop1 = await monitor.check_performance(predictions, outcomes)
        assert not perf_drop1  # Should not detect drop initially

        # Test with poor performance
        bad_predictions = [0, 1, 0, 1, 0]  # Opposite of outcomes

        perf_drop2 = await monitor.check_performance(bad_predictions, outcomes)
        # May detect performance drop depending on baseline

if __name__ == "__main__":
    pytest.main([__file__, "-v"])

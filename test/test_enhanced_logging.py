#!/usr/bin/env python3
"""
Test script for enhanced logging system
Demonstrates the new Rich-based logging features
"""

import asyncio
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from run_paper_trading_workflow import PaperTradingWorkflow

async def test_enhanced_logging():
    """Test the enhanced logging features"""
    print("Testing Enhanced Logging System...")
    print("=" * 50)
    
    # Create workflow instance
    workflow = PaperTradingWorkflow()
    
    # Test phase logging
    workflow._log_phase_start("Test Phase", "Testing enhanced logging capabilities")
    
    # Test stock processing
    test_stocks = ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK"]
    workflow.total_stocks = len(test_stocks)
    
    for stock in test_stocks:
        workflow._log_stock_processing(stock, "processing")
        await asyncio.sleep(0.5)
    
    # Test signal generation
    for stock in test_stocks:
        import random
        signal_type = random.choice(["BUY", "SELL", "HOLD"])
        confidence = random.uniform(0.6, 0.95)
        workflow._log_signal_generated(stock, signal_type, confidence)
        await asyncio.sleep(0.3)
    
    # Test trade execution
    trades = [
        ("RELIANCE", "BUY", 50, 2450.75),
        ("TCS", "BUY", 30, 3890.50),
        ("INFY", "SELL", 25, 1750.25)
    ]
    
    for stock, action, qty, price in trades:
        workflow._log_trade_execution(stock, action, qty, price)
        await asyncio.sleep(0.5)
    
    # Test agent status
    workflow._log_agent_status("Market Monitoring", "SUCCESS", "Downloaded data for 500 stocks")
    workflow._log_agent_status("Signal Generation", "SUCCESS", "Generated 150 signals")
    workflow._log_agent_status("Risk Management", "WARNING", "High volatility detected")
    
    # Test status table
    if hasattr(workflow, 'console') and workflow.console:
        status_table = workflow._create_status_table()
        if status_table:
            workflow.console.print(status_table)
    
    # Test final status report
    workflow.agents_status = {
        'market_monitoring': True,
        'signal_generation': True,
        'risk_management': True,
        'execution': True
    }
    workflow.print_status_report()

if __name__ == "__main__":
    asyncio.run(test_enhanced_logging())

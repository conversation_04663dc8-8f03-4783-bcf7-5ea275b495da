#!/usr/bin/env python3
"""
🧪 Enhanced WebSocket Integration Tests
Comprehensive test suite for real-time WebSocket functionality

Features:
- Connection stability tests
- Data quality validation tests
- Performance benchmarking
- Error handling verification
- Reconnection logic testing
- Memory usage monitoring
"""

import os
import sys
import asyncio
import logging
import time
import json
import threading
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from utils.enhanced_websocket_manager import (
    EnhancedWebSocketManager, 
    MarketDataTick, 
    ConnectionState,
    CircuitBreaker
)
from utils.nse_500_universe import NSE500Universe

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestEnhancedWebSocket(unittest.TestCase):
    """Test suite for Enhanced WebSocket Manager"""
    
    def setUp(self):
        """Set up test environment"""
        self.config = {
            'api_key': 'test_api_key',
            'username': 'test_user',
            'websocket': {
                'max_symbols_per_batch': 10,
                'max_reconnection_attempts': 3,
                'reconnection_delay': 1,
                'heartbeat_interval': 5
            }
        }
        
        self.ws_manager = EnhancedWebSocketManager(self.config)
        self.test_symbols = [
            {'symbol': 'RELIANCE', 'token': '2885', 'exchange': 'NSE'},
            {'symbol': 'TCS', 'token': '11536', 'exchange': 'NSE'},
            {'symbol': 'HDFCBANK', 'token': '1333', 'exchange': 'NSE'},
            {'symbol': 'INFY', 'token': '1594', 'exchange': 'NSE'},
            {'symbol': 'ICICIBANK', 'token': '4963', 'exchange': 'NSE'}
        ]
    
    def test_initialization(self):
        """Test WebSocket manager initialization"""
        self.assertIsNotNone(self.ws_manager)
        self.assertEqual(self.ws_manager.connection_state, ConnectionState.DISCONNECTED)
        self.assertEqual(len(self.ws_manager.subscribed_symbols), 0)
        self.assertEqual(len(self.ws_manager.subscription_batches), 0)
    
    def test_symbol_validation(self):
        """Test symbol data validation"""
        # Valid symbol
        valid_symbol = {'symbol': 'RELIANCE', 'token': '2885', 'exchange': 'NSE'}
        self.assertTrue(self.ws_manager._validate_symbol_data(valid_symbol))
        
        # Invalid symbols
        invalid_symbols = [
            {'token': '2885', 'exchange': 'NSE'},  # Missing symbol
            {'symbol': 'RELIANCE', 'exchange': 'NSE'},  # Missing token
            {'symbol': 'RELIANCE', 'token': '2885'},  # Missing exchange
            {}  # Empty
        ]
        
        for invalid_symbol in invalid_symbols:
            self.assertFalse(self.ws_manager._validate_symbol_data(invalid_symbol))
    
    def test_symbol_batching(self):
        """Test intelligent symbol batching"""
        # Add symbols
        self.ws_manager.add_symbols(self.test_symbols)
        
        # Check that symbols were added
        self.assertEqual(len(self.ws_manager.subscribed_symbols), len(self.test_symbols))
        
        # Check batching
        self.assertGreater(len(self.ws_manager.subscription_batches), 0)
        
        # Verify batch structure
        for batch in self.ws_manager.subscription_batches:
            self.assertIn('exchange', batch)
            self.assertIn('symbols', batch)
            self.assertIn('batch_id', batch)
            self.assertLessEqual(len(batch['symbols']), self.config['websocket']['max_symbols_per_batch'])
    
    def test_circuit_breaker(self):
        """Test circuit breaker functionality"""
        circuit_breaker = CircuitBreaker(failure_threshold=3, timeout=1)
        
        # Initially closed
        self.assertTrue(circuit_breaker.can_execute())
        self.assertEqual(circuit_breaker.state, "closed")
        
        # Record failures
        for i in range(3):
            circuit_breaker.call_failed()
        
        # Should be open now
        self.assertEqual(circuit_breaker.state, "open")
        self.assertFalse(circuit_breaker.can_execute())
        
        # Wait for timeout
        time.sleep(1.1)
        
        # Should be half-open now
        self.assertTrue(circuit_breaker.can_execute())
        
        # Successful call should close it
        circuit_breaker.call_succeeded()
        self.assertEqual(circuit_breaker.state, "closed")
    
    def test_market_data_tick_validation(self):
        """Test market data tick validation"""
        # Valid tick
        valid_tick_data = {
            'symbol': 'RELIANCE',
            'token': '2885',
            'exchange_type': 1,
            'timestamp': datetime.now(),
            'last_traded_price': 2500.0,
            'high_price': 2550.0,
            'low_price': 2450.0,
            'volume': 1000
        }
        
        tick = MarketDataTick(**valid_tick_data)
        self.assertTrue(tick.is_valid)
        self.assertEqual(len(tick.validation_errors), 0)
        self.assertGreaterEqual(tick.quality_score, 0.9)
        
        # Invalid tick - negative price
        invalid_tick_data = valid_tick_data.copy()
        invalid_tick_data['last_traded_price'] = -100.0
        
        invalid_tick = MarketDataTick(**invalid_tick_data)
        self.assertFalse(invalid_tick.is_valid)
        self.assertGreater(len(invalid_tick.validation_errors), 0)
        
        # Invalid tick - high < low
        inconsistent_tick_data = valid_tick_data.copy()
        inconsistent_tick_data['high_price'] = 2400.0
        inconsistent_tick_data['low_price'] = 2500.0
        
        inconsistent_tick = MarketDataTick(**inconsistent_tick_data)
        self.assertLess(inconsistent_tick.quality_score, 1.0)
    
    @patch('utils.enhanced_websocket_manager.SmartWebSocketV2')
    def test_authentication(self, mock_websocket):
        """Test authentication process"""
        # Test successful authentication
        result = asyncio.run(self.ws_manager.authenticate('test_auth_token', 'test_feed_token'))
        self.assertTrue(result)
        self.assertEqual(self.ws_manager.auth_token, 'test_auth_token')
        self.assertEqual(self.ws_manager.feed_token, 'test_feed_token')
        
        # Test authentication without SmartAPI
        with patch('utils.enhanced_websocket_manager.SMARTAPI_AVAILABLE', False):
            ws_manager_no_api = EnhancedWebSocketManager(self.config)
            result = asyncio.run(ws_manager_no_api.authenticate('token', 'feed'))
            self.assertFalse(result)
    
    def test_data_buffer_management(self):
        """Test data buffer management"""
        # Create test ticks
        test_ticks = []
        for i in range(15):  # More than buffer size to test circular buffer
            tick = MarketDataTick(
                symbol=f'TEST{i}',
                token=str(i),
                exchange_type=1,
                timestamp=datetime.now(),
                last_traded_price=100.0 + i
            )
            test_ticks.append(tick)
            self.ws_manager.data_buffer.append(tick)
        
        # Buffer should maintain max size
        self.assertLessEqual(len(self.ws_manager.data_buffer), 10000)
        
        # Test latest data retrieval
        latest_tick = self.ws_manager.get_latest_data('TEST14')
        self.assertIsNotNone(latest_tick)
        self.assertEqual(latest_tick.symbol, 'TEST14')
        
        # Test non-existent symbol
        non_existent = self.ws_manager.get_latest_data('NONEXISTENT')
        self.assertIsNone(non_existent)
    
    def test_callback_management(self):
        """Test callback registration and execution"""
        callback_called = False
        received_tick = None
        
        def test_callback(tick):
            nonlocal callback_called, received_tick
            callback_called = True
            received_tick = tick
        
        # Register callback
        self.ws_manager.add_data_callback(test_callback)
        self.assertEqual(len(self.ws_manager.data_callbacks), 1)
        
        # Simulate data reception
        test_data = {
            'symbol': 'RELIANCE',
            'token': '2885',
            'ltp': 2500.0,
            'volume': 1000
        }
        
        # Mock the websocket data handler
        self.ws_manager._on_websocket_data(None, json.dumps(test_data))
        
        # Check if callback was called
        self.assertTrue(callback_called)
        self.assertIsNotNone(received_tick)
        self.assertEqual(received_tick.symbol, 'RELIANCE')
    
    def test_performance_metrics(self):
        """Test performance metrics tracking"""
        # Initial metrics
        metrics = self.ws_manager.get_connection_metrics()
        self.assertEqual(metrics.total_messages, 0)
        self.assertEqual(metrics.error_count, 0)
        
        # Simulate message processing
        for i in range(10):
            test_data = {
                'symbol': f'TEST{i}',
                'token': str(i),
                'ltp': 100.0 + i
            }
            self.ws_manager._on_websocket_data(None, json.dumps(test_data))
        
        # Check updated metrics
        updated_metrics = self.ws_manager.get_connection_metrics()
        self.assertEqual(updated_metrics.total_messages, 10)
        self.assertIsNotNone(updated_metrics.last_message_time)
    
    def test_quality_metrics(self):
        """Test data quality metrics"""
        # Initial quality metrics
        quality_metrics = self.ws_manager.get_quality_metrics()
        self.assertEqual(quality_metrics['total_ticks'], 0)
        
        # Simulate valid and invalid data
        valid_data = {'symbol': 'RELIANCE', 'token': '2885', 'ltp': 2500.0}
        invalid_data = {'symbol': 'INVALID', 'token': '999', 'ltp': -100.0}
        
        self.ws_manager._on_websocket_data(None, json.dumps(valid_data))
        self.ws_manager._on_websocket_data(None, json.dumps(invalid_data))
        
        # Check quality metrics
        updated_quality = self.ws_manager.get_quality_metrics()
        self.assertEqual(updated_quality['total_ticks'], 2)
        self.assertGreater(updated_quality['valid_ticks'], 0)
        self.assertGreater(updated_quality['invalid_ticks'], 0)
    
    def test_exchange_type_mapping(self):
        """Test exchange type mapping"""
        test_cases = [
            ('NSE', 1),
            ('NFO', 2),
            ('BSE', 3),
            ('BFO', 4),
            ('MCX', 5),
            ('UNKNOWN', 1)  # Default case
        ]
        
        for exchange, expected_type in test_cases:
            result = self.ws_manager._get_exchange_type(exchange)
            self.assertEqual(result, expected_type)
    
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self.ws_manager, 'stop_event'):
            self.ws_manager.stop_event.set()

class WebSocketIntegrationTest:
    """Integration test for WebSocket with real NSE 500 data"""
    
    def __init__(self):
        self.config = {
            'api_key': os.getenv('SMARTAPI_API_KEY', ''),
            'username': os.getenv('SMARTAPI_USERNAME', ''),
            'websocket': {
                'max_symbols_per_batch': 50,
                'max_reconnection_attempts': 5,
                'reconnection_delay': 5,
                'heartbeat_interval': 30
            }
        }
        
        self.ws_manager = EnhancedWebSocketManager(self.config)
        self.universe = NSE500Universe()
        self.test_results = {}
    
    async def run_integration_test(self) -> Dict[str, Any]:
        """Run comprehensive integration test"""
        try:
            logger.info("🚀 Starting WebSocket Integration Test...")
            
            # Step 1: Load NSE 500 universe
            universe_loaded = self.universe.load_nse_500_universe()
            if not universe_loaded:
                return {"error": "Failed to load NSE 500 universe"}
            
            # Step 2: Select test symbols (top 100 for testing)
            large_cap_stocks = self.universe.get_stocks_by_market_cap("Large")[:100]
            test_symbols = [
                {
                    'symbol': stock.symbol,
                    'token': stock.token,
                    'exchange': stock.exchange
                }
                for stock in large_cap_stocks
            ]
            
            # Step 3: Add symbols to WebSocket manager
            self.ws_manager.add_symbols(test_symbols)
            
            # Step 4: Set up data collection
            received_data = []
            
            def data_collector(tick):
                received_data.append(tick)
                if len(received_data) % 100 == 0:
                    logger.info(f"📊 Received {len(received_data)} ticks")
            
            self.ws_manager.add_data_callback(data_collector)
            
            # Step 5: Test authentication (mock for demo)
            auth_success = await self.ws_manager.authenticate('mock_auth_token', 'mock_feed_token')
            
            # Step 6: Simulate streaming (in real scenario, would connect to actual WebSocket)
            logger.info("📡 Simulating WebSocket streaming...")
            await self._simulate_streaming(test_symbols, received_data)
            
            # Step 7: Analyze results
            results = self._analyze_test_results(received_data)
            
            logger.info("✅ WebSocket Integration Test completed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Integration test failed: {e}")
            return {"error": str(e)}
    
    async def _simulate_streaming(self, symbols: List[Dict], received_data: List):
        """Simulate WebSocket streaming for testing"""
        try:
            import random
            
            # Simulate 30 seconds of data
            for i in range(300):  # 10 ticks per second for 30 seconds
                # Pick random symbol
                symbol_data = random.choice(symbols)
                
                # Generate realistic tick data
                base_price = 1000 + random.randint(0, 2000)
                tick_data = {
                    'symbol': symbol_data['symbol'],
                    'token': symbol_data['token'],
                    'ltp': base_price + random.uniform(-50, 50),
                    'high': base_price + random.uniform(0, 100),
                    'low': base_price - random.uniform(0, 100),
                    'volume': random.randint(1000, 100000),
                    'change_percent': random.uniform(-5, 5)
                }
                
                # Process through WebSocket manager
                self.ws_manager._on_websocket_data(None, json.dumps(tick_data))
                
                # Small delay to simulate real-time
                await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"❌ Streaming simulation failed: {e}")
    
    def _analyze_test_results(self, received_data: List) -> Dict[str, Any]:
        """Analyze test results"""
        try:
            if not received_data:
                return {"error": "No data received"}
            
            # Performance metrics
            metrics = self.ws_manager.get_connection_metrics()
            quality_metrics = self.ws_manager.get_quality_metrics()
            
            # Symbol coverage
            unique_symbols = set(tick.symbol for tick in received_data)
            
            # Data quality analysis
            valid_ticks = [tick for tick in received_data if tick.is_valid]
            avg_quality_score = sum(tick.quality_score for tick in received_data) / len(received_data)
            
            return {
                "test_summary": {
                    "total_ticks_received": len(received_data),
                    "unique_symbols": len(unique_symbols),
                    "valid_ticks": len(valid_ticks),
                    "invalid_ticks": len(received_data) - len(valid_ticks),
                    "average_quality_score": avg_quality_score,
                    "data_validity_percentage": (len(valid_ticks) / len(received_data)) * 100
                },
                "performance_metrics": {
                    "total_messages": metrics.total_messages,
                    "messages_per_second": metrics.messages_per_second,
                    "connection_uptime": metrics.connection_uptime,
                    "error_count": metrics.error_count,
                    "reconnection_count": metrics.reconnection_count
                },
                "quality_metrics": quality_metrics,
                "symbol_coverage": list(unique_symbols)[:20],  # First 20 symbols
                "test_status": "PASSED" if len(valid_ticks) > 0 else "FAILED"
            }
            
        except Exception as e:
            logger.error(f"❌ Results analysis failed: {e}")
            return {"error": str(e)}

async def run_all_tests():
    """Run all WebSocket tests"""
    try:
        print("🧪 Running Enhanced WebSocket Tests...")
        
        # Unit tests
        print("\n📋 Running Unit Tests...")
        unittest.main(module='__main__', argv=[''], exit=False, verbosity=2)
        
        # Integration test
        print("\n🔗 Running Integration Test...")
        integration_test = WebSocketIntegrationTest()
        results = await integration_test.run_integration_test()
        
        print("\n📊 Integration Test Results:")
        print(json.dumps(results, indent=2, default=str))
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    # Run tests
    results = asyncio.run(run_all_tests())

#!/usr/bin/env python3
"""
Test Suite for Execution Agent
Comprehensive testing for order lifecycle management and trade execution

Test Categories:
🧪 1. Unit Tests - Individual method testing
🔗 2. Integration Tests - Angel One API integration
📊 3. Performance Tests - Execution speed and slippage
🛡️ 4. Error Handling Tests - Failure scenarios
⚙️ 5. Configuration Tests - Config validation
"""

import os
import sys
import asyncio
import pytest
import yaml
import json
from datetime import datetime, timedelta, time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, List, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the execution agent
from agents.execution_agent import (
    ExecutionAgent, SignalPayload, OrderRequest, OrderResponse, 
    TradeExecution, OrderStatus, OrderType, ProductType, TransactionType
)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TEST FIXTURES
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.fixture
def sample_config():
    """Sample configuration for testing"""
    return {
        'angel_one_api': {
            'enabled': True,
            'api_key': 'test_api_key',
            'username': 'test_user',
            'password': 'test_pass',
            'totp_token': 'test_token',
            'timeout': 10,
            'max_retries': 3,
            'requests_per_second': 10
        },
        'execution': {
            'default_order_type': 'LIMIT',
            'default_product_type': 'MIS',
            'max_execution_time_ms': 2000,
            'max_slippage_percent': 0.5,
            'auto_retry': True,
            'max_retries': 3,
            'retry_delay_minutes': 5
        },
        'market_hours': {
            'market_open_time': '09:15',
            'market_close_time': '15:25',
            'allow_pre_market': True,
            'pre_market_start': '09:00'
        },
        'risk_management': {
            'enabled': True,
            'min_rr_ratio': 1.5,
            'max_position_size_percent': 5.0
        },
        'monitoring': {
            'order_check_interval_seconds': 10,
            'order_timeout_minutes': 10
        },
        'logging': {
            'level': 'INFO',
            'file_enabled': False
        },
        'notifications': {
            'telegram': {'enabled': False}
        }
    }

@pytest.fixture
def sample_signal():
    """Sample signal payload for testing"""
    return SignalPayload(
        symbol="RELIANCE-EQ",
        exchange="NSE",
        symbol_token="2885",
        action="BUY",
        entry_price=2780.0,
        sl_price=2765.0,
        target_price=2815.0,
        quantity=1,
        order_type="LIMIT",
        product_type="MIS",
        strategy_name="test_strategy",
        signal_id="test_001",
        risk_reward_ratio=2.33,
        confidence_score=0.85
    )

@pytest.fixture
def mock_angel_api():
    """Mock Angel One API client"""
    mock_api = Mock()
    mock_api.smart_api = Mock()
    mock_api.authenticate = AsyncMock(return_value=True)
    mock_api.close_session = AsyncMock()
    return mock_api

@pytest.fixture
def mock_risk_agent():
    """Mock Risk Management Agent"""
    mock_risk = Mock()
    mock_risk.initialize = AsyncMock()
    mock_risk.validate_trade_request = AsyncMock(return_value=(True, "Approved"))
    return mock_risk

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 UNIT TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestExecutionAgentUnit:
    """Unit tests for ExecutionAgent methods"""
    
    @pytest.mark.asyncio
    async def test_initialization(self, sample_config, tmp_path):
        """Test agent initialization"""
        # Create temporary config file
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        # Initialize agent
        agent = ExecutionAgent(str(config_file))
        
        assert agent.config == sample_config
        assert agent.active_orders == {}
        assert agent.order_history == []
        assert len(agent.retry_queue) == 0
    
    def test_market_hours_validation(self, sample_config, tmp_path):
        """Test market hours validation"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        agent = ExecutionAgent(str(config_file))
        
        # Mock current time to be within market hours
        with patch('agents.execution_agent.datetime') as mock_datetime:
            mock_datetime.now.return_value.time.return_value = time(10, 30)  # 10:30 AM
            assert agent._is_market_hours() == True
            
            # Test outside market hours
            mock_datetime.now.return_value.time.return_value = time(8, 30)  # 8:30 AM
            assert agent._is_market_hours() == False
            
            # Test pre-market hours
            mock_datetime.now.return_value.time.return_value = time(9, 5)  # 9:05 AM
            assert agent._is_market_hours() == True  # Should be allowed with pre-market
    
    def test_signal_validation(self, sample_config, sample_signal, tmp_path):
        """Test signal validation logic"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        agent = ExecutionAgent(str(config_file))
        
        # Mock market hours to be valid
        with patch.object(agent, '_is_market_hours', return_value=True):
            # Test valid signal
            is_valid, message = agent._validate_signal(sample_signal)
            assert is_valid == True
            assert "validation passed" in message.lower()
            
            # Test invalid quantity
            invalid_signal = sample_signal
            invalid_signal.quantity = 0
            is_valid, message = agent._validate_signal(invalid_signal)
            assert is_valid == False
            assert "invalid quantity" in message.lower()
            
            # Test invalid entry price
            invalid_signal.quantity = 1
            invalid_signal.entry_price = 0
            is_valid, message = agent._validate_signal(invalid_signal)
            assert is_valid == False
            assert "invalid entry price" in message.lower()
    
    def test_order_request_creation(self, sample_signal):
        """Test order request creation"""
        order_request = OrderRequest(
            variety="NORMAL",
            tradingsymbol=sample_signal.symbol,
            symboltoken=sample_signal.symbol_token,
            transactiontype=sample_signal.action,
            exchange=sample_signal.exchange,
            ordertype=sample_signal.order_type,
            producttype=sample_signal.product_type,
            duration="DAY",
            price=str(sample_signal.entry_price),
            quantity=str(sample_signal.quantity)
        )
        
        order_dict = order_request.to_dict()
        
        assert order_dict['tradingsymbol'] == "RELIANCE-EQ"
        assert order_dict['transactiontype'] == "BUY"
        assert order_dict['price'] == "2780.0"
        assert order_dict['quantity'] == "1"
    
    @pytest.mark.asyncio
    async def test_execution_stats_update(self, sample_config, tmp_path):
        """Test execution statistics update"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        agent = ExecutionAgent(str(config_file))
        
        # Initialize stats
        agent.execution_stats['successful_orders'] = 1
        agent.execution_stats['avg_execution_time_ms'] = 1000.0
        agent.execution_stats['avg_slippage_percent'] = 0.2
        
        # Update with new execution
        agent._update_execution_stats(1500.0, 0.3)
        
        # Check updated averages
        assert agent.execution_stats['avg_execution_time_ms'] == 1250.0  # (1000 + 1500) / 2
        assert agent.execution_stats['avg_slippage_percent'] == 0.25  # (0.2 + 0.3) / 2

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 INTEGRATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestExecutionAgentIntegration:
    """Integration tests with mocked external services"""
    
    @pytest.mark.asyncio
    async def test_successful_order_placement(self, sample_config, sample_signal, mock_angel_api, tmp_path):
        """Test successful order placement flow"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        # Mock successful API response
        mock_angel_api.smart_api.placeOrder.return_value = {
            'status': True,
            'data': {'orderid': 'TEST123'},
            'message': 'Order placed successfully'
        }
        
        agent = ExecutionAgent(str(config_file))
        agent.angel_api = mock_angel_api
        
        # Mock market hours and validation
        with patch.object(agent, '_is_market_hours', return_value=True):
            success, response = await agent._place_entry_order(sample_signal)
            
            assert success == True
            assert isinstance(response, OrderResponse)
            assert response.order_id == 'TEST123'
            assert response.status == 'PLACED'
            
            # Verify API was called with correct parameters
            mock_angel_api.smart_api.placeOrder.assert_called_once()
            call_args = mock_angel_api.smart_api.placeOrder.call_args[0][0]
            assert call_args['tradingsymbol'] == 'RELIANCE-EQ'
            assert call_args['transactiontype'] == 'BUY'
    
    @pytest.mark.asyncio
    async def test_order_placement_failure(self, sample_config, sample_signal, mock_angel_api, tmp_path):
        """Test order placement failure handling"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        # Mock failed API response
        mock_angel_api.smart_api.placeOrder.return_value = {
            'status': False,
            'message': 'Insufficient margin'
        }
        
        agent = ExecutionAgent(str(config_file))
        agent.angel_api = mock_angel_api
        
        success, response = await agent._place_entry_order(sample_signal)
        
        assert success == False
        assert "Insufficient margin" in response
    
    @pytest.mark.asyncio
    async def test_order_modification(self, sample_config, sample_signal, mock_angel_api, tmp_path):
        """Test order modification"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        # Setup agent with active order
        agent = ExecutionAgent(str(config_file))
        agent.angel_api = mock_angel_api
        
        # Create mock trade execution
        order_response = OrderResponse(
            order_id="TEST123",
            status="PLACED",
            message="Order placed",
            timestamp=datetime.now(),
            order_request=OrderRequest(
                tradingsymbol=sample_signal.symbol,
                ordertype="LIMIT",
                producttype="MIS",
                quantity="1"
            )
        )
        
        trade_execution = TradeExecution(
            signal_payload=sample_signal,
            entry_order=order_response
        )
        
        agent.active_orders["TEST123"] = trade_execution
        
        # Mock successful modification
        mock_angel_api.smart_api.modifyOrder.return_value = {
            'status': True,
            'message': 'Order modified successfully'
        }
        
        success, message = await agent.modify_order("TEST123", 2785.0)
        
        assert success == True
        assert "modified successfully" in message.lower()
        mock_angel_api.smart_api.modifyOrder.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_order_cancellation(self, sample_config, sample_signal, mock_angel_api, tmp_path):
        """Test order cancellation"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        agent = ExecutionAgent(str(config_file))
        agent.angel_api = mock_angel_api
        
        # Setup active order
        trade_execution = TradeExecution(signal_payload=sample_signal)
        agent.active_orders["TEST123"] = trade_execution
        
        # Mock successful cancellation
        mock_angel_api.smart_api.cancelOrder.return_value = {
            'status': True,
            'message': 'Order cancelled successfully'
        }
        
        success, message = await agent.cancel_order("TEST123", "Test cancellation")
        
        assert success == True
        assert "cancelled successfully" in message.lower()
        assert "TEST123" not in agent.active_orders
        assert len(agent.order_history) == 1
        assert agent.order_history[0].status == "CANCELLED"

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 PERFORMANCE TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestExecutionAgentPerformance:
    """Performance tests for execution speed and efficiency"""

    @pytest.mark.asyncio
    async def test_execution_speed(self, sample_config, sample_signal, mock_angel_api, tmp_path):
        """Test order execution speed"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)

        # Mock fast API response
        mock_angel_api.smart_api.placeOrder.return_value = {
            'status': True,
            'data': {'orderid': 'SPEED_TEST'},
            'message': 'Order placed successfully'
        }

        agent = ExecutionAgent(str(config_file))
        agent.angel_api = mock_angel_api

        start_time = datetime.now()
        success, response = await agent._place_entry_order(sample_signal)
        execution_time = (datetime.now() - start_time).total_seconds() * 1000

        assert success == True
        assert execution_time < 100  # Should complete in under 100ms (mocked)
        assert response.execution_time_ms < 100

    @pytest.mark.asyncio
    async def test_concurrent_order_processing(self, sample_config, mock_angel_api, tmp_path):
        """Test handling multiple concurrent orders"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)

        agent = ExecutionAgent(str(config_file))
        agent.angel_api = mock_angel_api

        # Mock API responses
        mock_angel_api.smart_api.placeOrder.return_value = {
            'status': True,
            'data': {'orderid': 'CONCURRENT_TEST'},
            'message': 'Order placed successfully'
        }

        # Create multiple signals
        signals = []
        for i in range(5):
            signal = SignalPayload(
                symbol=f"STOCK{i}-EQ",
                exchange="NSE",
                symbol_token=f"100{i}",
                action="BUY",
                entry_price=1000.0 + i * 10,
                sl_price=990.0 + i * 10,
                target_price=1020.0 + i * 10,
                quantity=1,
                signal_id=f"concurrent_test_{i}"
            )
            signals.append(signal)

        # Process signals concurrently
        with patch.object(agent, '_is_market_hours', return_value=True):
            tasks = [agent._place_entry_order(signal) for signal in signals]
            results = await asyncio.gather(*tasks)

        # Verify all orders were processed
        assert len(results) == 5
        for success, response in results:
            assert success == True
            assert isinstance(response, OrderResponse)

# ═══════════════════════════════════════════════════════════════════════════════
# 🛡️ ERROR HANDLING TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestExecutionAgentErrorHandling:
    """Error handling and failure scenario tests"""

    @pytest.mark.asyncio
    async def test_api_timeout_handling(self, sample_config, sample_signal, tmp_path):
        """Test API timeout handling"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)

        agent = ExecutionAgent(str(config_file))

        # Mock API client that raises timeout
        mock_api = Mock()
        mock_api.smart_api.placeOrder.side_effect = asyncio.TimeoutError("API timeout")
        agent.angel_api = mock_api

        success, response = await agent._place_entry_order(sample_signal)

        assert success == False
        assert "timeout" in response.lower()

    @pytest.mark.asyncio
    async def test_network_error_handling(self, sample_config, sample_signal, tmp_path):
        """Test network error handling"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)

        agent = ExecutionAgent(str(config_file))

        # Mock API client that raises connection error
        mock_api = Mock()
        mock_api.smart_api.placeOrder.side_effect = ConnectionError("Network error")
        agent.angel_api = mock_api

        success, response = await agent._place_entry_order(sample_signal)

        assert success == False
        assert "network error" in response.lower()

    @pytest.mark.asyncio
    async def test_invalid_order_parameters(self, sample_config, tmp_path):
        """Test handling of invalid order parameters"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)

        agent = ExecutionAgent(str(config_file))

        # Create signal with invalid parameters
        invalid_signal = SignalPayload(
            symbol="",  # Empty symbol
            exchange="NSE",
            symbol_token="2885",
            action="BUY",
            entry_price=-100,  # Negative price
            sl_price=0,
            target_price=0,
            quantity=-5  # Negative quantity
        )

        with patch.object(agent, '_is_market_hours', return_value=True):
            is_valid, message = agent._validate_signal(invalid_signal)
            assert is_valid == False
            assert "missing symbol" in message.lower() or "invalid" in message.lower()

    @pytest.mark.asyncio
    async def test_retry_mechanism(self, sample_config, sample_signal, tmp_path):
        """Test retry mechanism for failed orders"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)

        agent = ExecutionAgent(str(config_file))

        # Add signal to retry queue
        agent.retry_queue.append((sample_signal, datetime.now() - timedelta(minutes=10)))

        # Mock successful retry
        mock_api = Mock()
        mock_api.smart_api.placeOrder.return_value = {
            'status': True,
            'data': {'orderid': 'RETRY_TEST'},
            'message': 'Order placed successfully'
        }
        agent.angel_api = mock_api

        # Process retry queue (simulate background task)
        with patch.object(agent, '_is_market_hours', return_value=True):
            with patch.object(agent, 'process_signal', return_value=(True, "Success", None)) as mock_process:
                # Simulate retry processing
                if agent.retry_queue:
                    signal, retry_time = agent.retry_queue.popleft()
                    signal.retry_count = getattr(signal, 'retry_count', 0) + 1
                    await agent.process_signal(signal)

                mock_process.assert_called_once()

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ CONFIGURATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestExecutionAgentConfiguration:
    """Configuration validation and loading tests"""

    def test_config_loading(self, sample_config, tmp_path):
        """Test configuration file loading"""
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)

        agent = ExecutionAgent(str(config_file))

        assert agent.config == sample_config
        assert agent.config['execution']['default_order_type'] == 'LIMIT'
        assert agent.config['market_hours']['market_open_time'] == '09:15'

    def test_missing_config_file(self, tmp_path):
        """Test handling of missing configuration file"""
        non_existent_file = tmp_path / "missing_config.yaml"

        agent = ExecutionAgent(str(non_existent_file))

        # Should initialize with empty config
        assert agent.config == {}

    def test_invalid_config_format(self, tmp_path):
        """Test handling of invalid configuration format"""
        config_file = tmp_path / "invalid_config.yaml"
        with open(config_file, 'w') as f:
            f.write("invalid: yaml: content: [")

        agent = ExecutionAgent(str(config_file))

        # Should handle gracefully
        assert agent.config == {}

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 UTILITY FUNCTIONS FOR TESTING
# ═══════════════════════════════════════════════════════════════════════════════

def create_test_config(overrides: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create test configuration with optional overrides"""
    base_config = {
        'angel_one_api': {'enabled': True},
        'execution': {'default_order_type': 'LIMIT'},
        'market_hours': {'allow_pre_market': True},
        'logging': {'file_enabled': False}
    }

    if overrides:
        base_config.update(overrides)

    return base_config

def create_test_signal(overrides: Dict[str, Any] = None) -> SignalPayload:
    """Create test signal with optional overrides"""
    base_signal = {
        'symbol': 'TEST-EQ',
        'exchange': 'NSE',
        'symbol_token': '1234',
        'action': 'BUY',
        'entry_price': 100.0,
        'sl_price': 95.0,
        'target_price': 110.0,
        'quantity': 1,
        'signal_id': 'test_signal'
    }

    if overrides:
        base_signal.update(overrides)

    return SignalPayload(**base_signal)

# ═══════════════════════════════════════════════════════════════════════════════
# 🏃 TEST RUNNER
# ═══════════════════════════════════════════════════════════════════════════════

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])

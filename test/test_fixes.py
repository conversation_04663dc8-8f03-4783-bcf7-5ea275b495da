#!/usr/bin/env python3
"""
Test script to verify all the fixes for the trading system issues
"""

import sys
import polars as pl
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import asyncio
import logging

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_polars_api_compatibility():
    """Test that all Polars API calls work with current version"""
    logger.info("Testing Polars API compatibility...")
    
    # Create sample OHLCV data
    start_date = datetime.now() - timedelta(days=30)
    dates = []
    current_date = start_date
    for i in range(1000):  # Generate 1000 minutes of data
        dates.append(current_date)
        current_date += timedelta(minutes=1)
    
    np.random.seed(42)
    n = len(dates)
    
    # Generate realistic OHLCV data
    base_price = 100.0
    prices = []
    volumes = []
    
    current_price = base_price
    for i in range(n):
        # Random walk for price
        change = np.random.normal(0, 0.5)
        current_price = max(50, current_price + change)  # Don't go below 50
        
        # OHLC for this minute
        high = current_price + abs(np.random.normal(0, 0.2))
        low = current_price - abs(np.random.normal(0, 0.2))
        open_price = current_price + np.random.normal(0, 0.1)
        close_price = current_price + np.random.normal(0, 0.1)
        
        prices.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price
        })
        
        volumes.append(np.random.randint(1000, 10000))
    
    # Create DataFrame
    df = pl.DataFrame({
        'timestamp': dates,
        'open': [p['open'] for p in prices],
        'high': [p['high'] for p in prices],
        'low': [p['low'] for p in prices],
        'close': [p['close'] for p in prices],
        'volume': volumes
    })
    
    logger.info(f"Created test data with {len(df)} rows")
    
    try:
        # Test 1: RSI calculation with clip
        logger.info("Testing RSI calculation...")
        delta = df['close'].diff()
        gain = delta.clip(lower_bound=0).rolling_mean(window_size=14)
        loss = (-delta.clip(upper_bound=0)).rolling_mean(window_size=14)
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        logger.info(f"✓ RSI calculation successful, last value: {rsi.last()}")
        
        # Test 2: VWAP calculation with cum_sum
        logger.info("Testing VWAP calculation...")
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cum_sum() / df['volume'].cum_sum()
        logger.info(f"✓ VWAP calculation successful, last value: {vwap.last()}")
        
        # Test 3: True Range calculation with max_horizontal
        logger.info("Testing True Range calculation...")
        tr1 = df['high'] - df['low']
        tr2 = (df['high'] - df['close'].shift(1)).abs()
        tr3 = (df['low'] - df['close'].shift(1)).abs()
        true_range = pl.max_horizontal([tr1, tr2, tr3])
        atr = true_range.rolling_mean(window_size=14)
        logger.info(f"✓ True Range calculation successful, last ATR: {atr.last()}")
        
        # Test 4: Timeframe aggregation
        logger.info("Testing timeframe aggregation...")
        df_5min = df.group_by_dynamic(
            "timestamp",
            every="5m",
            period="5m",
            closed="left"
        ).agg([
            pl.col("open").first().alias("open"),
            pl.col("high").max().alias("high"),
            pl.col("low").min().alias("low"),
            pl.col("close").last().alias("close"),
            pl.col("volume").sum().alias("volume")
        ]).sort("timestamp")
        
        df_5min = df_5min.drop_nulls()
        logger.info(f"✓ Timeframe aggregation successful, 5min data: {len(df_5min)} rows")
        
        logger.info("🎉 All Polars API tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Polars API test failed: {e}")
        return False

def test_feature_engineering_dict_conversion():
    """Test the feature engineering dict to DataFrame conversion"""
    logger.info("Testing feature engineering dict conversion...")
    
    try:
        # Mock feature engineering results
        from agents.live_feature_engineering_agent import FeatureEngineering
        
        features_dict = {
            'RELIANCE': FeatureEngineering(
                symbol='RELIANCE',
                features={
                    'rsi_14': 65.5,
                    'macd': 0.25,
                    'bb_width': 0.05,
                    'volume_ratio': 1.2
                },
                target_return=0.02,
                market_conditions={'trend_strength': 'strong'},
                feature_count=4,
                is_valid=True,
                quality_score=0.9
            ),
            'TCS': FeatureEngineering(
                symbol='TCS',
                features={
                    'rsi_14': 45.2,
                    'macd': -0.15,
                    'bb_width': 0.03,
                    'volume_ratio': 0.8
                },
                target_return=-0.01,
                market_conditions={'trend_strength': 'weak'},
                feature_count=4,
                is_valid=True,
                quality_score=0.85
            )
        }
        
        # Test conversion
        rows = []
        for symbol, feature_eng in features_dict.items():
            if feature_eng.is_valid:
                row = {'symbol': symbol}
                row.update(feature_eng.features)
                if feature_eng.target_return is not None:
                    row['target_return'] = feature_eng.target_return
                rows.append(row)
        
        if rows:
            features_df = pl.DataFrame(rows)
            logger.info(f"✓ Dict to DataFrame conversion successful: {len(features_df)} rows, {len(features_df.columns)} columns")
            logger.info(f"Columns: {features_df.columns}")
            return True
        else:
            logger.error("❌ No valid features to convert")
            return False
            
    except Exception as e:
        logger.error(f"❌ Feature engineering dict conversion test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("🧪 Starting comprehensive fix verification tests...")
    logger.info("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Polars API compatibility
    if test_polars_api_compatibility():
        tests_passed += 1
    
    # Test 2: Feature engineering dict conversion
    if test_feature_engineering_dict_conversion():
        tests_passed += 1
    
    logger.info("=" * 60)
    logger.info(f"🏁 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        logger.info("🎉 All tests passed! The fixes should work correctly.")
    else:
        logger.error("❌ Some tests failed. Please check the issues above.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    asyncio.run(main())
#!/usr/bin/env python3
"""
GPU Acceleration Test for Enhanced Backtesting System
Tests Numba CUDA kernels vs CPU performance
"""

import time
import numpy as np
import polars as pl
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_acceleration_availability():
    """Test acceleration availability and capabilities"""
    print("🔍 Testing Acceleration Availability...")

    # Test Numba JIT
    try:
        from numba import jit

        @jit(nopython=True)
        def test_numba_func(n):
            total = 0
            for i in range(n):
                total += i * i
            return total

        result = test_numba_func(1000)
        print(f"✅ Numba JIT test successful: {result}")
        numba_available = True

    except ImportError as e:
        print(f"❌ Numba not available: {e}")
        numba_available = False
    except Exception as e:
        print(f"❌ Numba test failed: {e}")
        numba_available = False

    # Test CUDA (if available)
    cuda_available = False
    try:
        from numba import cuda

        # Test CUDA context
        device = cuda.get_current_device()
        print(f"🔧 CUDA Device: {device.name}")

        # Test memory info
        meminfo = cuda.current_context().get_memory_info()
        total_memory = meminfo[1] / 1024**3  # Convert to GB
        free_memory = meminfo[0] / 1024**3
        print(f"🔧 GPU Memory: {free_memory:.1f} GB free / {total_memory:.1f} GB total")

        cuda_available = True

    except Exception as e:
        print(f"⚠️ CUDA not available: {e}")

    return numba_available, cuda_available

def create_test_data(n_rows=50000):
    """Create test market data"""
    print(f"📊 Creating test data with {n_rows:,} rows...")
    
    # Generate realistic price data
    np.random.seed(42)
    base_price = 100.0
    returns = np.random.normal(0, 0.02, n_rows)  # 2% daily volatility
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    closes = np.array(prices)
    highs = closes * (1 + np.abs(np.random.normal(0, 0.01, n_rows)))
    lows = closes * (1 - np.abs(np.random.normal(0, 0.01, n_rows)))
    opens = np.roll(closes, 1)
    opens[0] = base_price
    volumes = np.random.randint(1000, 10000, n_rows)
    
    # Create datetime index
    start_date = datetime(2023, 1, 1)
    dates = [start_date + timedelta(minutes=i) for i in range(n_rows)]
    
    df = pl.DataFrame({
        'datetime': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    })
    
    print(f"✅ Test data created: {len(df):,} rows")
    return df

def test_cpu_vs_gpu_performance():
    """Compare CPU vs GPU performance"""
    print("\n🏁 Performance Comparison: CPU vs GPU")
    print("=" * 50)
    
    # Test different data sizes
    test_sizes = [1000, 5000, 10000, 50000, 100000]
    results = []
    
    for size in test_sizes:
        print(f"\n📊 Testing with {size:,} rows...")
        
        # Create test data
        df = create_test_data(size)
        
        # Test CPU performance
        print("  🔧 CPU Test...")
        start_time = time.time()
        
        # CPU calculations
        cpu_result = df.with_columns([
            (pl.col("high") - pl.col("low")).alias("price_range"),
            ((pl.col("high") + pl.col("low")) / 2).alias("mid_price"),
            pl.col("close").rolling_mean(20).alias("sma_20")
        ])
        
        cpu_time = time.time() - start_time
        print(f"    ⏱️  CPU Time: {cpu_time:.4f}s")
        
        # Test GPU performance (if available)
        gpu_time = None
        speedup = None
        
        try:
            from numba import jit

            print("  ⚡ Numba JIT Test...")
            start_time = time.time()

            # Extract arrays
            close_np = df['close'].to_numpy().astype(np.float32)
            high_np = df['high'].to_numpy().astype(np.float32)
            low_np = df['low'].to_numpy().astype(np.float32)

            # Numba JIT optimized function
            @jit(nopython=True)
            def numba_calculations(close, high, low):
                price_range = high - low
                mid_price = (high + low) / 2
                return price_range, mid_price

            # Numba calculations
            price_range, mid_price = numba_calculations(close_np, high_np, low_np)

            gpu_time = time.time() - start_time
            speedup = cpu_time / gpu_time if gpu_time > 0 else 0

            print(f"    ⏱️  Numba Time: {gpu_time:.4f}s")
            print(f"    🎯 Speedup: {speedup:.2f}x")

        except Exception as e:
            print(f"    ❌ Numba test failed: {e}")
            gpu_time = None
            speedup = None
        
        results.append({
            'size': size,
            'cpu_time': cpu_time,
            'gpu_time': gpu_time,
            'speedup': speedup
        })
    
    # Print summary
    print("\n📈 Performance Summary:")
    print("-" * 60)
    print(f"{'Size':<10} {'CPU Time':<12} {'GPU Time':<12} {'Speedup':<10}")
    print("-" * 60)
    
    for result in results:
        size = f"{result['size']:,}"
        cpu_time = f"{result['cpu_time']:.4f}s"
        gpu_time = f"{result['gpu_time']:.4f}s" if result['gpu_time'] else "N/A"
        speedup = f"{result['speedup']:.2f}x" if result['speedup'] else "N/A"
        
        print(f"{size:<10} {cpu_time:<12} {gpu_time:<12} {speedup:<10}")
    
    return results

def test_backtesting_integration():
    """Test GPU integration with backtesting system"""
    print("\n🔬 Testing Backtesting Integration...")
    
    try:
        from agents.enhanced_backtesting_kimi import (
            accelerated_signal_processing,
            accelerated_trade_simulation,
            accelerated_performance_metrics,
            CUDA_AVAILABLE
        )
        
        if not CUDA_AVAILABLE and not NUMBA_AVAILABLE:
            print("❌ No acceleration available for backtesting integration test")
            return False
        
        # Create test data
        df = create_test_data(10000)
        
        # Test signal processing
        print("  🔧 Testing GPU signal processing...")
        start_time = time.time()
        
        strategy = {'name': 'Test_Strategy'}
        df_with_signals = accelerated_signal_processing(df, strategy)

        signal_time = time.time() - start_time
        print(f"    ⏱️  Signal processing: {signal_time:.4f}s")

        if 'accelerated_signals' in df_with_signals.columns:
            signals = df_with_signals['accelerated_signals'].to_numpy()
            signal_count = np.sum(np.abs(signals))
            print(f"    📊 Generated {signal_count} signals")

            # Test trade simulation
            print("  🔧 Testing accelerated trade simulation...")
            start_time = time.time()

            trades = accelerated_trade_simulation(df, signals)

            trade_time = time.time() - start_time
            print(f"    ⏱️  Trade simulation: {trade_time:.4f}s")
            print(f"    📊 Generated {len(trades)} trades")

            if trades:
                # Test performance metrics
                print("  🔧 Testing accelerated performance metrics...")
                start_time = time.time()

                metrics = accelerated_performance_metrics(trades)
                
                metrics_time = time.time() - start_time
                print(f"    ⏱️  Performance metrics: {metrics_time:.4f}s")
                print(f"    📊 Calculated {len(metrics)} metrics")
                
                # Print sample metrics
                if metrics:
                    print("    📈 Sample metrics:")
                    for key, value in list(metrics.items())[:5]:
                        print(f"      {key}: {value}")
        
        print("✅ Backtesting integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ Backtesting integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 GPU Acceleration Test Suite")
    print("=" * 50)
    
    # Test 1: Acceleration Availability
    numba_available, cuda_available = test_acceleration_availability()
    
    # Test 2: Performance Comparison
    if numba_available:
        performance_results = test_cpu_vs_gpu_performance()

        # Test 3: Backtesting Integration
        integration_success = test_backtesting_integration()

        # Final summary
        print("\n🎯 Final Summary:")
        print("-" * 30)
        print(f"Numba JIT: {'✅ Yes' if numba_available else '❌ No'}")
        print(f"CUDA: {'✅ Yes' if cuda_available else '❌ No'}")

        if performance_results:
            max_speedup = max([r['speedup'] for r in performance_results if r['speedup']], default=0)
            print(f"Max Speedup: {max_speedup:.2f}x")

        print(f"Integration: {'✅ Success' if integration_success else '❌ Failed'}")

        if numba_available and max_speedup > 1.5:
            acceleration_type = "🚀 CUDA + Numba" if cuda_available else "⚡ Numba JIT"
            print(f"\n🎉 {acceleration_type} acceleration is working effectively!")
            print("   The system should show significant performance improvements.")
        else:
            print("\n⚠️  Acceleration may not provide significant benefits.")
            print("   System will use standard Python processing.")

    else:
        print("\n⚠️  No acceleration available. System will use standard Python processing.")

if __name__ == "__main__":
    main()

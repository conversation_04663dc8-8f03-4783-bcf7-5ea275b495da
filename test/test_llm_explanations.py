"""
Tests for Feature 10: LLM-Explainable Results Summary
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, AsyncMock

from agents.enhanced_backtesting_polars import LLMResultsExplainer


class TestLLMResultsExplainer:
    """Test suite for LLMResultsExplainer class"""
    
    @pytest.fixture
    def explainer(self):
        """Create LLMResultsExplainer instance"""
        return LLMResultsExplainer()
    
    @pytest.mark.asyncio
    async def test_initialization(self, explainer):
        """Test proper initialization of LLMResultsExplainer"""
        assert explainer.llm_client is not None
        assert explainer.explanation_templates == {}
        assert explainer.context_memory == []
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_generate_comprehensive_explanation(self, mock_openai, explainer, 
                                                    sample_backtest_results, sample_trades):
        """Test comprehensive explanation generation"""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'This is a comprehensive analysis...'}
        mock_openai.return_value = mock_response
        
        explanation = await explainer.generate_comprehensive_explanation(
            sample_backtest_results, sample_trades, strategy_name="Test Strategy"
        )
        
        assert 'executive_summary' in explanation
        assert 'performance_analysis' in explanation
        assert 'risk_assessment' in explanation
        assert 'trade_analysis' in explanation
        assert 'recommendations' in explanation
        assert 'market_insights' in explanation
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_explain_performance_metrics(self, mock_openai, explainer, sample_metrics):
        """Test performance metrics explanation"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Performance metrics analysis...'}
        mock_openai.return_value = mock_response
        
        explanation = await explainer.explain_performance_metrics(sample_metrics)
        
        assert 'metrics_interpretation' in explanation
        assert 'strengths' in explanation
        assert 'weaknesses' in explanation
        assert 'benchmark_comparison' in explanation
        assert 'improvement_suggestions' in explanation
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_analyze_trade_patterns(self, mock_openai, explainer, sample_trades):
        """Test trade pattern analysis"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Trade pattern insights...'}
        mock_openai.return_value = mock_response
        
        analysis = await explainer.analyze_trade_patterns(sample_trades)
        
        assert 'pattern_identification' in analysis
        assert 'success_patterns' in analysis
        assert 'failure_patterns' in analysis
        assert 'timing_insights' in analysis
        assert 'behavioral_observations' in analysis
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_generate_risk_assessment(self, mock_openai, explainer, sample_backtest_results):
        """Test risk assessment generation"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Risk assessment analysis...'}
        mock_openai.return_value = mock_response
        
        risk_assessment = await explainer.generate_risk_assessment(sample_backtest_results)
        
        assert 'risk_profile' in risk_assessment
        assert 'key_risks' in risk_assessment
        assert 'risk_mitigation' in risk_assessment
        assert 'stress_test_insights' in risk_assessment
        assert 'risk_adjusted_recommendations' in risk_assessment
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_compare_strategies(self, mock_openai, explainer, sample_backtest_results):
        """Test strategy comparison explanation"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Strategy comparison analysis...'}
        mock_openai.return_value = mock_response
        
        strategy_results = {
            'Strategy A': sample_backtest_results,
            'Strategy B': {**sample_backtest_results, 'total_return': 18.5, 'sharpe_ratio': 1.4}
        }
        
        comparison = await explainer.compare_strategies(strategy_results)
        
        assert 'comparison_summary' in comparison
        assert 'relative_strengths' in comparison
        assert 'relative_weaknesses' in comparison
        assert 'selection_recommendation' in comparison
        assert 'combination_opportunities' in comparison
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_explain_market_regime_performance(self, mock_openai, explainer):
        """Test market regime performance explanation"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Market regime analysis...'}
        mock_openai.return_value = mock_response
        
        regime_data = {
            'bull_market': {'return': 25.0, 'trades': 45, 'win_rate': 0.67},
            'bear_market': {'return': -5.0, 'trades': 30, 'win_rate': 0.43},
            'sideways_market': {'return': 8.0, 'trades': 60, 'win_rate': 0.55}
        }
        
        explanation = await explainer.explain_market_regime_performance(regime_data)
        
        assert 'regime_analysis' in explanation
        assert 'adaptability_assessment' in explanation
        assert 'regime_specific_insights' in explanation
        assert 'optimization_suggestions' in explanation
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_generate_optimization_recommendations(self, mock_openai, explainer, sample_backtest_results):
        """Test optimization recommendations generation"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Optimization recommendations...'}
        mock_openai.return_value = mock_response
        
        optimization_data = {
            'current_performance': sample_backtest_results,
            'parameter_sensitivity': {'rsi_period': 0.8, 'threshold': 0.6},
            'improvement_potential': 15.0
        }
        
        recommendations = await explainer.generate_optimization_recommendations(optimization_data)
        
        assert 'priority_improvements' in recommendations
        assert 'parameter_adjustments' in recommendations
        assert 'risk_considerations' in recommendations
        assert 'implementation_roadmap' in recommendations
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_explain_drawdown_periods(self, mock_openai, explainer, sample_trades):
        """Test drawdown period explanation"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Drawdown analysis...'}
        mock_openai.return_value = mock_response
        
        drawdown_periods = [
            {'start': datetime(2023, 1, 15), 'end': datetime(2023, 1, 25), 'magnitude': 0.12},
            {'start': datetime(2023, 3, 10), 'end': datetime(2023, 3, 20), 'magnitude': 0.08}
        ]
        
        explanation = await explainer.explain_drawdown_periods(drawdown_periods, sample_trades)
        
        assert 'drawdown_analysis' in explanation
        assert 'causes_identification' in explanation
        assert 'recovery_patterns' in explanation
        assert 'prevention_strategies' in explanation
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_generate_executive_summary(self, mock_openai, explainer, sample_backtest_results):
        """Test executive summary generation"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Executive summary...'}
        mock_openai.return_value = mock_response
        
        summary = await explainer.generate_executive_summary(
            sample_backtest_results, strategy_name="Test Strategy"
        )
        
        assert 'key_findings' in summary
        assert 'performance_highlights' in summary
        assert 'risk_summary' in summary
        assert 'strategic_recommendations' in summary
        assert 'next_steps' in summary
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    async def test_create_explanation_context(self, explainer, sample_backtest_results, sample_trades):
        """Test explanation context creation"""
        context = await explainer._create_explanation_context(
            sample_backtest_results, sample_trades, strategy_name="Test Strategy"
        )
        
        assert 'strategy_info' in context
        assert 'performance_summary' in context
        assert 'trade_statistics' in context
        assert 'risk_metrics' in context
        assert 'market_context' in context
        
        # Check that context contains relevant information
        assert context['strategy_info']['name'] == "Test Strategy"
        assert 'total_return' in context['performance_summary']
        assert 'total_trades' in context['trade_statistics']
    
    @pytest.mark.asyncio
    async def test_format_explanation_output(self, explainer):
        """Test explanation output formatting"""
        raw_explanation = "This is a raw explanation with key insights about performance and risk."
        
        formatted_output = await explainer._format_explanation_output(
            raw_explanation, output_format='structured'
        )
        
        assert isinstance(formatted_output, dict)
        assert 'content' in formatted_output
        assert 'metadata' in formatted_output
        assert 'timestamp' in formatted_output
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_explain_parameter_sensitivity(self, mock_openai, explainer):
        """Test parameter sensitivity explanation"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Parameter sensitivity analysis...'}
        mock_openai.return_value = mock_response
        
        sensitivity_data = {
            'rsi_period': {'sensitivity': 0.8, 'optimal_range': (12, 16)},
            'threshold': {'sensitivity': 0.6, 'optimal_range': (0.2, 0.4)},
            'sma_period': {'sensitivity': 0.3, 'optimal_range': (18, 22)}
        }
        
        explanation = await explainer.explain_parameter_sensitivity(sensitivity_data)
        
        assert 'sensitivity_analysis' in explanation
        assert 'critical_parameters' in explanation
        assert 'optimization_priorities' in explanation
        assert 'robustness_assessment' in explanation
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create')
    async def test_generate_scenario_insights(self, mock_openai, explainer):
        """Test scenario analysis insights"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = {'content': 'Scenario analysis insights...'}
        mock_openai.return_value = mock_response
        
        scenario_results = {
            'normal_market': {'return': 15.0, 'max_drawdown': 0.08},
            'market_crash': {'return': -12.0, 'max_drawdown': 0.25},
            'bull_market': {'return': 28.0, 'max_drawdown': 0.05}
        }
        
        insights = await explainer.generate_scenario_insights(scenario_results)
        
        assert 'scenario_performance' in insights
        assert 'resilience_assessment' in insights
        assert 'stress_test_conclusions' in insights
        assert 'adaptive_recommendations' in insights
        
        mock_openai.assert_called()
    
    @pytest.mark.asyncio
    async def test_explanation_caching(self, explainer, sample_backtest_results):
        """Test explanation caching mechanism"""
        # First call should generate new explanation
        with patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create') as mock_openai:
            mock_response = MagicMock()
            mock_response.choices = [MagicMock()]
            mock_response.choices[0].message = {'content': 'Cached explanation...'}
            mock_openai.return_value = mock_response
            
            explanation1 = await explainer.generate_executive_summary(
                sample_backtest_results, strategy_name="Test Strategy", use_cache=True
            )
            
            # Second call with same data should use cache
            explanation2 = await explainer.generate_executive_summary(
                sample_backtest_results, strategy_name="Test Strategy", use_cache=True
            )
            
            # Should only call OpenAI once due to caching
            assert mock_openai.call_count == 1
            assert explanation1 == explanation2
    
    @pytest.mark.asyncio
    async def test_custom_explanation_templates(self, explainer):
        """Test custom explanation templates"""
        custom_template = {
            'name': 'custom_performance_template',
            'prompt': 'Analyze the performance with focus on {focus_area}',
            'variables': ['focus_area', 'performance_data']
        }
        
        explainer.add_custom_template(custom_template)
        
        assert 'custom_performance_template' in explainer.explanation_templates
        assert explainer.explanation_templates['custom_performance_template'] == custom_template
    
    @pytest.mark.asyncio
    async def test_multilingual_explanations(self, explainer, sample_backtest_results):
        """Test multilingual explanation generation"""
        with patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create') as mock_openai:
            mock_response = MagicMock()
            mock_response.choices = [MagicMock()]
            mock_response.choices[0].message = {'content': 'Explanation in Spanish...'}
            mock_openai.return_value = mock_response
            
            explanation = await explainer.generate_executive_summary(
                sample_backtest_results, strategy_name="Test Strategy", language='spanish'
            )
            
            assert 'key_findings' in explanation
            mock_openai.assert_called()
    
    @pytest.mark.asyncio
    async def test_explanation_quality_validation(self, explainer):
        """Test explanation quality validation"""
        explanation_text = "This strategy shows strong performance with a Sharpe ratio of 1.5 and total return of 15%."
        
        quality_score = await explainer._validate_explanation_quality(explanation_text)
        
        assert 0 <= quality_score <= 1
        assert isinstance(quality_score, float)
    
    @pytest.mark.asyncio
    async def test_error_handling_llm_failure(self, explainer, sample_backtest_results):
        """Test error handling when LLM fails"""
        with patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create') as mock_openai:
            mock_openai.side_effect = Exception("API Error")
            
            explanation = await explainer.generate_executive_summary(
                sample_backtest_results, strategy_name="Test Strategy"
            )
            
            # Should return fallback explanation
            assert 'error' in explanation
            assert 'fallback_summary' in explanation
    
    @pytest.mark.asyncio
    async def test_context_memory_management(self, explainer, sample_backtest_results):
        """Test context memory management"""
        # Add multiple contexts to memory
        for i in range(10):
            context = {
                'strategy_name': f"Strategy_{i}",
                'performance': sample_backtest_results,
                'timestamp': datetime.now()
            }
            explainer.context_memory.append(context)
        
        # Memory should be managed (limited size)
        await explainer._manage_context_memory()
        
        assert len(explainer.context_memory) <= explainer.max_context_memory
    
    @pytest.mark.asyncio
    async def test_explanation_personalization(self, explainer, sample_backtest_results):
        """Test explanation personalization"""
        user_preferences = {
            'experience_level': 'intermediate',
            'focus_areas': ['risk_management', 'optimization'],
            'explanation_style': 'detailed'
        }
        
        with patch('agents.enhanced_backtesting_polars.openai.ChatCompletion.create') as mock_openai:
            mock_response = MagicMock()
            mock_response.choices = [MagicMock()]
            mock_response.choices[0].message = {'content': 'Personalized explanation...'}
            mock_openai.return_value = mock_response
            
            explanation = await explainer.generate_personalized_explanation(
                sample_backtest_results, user_preferences
            )
            
            assert 'personalized_insights' in explanation
            assert 'targeted_recommendations' in explanation
            mock_openai.assert_called()

#!/usr/bin/env python3
"""
Test Suite for LLM Interface Agent

Comprehensive testing framework for the LLM Interface Agent including:
- Unit tests for core functionality
- Integration tests with mock agents
- Model selection validation
- Query routing verification
- Performance benchmarking

Author: AI Assistant
Date: 2025-01-16
"""

import unittest
import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import tempfile
import yaml

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.llm_interface_agent import LLMInterfaceAgent, QueryRequest, QueryResponse
from agents.model_selector import ModelSelector, TaskType
from agents.query_router import QueryRouter, IntentCategory, QueryType

class TestModelSelector(unittest.TestCase):
    """Test cases for Model Selector"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.selector = ModelSelector()
    
    def test_model_selection_code_generation(self):
        """Test model selection for code generation tasks"""
        query = "Generate a new RSI scalping strategy in Python"
        model, confidence, metadata = self.selector.select_model(query)
        
        self.assertEqual(model, "deepseek-coder-6.7b")
        self.assertGreater(confidence, 0.7)
        self.assertEqual(metadata['task_type'], TaskType.CODE_GENERATION.value)
    
    def test_model_selection_explanation(self):
        """Test model selection for explanation tasks"""
        query = "Explain how the SuperTrend indicator works in detail"
        model, confidence, metadata = self.selector.select_model(query)
        
        self.assertEqual(model, "codellama-7b-instruct")
        self.assertGreater(confidence, 0.6)
        self.assertEqual(metadata['task_type'], TaskType.CODE_EXPLANATION.value)
    
    def test_model_selection_quick_response(self):
        """Test model selection for quick response tasks"""
        query = "Quick status check of the system"
        model, confidence, metadata = self.selector.select_model(query)
        
        self.assertEqual(model, "phi4-mini")
        self.assertGreater(confidence, 0.5)
        self.assertEqual(metadata['task_type'], TaskType.QUICK_RESPONSE.value)
    
    def test_model_selection_general_reasoning(self):
        """Test model selection for general reasoning tasks"""
        query = "What's the best strategy for volatile market conditions?"
        model, confidence, metadata = self.selector.select_model(query)
        
        self.assertEqual(model, "qwen3-8b")
        self.assertGreater(confidence, 0.6)
    
    def test_temperature_recommendations(self):
        """Test temperature recommendations for different tasks"""
        # Code generation should have lower temperature
        code_query = "Fix this Python function"
        _, _, metadata = self.selector.select_model(code_query)
        self.assertLess(metadata['recommended_temperature'], 0.5)
        
        # Creative tasks should have higher temperature
        creative_query = "Explain market psychology in trading"
        _, _, metadata = self.selector.select_model(creative_query)
        self.assertGreater(metadata['recommended_temperature'], 0.5)
    
    def test_performance_recording(self):
        """Test performance recording functionality"""
        initial_stats = self.selector.get_model_stats()
        
        # Record some performance
        self.selector.record_performance("qwen3-8b", 0.8)
        self.selector.record_performance("deepseek-coder-6.7b", 0.9)
        
        stats = self.selector.get_model_stats()
        self.assertIn("qwen3-8b", stats['performance_history'])
        self.assertEqual(stats['performance_history']['qwen3-8b']['latest'], 0.8)

class TestQueryRouter(unittest.TestCase):
    """Test cases for Query Router"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.router = QueryRouter()
    
    def test_performance_query_routing(self):
        """Test routing of performance analysis queries"""
        query = "What's the ROI of my RSI strategy on RELIANCE?"
        decision = self.router.route_query(query)
        
        self.assertEqual(decision.agent_name, "performance_analysis_agent")
        self.assertEqual(decision.intent_category, IntentCategory.PERFORMANCE_ANALYSIS)
        self.assertEqual(decision.query_type, QueryType.ROI_QUERY)
        self.assertIn("RELIANCE", decision.extracted_entities['symbols'])
        self.assertIn("RSI", decision.extracted_entities['strategies'])
    
    def test_trading_query_routing(self):
        """Test routing of trading execution queries"""
        query = "Buy 100 shares of ADANIPORTS at market price"
        decision = self.router.route_query(query)
        
        self.assertEqual(decision.agent_name, "execution_agent")
        self.assertEqual(decision.intent_category, IntentCategory.TRADING_EXECUTION)
        self.assertEqual(decision.query_type, QueryType.ORDER_PLACEMENT)
        self.assertIn("ADANIPORTS", decision.extracted_entities['symbols'])
        self.assertIn("buy", decision.extracted_entities['actions'])
    
    def test_market_monitoring_routing(self):
        """Test routing of market monitoring queries"""
        query = "Show me current market regime and RSI levels"
        decision = self.router.route_query(query)
        
        self.assertEqual(decision.agent_name, "market_monitoring_agent")
        self.assertEqual(decision.intent_category, IntentCategory.MARKET_MONITORING)
        self.assertIn("RSI", decision.extracted_entities['indicators'])
    
    def test_code_assistance_routing(self):
        """Test routing of code assistance queries"""
        query = "Fix the error in my MACD strategy implementation"
        decision = self.router.route_query(query)
        
        self.assertEqual(decision.agent_name, "code_generation")
        self.assertEqual(decision.intent_category, IntentCategory.CODE_ASSISTANCE)
        self.assertEqual(decision.query_type, QueryType.BUG_FIXING)
    
    def test_entity_extraction(self):
        """Test entity extraction from queries"""
        query = "Analyze RELIANCE and TCS performance using RSI and MACD strategies for 15m timeframe"
        entities = self.router._extract_entities(query)
        
        self.assertIn("RELIANCE", entities.symbols)
        self.assertIn("TCS", entities.symbols)
        self.assertIn("RSI", entities.strategies)
        self.assertIn("MACD", entities.strategies)
        self.assertTrue(any("15" in tf for tf in entities.timeframes))
    
    def test_complexity_estimation(self):
        """Test query complexity estimation"""
        simple_query = "Status check"
        complex_query = "Analyze comprehensive performance metrics for multiple strategies across various timeframes with detailed optimization recommendations"
        
        simple_entities = self.router._extract_entities(simple_query)
        complex_entities = self.router._extract_entities(complex_query)
        
        simple_complexity = self.router._estimate_complexity(simple_query, simple_entities)
        complex_complexity = self.router._estimate_complexity(complex_query, complex_entities)
        
        self.assertLess(simple_complexity, complex_complexity)
    
    def test_real_time_detection(self):
        """Test real-time requirement detection"""
        real_time_query = "What's the current price of RELIANCE?"
        historical_query = "Show me last month's performance"
        
        rt_decision = self.router.route_query(real_time_query)
        hist_decision = self.router.route_query(historical_query)
        
        self.assertTrue(rt_decision.requires_real_time)
        self.assertFalse(hist_decision.requires_real_time)

class TestLLMInterfaceAgent(unittest.TestCase):
    """Test cases for LLM Interface Agent"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create temporary config file
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        config_data = {
            'llm_interface': {
                'models': {
                    'general_reasoning': {
                        'model': 'qwen3-8b',
                        'temperature': 0.7,
                        'max_tokens': 2048
                    }
                },
                'system': {
                    'ollama': {
                        'base_url': 'http://localhost:11434'
                    },
                    'logging': {
                        'level': 'INFO'
                    }
                }
            }
        }
        yaml.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        self.agent = LLMInterfaceAgent(self.temp_config.name)
    
    def tearDown(self):
        """Clean up test fixtures"""
        os.unlink(self.temp_config.name)
    
    @patch('agents.llm_interface_agent.ChatOllama')
    async def test_agent_initialization(self, mock_ollama):
        """Test agent initialization"""
        mock_ollama.return_value = Mock()
        
        success = await self.agent.initialize()
        # Should succeed even without actual Ollama connection in test
        self.assertIsNotNone(self.agent.config)
    
    def test_query_classification(self):
        """Test query classification"""
        performance_query = "show me roi performance"
        query_type = self.agent._classify_query(performance_query)
        self.assertEqual(query_type, 'backtest_queries')
        
        code_query = "fix this python error"
        query_type = self.agent._classify_query(code_query)
        self.assertEqual(query_type, 'code_queries')
    
    def test_model_selection_for_query(self):
        """Test model selection for different query types"""
        code_type = self.agent._select_model_for_query('code_queries')
        self.assertEqual(code_type, 'code_generation')
        
        quick_type = self.agent._select_model_for_query('quick_queries')
        self.assertEqual(quick_type, 'fast_chat')
    
    async def test_fallback_processing(self):
        """Test fallback query processing"""
        query_request = QueryRequest(query="Test query")
        
        # Mock the fallback method
        with patch.object(self.agent, '_process_with_fallback') as mock_fallback:
            mock_fallback.return_value = QueryResponse(
                response="Test response",
                model_used="test_model",
                confidence=0.8
            )
            
            response = await self.agent._process_with_fallback(query_request)
            self.assertIsInstance(response, QueryResponse)
    
    def test_performance_metrics(self):
        """Test performance metrics tracking"""
        initial_metrics = self.agent.get_performance_metrics()
        self.assertIn('queries_processed', initial_metrics)
        self.assertIn('successful_queries', initial_metrics)
        self.assertIn('failed_queries', initial_metrics)
    
    def test_system_status(self):
        """Test system status reporting"""
        status = self.agent.get_system_status()
        self.assertIn('agent_status', status)
        self.assertIn('models', status)
        self.assertIn('performance', status)

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.selector = ModelSelector()
        self.router = QueryRouter()
    
    def test_end_to_end_query_processing(self):
        """Test complete query processing pipeline"""
        query = "Generate a momentum breakout strategy for NIFTY 50 stocks"
        
        # Test routing
        routing_decision = self.router.route_query(query)
        self.assertEqual(routing_decision.agent_name, "code_generation")
        
        # Test model selection
        model, confidence, metadata = self.selector.select_model(query)
        self.assertEqual(model, "deepseek-coder-6.7b")
        
        # Verify consistency
        self.assertGreater(confidence, 0.5)
        self.assertIn("NIFTY", routing_decision.extracted_entities['symbols'])
    
    def test_multi_intent_query_handling(self):
        """Test handling of queries with multiple intents"""
        query = "Show me RELIANCE performance and then buy 100 shares if ROI > 5%"
        
        routing_decision = self.router.route_query(query)
        
        # Should route to primary intent (performance analysis)
        self.assertEqual(routing_decision.intent_category, IntentCategory.PERFORMANCE_ANALYSIS)
        
        # Should have fallback for secondary intent
        self.assertIn("execution_agent", routing_decision.fallback_agents)
    
    def test_context_aware_routing(self):
        """Test context-aware query routing"""
        context = {
            'previous_intent': 'performance_analysis',
            'session_focus': 'performance_analysis'
        }
        
        query = "Show more details"
        routing_decision = self.router.route_query(query, context=context)
        
        # Should boost performance analysis due to context
        self.assertEqual(routing_decision.intent_category, IntentCategory.PERFORMANCE_ANALYSIS)

def run_performance_benchmark():
    """Run performance benchmarks"""
    print("🚀 Running Performance Benchmarks...")
    
    selector = ModelSelector()
    router = QueryRouter()
    
    test_queries = [
        "Generate RSI strategy",
        "Fix Python error",
        "Show portfolio status",
        "Current market regime",
        "Buy RELIANCE shares"
    ] * 20  # 100 queries total
    
    # Benchmark model selection
    start_time = datetime.now()
    for query in test_queries:
        selector.select_model(query)
    model_selection_time = (datetime.now() - start_time).total_seconds()
    
    # Benchmark query routing
    start_time = datetime.now()
    for query in test_queries:
        router.route_query(query)
    routing_time = (datetime.now() - start_time).total_seconds()
    
    print(f"📊 Model Selection: {len(test_queries)} queries in {model_selection_time:.2f}s")
    print(f"   Average: {(model_selection_time/len(test_queries)*1000):.1f}ms per query")
    
    print(f"📊 Query Routing: {len(test_queries)} queries in {routing_time:.2f}s")
    print(f"   Average: {(routing_time/len(test_queries)*1000):.1f}ms per query")

if __name__ == '__main__':
    print("🧪 LLM Interface Agent Test Suite")
    print("=" * 50)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance benchmarks
    print("\n" + "=" * 50)
    run_performance_benchmark()
    
    print("\n✅ All tests completed!")

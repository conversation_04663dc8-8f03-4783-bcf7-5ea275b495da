#!/usr/bin/env python3
"""
Comprehensive Test Suite for Paper Trading System
Tests paper trading functionality, margin calculation, and trading mode switching
"""

import os
import sys
import asyncio
import logging
import pytest
from datetime import datetime, date
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
try:
    from utils.paper_trading import VirtualAccount, PaperTrade
    from agents.execution_agent import ExecutionAgent, SignalPayload
    from main import TradingSystemOrchestrator
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestPaperTradingSystem:
    """Comprehensive test suite for paper trading system"""
    
    def setup_method(self):
        """Setup test environment"""
        # Set environment variables for testing
        os.environ['TRADING_MODE'] = 'paper'
        os.environ['PAPER_TRADING_INITIAL_BALANCE'] = '100000'
        os.environ['PAPER_TRADING_MAX_TRADES_PER_DAY'] = '5'
        os.environ['PAPER_TRADING_COMMISSION_RATE'] = '0.0003'
        os.environ['PAPER_TRADING_MAX_POSITION_SIZE'] = '20000'
        os.environ['PAPER_TRADING_MAX_DAILY_LOSS'] = '5000'
        
        # Test configuration
        self.test_config = {
            'paper_trading': {
                'initial_balance': 100000,
                'max_trades_per_day': 5,
                'commission_rate': 0.0003,
                'max_position_size': 20000,
                'max_daily_loss': 5000,
                'margin_multiplier': 3.5
            }
        }
    
    def test_virtual_account_initialization(self):
        """Test virtual account initialization"""
        logger.info("🧪 Testing virtual account initialization...")
        
        virtual_account = VirtualAccount(self.test_config)
        
        assert virtual_account.initial_balance == 100000
        assert virtual_account.current_balance == 100000
        assert virtual_account.max_trades_per_day == 5
        assert virtual_account.commission_rate == 0.0003
        assert len(virtual_account.trades) == 0
        assert len(virtual_account.positions) == 0
        
        logger.info("✅ Virtual account initialization test passed")
    
    def test_commission_calculation(self):
        """Test commission calculation"""
        logger.info("🧪 Testing commission calculation...")
        
        virtual_account = VirtualAccount(self.test_config)
        
        # Test commission for a typical trade
        charges = virtual_account.calculate_charges(
            symbol="RELIANCE-EQ",
            quantity=10,
            price=2500.0,
            transaction_type="BUY",
            product_type="MIS"
        )
        
        assert 'total_charges' in charges
        assert charges['total_charges'] > 0
        assert 'brokerage' in charges
        assert 'stt' in charges
        assert 'gst' in charges
        
        # Brokerage should be minimum of flat rate or percentage
        expected_brokerage = min(20, 25000 * 0.0003)  # 20 or 7.5
        assert charges['brokerage'] == expected_brokerage
        
        logger.info(f"✅ Commission calculation test passed: ₹{charges['total_charges']:.2f}")
    
    async def test_paper_trade_execution(self):
        """Test paper trade execution"""
        logger.info("🧪 Testing paper trade execution...")
        
        virtual_account = VirtualAccount(self.test_config)
        
        # Test buy order
        success, message, trade = await virtual_account.execute_trade(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            quantity=10,
            price=2500.0,
            transaction_type="BUY",
            product_type="MIS",
            strategy_name="test_strategy"
        )
        
        assert success == True
        assert trade is not None
        assert trade.symbol == "RELIANCE-EQ"
        assert trade.quantity == 10
        assert trade.price == 2500.0
        assert trade.transaction_type == "BUY"
        assert trade.total_charges > 0
        
        # Check account balance
        account_summary = virtual_account.get_account_summary()
        assert account_summary['total_trades'] == 1
        assert account_summary['active_positions'] == 1
        
        logger.info("✅ Paper trade execution test passed")
    
    async def test_trade_limits(self):
        """Test trade limits enforcement"""
        logger.info("🧪 Testing trade limits...")
        
        virtual_account = VirtualAccount(self.test_config)
        
        # Test position size limit
        can_trade, reason = virtual_account.can_place_trade(
            symbol="RELIANCE-EQ",
            quantity=100,  # Large quantity
            price=2500.0,
            transaction_type="BUY",
            product_type="MIS"
        )
        
        assert can_trade == False
        assert "Position size exceeds limit" in reason
        
        # Test daily trade limit
        for i in range(5):  # Max trades per day
            success, _, _ = await virtual_account.execute_trade(
                symbol=f"STOCK{i}-EQ",
                exchange="NSE",
                quantity=1,
                price=100.0,
                transaction_type="BUY",
                product_type="MIS"
            )
            assert success == True
        
        # 6th trade should fail
        can_trade, reason = virtual_account.can_place_trade(
            symbol="STOCK6-EQ",
            quantity=1,
            price=100.0,
            transaction_type="BUY",
            product_type="MIS"
        )
        
        assert can_trade == False
        assert "Daily trade limit exceeded" in reason
        
        logger.info("✅ Trade limits test passed")
    
    async def test_pnl_calculation(self):
        """Test PnL calculation"""
        logger.info("🧪 Testing PnL calculation...")
        
        virtual_account = VirtualAccount(self.test_config)
        
        # Buy trade
        buy_success, _, buy_trade = await virtual_account.execute_trade(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            quantity=10,
            price=2500.0,
            transaction_type="BUY",
            product_type="MIS"
        )
        assert buy_success == True
        
        # Sell trade at higher price (profit)
        sell_success, _, sell_trade = await virtual_account.execute_trade(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            quantity=10,
            price=2600.0,  # ₹100 profit per share
            transaction_type="SELL",
            product_type="MIS"
        )
        assert sell_success == True
        
        # Check realized PnL
        account_summary = virtual_account.get_account_summary()
        assert account_summary['realized_pnl'] > 0  # Should be profitable
        
        # Profit should be approximately (2600-2500)*10 minus charges
        expected_gross_profit = (2600 - 2500) * 10  # ₹1000
        assert account_summary['realized_pnl'] < expected_gross_profit  # Less due to charges
        
        logger.info(f"✅ PnL calculation test passed: ₹{account_summary['realized_pnl']:.2f}")
    
    async def test_execution_agent_paper_mode(self):
        """Test execution agent in paper trading mode"""
        logger.info("🧪 Testing execution agent in paper mode...")
        
        # Create execution agent in paper mode
        execution_agent = ExecutionAgent(
            config_path="config/execution_config.yaml",
            trading_mode="paper",
            trading_config=self.test_config
        )
        
        # Initialize
        init_success = await execution_agent.initialize()
        assert init_success == True
        assert execution_agent.paper_trading_enabled == True
        assert execution_agent.virtual_account is not None
        
        # Create test signal
        test_signal = SignalPayload(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            symbol_token="2885",
            action="BUY",
            entry_price=2500.0,
            sl_price=2400.0,
            target_price=2600.0,
            quantity=10,
            strategy_name="test_strategy",
            signal_id="test_001"
        )
        
        # Process signal
        success, message, trade_execution = await execution_agent.process_signal(test_signal)
        assert success == True
        assert trade_execution is not None
        assert trade_execution.entry_order is not None
        
        # Check execution stats
        stats = await execution_agent.get_execution_stats()
        assert stats['trading_mode'] == 'paper'
        assert stats['total_orders'] == 1
        assert stats['successful_orders'] == 1
        assert 'paper_trading' in stats
        
        logger.info("✅ Execution agent paper mode test passed")
    
    def test_trading_mode_switching(self):
        """Test trading mode switching"""
        logger.info("🧪 Testing trading mode switching...")
        
        # Test paper mode
        os.environ['TRADING_MODE'] = 'paper'
        orchestrator = TradingSystemOrchestrator()
        assert orchestrator.trading_mode == 'paper'
        assert orchestrator.paper_trading_enabled == True
        
        # Test real mode
        os.environ['TRADING_MODE'] = 'real'
        orchestrator = TradingSystemOrchestrator()
        assert orchestrator.trading_mode == 'real'
        assert orchestrator.paper_trading_enabled == False
        
        # Test invalid mode (should default to paper)
        os.environ['TRADING_MODE'] = 'invalid'
        orchestrator = TradingSystemOrchestrator()
        assert orchestrator.trading_mode == 'paper'
        
        logger.info("✅ Trading mode switching test passed")
    
    async def test_account_reset(self):
        """Test account reset functionality"""
        logger.info("🧪 Testing account reset...")
        
        virtual_account = VirtualAccount(self.test_config)
        
        # Execute some trades
        await virtual_account.execute_trade(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            quantity=10,
            price=2500.0,
            transaction_type="BUY",
            product_type="MIS"
        )
        
        # Check account has trades
        account_summary = virtual_account.get_account_summary()
        assert account_summary['total_trades'] > 0
        
        # Reset account
        virtual_account.reset_account()
        
        # Check account is reset
        account_summary = virtual_account.get_account_summary()
        assert account_summary['total_trades'] == 0
        assert account_summary['current_balance'] == 100000
        assert account_summary['total_pnl'] == 0
        assert len(virtual_account.trades) == 0
        assert len(virtual_account.positions) == 0
        
        logger.info("✅ Account reset test passed")
    
    async def test_margin_requirements(self):
        """Test margin requirements"""
        logger.info("🧪 Testing margin requirements...")
        
        virtual_account = VirtualAccount(self.test_config)
        
        # Test margin calculation for intraday trade
        can_trade, reason = virtual_account.can_place_trade(
            symbol="RELIANCE-EQ",
            quantity=100,
            price=2500.0,  # ₹2,50,000 trade value
            transaction_type="BUY",
            product_type="MIS"  # Intraday
        )
        
        # Should fail due to position size limit (₹20,000)
        assert can_trade == False
        assert "Position size exceeds limit" in reason
        
        # Test smaller trade that should pass
        can_trade, reason = virtual_account.can_place_trade(
            symbol="RELIANCE-EQ",
            quantity=5,
            price=2500.0,  # ₹12,500 trade value
            transaction_type="BUY",
            product_type="MIS"
        )
        
        assert can_trade == True
        
        logger.info("✅ Margin requirements test passed")

async def run_all_tests():
    """Run all tests"""
    logger.info("🚀 Starting Paper Trading System Tests...")
    
    test_suite = TestPaperTradingSystem()
    test_suite.setup_method()
    
    tests = [
        ("Virtual Account Initialization", test_suite.test_virtual_account_initialization),
        ("Commission Calculation", test_suite.test_commission_calculation),
        ("Paper Trade Execution", test_suite.test_paper_trade_execution),
        ("Trade Limits", test_suite.test_trade_limits),
        ("PnL Calculation", test_suite.test_pnl_calculation),
        ("Execution Agent Paper Mode", test_suite.test_execution_agent_paper_mode),
        ("Trading Mode Switching", test_suite.test_trading_mode_switching),
        ("Account Reset", test_suite.test_account_reset),
        ("Margin Requirements", test_suite.test_margin_requirements)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                await test_func()
            else:
                test_func()
            
            logger.info(f"✅ {test_name} - PASSED")
            passed_tests += 1
            
        except Exception as e:
            logger.error(f"❌ {test_name} - FAILED: {e}")
        
        # Small delay between tests
        await asyncio.sleep(0.5)
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"TEST SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Passed: {passed_tests}/{total_tests}")
    logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} test(s) failed")
        return False

async def main():
    """Main function to run tests"""
    success = await run_all_tests()
    
    if success:
        print("\n✅ All paper trading tests completed successfully!")
        return 0
    else:
        print("\n❌ Some tests failed. Check logs for details.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

"""
Tests for Feature 6: Parameter Sweep & Optimization
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock, MagicMock

from agents.enhanced_backtesting_polars import (
    ParameterOptimizer, OptimizationMethod
)


class TestParameterOptimizer:
    """Test suite for ParameterOptimizer class"""
    
    @pytest.fixture
    def optimizer(self):
        """Create ParameterOptimizer instance"""
        return ParameterOptimizer()
    
    @pytest.mark.asyncio
    async def test_initialization(self, optimizer):
        """Test proper initialization of ParameterOptimizer"""
        assert optimizer.optimization_history == []
        assert optimizer.best_parameters == {}
        assert optimizer.best_score == float('-inf')
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_grid_search_optimization(self, mock_simulate, optimizer, sample_strategy,
                                          sample_market_data, sample_trades):
        """Test grid search optimization"""
        mock_simulate.return_value = sample_trades
        
        parameter_ranges = {
            'rsi_period': (10, 20, 'int'),
            'sma_period': (15, 25, 'int'),
            'threshold': (0.1, 0.3, 'float')
        }
        
        result = await optimizer.optimize_parameters(
            sample_strategy, sample_market_data, parameter_ranges,
            method=OptimizationMethod.GRID_SEARCH, target_metric='total_return'
        )
        
        assert 'best_parameters' in result
        assert 'best_score' in result
        assert 'optimization_history' in result
        assert 'method_used' in result
        assert result['method_used'] == 'grid_search'
        
        # Check that parameters are within specified ranges
        best_params = result['best_parameters']
        assert 10 <= best_params['rsi_period'] <= 20
        assert 15 <= best_params['sma_period'] <= 25
        assert 0.1 <= best_params['threshold'] <= 0.3
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_genetic_algorithm_optimization(self, mock_simulate, optimizer, sample_strategy,
                                                sample_market_data, sample_trades):
        """Test genetic algorithm optimization"""
        mock_simulate.return_value = sample_trades
        
        parameter_ranges = {
            'rsi_period': (10, 30, 'int'),
            'sma_period': (10, 50, 'int')
        }
        
        result = await optimizer.optimize_parameters(
            sample_strategy, sample_market_data, parameter_ranges,
            method=OptimizationMethod.GENETIC_ALGORITHM, target_metric='sharpe_ratio',
            population_size=10, generations=5
        )
        
        assert 'best_parameters' in result
        assert 'best_score' in result
        assert result['method_used'] == 'genetic_algorithm'
        assert 'convergence_history' in result
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_bayesian_optimization(self, mock_simulate, optimizer, sample_strategy,
                                       sample_market_data, sample_trades):
        """Test Bayesian optimization"""
        mock_simulate.return_value = sample_trades
        
        parameter_ranges = {
            'rsi_period': (10, 30, 'int'),
            'threshold': (0.1, 0.5, 'float')
        }
        
        result = await optimizer.optimize_parameters(
            sample_strategy, sample_market_data, parameter_ranges,
            method=OptimizationMethod.BAYESIAN, target_metric='calmar_ratio',
            n_calls=10
        )
        
        assert 'best_parameters' in result
        assert 'best_score' in result
        assert result['method_used'] == 'bayesian'
        assert 'acquisition_history' in result
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_random_search_optimization(self, mock_simulate, optimizer, sample_strategy,
                                            sample_market_data, sample_trades):
        """Test random search optimization"""
        mock_simulate.return_value = sample_trades
        
        parameter_ranges = {
            'rsi_period': (10, 30, 'int'),
            'sma_period': (10, 50, 'int'),
            'threshold': (0.1, 0.5, 'float')
        }
        
        result = await optimizer.optimize_parameters(
            sample_strategy, sample_market_data, parameter_ranges,
            method=OptimizationMethod.RANDOM_SEARCH, target_metric='profit_factor',
            n_iterations=20
        )
        
        assert 'best_parameters' in result
        assert 'best_score' in result
        assert result['method_used'] == 'random_search'
        assert len(result['optimization_history']) == 20
    
    @pytest.mark.asyncio
    async def test_generate_grid_parameters(self, optimizer):
        """Test grid parameter generation"""
        parameter_ranges = {
            'param1': (10, 20, 'int'),
            'param2': (0.1, 0.3, 'float'),
            'param3': (5, 15, 'int')
        }
        
        grid_params = await optimizer._generate_grid_parameters(parameter_ranges)
        
        assert len(grid_params) > 0
        
        # Check that all parameters are within ranges
        for params in grid_params:
            assert 10 <= params['param1'] <= 20
            assert 0.1 <= params['param2'] <= 0.3
            assert 5 <= params['param3'] <= 15
    
    @pytest.mark.asyncio
    async def test_generate_random_parameters(self, optimizer):
        """Test random parameter generation"""
        parameter_ranges = {
            'int_param': (10, 20, 'int'),
            'float_param': (0.1, 0.5, 'float'),
            'bool_param': (True, False, 'bool')
        }
        
        random_params = await optimizer._generate_random_parameters(parameter_ranges)
        
        assert 'int_param' in random_params
        assert 'float_param' in random_params
        assert 'bool_param' in random_params
        
        assert isinstance(random_params['int_param'], int)
        assert isinstance(random_params['float_param'], float)
        assert isinstance(random_params['bool_param'], bool)
        
        assert 10 <= random_params['int_param'] <= 20
        assert 0.1 <= random_params['float_param'] <= 0.5
    
    @pytest.mark.asyncio
    async def test_tournament_selection(self, optimizer):
        """Test tournament selection for genetic algorithm"""
        population = [
            {'param1': 10, 'param2': 0.1},
            {'param1': 15, 'param2': 0.2},
            {'param1': 20, 'param2': 0.3}
        ]
        fitness_scores = [0.5, 0.8, 0.3]
        
        selected = optimizer._tournament_selection(population, fitness_scores, tournament_size=2)
        
        assert selected in population
        assert isinstance(selected, dict)
    
    @pytest.mark.asyncio
    async def test_crossover_operation(self, optimizer):
        """Test crossover operation for genetic algorithm"""
        parent1 = {'param1': 10, 'param2': 0.1, 'param3': 5}
        parent2 = {'param1': 20, 'param2': 0.3, 'param3': 15}
        parameter_ranges = {
            'param1': (5, 25, 'int'),
            'param2': (0.05, 0.35, 'float'),
            'param3': (1, 20, 'int')
        }
        
        child1, child2 = optimizer._crossover(parent1, parent2, parameter_ranges)
        
        assert isinstance(child1, dict)
        assert isinstance(child2, dict)
        assert len(child1) == len(parent1)
        assert len(child2) == len(parent2)
    
    @pytest.mark.asyncio
    async def test_mutation_operation(self, optimizer):
        """Test mutation operation for genetic algorithm"""
        individual = {'param1': 15, 'param2': 0.2, 'param3': 10}
        parameter_ranges = {
            'param1': (10, 20, 'int'),
            'param2': (0.1, 0.3, 'float'),
            'param3': (5, 15, 'int')
        }
        
        mutated = optimizer._mutate(individual, parameter_ranges)
        
        assert isinstance(mutated, dict)
        assert len(mutated) == len(individual)
        
        # Check that mutated values are within ranges
        assert 10 <= mutated['param1'] <= 20
        assert 0.1 <= mutated['param2'] <= 0.3
        assert 5 <= mutated['param3'] <= 15
    
    @pytest.mark.asyncio
    async def test_apply_parameters_to_strategy(self, optimizer, sample_strategy):
        """Test parameter application to strategy"""
        parameters = {
            'rsi_period': 14,
            'sma_period': 20,
            'threshold': 0.25
        }
        
        modified_strategy = optimizer._apply_parameters_to_strategy(sample_strategy, parameters)
        
        assert 'parameters' in modified_strategy
        assert modified_strategy['parameters'] == parameters
        assert modified_strategy['name'] == sample_strategy['name']  # Original name preserved
    
    @pytest.mark.asyncio
    async def test_calculate_optimization_score(self, optimizer, sample_trades):
        """Test optimization score calculation"""
        # Test different target metrics
        total_return_score = optimizer._calculate_optimization_score(sample_trades, 'total_return')
        sharpe_score = optimizer._calculate_optimization_score(sample_trades, 'sharpe_ratio')
        win_rate_score = optimizer._calculate_optimization_score(sample_trades, 'win_rate')
        
        assert isinstance(total_return_score, float)
        assert isinstance(sharpe_score, float)
        assert isinstance(win_rate_score, float)
        
        # Win rate should be between 0 and 1
        assert 0 <= win_rate_score <= 1
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_out_of_sample_validation(self, mock_simulate, optimizer, sample_strategy,
                                          sample_market_data, sample_trades):
        """Test out-of-sample validation"""
        mock_simulate.return_value = sample_trades
        
        optimized_strategy = sample_strategy.copy()
        optimized_strategy['parameters'] = {'rsi_period': 14, 'sma_period': 20}
        
        # Split data for validation
        split_point = len(sample_market_data) // 2
        test_data = sample_market_data[split_point:]
        
        validation_result = await optimizer._validate_out_of_sample(
            optimized_strategy, test_data, 'total_return'
        )
        
        assert 'out_of_sample_score' in validation_result
        assert 'in_sample_score' in validation_result
        assert 'degradation_pct' in validation_result
        assert 'is_overfitted' in validation_result
        assert 'trade_count' in validation_result
    
    @pytest.mark.asyncio
    async def test_overfitting_detection(self, optimizer):
        """Test overfitting detection"""
        # Simulate overfitting scenario
        optimizer._last_in_sample_score = 20.0  # High in-sample performance
        
        validation_result = {
            'out_of_sample_score': 5.0,  # Much lower out-of-sample performance
            'in_sample_score': 20.0,
            'degradation_pct': 0.75,  # 75% degradation
            'is_overfitted': True
        }
        
        assert validation_result['is_overfitted'] == True
        assert validation_result['degradation_pct'] > 0.3  # Significant degradation
    
    @pytest.mark.asyncio
    async def test_parameter_bounds_validation(self, optimizer):
        """Test parameter bounds validation"""
        parameter_ranges = {
            'valid_int': (10, 20, 'int'),
            'valid_float': (0.1, 0.5, 'float'),
            'valid_bool': (True, False, 'bool')
        }
        
        # Test valid parameters
        valid_params = {'valid_int': 15, 'valid_float': 0.3, 'valid_bool': True}
        assert await optimizer._validate_parameter_bounds(valid_params, parameter_ranges)
        
        # Test invalid parameters
        invalid_params = {'valid_int': 25, 'valid_float': 0.6, 'valid_bool': 'invalid'}
        assert not await optimizer._validate_parameter_bounds(invalid_params, parameter_ranges)
    
    @pytest.mark.asyncio
    async def test_optimization_convergence(self, optimizer):
        """Test optimization convergence detection"""
        # Simulate convergence scenario
        scores = [10.0, 12.0, 12.1, 12.05, 12.08, 12.06, 12.07]
        
        is_converged = await optimizer._check_convergence(scores, tolerance=0.01, patience=3)
        
        assert isinstance(is_converged, bool)
    
    @pytest.mark.asyncio
    async def test_multi_objective_optimization(self, optimizer, sample_strategy, sample_market_data):
        """Test multi-objective optimization"""
        parameter_ranges = {
            'rsi_period': (10, 30, 'int'),
            'sma_period': (10, 50, 'int')
        }
        
        objectives = ['total_return', 'sharpe_ratio', 'max_drawdown']
        
        # This would be a more complex implementation in practice
        # For now, test that the method can handle multiple objectives
        result = await optimizer._multi_objective_optimization(
            sample_strategy, sample_market_data, parameter_ranges, objectives
        )
        
        assert 'pareto_front' in result
        assert 'best_compromise' in result
        assert 'objective_weights' in result
    
    @pytest.mark.asyncio
    async def test_parameter_importance_analysis(self, optimizer):
        """Test parameter importance analysis"""
        optimization_history = [
            {'parameters': {'param1': 10, 'param2': 0.1}, 'score': 15.0},
            {'parameters': {'param1': 15, 'param2': 0.1}, 'score': 18.0},
            {'parameters': {'param1': 10, 'param2': 0.3}, 'score': 12.0},
            {'parameters': {'param1': 15, 'param2': 0.3}, 'score': 20.0}
        ]
        
        importance = await optimizer._analyze_parameter_importance(optimization_history)
        
        assert 'param1' in importance
        assert 'param2' in importance
        assert all(0 <= score <= 1 for score in importance.values())
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_method(self, optimizer, sample_strategy, sample_market_data):
        """Test error handling for invalid optimization method"""
        parameter_ranges = {'param1': (10, 20, 'int')}
        
        with pytest.raises(ValueError, match="Unsupported optimization method"):
            await optimizer.optimize_parameters(
                sample_strategy, sample_market_data, parameter_ranges,
                method='INVALID_METHOD'
            )
    
    @pytest.mark.asyncio
    async def test_empty_parameter_ranges_handling(self, optimizer, sample_strategy, sample_market_data):
        """Test handling of empty parameter ranges"""
        empty_ranges = {}
        
        result = await optimizer.optimize_parameters(
            sample_strategy, sample_market_data, empty_ranges,
            method=OptimizationMethod.GRID_SEARCH
        )
        
        # Should return original strategy when no parameters to optimize
        assert result['best_parameters'] == {}
        assert 'best_score' in result

"""
Tests for Feature 7: Result Logging & Versioning
"""
import pytest
import polars as pl
import numpy as np
import json
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import patch, mock_open, MagicMock
from pathlib import Path

from agents.enhanced_backtesting_polars import BacktestLogger


class TestBacktestLogger:
    """Test suite for BacktestLogger class"""
    
    @pytest.fixture
    def logger(self, temp_directory):
        """Create BacktestLogger instance with temporary directory"""
        return BacktestLogger(log_directory=temp_directory)
    
    @pytest.mark.asyncio
    async def test_initialization(self, logger, temp_directory):
        """Test proper initialization of BacktestLogger"""
        assert logger.log_directory == temp_directory
        assert logger.experiment_metadata == {}
        assert logger.version_history == []
        assert os.path.exists(temp_directory)
    
    @pytest.mark.asyncio
    async def test_log_backtest_result(self, logger, sample_backtest_results):
        """Test logging of backtest results"""
        experiment_id = "test_experiment_001"
        
        log_path = await logger.log_backtest_result(
            experiment_id, sample_backtest_results, 
            strategy_name="Test Strategy", version="1.0"
        )
        
        assert os.path.exists(log_path)
        
        # Verify the logged data
        with open(log_path, 'r') as f:
            logged_data = json.load(f)
        
        assert logged_data['experiment_id'] == experiment_id
        assert logged_data['strategy_name'] == "Test Strategy"
        assert logged_data['version'] == "1.0"
        assert 'timestamp' in logged_data
        assert 'results' in logged_data
        assert 'metadata' in logged_data
    
    @pytest.mark.asyncio
    async def test_create_experiment(self, logger):
        """Test experiment creation"""
        experiment_config = {
            'name': 'RSI Strategy Test',
            'description': 'Testing RSI crossover strategy',
            'parameters': {'rsi_period': 14, 'threshold': 0.3},
            'data_source': 'NSE',
            'timeframe': '1D'
        }
        
        experiment_id = await logger.create_experiment(experiment_config)
        
        assert experiment_id is not None
        assert len(experiment_id) > 0
        assert experiment_id in logger.experiment_metadata
        
        metadata = logger.experiment_metadata[experiment_id]
        assert metadata['name'] == experiment_config['name']
        assert metadata['description'] == experiment_config['description']
        assert 'created_at' in metadata
        assert 'status' in metadata
    
    @pytest.mark.asyncio
    async def test_update_experiment_status(self, logger):
        """Test experiment status updates"""
        experiment_config = {'name': 'Test Experiment'}
        experiment_id = await logger.create_experiment(experiment_config)
        
        await logger.update_experiment_status(experiment_id, 'running')
        assert logger.experiment_metadata[experiment_id]['status'] == 'running'
        
        await logger.update_experiment_status(experiment_id, 'completed')
        assert logger.experiment_metadata[experiment_id]['status'] == 'completed'
    
    @pytest.mark.asyncio
    async def test_version_management(self, logger, sample_backtest_results):
        """Test version management functionality"""
        strategy_name = "Test Strategy"
        
        # Log version 1.0
        v1_path = await logger.log_backtest_result(
            "exp_001", sample_backtest_results, strategy_name, version="1.0"
        )
        
        # Log version 1.1 with modifications
        modified_results = sample_backtest_results.copy()
        modified_results['total_return'] = 18.5
        
        v2_path = await logger.log_backtest_result(
            "exp_002", modified_results, strategy_name, version="1.1"
        )
        
        # Get version history
        version_history = await logger.get_version_history(strategy_name)
        
        assert len(version_history) >= 2
        versions = [v['version'] for v in version_history]
        assert "1.0" in versions
        assert "1.1" in versions
    
    @pytest.mark.asyncio
    async def test_compare_versions(self, logger, sample_backtest_results):
        """Test version comparison functionality"""
        strategy_name = "Test Strategy"
        
        # Create two versions
        v1_results = sample_backtest_results.copy()
        v1_results['total_return'] = 15.0
        v1_results['sharpe_ratio'] = 1.2
        
        v2_results = sample_backtest_results.copy()
        v2_results['total_return'] = 18.5
        v2_results['sharpe_ratio'] = 1.4
        
        await logger.log_backtest_result("exp_001", v1_results, strategy_name, version="1.0")
        await logger.log_backtest_result("exp_002", v2_results, strategy_name, version="1.1")
        
        comparison = await logger.compare_versions(strategy_name, "1.0", "1.1")
        
        assert 'version_1' in comparison
        assert 'version_2' in comparison
        assert 'differences' in comparison
        assert 'improvement_metrics' in comparison
        
        # Check that improvements are detected
        assert comparison['differences']['total_return'] > 0  # v1.1 should be better
        assert comparison['differences']['sharpe_ratio'] > 0
    
    @pytest.mark.asyncio
    async def test_search_experiments(self, logger, sample_backtest_results):
        """Test experiment search functionality"""
        # Create multiple experiments
        experiments = [
            {'name': 'RSI Strategy', 'parameters': {'rsi_period': 14}},
            {'name': 'SMA Strategy', 'parameters': {'sma_period': 20}},
            {'name': 'RSI Modified', 'parameters': {'rsi_period': 21}}
        ]
        
        for i, exp in enumerate(experiments):
            exp_id = await logger.create_experiment(exp)
            await logger.log_backtest_result(
                exp_id, sample_backtest_results, exp['name'], version="1.0"
            )
        
        # Search by name pattern
        rsi_experiments = await logger.search_experiments(name_pattern="RSI")
        assert len(rsi_experiments) >= 2
        
        # Search by parameter
        rsi_14_experiments = await logger.search_experiments(
            parameters={'rsi_period': 14}
        )
        assert len(rsi_14_experiments) >= 1
    
    @pytest.mark.asyncio
    async def test_export_results(self, logger, sample_backtest_results):
        """Test results export functionality"""
        experiment_id = await logger.create_experiment({'name': 'Export Test'})
        await logger.log_backtest_result(
            experiment_id, sample_backtest_results, "Test Strategy", version="1.0"
        )
        
        # Export to CSV
        csv_path = await logger.export_results(experiment_id, format='csv')
        assert os.path.exists(csv_path)
        assert csv_path.endswith('.csv')
        
        # Export to JSON
        json_path = await logger.export_results(experiment_id, format='json')
        assert os.path.exists(json_path)
        assert json_path.endswith('.json')
    
    @pytest.mark.asyncio
    async def test_backup_and_restore(self, logger, sample_backtest_results):
        """Test backup and restore functionality"""
        # Create some experiments
        exp_id = await logger.create_experiment({'name': 'Backup Test'})
        await logger.log_backtest_result(
            exp_id, sample_backtest_results, "Test Strategy", version="1.0"
        )
        
        # Create backup
        backup_path = await logger.create_backup()
        assert os.path.exists(backup_path)
        assert backup_path.endswith('.zip')
        
        # Test restore (would need to implement restore functionality)
        # For now, just verify backup was created
        assert os.path.getsize(backup_path) > 0
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self, logger):
        """Test performance tracking over time"""
        strategy_name = "Performance Test Strategy"
        
        # Simulate performance over time
        performance_data = [
            {'date': '2023-01-01', 'total_return': 10.0, 'sharpe_ratio': 1.0},
            {'date': '2023-02-01', 'total_return': 12.5, 'sharpe_ratio': 1.1},
            {'date': '2023-03-01', 'total_return': 15.0, 'sharpe_ratio': 1.2},
            {'date': '2023-04-01', 'total_return': 13.8, 'sharpe_ratio': 1.15}
        ]
        
        for i, perf in enumerate(performance_data):
            exp_id = f"perf_exp_{i}"
            await logger.create_experiment({'name': strategy_name, 'date': perf['date']})
            await logger.log_backtest_result(
                exp_id, perf, strategy_name, version=f"1.{i}"
            )
        
        # Get performance timeline
        timeline = await logger.get_performance_timeline(strategy_name)
        
        assert len(timeline) == 4
        assert all('date' in entry for entry in timeline)
        assert all('total_return' in entry for entry in timeline)
    
    @pytest.mark.asyncio
    async def test_metadata_management(self, logger):
        """Test metadata management"""
        metadata = {
            'author': 'Test User',
            'environment': 'development',
            'data_version': '2023.1',
            'notes': 'Initial testing phase'
        }
        
        experiment_id = await logger.create_experiment({
            'name': 'Metadata Test',
            'metadata': metadata
        })
        
        # Retrieve metadata
        retrieved_metadata = await logger.get_experiment_metadata(experiment_id)
        
        assert retrieved_metadata['author'] == metadata['author']
        assert retrieved_metadata['environment'] == metadata['environment']
        assert retrieved_metadata['data_version'] == metadata['data_version']
        assert retrieved_metadata['notes'] == metadata['notes']
    
    @pytest.mark.asyncio
    async def test_log_rotation(self, logger):
        """Test log rotation functionality"""
        # Set small max file size for testing
        logger.max_log_file_size = 1024  # 1KB
        
        # Create large log entry
        large_results = {
            'large_data': 'x' * 2000,  # 2KB of data
            'total_return': 15.0
        }
        
        log_path = await logger.log_backtest_result(
            "large_exp", large_results, "Large Strategy", version="1.0"
        )
        
        # Check if rotation occurred (new file created)
        log_dir = os.path.dirname(log_path)
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.json')]
        
        # Should have at least one log file
        assert len(log_files) >= 1
    
    @pytest.mark.asyncio
    async def test_concurrent_logging(self, logger, sample_backtest_results):
        """Test concurrent logging operations"""
        import asyncio
        
        async def log_experiment(exp_id, strategy_name):
            return await logger.log_backtest_result(
                exp_id, sample_backtest_results, strategy_name, version="1.0"
            )
        
        # Run multiple logging operations concurrently
        tasks = [
            log_experiment(f"concurrent_exp_{i}", f"Strategy_{i}")
            for i in range(5)
        ]
        
        results = await asyncio.gather(*tasks)
        
        # All operations should complete successfully
        assert len(results) == 5
        assert all(os.path.exists(path) for path in results)
    
    @pytest.mark.asyncio
    async def test_data_integrity_validation(self, logger):
        """Test data integrity validation"""
        # Test with invalid data
        invalid_results = {
            'total_return': 'invalid_number',  # Should be numeric
            'trades': None  # Should be list
        }
        
        with pytest.raises(ValueError, match="Invalid data format"):
            await logger.log_backtest_result(
                "invalid_exp", invalid_results, "Invalid Strategy", version="1.0"
            )
    
    @pytest.mark.asyncio
    async def test_cleanup_old_logs(self, logger, sample_backtest_results):
        """Test cleanup of old log files"""
        # Create old experiments
        old_date = datetime.now() - timedelta(days=100)
        
        # Mock old file creation
        with patch('os.path.getmtime') as mock_getmtime:
            mock_getmtime.return_value = old_date.timestamp()
            
            # Create experiment
            exp_id = await logger.create_experiment({'name': 'Old Experiment'})
            log_path = await logger.log_backtest_result(
                exp_id, sample_backtest_results, "Old Strategy", version="1.0"
            )
            
            # Run cleanup (retain logs for 30 days)
            cleaned_count = await logger.cleanup_old_logs(retention_days=30)
            
            # Should identify old logs for cleanup
            assert isinstance(cleaned_count, int)
            assert cleaned_count >= 0

{"timestamp": "2025-08-14T11:25:37.689592", "test_summary": {"total_tests": 17, "passed_tests": 3, "failed_tests": 14, "success_rate": 0.17647058823529413, "production_ready": false}, "component_tests": {"risk_manager": {"status": "FAILED", "error": "Can't instantiate abstract class ProductionRiskManager without an implementation for abstract methods 'initialize', 'stop'", "tests_run": 4, "tests_passed": 0}, "execution_agent": {"status": "FAILED", "error": "Can't instantiate abstract class ProductionExecutionAgent without an implementation for abstract methods 'initialize', 'stop'", "tests_run": 3, "tests_passed": 0}, "position_sizing": {"status": "FAILED", "error": "Position size should be positive", "tests_run": 3, "tests_passed": 0}, "circuit_breakers": {"status": "PASSED", "tests_run": 3, "tests_passed": 3, "details": {"initialization": "PASSED", "kill_switch": "PASSED", "status_reporting": "PASSED"}}, "health_monitor": {"status": "FAILED", "error": "Can't instantiate abstract class AgentHealthMonitor without an implementation for abstract methods 'initialize', 'stop'", "tests_run": 2, "tests_passed": 0}}, "integration_tests": {"error": "'ProductionSystemTests' object has no attribute '_test_trade_flow_integration'"}, "performance_tests": {"error": "'ProductionSystemTests' object has no attribute '_test_execution_latency'"}, "risk_tests": {"error": "'ProductionSystemTests' object has no attribute '_test_risk_scenario'"}, "recovery_tests": {"status": "FAILED", "error": "Recovery should initiate successfully", "tests_run": 2, "tests_passed": 0}, "overall_status": "FAILED"}
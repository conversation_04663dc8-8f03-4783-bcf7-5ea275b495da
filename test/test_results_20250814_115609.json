{"timestamp": "2025-08-14T11:56:09.470395", "test_summary": {"total_tests": 27, "passed_tests": 27, "failed_tests": 0, "success_rate": 1.0, "production_ready": true}, "component_tests": {"risk_manager": {"status": "PASSED", "tests_run": 4, "tests_passed": 4, "details": {"initialization": "PASSED", "position_sizing": "PASSED", "risk_calculation": "PASSED", "risk_reward_ratio": "PASSED"}}, "execution_agent": {"status": "PASSED", "tests_run": 3, "tests_passed": 3, "execution_time_ms": 101.99761390686035, "details": {"initialization": "PASSED", "order_creation": "PASSED", "paper_execution": "PASSED"}}, "position_sizing": {"status": "PASSED", "tests_run": 3, "tests_passed": 3, "details": {"fixed_sizing": "PASSED", "kelly_sizing": "PASSED", "volatility_sizing": "PASSED"}}, "circuit_breakers": {"status": "PASSED", "tests_run": 3, "tests_passed": 3, "details": {"initialization": "PASSED", "kill_switch": "PASSED", "status_reporting": "PASSED"}}, "health_monitor": {"status": "PASSED", "tests_run": 2, "tests_passed": 2, "details": {"initialization": "PASSED", "health_summary": "PASSED"}}}, "integration_tests": {"trade_flow": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "details": {"trade_flow": "PASSED"}}, "risk_integration": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "details": {"risk_integration": "PASSED"}}, "monitoring_integration": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "details": {"monitoring_integration": "PASSED"}}}, "performance_tests": {"latency": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "latency_ms": 14.996528625488281, "details": {"execution_latency": "PASSED"}}, "throughput": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "throughput_ops_per_sec": 1000, "details": {"system_throughput": "PASSED"}}, "memory": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "memory_usage_percent": 61.7, "details": {"memory_usage": "PASSED"}}}, "risk_tests": {"risk_scenario_1": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "scenario": {"drawdown": 0.05, "expected_action": "continue"}, "actual_action": "continue", "expected_action": "continue", "details": {"risk_scenario_0.05": "PASSED"}}, "risk_scenario_2": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "scenario": {"drawdown": 0.1, "expected_action": "reduce_risk"}, "actual_action": "reduce_risk", "expected_action": "reduce_risk", "details": {"risk_scenario_0.1": "PASSED"}}, "risk_scenario_3": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "scenario": {"drawdown": 0.15, "expected_action": "stop_trading"}, "actual_action": "stop_trading", "expected_action": "stop_trading", "details": {"risk_scenario_0.15": "PASSED"}}, "risk_scenario_4": {"status": "PASSED", "tests_run": 1, "tests_passed": 1, "scenario": {"drawdown": 0.2, "expected_action": "emergency_stop"}, "actual_action": "emergency_stop", "expected_action": "emergency_stop", "details": {"risk_scenario_0.2": "PASSED"}}}, "recovery_tests": {"status": "PASSED", "tests_run": 2, "tests_passed": 2, "details": {"recovery_initiation": "PASSED", "status_reporting": "PASSED"}}, "overall_status": "PASSED"}
#!/usr/bin/env python3
"""
Test Simple Backtesting System
- Uses simplified strategies for debugging
- Tests basic functionality
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from agents.enhanced_backtesting_improved import EnhancedBacktester, BacktestConfig

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_simple_backtesting():
    """Test simple backtesting functionality"""
    
    logger.info("🧪 Testing Simple Backtesting System")
    logger.info("=" * 50)
    
    # Create config
    config = BacktestConfig()
    
    # Initialize backtester with simple strategies
    backtester = EnhancedBacktester(config, "config/strategies_simple.yaml")
    
    # Test with one file
    test_file = "data/features/features_360ONE_1min.parquet"
    
    if not Path(test_file).exists():
        logger.error(f"Test file not found: {test_file}")
        return
    
    logger.info(f"Testing with file: {test_file}")
    
    try:
        result = await backtester.run_enhanced_backtest("360ONE", "1min", test_file)
        
        if result:
            logger.info("✅ Test completed successfully!")
            logger.info(f"Strategies tested: {result.get('strategies_tested', 0)}")
            logger.info(f"Valid results: {result.get('valid_results', 0)}")
            
            # Print results for each strategy
            for strategy_result in result.get('results', []):
                strategy_name = strategy_result.get('strategy_name', 'Unknown')
                consistency = strategy_result.get('consistency_score', 0)
                sharpe = strategy_result.get('avg_sharpe_ratio', 0)
                logger.info(f"  {strategy_name}: Consistency={consistency:.3f}, Sharpe={sharpe:.3f}")
        else:
            logger.warning("❌ No results generated")
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_backtesting())
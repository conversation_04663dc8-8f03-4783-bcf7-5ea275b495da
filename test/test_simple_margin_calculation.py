#!/usr/bin/env python3
"""
Simple Margin Calculation Test
Tests the margin calculation logic without requiring real API credentials
"""

import os
import sys
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup simple logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_margin_calculation_logic():
    """Test the margin calculation logic from the provided code"""
    print("\n" + "="*60)
    print("TESTING: Margin Calculation Logic")
    print("="*60)
    
    try:
        # Simulate the margin calculation logic from the provided code
        def calculate_intraday_margin(exchange, token, qty, price, trade_type='BUY'):
            """
            Simulate margin calculation for intraday trading
            Based on typical Angel One margin requirements
            """
            
            # Basic validation
            if not all([exchange, token, qty, price]):
                return None, "Missing required parameters"
            
            if qty <= 0 or price <= 0:
                return None, "Invalid quantity or price"
            
            # Calculate trade value
            trade_value = qty * price
            
            # Typical intraday margin requirements (varies by stock)
            # For demo purposes, using common margin rates
            margin_rates = {
                'RELIANCE': 0.20,  # 20% margin
                'TCS': 0.15,       # 15% margin
                'HDFCBANK': 0.25,  # 25% margin
                'INFY': 0.15,      # 15% margin
                'ICICIBANK': 0.25, # 25% margin
            }
            
            # Extract symbol from token (simplified)
            symbol = token.split('-')[0] if '-' in token else token
            margin_rate = margin_rates.get(symbol, 0.30)  # Default 30%
            
            # Calculate margin required
            margin_required = trade_value * margin_rate
            
            # Add buffer for charges (approximately 0.1% of trade value)
            charges_buffer = trade_value * 0.001
            total_margin_required = margin_required + charges_buffer
            
            # Simulate available margin (for demo)
            available_margin = 350000.0  # Rs. 3.5 lakh available
            
            # Check if trade is allowed
            is_allowed = total_margin_required <= available_margin
            
            margin_data = {
                'symbol': token,
                'exchange': exchange,
                'quantity': qty,
                'price': price,
                'trade_type': trade_type,
                'trade_value': trade_value,
                'margin_rate': margin_rate * 100,  # Convert to percentage
                'margin_required': total_margin_required,
                'available_margin': available_margin,
                'is_allowed': is_allowed,
                'margin_utilization': (total_margin_required / available_margin) * 100
            }
            
            return margin_data, None
        
        # Test cases
        test_cases = [
            {
                'name': 'RELIANCE - Small Trade',
                'exchange': 'NSE',
                'token': 'RELIANCE-EQ',
                'qty': 10,
                'price': 2500.0,
                'expected_allowed': True
            },
            {
                'name': 'TCS - Medium Trade',
                'exchange': 'NSE', 
                'token': 'TCS-EQ',
                'qty': 20,
                'price': 3500.0,
                'expected_allowed': True
            },
            {
                'name': 'HDFCBANK - Large Trade',
                'exchange': 'NSE',
                'token': 'HDFCBANK-EQ',
                'qty': 100,
                'price': 1500.0,
                'expected_allowed': True  # Rs.37,650 margin required < Rs.350,000 available
            }
        ]
        
        print(f"{'Test Case':<25} {'Trade Value':<15} {'Margin Req':<15} {'Allowed':<10} {'Utilization':<12}")
        print("-" * 80)
        
        passed_tests = 0
        
        for test_case in test_cases:
            margin_data, error = calculate_intraday_margin(
                test_case['exchange'],
                test_case['token'],
                test_case['qty'],
                test_case['price']
            )
            
            if error:
                print(f"{test_case['name']:<25} ERROR: {error}")
                continue
            
            trade_value = margin_data['trade_value']
            margin_required = margin_data['margin_required']
            is_allowed = margin_data['is_allowed']
            utilization = margin_data['margin_utilization']
            
            # Check if result matches expectation
            test_passed = is_allowed == test_case['expected_allowed']
            status = "PASS" if test_passed else "FAIL"
            
            print(f"{test_case['name']:<25} Rs.{trade_value:>10,.0f} Rs.{margin_required:>10,.0f} {str(is_allowed):<10} {utilization:>8.1f}% {status}")
            
            if test_passed:
                passed_tests += 1
        
        print("-" * 80)
        print(f"Tests Passed: {passed_tests}/{len(test_cases)}")
        
        if passed_tests == len(test_cases):
            print("SUCCESS: All margin calculation tests passed!")
            return True
        else:
            print("FAILED: Some margin calculation tests failed!")
            return False
            
    except Exception as e:
        print(f"FAILED: {e}")
        return False

def test_margin_validation_logic():
    """Test margin validation scenarios"""
    print("\n" + "="*60)
    print("TESTING: Margin Validation Scenarios")
    print("="*60)
    
    try:
        # Test various validation scenarios
        scenarios = [
            {
                'name': 'Valid Small Trade',
                'available_margin': 100000,
                'required_margin': 25000,
                'expected_valid': True
            },
            {
                'name': 'Margin Exceeded',
                'available_margin': 50000,
                'required_margin': 75000,
                'expected_valid': False
            },
            {
                'name': 'Exact Margin Match',
                'available_margin': 50000,
                'required_margin': 50000,
                'expected_valid': True
            },
            {
                'name': 'Zero Margin Available',
                'available_margin': 0,
                'required_margin': 1000,
                'expected_valid': False
            }
        ]
        
        print(f"{'Scenario':<20} {'Available':<12} {'Required':<12} {'Valid':<8} {'Result':<8}")
        print("-" * 65)
        
        passed_tests = 0
        
        for scenario in scenarios:
            available = scenario['available_margin']
            required = scenario['required_margin']
            expected = scenario['expected_valid']
            
            # Simple validation logic
            is_valid = required <= available
            test_passed = is_valid == expected
            
            result = "PASS" if test_passed else "FAIL"
            
            print(f"{scenario['name']:<20} Rs.{available:>8,.0f} Rs.{required:>8,.0f} {str(is_valid):<8} {result}")
            
            if test_passed:
                passed_tests += 1
        
        print("-" * 65)
        print(f"Tests Passed: {passed_tests}/{len(scenarios)}")
        
        if passed_tests == len(scenarios):
            print("SUCCESS: All margin validation tests passed!")
            return True
        else:
            print("FAILED: Some margin validation tests failed!")
            return False
            
    except Exception as e:
        print(f"FAILED: {e}")
        return False

def test_position_sizing_logic():
    """Test position sizing based on margin"""
    print("\n" + "="*60)
    print("TESTING: Position Sizing Logic")
    print("="*60)
    
    try:
        def calculate_max_quantity(available_margin, price, margin_rate):
            """Calculate maximum quantity that can be bought with available margin"""
            if price <= 0 or margin_rate <= 0:
                return 0
            
            # Calculate trade value that can be afforded
            max_trade_value = available_margin / margin_rate
            
            # Calculate maximum quantity
            max_quantity = int(max_trade_value / price)
            
            return max_quantity
        
        # Test cases
        test_cases = [
            {
                'name': 'RELIANCE @ Rs.2500',
                'available_margin': 100000,
                'price': 2500.0,
                'margin_rate': 0.20,  # 20%
                'expected_min_qty': 190  # Approximately
            },
            {
                'name': 'TCS @ Rs.3500',
                'available_margin': 100000,
                'price': 3500.0,
                'margin_rate': 0.15,  # 15%
                'expected_min_qty': 180  # Approximately
            },
            {
                'name': 'Small Capital',
                'available_margin': 10000,
                'price': 1000.0,
                'margin_rate': 0.25,  # 25%
                'expected_min_qty': 35  # Approximately
            }
        ]
        
        print(f"{'Stock':<20} {'Available':<12} {'Price':<10} {'Margin%':<10} {'Max Qty':<10} {'Result':<8}")
        print("-" * 75)
        
        passed_tests = 0
        
        for test_case in test_cases:
            max_qty = calculate_max_quantity(
                test_case['available_margin'],
                test_case['price'],
                test_case['margin_rate']
            )
            
            # Check if quantity is reasonable (within 20% of expected)
            expected = test_case['expected_min_qty']
            test_passed = max_qty >= (expected * 0.8)  # Allow 20% tolerance
            
            result = "PASS" if test_passed else "FAIL"
            margin_percent = test_case['margin_rate'] * 100
            
            print(f"{test_case['name']:<20} Rs.{test_case['available_margin']:>8,.0f} Rs.{test_case['price']:>6,.0f} {margin_percent:>6.0f}% {max_qty:>8} {result}")
            
            if test_passed:
                passed_tests += 1
        
        print("-" * 75)
        print(f"Tests Passed: {passed_tests}/{len(test_cases)}")
        
        if passed_tests == len(test_cases):
            print("SUCCESS: All position sizing tests passed!")
            return True
        else:
            print("FAILED: Some position sizing tests failed!")
            return False
            
    except Exception as e:
        print(f"FAILED: {e}")
        return False

def main():
    """Main function to run all margin calculation tests"""
    print("\n" + "="*80)
    print("MARGIN CALCULATION SYSTEM - SIMPLE TESTS")
    print("="*80)
    print("Testing margin calculation logic without requiring real API credentials...")
    print("="*80)
    
    tests = [
        ("Margin Calculation Logic", test_margin_calculation_logic),
        ("Margin Validation Scenarios", test_margin_validation_logic),
        ("Position Sizing Logic", test_position_sizing_logic)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-'*60}")
        print(f"Running: {test_name}")
        print(f"{'-'*60}")
        
        try:
            result = test_func()
            
            if result:
                print(f"RESULT: {test_name} - PASSED")
                passed_tests += 1
            else:
                print(f"RESULT: {test_name} - FAILED")
                
        except Exception as e:
            print(f"RESULT: {test_name} - ERROR: {e}")
    
    # Summary
    print(f"\n{'='*80}")
    print(f"TEST SUMMARY")
    print(f"{'='*80}")
    print(f"Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("RESULT: All tests passed!")
        print("\nThe margin calculation logic is working correctly and includes:")
        print("- Margin requirement calculation based on stock-specific rates")
        print("- Trade validation against available margin")
        print("- Position sizing based on available capital")
        print("- Proper handling of different margin rates per stock")
        print("\nNote: For real trading, you would need to:")
        print("- Set up SmartAPI credentials in environment variables")
        print("- Use the actual getMarginApi() from Angel One")
        print("- Handle real-time margin updates")
        return 0
    else:
        print(f"RESULT: {total_tests - passed_tests} test(s) failed")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
Simple Paper Trading Test
Tests core paper trading functionality without complex dependencies
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set environment for paper trading
os.environ['TRADING_MODE'] = 'paper'
os.environ['PAPER_TRADING_INITIAL_BALANCE'] = '100000'
os.environ['PAPER_TRADING_MAX_TRADES_PER_DAY'] = '5'

# Import modules
try:
    from utils.paper_trading import VirtualAccount
except ImportError as e:
    print(f"Failed to import modules: {e}")
    sys.exit(1)

# Setup simple logging without emojis
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_virtual_account_basic():
    """Test basic virtual account functionality"""
    print("\n" + "="*60)
    print("TESTING: Virtual Account Basic Functionality")
    print("="*60)
    
    # Test configuration
    config = {
        'paper_trading': {
            'initial_balance': 100000,
            'max_trades_per_day': 5,
            'commission_rate': 0.0003,
            'max_position_size': 20000,
            'max_daily_loss': 5000,
            'margin_multiplier': 3.5
        }
    }
    
    try:
        # Initialize virtual account
        virtual_account = VirtualAccount(config)
        print(f"SUCCESS: Virtual account initialized with balance: Rs.{virtual_account.initial_balance:,.2f}")
        
        # Test account summary
        summary = virtual_account.get_account_summary()
        print(f"Current Balance: Rs.{summary.get('current_balance', 0):,.2f}")
        print(f"Available Margin: Rs.{summary.get('available_margin', 0):,.2f}")
        print(f"Total Trades: {summary.get('total_trades', 0)}")
        
        return True
        
    except Exception as e:
        print(f"FAILED: {e}")
        return False

def test_commission_calculation():
    """Test commission calculation"""
    print("\n" + "="*60)
    print("TESTING: Commission Calculation")
    print("="*60)
    
    config = {
        'paper_trading': {
            'initial_balance': 100000,
            'max_trades_per_day': 5,
            'commission_rate': 0.0003,
            'max_position_size': 20000,
            'max_daily_loss': 5000,
            'margin_multiplier': 3.5
        }
    }
    
    try:
        virtual_account = VirtualAccount(config)
        
        # Test commission for a typical trade
        charges = virtual_account.calculate_charges(
            symbol="RELIANCE-EQ",
            quantity=10,
            price=2500.0,
            transaction_type="BUY",
            product_type="MIS"
        )
        
        print(f"Trade Value: Rs.{10 * 2500.0:,.2f}")
        print(f"Brokerage: Rs.{charges.get('brokerage', 0):.2f}")
        print(f"STT: Rs.{charges.get('stt', 0):.2f}")
        print(f"GST: Rs.{charges.get('gst', 0):.2f}")
        print(f"Total Charges: Rs.{charges.get('total_charges', 0):.2f}")
        
        if charges.get('total_charges', 0) > 0:
            print("SUCCESS: Commission calculation working")
            return True
        else:
            print("FAILED: No charges calculated")
            return False
            
    except Exception as e:
        print(f"FAILED: {e}")
        return False

async def test_trade_execution():
    """Test trade execution"""
    print("\n" + "="*60)
    print("TESTING: Trade Execution")
    print("="*60)
    
    config = {
        'paper_trading': {
            'initial_balance': 100000,
            'max_trades_per_day': 5,
            'commission_rate': 0.0003,
            'max_position_size': 20000,
            'max_daily_loss': 5000,
            'margin_multiplier': 3.5
        }
    }
    
    try:
        virtual_account = VirtualAccount(config)
        
        # Execute a buy trade (smaller size to fit within limits)
        success, message, trade = await virtual_account.execute_trade(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            quantity=5,  # Reduced quantity
            price=1500.0,  # Reduced price (Rs.7,500 total < Rs.20,000 limit)
            transaction_type="BUY",
            product_type="MIS",
            strategy_name="test_strategy"
        )
        
        if success and trade:
            print(f"SUCCESS: Trade executed - {trade.trade_id}")
            print(f"Symbol: {trade.symbol}")
            print(f"Quantity: {trade.quantity}")
            print(f"Price: Rs.{trade.price:.2f}")
            print(f"Total Charges: Rs.{trade.total_charges:.2f}")
            print(f"Net Amount: Rs.{trade.net_amount:.2f}")
            
            # Check account summary
            summary = virtual_account.get_account_summary()
            print(f"Updated Balance: Rs.{summary.get('current_balance', 0):,.2f}")
            print(f"Active Positions: {summary.get('active_positions', 0)}")
            
            return True
        else:
            print(f"FAILED: {message}")
            return False
            
    except Exception as e:
        print(f"FAILED: {e}")
        return False

async def test_trade_limits():
    """Test trade limits"""
    print("\n" + "="*60)
    print("TESTING: Trade Limits")
    print("="*60)
    
    config = {
        'paper_trading': {
            'initial_balance': 100000,
            'max_trades_per_day': 5,
            'commission_rate': 0.0003,
            'max_position_size': 20000,
            'max_daily_loss': 5000,
            'margin_multiplier': 3.5
        }
    }
    
    try:
        virtual_account = VirtualAccount(config)
        
        # Test position size limit
        can_trade, reason = virtual_account.can_place_trade(
            symbol="RELIANCE-EQ",
            quantity=100,  # Large quantity
            price=2500.0,
            transaction_type="BUY",
            product_type="MIS"
        )
        
        if not can_trade and "Position size exceeds limit" in reason:
            print("SUCCESS: Position size limit enforced")
            print(f"Reason: {reason}")
            return True
        else:
            print(f"FAILED: Position size limit not enforced - {reason}")
            return False
            
    except Exception as e:
        print(f"FAILED: {e}")
        return False

async def test_pnl_calculation():
    """Test PnL calculation"""
    print("\n" + "="*60)
    print("TESTING: PnL Calculation")
    print("="*60)
    
    config = {
        'paper_trading': {
            'initial_balance': 100000,
            'max_trades_per_day': 5,
            'commission_rate': 0.0003,
            'max_position_size': 20000,
            'max_daily_loss': 5000,
            'margin_multiplier': 3.5
        }
    }
    
    try:
        virtual_account = VirtualAccount(config)
        
        # Buy trade (smaller size to fit within limits)
        buy_success, _, buy_trade = await virtual_account.execute_trade(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            quantity=5,  # Reduced quantity
            price=1500.0,  # Reduced price (Rs.7,500 total < Rs.20,000 limit)
            transaction_type="BUY",
            product_type="MIS"
        )
        
        if not buy_success:
            print("FAILED: Could not execute buy trade")
            return False
        
        print(f"Buy trade executed at Rs.{buy_trade.price:.2f}")
        
        # Sell trade at higher price (profit)
        sell_success, _, sell_trade = await virtual_account.execute_trade(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            quantity=5,  # Same quantity as buy
            price=1600.0,  # Rs.100 profit per share
            transaction_type="SELL",
            product_type="MIS"
        )
        
        if not sell_success:
            print("FAILED: Could not execute sell trade")
            return False
        
        print(f"Sell trade executed at Rs.{sell_trade.price:.2f}")
        
        # Check realized PnL
        summary = virtual_account.get_account_summary()
        realized_pnl = summary.get('realized_pnl', 0)
        
        print(f"Realized P&L: Rs.{realized_pnl:.2f}")
        
        if realized_pnl > 0:
            print("SUCCESS: Profitable trade recorded")
            return True
        else:
            print("FAILED: No profit recorded")
            return False
            
    except Exception as e:
        print(f"FAILED: {e}")
        return False

def test_trading_mode_switching():
    """Test trading mode switching"""
    print("\n" + "="*60)
    print("TESTING: Trading Mode Switching")
    print("="*60)
    
    try:
        # Test paper mode
        os.environ['TRADING_MODE'] = 'paper'
        from main import TradingSystemOrchestrator
        
        orchestrator = TradingSystemOrchestrator()
        if orchestrator.trading_mode == 'paper':
            print("SUCCESS: Paper mode detected")
        else:
            print(f"FAILED: Expected paper mode, got {orchestrator.trading_mode}")
            return False
        
        # Test real mode
        os.environ['TRADING_MODE'] = 'real'
        os.environ['PAPER_TRADING_ENABLED'] = 'false'  # Disable override
        
        # Need to reload the module to pick up new environment
        import importlib
        import main
        importlib.reload(main)
        
        orchestrator2 = main.TradingSystemOrchestrator()
        if orchestrator2.trading_mode == 'real':
            print("SUCCESS: Real mode detected")
            return True
        else:
            print(f"FAILED: Expected real mode, got {orchestrator2.trading_mode}")
            return False
            
    except Exception as e:
        print(f"FAILED: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("\n" + "="*80)
    print("PAPER TRADING SYSTEM - SIMPLE TESTS")
    print("="*80)
    print("Testing core paper trading functionality...")
    print("="*80)
    
    tests = [
        ("Virtual Account Basic", test_virtual_account_basic),
        ("Commission Calculation", test_commission_calculation),
        ("Trade Execution", test_trade_execution),
        ("Trade Limits", test_trade_limits),
        ("PnL Calculation", test_pnl_calculation),
        ("Trading Mode Switching", test_trading_mode_switching)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-'*60}")
        print(f"Running: {test_name}")
        print(f"{'-'*60}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                print(f"RESULT: {test_name} - PASSED")
                passed_tests += 1
            else:
                print(f"RESULT: {test_name} - FAILED")
                
        except Exception as e:
            print(f"RESULT: {test_name} - ERROR: {e}")
        
        # Small delay between tests
        await asyncio.sleep(0.5)
    
    # Summary
    print(f"\n{'='*80}")
    print(f"TEST SUMMARY")
    print(f"{'='*80}")
    print(f"Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("RESULT: All tests passed!")
        return True
    else:
        print(f"RESULT: {total_tests - passed_tests} test(s) failed")
        return False

async def main():
    """Main function"""
    success = await run_all_tests()
    
    if success:
        print("\nSUCCESS: All paper trading tests completed successfully!")
        print("\nThe paper trading system is working correctly and includes:")
        print("- Virtual account management with Rs.100,000 initial balance")
        print("- Realistic commission calculation based on Angel One rates")
        print("- Trade execution simulation with proper validation")
        print("- Risk limits enforcement (position size, daily trades)")
        print("- P&L tracking and position management")
        print("- Trading mode switching between paper and real")
        print("\nYou can now use the paper trading system for:")
        print("- Testing trading strategies risk-free")
        print("- Learning how the system works")
        print("- Validating your trading logic before going live")
        return 0
    else:
        print("\nFAILED: Some tests failed. Check output for details.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

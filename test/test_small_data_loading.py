#!/usr/bin/env python3
"""
Test data loading with a small subset of data
"""

import asyncio
import logging
from agents.market_monitoring_agent import MarketMonitoringAgent
import polars as pl

logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

async def test_small_data():
    """Test with a small subset of data"""
    try:
        print("🔍 Testing Small Data Loading")
        
        # Load a small subset of historical data
        print("Loading small subset of historical data...")
        df_full = pl.read_parquet('data/historical/historical_5min.parquet')
        
        # Take only data for a few symbols and recent dates
        test_symbols = ['INFY-EQ', 'RELIANCE-EQ', 'HDFCBANK-EQ']
        df_small = df_full.filter(pl.col('symbol').is_in(test_symbols))
        
        # Take only recent data (last 1000 records per symbol)
        df_small = df_small.group_by('symbol').tail(1000).sort(['symbol', 'date', 'time'])
        
        print(f"Small dataset: {len(df_small)} records for {df_small['symbol'].n_unique()} symbols")
        
        # Save to a temporary file
        temp_path = "data/temp_small_5min.parquet"
        df_small.write_parquet(temp_path)
        print(f"Saved small dataset to {temp_path}")
        
        # Initialize agent
        agent = MarketMonitoringAgent()
        
        # Manually load the small dataset
        print("Loading small dataset into agent...")
        df_processed = agent._preprocess_market_data(df_small)
        print(f"Processed data: {len(df_processed)} records")
        
        # Test timeframe generation with small data
        timeframes = ['1min', '5min', '15min']
        print(f"Generating timeframes: {timeframes}")
        
        await agent._generate_timeframes_from_5min(df_processed, timeframes)
        
        print(f"Market data loaded: {len(agent.market_data)} symbols")
        
        # Check specific symbols
        test_symbols_clean = ['INFY', 'RELIANCE', 'HDFCBANK']
        for symbol in test_symbols_clean:
            if symbol in agent.market_data:
                count_5min = len(agent.market_data[symbol]['5min'])
                count_1min = len(agent.market_data[symbol]['1min'])
                print(f"{symbol}: 5min={count_5min}, 1min={count_1min}")
                
                # Test indicator calculation
                if count_5min > 20:
                    await agent._calculate_indicators(symbol)
                    if symbol in agent.indicators:
                        indicators = agent.indicators[symbol]
                        print(f"  Indicators: RSI={getattr(indicators, 'rsi_14', 'N/A')}")
                    else:
                        print(f"  Indicators: Failed to calculate")
            else:
                print(f"{symbol}: Not found")
        
        # Cleanup
        import os
        if os.path.exists(temp_path):
            os.remove(temp_path)
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_small_data())

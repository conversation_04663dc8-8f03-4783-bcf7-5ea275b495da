"""
Tests for Feature 2: Smart Backtesting Modes
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock

from agents.enhanced_backtesting_polars import (
    SmartBacktestEngine, BacktestConfig, BacktestMode, RiskModel
)


class TestSmartBacktestEngine:
    """Test suite for SmartBacktestEngine class"""
    
    @pytest.fixture
    def engine(self):
        """Create SmartBacktestEngine instance"""
        return SmartBacktestEngine()
    
    @pytest.mark.asyncio
    async def test_initialization(self, engine):
        """Test proper initialization of SmartBacktestEngine"""
        assert engine.ml_models == {}
        assert engine.adaptive_parameters == {}
        assert engine.performance_history == []
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_deterministic_mode(self, mock_simulate, engine, sample_strategy, 
                                    sample_market_data, sample_trades):
        """Test deterministic backtesting mode"""
        mock_simulate.return_value = sample_trades
        
        config = BacktestConfig(
            mode=BacktestMode.DETERMINISTIC,
            risk_model=RiskModel.FIXED_FRACTIONAL,
            initial_capital=100000
        )
        
        result = await engine.run_smart_backtest(sample_strategy, sample_market_data, config)
        
        assert result['mode'] == 'deterministic'
        assert 'trades' in result
        assert 'metrics' in result
        assert result['confidence_level'] == 1.0  # Deterministic should have full confidence
        mock_simulate.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_probabilistic_mode(self, mock_simulate, engine, sample_strategy,
                                    sample_market_data, sample_trades):
        """Test probabilistic backtesting mode"""
        mock_simulate.return_value = sample_trades
        
        config = BacktestConfig(
            mode=BacktestMode.PROBABILISTIC,
            risk_model=RiskModel.FIXED_FRACTIONAL,
            initial_capital=100000,
            monte_carlo_runs=10
        )
        
        result = await engine.run_smart_backtest(sample_strategy, sample_market_data, config)
        
        assert result['mode'] == 'probabilistic'
        assert 'monte_carlo_results' in result
        assert 'confidence_intervals' in result
        assert len(result['monte_carlo_results']) == 10
        assert 'mean_return' in result['confidence_intervals']
        assert 'std_return' in result['confidence_intervals']
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_monte_carlo_simulation(self, mock_simulate, engine, sample_strategy,
                                        sample_market_data, sample_trades):
        """Test Monte Carlo simulation functionality"""
        mock_simulate.return_value = sample_trades
        
        num_runs = 5
        results = await engine._run_monte_carlo_simulation(
            sample_strategy, sample_market_data, num_runs
        )
        
        assert len(results) == num_runs
        assert all('total_return' in result for result in results)
        assert all('trades' in result for result in results)
        assert mock_simulate.call_count == num_runs
    
    @pytest.mark.asyncio
    async def test_calculate_confidence_intervals(self, engine):
        """Test confidence interval calculation"""
        # Sample Monte Carlo results
        mc_results = [
            {'total_return': 10.5, 'sharpe_ratio': 1.2, 'max_drawdown': 0.05},
            {'total_return': 12.3, 'sharpe_ratio': 1.4, 'max_drawdown': 0.08},
            {'total_return': 8.7, 'sharpe_ratio': 0.9, 'max_drawdown': 0.12},
            {'total_return': 15.1, 'sharpe_ratio': 1.6, 'max_drawdown': 0.06},
            {'total_return': 9.8, 'sharpe_ratio': 1.1, 'max_drawdown': 0.09}
        ]
        
        confidence_intervals = await engine._calculate_confidence_intervals(mc_results)
        
        assert 'mean_return' in confidence_intervals
        assert 'std_return' in confidence_intervals
        assert 'ci_95_lower' in confidence_intervals
        assert 'ci_95_upper' in confidence_intervals
        
        # Check that confidence interval bounds make sense
        assert confidence_intervals['ci_95_lower'] < confidence_intervals['mean_return']
        assert confidence_intervals['ci_95_upper'] > confidence_intervals['mean_return']
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_adaptive_ai_mode(self, mock_simulate, engine, sample_strategy,
                                  sample_market_data, sample_trades):
        """Test adaptive AI backtesting mode"""
        mock_simulate.return_value = sample_trades
        
        config = BacktestConfig(
            mode=BacktestMode.ADAPTIVE_AI,
            risk_model=RiskModel.FIXED_FRACTIONAL,
            initial_capital=100000
        )
        
        result = await engine.run_smart_backtest(sample_strategy, sample_market_data, config)
        
        assert result['mode'] == 'adaptive_ai'
        assert 'adaptive_adjustments' in result
        assert 'ml_predictions' in result
        assert 'optimization_history' in result
    
    @pytest.mark.asyncio
    async def test_train_ml_model(self, engine, sample_market_data, sample_trades):
        """Test ML model training"""
        # Mock sklearn components
        with patch('agents.enhanced_backtesting_polars.RandomForestRegressor') as mock_rf:
            mock_model = MagicMock()
            mock_rf.return_value = mock_model
            
            model = await engine._train_ml_model(sample_market_data, sample_trades, 'return_prediction')
            
            assert model is not None
            mock_model.fit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_adaptive_parameter_optimization(self, engine, sample_strategy):
        """Test adaptive parameter optimization"""
        # Sample performance history
        engine.performance_history = [
            {'parameters': {'param1': 10, 'param2': 0.5}, 'performance': 0.15},
            {'parameters': {'param1': 15, 'param2': 0.3}, 'performance': 0.12},
            {'parameters': {'param1': 12, 'param2': 0.4}, 'performance': 0.18}
        ]
        
        optimized_params = await engine._adaptive_parameter_optimization(sample_strategy)
        
        assert 'param1' in optimized_params
        assert 'param2' in optimized_params
        assert isinstance(optimized_params['param1'], (int, float))
        assert isinstance(optimized_params['param2'], (int, float))
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_walk_forward_analysis(self, mock_simulate, engine, sample_strategy,
                                       sample_market_data, sample_trades):
        """Test walk-forward analysis"""
        mock_simulate.return_value = sample_trades
        
        config = BacktestConfig(
            mode=BacktestMode.WALK_FORWARD,
            risk_model=RiskModel.FIXED_FRACTIONAL,
            initial_capital=100000
        )
        
        result = await engine.run_smart_backtest(sample_strategy, sample_market_data, config)
        
        assert result['mode'] == 'walk_forward'
        assert 'walk_forward_results' in result
        assert 'out_of_sample_performance' in result
        assert len(result['walk_forward_results']) > 0
    
    @pytest.mark.asyncio
    async def test_split_data_for_walk_forward(self, engine, sample_market_data):
        """Test data splitting for walk-forward analysis"""
        splits = await engine._split_data_for_walk_forward(sample_market_data, window_size=30, step_size=10)
        
        assert len(splits) > 0
        for split in splits:
            assert 'train_data' in split
            assert 'test_data' in split
            assert len(split['train_data']) == 30
            assert len(split['test_data']) > 0
    
    @pytest.mark.asyncio
    async def test_generate_market_scenarios(self, engine, sample_market_data):
        """Test market scenario generation"""
        scenarios = await engine._generate_market_scenarios(sample_market_data, num_scenarios=3)
        
        assert len(scenarios) == 3
        for scenario in scenarios:
            assert len(scenario) == len(sample_market_data)
            assert 'close' in scenario.columns
            assert 'volume' in scenario.columns
    
    @pytest.mark.asyncio
    async def test_apply_market_noise(self, engine, sample_market_data):
        """Test market noise application"""
        noisy_data = await engine._apply_market_noise(sample_market_data, noise_level=0.01)
        
        assert len(noisy_data) == len(sample_market_data)
        assert 'close' in noisy_data.columns
        
        # Check that noise was applied (prices should be different)
        original_prices = sample_market_data['close'].to_list()
        noisy_prices = noisy_data['close'].to_list()
        
        # At least some prices should be different
        differences = sum(1 for i in range(len(original_prices)) 
                         if abs(original_prices[i] - noisy_prices[i]) > 1e-10)
        assert differences > 0
    
    @pytest.mark.asyncio
    async def test_calculate_regime_adjustments(self, engine, sample_market_data):
        """Test regime-based adjustments"""
        adjustments = await engine._calculate_regime_adjustments(sample_market_data)
        
        assert 'regime' in adjustments
        assert 'volatility_adjustment' in adjustments
        assert 'trend_adjustment' in adjustments
        assert adjustments['volatility_adjustment'] > 0
        assert adjustments['trend_adjustment'] != 0
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_mode(self, engine, sample_strategy, sample_market_data):
        """Test error handling for invalid backtest mode"""
        config = BacktestConfig(
            mode='INVALID_MODE',  # This should cause an error
            risk_model=RiskModel.FIXED_FRACTIONAL,
            initial_capital=100000
        )
        
        with pytest.raises(ValueError, match="Unsupported backtest mode"):
            await engine.run_smart_backtest(sample_strategy, sample_market_data, config)
    
    @pytest.mark.asyncio
    async def test_confidence_level_calculation(self, engine):
        """Test confidence level calculation for different modes"""
        # Deterministic mode should have confidence 1.0
        confidence = await engine._calculate_confidence_level(BacktestMode.DETERMINISTIC, {})
        assert confidence == 1.0
        
        # Probabilistic mode confidence should depend on variance
        mc_results = [{'total_return': 10 + i} for i in range(5)]
        confidence = await engine._calculate_confidence_level(
            BacktestMode.PROBABILISTIC, {'monte_carlo_results': mc_results}
        )
        assert 0 < confidence <= 1.0
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self, engine, sample_strategy):
        """Test performance history tracking"""
        initial_count = len(engine.performance_history)
        
        await engine._track_performance(sample_strategy, {'total_return': 15.5})
        
        assert len(engine.performance_history) == initial_count + 1
        assert engine.performance_history[-1]['performance'] == 15.5

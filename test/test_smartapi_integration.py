#!/usr/bin/env python3
"""
Test script to verify SmartAPI integration and enhanced paper trading workflow
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

async def test_market_monitoring_agent():
    """Test the market monitoring agent with SmartAPI integration"""
    try:
        logger.info("🧪 Testing Market Monitoring Agent with SmartAPI integration...")
        
        # Import the agent
        from agents.market_monitoring_agent import MarketMonitoringAgent
        
        # Create agent instance
        agent = MarketMonitoringAgent()
        
        # Test symbol loading from backup file
        logger.info("📂 Testing symbol loading from backup file...")
        symbols = await agent._get_symbol_list(20)  # Get top 20 symbols
        
        if symbols:
            logger.info(f"✅ Successfully loaded {len(symbols)} symbols")
            logger.info(f"📋 Sample symbols: {[s['symbol'] for s in symbols[:5]]}")
        else:
            logger.warning("⚠️  No symbols loaded")
        
        # Test data paths
        logger.info("📁 Testing data directory structure...")
        live_dir = Path("data/live")
        if live_dir.exists():
            logger.info(f"✅ Live data directory exists: {live_dir}")
        else:
            logger.info(f"📁 Creating live data directory: {live_dir}")
            live_dir.mkdir(parents=True, exist_ok=True)
        
        # Test timeframe generation
        logger.info("⏱️  Testing timeframe generation logic...")
        timeframes = ['5min', '15min', '30min', '1hr']
        logger.info(f"📊 Configured timeframes: {timeframes}")
        
        logger.info("✅ Market Monitoring Agent tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Market Monitoring Agent test failed: {e}")
        return False

async def test_paper_trading_workflow():
    """Test the enhanced paper trading workflow"""
    try:
        logger.info("🧪 Testing Enhanced Paper Trading Workflow...")
        
        # Test the new testing mode
        logger.info("🎯 Testing new 'testing' mode...")
        
        # Import workflow
        from run_paper_trading_workflow import PaperTradingWorkflow
        
        # Create workflow instance
        workflow = PaperTradingWorkflow()
        
        # Test top 20 stocks configuration
        top_20_stocks = [
            "RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK", 
            "KOTAKBANK", "HINDUNILVR", "SBIN", "BHARTIARTL", "ITC",
            "ASIANPAINT", "LT", "AXISBANK", "MARUTI", "SUNPHARMA",
            "ULTRACEMCO", "TITAN", "WIPRO", "NESTLEIND", "POWERGRID"
        ]
        
        logger.info(f"📋 Top 20 stocks configured: {len(top_20_stocks)} stocks")
        logger.info(f"🔍 Sample stocks: {top_20_stocks[:5]}")
        
        # Test enhanced logging features
        logger.info("📝 Testing enhanced logging features...")
        workflow._log_phase_start("Test Phase", "Testing enhanced logging capabilities")
        workflow._log_stock_processing("RELIANCE", "testing")
        workflow._log_signal_generated("RELIANCE", "BUY", 0.85)
        workflow._log_trade_execution("RELIANCE", "BUY", 25, 2455.75)
        
        logger.info("✅ Paper Trading Workflow tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Paper Trading Workflow test failed: {e}")
        return False

async def test_data_structure():
    """Test the data structure and file paths"""
    try:
        logger.info("🧪 Testing Data Structure and File Paths...")
        
        # Test data directories
        directories = [
            "data/live",
            "data/historical",
            "data/historical/backup",
            "logs"
        ]
        
        for directory in directories:
            dir_path = Path(directory)
            if dir_path.exists():
                logger.info(f"✅ Directory exists: {directory}")
            else:
                logger.info(f"📁 Creating directory: {directory}")
                dir_path.mkdir(parents=True, exist_ok=True)
        
        # Test expected file paths
        expected_files = [
            "data/live/live_5min.parquet",
            "data/live/live_15min.parquet", 
            "data/live/live_30min.parquet",
            "data/live/live_1hr.parquet",
            "data/historical/backup/historical_5min.parquet"
        ]
        
        for file_path in expected_files:
            path = Path(file_path)
            if path.exists():
                logger.info(f"✅ File exists: {file_path}")
            else:
                logger.info(f"📄 Expected file (will be created): {file_path}")
        
        logger.info("✅ Data structure tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Data structure test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("🚀 Starting SmartAPI Integration and Enhanced Workflow Tests")
    logger.info("=" * 60)
    
    # Run all tests
    tests = [
        ("Data Structure", test_data_structure),
        ("Market Monitoring Agent", test_market_monitoring_agent),
        ("Paper Trading Workflow", test_paper_trading_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} Test...")
        logger.info("-" * 40)
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} Test: PASSED")
            else:
                logger.error(f"❌ {test_name} Test: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} Test: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! SmartAPI integration is ready.")
        return 0
    else:
        logger.error("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

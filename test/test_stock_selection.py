#!/usr/bin/env python3
"""
Test script to verify the stock selection workflow works correctly
"""

import asyncio
import logging
from pathlib import Path
import polars as pl
from datetime import datetime, timedelta
import numpy as np

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the workflow
from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow
from core.event_system import EventBus

async def test_stock_selection():
    """Test the stock selection workflow with a small dataset"""
    
    # Create event bus
    event_bus = EventBus()
    await event_bus.start_processor()
    
    try:
        # Initialize workflow
        workflow = LiveStockSelectionWorkflow(event_bus, "test_session", skip_download=True)
        
        # Initialize workflow
        success = await workflow.initialize()
        if not success:
            logger.error("Failed to initialize workflow")
            return
            
        # Test with a small set of stocks that have existing data
        test_stocks = ["RELIANCE", "TCS", "INFY", "HDFCBANK", "ICICIBANK"]
        
        logger.info(f"Testing workflow with {len(test_stocks)} stocks: {test_stocks}")
        
        # Execute workflow
        result = await workflow.execute_workflow(test_stocks)
        
        # Check results
        if result and result.selected_stocks:
            logger.info(f"✅ SUCCESS: Selected {len(result.selected_stocks)} stocks")
            logger.info(f"Selected stocks: {result.selected_stocks}")
            
            if result.strategy_assignments:
                logger.info(f"Strategy assignments: {len(result.strategy_assignments)}")
                
            if result.warnings:
                logger.info(f"Warnings: {result.warnings}")
                
        else:
            logger.error("❌ FAILED: No stocks selected")
            
        # Cleanup
        await workflow.cleanup()
        
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
        
    finally:
        await event_bus.stop_processor()

if __name__ == "__main__":
    asyncio.run(test_stock_selection())
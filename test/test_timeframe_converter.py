#!/usr/bin/env python3
"""
Test script for timeframe converter
Tests with a small sample of data first
"""

import polars as pl
import asyncio
import logging
from pathlib import Path
from scripts.timeframe_converter import TimeframeConverter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_with_sample():
    """Test the converter with a small sample of data"""
    
    # Create test directory
    test_dir = Path("test")
    test_dir.mkdir(exist_ok=True)
    
    # Read a small sample from the original file
    logger.info("Loading sample data for testing...")
    sample_df = pl.read_parquet("data/historical_5min.parquet").head(10000)
    
    # Save sample to test file
    test_input = test_dir / "sample_5min.parquet"
    sample_df.write_parquet(test_input)
    logger.info(f"Created test sample with {len(sample_df)} rows")
    
    # Test the converter
    converter = TimeframeConverter(
        input_file=str(test_input),
        output_dir=str(test_dir),
        chunk_size=2000  # Small chunk size for testing
    )
    
    await converter.convert_timeframes()
    
    # Verify outputs
    timeframes = ["15min", "30min", "1hr"]
    for tf in timeframes:
        output_file = test_dir / f"historical_{tf}.parquet"
        if output_file.exists():
            df = pl.read_parquet(output_file)
            logger.info(f"Test {tf} output: {len(df)} rows")
            print(f"\n{tf.upper()} Sample:")
            print(df.head(3))
        else:
            logger.error(f"Test output file not found: {output_file}")

if __name__ == "__main__":
    asyncio.run(test_with_sample())

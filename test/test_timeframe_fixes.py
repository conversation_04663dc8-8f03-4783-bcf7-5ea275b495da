#!/usr/bin/env python3
"""
Test script to verify timeframe conversion fixes in market monitoring agent
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# Add paths for imports
sys.path.append('agents')
sys.path.append('.')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_timeframe_conversion_fixes():
    """Test the timeframe conversion fixes"""
    logger.info("=== Testing Timeframe Conversion Fixes ===")
    
    try:
        # Import the agent
        from market_monitoring_agent import MarketMonitoringAgent
        
        # Create agent instance (without full setup)
        config_path = "config/market_monitoring_config.yaml"
        agent = MarketMonitoringAgent(config_path)
        
        logger.info("✅ Agent created successfully")
        
        # Test the data loading and timeframe conversion
        logger.info("🧪 Testing timeframe data loading...")
        
        # Check if live data exists
        live_data_path = "data/live/live_5min.parquet"
        if not os.path.exists(live_data_path):
            logger.error("❌ Live data file not found. Please run data download first.")
            return False
        
        # Test the timeframe loading function
        await agent._load_all_timeframes_from_live_data()
        
        # Check if data was loaded successfully
        total_symbols = len(agent.market_data)
        logger.info(f"📊 Market data loaded for {total_symbols} symbols")
        
        if total_symbols > 0:
            # Check a sample symbol
            sample_symbol = list(agent.market_data.keys())[0]
            symbol_data = agent.market_data[sample_symbol]
            
            logger.info(f"📈 Sample symbol: {sample_symbol}")
            for timeframe in ['5min', '15min', '30min', '1hr']:
                if timeframe in symbol_data:
                    count = len(symbol_data[timeframe])
                    logger.info(f"  {timeframe}: {count} candles")
                else:
                    logger.warning(f"  {timeframe}: No data")
            
            logger.info("✅ Timeframe conversion completed successfully!")
            return True
        else:
            logger.error("❌ No market data loaded")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing timeframe conversion: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def test_data_sorting():
    """Test the data sorting functionality"""
    logger.info("=== Testing Data Sorting ===")
    
    try:
        import polars as pl
        
        # Load live data
        live_data_path = "data/live/live_5min.parquet"
        if not os.path.exists(live_data_path):
            logger.error("❌ Live data file not found")
            return False
            
        df = pl.read_parquet(live_data_path)
        logger.info(f"📊 Loaded data: {df.shape}")
        
        # Import the agent to use its sorting function
        from market_monitoring_agent import MarketMonitoringAgent
        config_path = "config/market_monitoring_config.yaml"
        agent = MarketMonitoringAgent(config_path)
        
        # Test the sorting function
        sorted_df = agent._ensure_data_sorted(df)
        
        # Verify sorting for a few symbols
        symbols_to_check = sorted_df['symbol'].unique()[:3]
        all_sorted = True
        
        for symbol in symbols_to_check:
            symbol_data = sorted_df.filter(pl.col('symbol') == symbol)
            is_sorted = symbol_data['timestamp'].is_sorted()
            logger.info(f"  {symbol}: sorted = {is_sorted}")
            if not is_sorted:
                all_sorted = False
        
        if all_sorted:
            logger.info("✅ Data sorting works correctly!")
            return True
        else:
            logger.error("❌ Data sorting failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing data sorting: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Run all tests"""
    logger.info("🧪 Starting Timeframe Conversion Fix Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Data Sorting", test_data_sorting),
        ("Timeframe Conversion", test_timeframe_conversion_fixes)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*40}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*40}")
        
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"{test_name}: ❌ FAILED with exception: {e}")
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The timeframe conversion fixes are working!")
        logger.info("💡 The original errors should now be resolved.")
    else:
        logger.warning("⚠️  Some tests failed. Check the logs above for details.")

if __name__ == "__main__":
    asyncio.run(main())

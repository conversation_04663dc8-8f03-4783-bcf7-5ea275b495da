#!/usr/bin/env python3
"""
Test script to verify trading system fixes
"""

import os
import sys
from pathlib import Path
import asyncio
import logging

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from utils.paper_trading import VirtualAccount
from agents.risk_management_agent import RiskManagementAgent
from core.event_system import EventBus

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockSignal:
    def __init__(self, symbol, signal_type, entry_price, stop_loss, target_price, strength=0.8, confidence=0.7):
        self.symbol = symbol
        self.signal_type = signal_type
        self.entry_price = entry_price
        self.stop_loss = stop_loss
        self.target_price = target_price
        self.strength = strength
        self.confidence = confidence

class MockConfig:
    def __init__(self):
        self.initial_balance = 100000
        self.risk_limits = {
            'max_position_size': 0.2,
            'max_daily_loss': 0.05,
            'max_drawdown': 0.15
        }

async def test_paper_trading_limits():
    """Test paper trading limits and position size calculations"""
    logger.info("=" * 60)
    logger.info("TESTING PAPER TRADING LIMITS")
    logger.info("=" * 60)
    
    # Create virtual account
    config = {}
    account = VirtualAccount(config)
    
    # Test 1: Check initial settings
    logger.info(f"Initial balance: Rs.{account.initial_balance:,.2f}")
    logger.info(f"Max trades per day: {account.max_trades_per_day}")
    logger.info(f"Max position size: Rs.{account.max_position_size:,.2f}")
    logger.info(f"Max daily loss: Rs.{account.max_daily_loss:,.2f}")
    
    # Test 2: Try to place a trade that exceeds position size limit
    logger.info("\n--- Test 2: Position Size Limit ---")
    symbol = "AMBUJACEM"
    price = 1119.0  # Approximate price from error log
    quantity = 89   # Quantity from error log
    trade_value = quantity * price
    
    logger.info(f"Attempting trade: {quantity} shares of {symbol} @ Rs.{price:.2f}")
    logger.info(f"Trade value: Rs.{trade_value:.2f}")
    
    can_trade, reason = account.can_place_trade(symbol, quantity, price, "BUY", "MIS")
    logger.info(f"Can trade: {can_trade}")
    logger.info(f"Reason: {reason}")
    
    # Test 3: Calculate correct position size
    logger.info("\n--- Test 3: Correct Position Size Calculation ---")
    max_shares = int(account.max_position_size / price)
    logger.info(f"Max shares within limit: {max_shares}")
    logger.info(f"Max trade value: Rs.{max_shares * price:.2f}")
    
    # Test 4: Test trade counting
    logger.info("\n--- Test 4: Trade Counting ---")
    account_summary = account.get_account_summary()
    logger.info(f"Today trades: {account_summary['today_trades']}")
    logger.info(f"Trades remaining: {account_summary['trades_remaining_today']}")
    
    return True

async def test_risk_management():
    """Test risk management position sizing"""
    logger.info("\n" + "=" * 60)
    logger.info("TESTING RISK MANAGEMENT")
    logger.info("=" * 60)
    
    # Create mock components
    event_bus = EventBus()
    config = MockConfig()
    
    # Create risk management agent
    risk_agent = RiskManagementAgent(event_bus, config, "test_session")
    
    # Test signal
    signal = MockSignal(
        symbol="AMBUJACEM",
        signal_type="BUY",
        entry_price=1119.0,
        stop_loss=1050.0,
        target_price=1200.0
    )
    
    # Calculate position size
    position_size = await risk_agent._calculate_position_size(signal)
    position_value = position_size * signal.entry_price
    
    logger.info(f"Signal: {signal.signal_type} {signal.symbol} @ Rs.{signal.entry_price:.2f}")
    logger.info(f"Stop loss: Rs.{signal.stop_loss:.2f}")
    logger.info(f"Target: Rs.{signal.target_price:.2f}")
    logger.info(f"Calculated position size: {position_size} shares")
    logger.info(f"Position value: Rs.{position_value:.2f}")
    
    # Check if it respects the Rs.50,000 limit
    max_position_limit = float(os.getenv('PAPER_TRADING_MAX_POSITION_SIZE', 50000))
    logger.info(f"Max position limit: Rs.{max_position_limit:.2f}")
    logger.info(f"Within limit: {position_value <= max_position_limit}")
    
    return True

async def main():
    """Run all tests"""
    logger.info("🧪 Starting Trading System Fixes Test")
    
    try:
        # Test paper trading limits
        await test_paper_trading_limits()
        
        # Test risk management
        await test_risk_management()
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ ALL TESTS COMPLETED")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
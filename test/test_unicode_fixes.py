#!/usr/bin/env python3
"""
Test Unicode Fixes
Verify that Unicode encoding issues are resolved and graceful shutdown works
"""

import asyncio
import logging
import signal
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Test logging with Unicode support
def test_logging_setup():
    """Test logging configuration with Unicode support"""
    print("[TEST] Testing logging setup with Unicode support...")
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    # Configure logging with UTF-8 encoding
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler with UTF-8 encoding
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    if hasattr(console_handler.stream, 'reconfigure'):
        try:
            console_handler.stream.reconfigure(encoding='utf-8')
            print("[SUCCESS] Console handler configured with UTF-8 encoding")
        except Exception as e:
            print(f"[WARNING] Could not reconfigure console encoding: {e}")
    
    # File handler with UTF-8 encoding
    from logging.handlers import RotatingFileHandler
    file_handler = RotatingFileHandler(
        filename='logs/test_unicode.log',
        maxBytes=1024*1024,  # 1MB
        backupCount=2,
        encoding='utf-8'  # Explicit UTF-8 encoding
    )
    file_handler.setFormatter(formatter)
    
    # Configure root logger
    logger = logging.getLogger('test_unicode')
    logger.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    # Test various log messages
    logger.info("[SUCCESS] Logging configured successfully with Unicode support")
    logger.debug("[COMPILE] Compiled strategy expressions for test_strategy")
    logger.info("[SIGNAL] Signal generated: RELIANCE | RSI_Strategy | BUY")
    logger.info("[SAVE] Agent state saved")
    logger.warning("[STOP] Some tasks didn't cancel within timeout, forcing shutdown")
    
    print("[SUCCESS] Logging test completed - check logs/test_unicode.log")

def test_date_range_calculation():
    """Test date range calculation for historical data"""
    print("[TEST] Testing date range calculation...")
    
    # Test 35 days back calculation
    from datetime import datetime, timedelta
    import pytz
    
    # Get current time in IST
    ist = pytz.timezone('Asia/Kolkata')
    end_date = datetime.now(ist).replace(tzinfo=None)
    
    # Calculate 35 days back
    days_back = 35
    start_date = end_date - timedelta(days=days_back)
    
    # Set proper market hours
    if start_date.weekday() < 5:  # Monday to Friday
        start_date = start_date.replace(hour=9, minute=15)  # Market open
    
    if end_date.weekday() < 5:  # Monday to Friday
        end_date = end_date.replace(hour=15, minute=30)  # Market close
    
    print(f"[INFO] Date range: {start_date.strftime('%Y-%m-%d %H:%M')} to {end_date.strftime('%Y-%m-%d %H:%M')}")
    print(f"[INFO] Total days: {(end_date - start_date).days}")
    print(f"[INFO] Trading days (approx): {(end_date - start_date).days * 5/7:.0f}")
    
    # Test cutoff date calculation (should be 35 days, not 8)
    cutoff_date = datetime.now() - timedelta(days=35)
    cutoff_str = cutoff_date.strftime("%Y-%m-%dT%H:%M:%S")
    print(f"[INFO] Data cutoff date: {cutoff_str}")
    
    print("[SUCCESS] Date range calculation test completed")

async def test_graceful_shutdown():
    """Test graceful shutdown mechanism"""
    print("[TEST] Testing graceful shutdown mechanism...")
    
    # Simulate background tasks
    shutdown_requested = False
    
    async def mock_monitoring_task():
        """Mock monitoring task"""
        cycle = 0
        while not shutdown_requested:
            cycle += 1
            print(f"[MONITOR] Monitoring cycle {cycle}")
            await asyncio.sleep(1)
        print("[STOP] Monitoring task stopped")
    
    async def mock_signal_task():
        """Mock signal generation task"""
        cycle = 0
        while not shutdown_requested:
            cycle += 1
            print(f"[SIGNAL] Signal generation cycle {cycle}")
            await asyncio.sleep(2)
        print("[STOP] Signal generation task stopped")
    
    # Start background tasks
    tasks = [
        asyncio.create_task(mock_monitoring_task()),
        asyncio.create_task(mock_signal_task())
    ]
    
    print("[INFO] Started 2 background tasks")
    
    # Let them run for a bit
    await asyncio.sleep(5)
    
    # Simulate shutdown
    print("[STOP] Initiating graceful shutdown...")
    shutdown_requested = True
    
    # Cancel tasks with timeout
    for task in tasks:
        if not task.done():
            task.cancel()
    
    try:
        await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=3.0
        )
        print("[SUCCESS] All tasks cancelled successfully")
    except asyncio.TimeoutError:
        print("[WARNING] Some tasks didn't cancel within timeout")
    
    print("[SUCCESS] Graceful shutdown test completed")

def test_data_merge_logic():
    """Test data merging logic"""
    print("[TEST] Testing data merge logic...")
    
    # Test that we keep 35 days of data, not 8
    from datetime import datetime, timedelta
    
    # Simulate existing data timestamps
    now = datetime.now()
    existing_timestamps = []
    
    # Create timestamps for last 40 days
    for i in range(40):
        timestamp = now - timedelta(days=i)
        existing_timestamps.append(timestamp.strftime("%Y-%m-%dT%H:%M:%S"))
    
    # Test cutoff logic (35 days)
    cutoff_date = now - timedelta(days=35)
    cutoff_str = cutoff_date.strftime("%Y-%m-%dT%H:%M:%S")
    
    # Filter timestamps (simulate polars filter)
    filtered_timestamps = [ts for ts in existing_timestamps if ts >= cutoff_str]
    
    print(f"[INFO] Original timestamps: {len(existing_timestamps)}")
    print(f"[INFO] After 35-day cutoff: {len(filtered_timestamps)}")
    print(f"[INFO] Oldest kept: {min(filtered_timestamps)}")
    print(f"[INFO] Newest kept: {max(filtered_timestamps)}")
    
    # Verify we keep approximately 35 days (allow for 36 due to inclusive comparison)
    assert len(filtered_timestamps) <= 36, f"Should keep <= 36 days, got {len(filtered_timestamps)}"
    assert len(filtered_timestamps) >= 30, f"Should keep >= 30 days, got {len(filtered_timestamps)}"
    
    print("[SUCCESS] Data merge logic test completed")

async def main():
    """Main test function"""
    print("=" * 60)
    print("UNICODE FIXES AND SHUTDOWN MECHANISM TEST")
    print("=" * 60)
    
    try:
        # Test 1: Logging setup
        test_logging_setup()
        print()
        
        # Test 2: Date range calculation
        test_date_range_calculation()
        print()
        
        # Test 3: Data merge logic
        test_data_merge_logic()
        print()
        
        # Test 4: Graceful shutdown
        await test_graceful_shutdown()
        print()
        
        print("=" * 60)
        print("[SUCCESS] ALL TESTS PASSED")
        print("=" * 60)
        print()
        print("Key Fixes Applied:")
        print("1. [UNICODE] UTF-8 encoding for console and file handlers")
        print("2. [EMOJI] Replaced emoji characters with ASCII alternatives")
        print("3. [SHUTDOWN] Enhanced graceful shutdown with timeouts")
        print("4. [DATA] Fixed data retention from 8 days to 35 days")
        print("5. [MERGE] Removed timestamp filtering that limited historical data")
        
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())

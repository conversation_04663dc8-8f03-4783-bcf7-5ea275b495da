#!/usr/bin/env python3
"""
[TARGET] Advanced Technical Indicators
Custom implementations of sophisticated technical indicators for enhanced market analysis

Features:
- Ichimoku Cloud components
- Keltner Channels
- Bollinger Band derivatives
- Volume-weighted indicators
- Custom oscillators and momentum indicators
- Support/Resistance levels
"""

import numpy as np
import polars as pl
import pandas as pd
from typing import Tuple, Optional, List
import logging

logger = logging.getLogger(__name__)

class AdvancedIndicators:
    """
    Advanced technical indicators with custom implementations
    """
    
    @staticmethod
    def ichimoku_cloud(df: pl.DataFrame, 
                      tenkan_period: int = 9,
                      kijun_period: int = 26,
                      senkou_b_period: int = 52,
                      displacement: int = 26) -> pl.DataFrame:
        """
        Calculate Ichimoku Cloud components
        
        Args:
            df: DataFrame with OHLC data
            tenkan_period: Tenkan-sen period (default 9)
            kijun_period: Kijun-sen period (default 26)
            senkou_b_period: Senkou Span B period (default 52)
            displacement: Displacement for Senkou spans (default 26)
            
        Returns:
            DataFrame with Ichimoku components
        """
        try:
            result_df = df.clone()
            
            # Tenkan-sen (Conversion Line)
            tenkan_high = pl.col('high').rolling_max(window_size=tenkan_period)
            tenkan_low = pl.col('low').rolling_min(window_size=tenkan_period)
            tenkan_sen = (tenkan_high + tenkan_low) / 2
            
            # Kijun-sen (Base Line)
            kijun_high = pl.col('high').rolling_max(window_size=kijun_period)
            kijun_low = pl.col('low').rolling_min(window_size=kijun_period)
            kijun_sen = (kijun_high + kijun_low) / 2
            
            # Senkou Span A (Leading Span A)
            senkou_a = (tenkan_sen + kijun_sen) / 2
            
            # Senkou Span B (Leading Span B)
            senkou_b_high = pl.col('high').rolling_max(window_size=senkou_b_period)
            senkou_b_low = pl.col('low').rolling_min(window_size=senkou_b_period)
            senkou_b = (senkou_b_high + senkou_b_low) / 2
            
            # Chikou Span (Lagging Span) - shifted close price
            chikou_span = pl.col('close').shift(-displacement)
            
            result_df = result_df.with_columns([
                tenkan_sen.alias('ichimoku_tenkan'),
                kijun_sen.alias('ichimoku_kijun'),
                senkou_a.shift(displacement).alias('ichimoku_senkou_a'),
                senkou_b.shift(displacement).alias('ichimoku_senkou_b'),
                chikou_span.alias('ichimoku_chikou')
            ])
            
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Ichimoku Cloud calculation failed: {e}")
            return df
    
    @staticmethod
    def keltner_channels(df: pl.DataFrame, 
                        period: int = 20,
                        multiplier: float = 2.0) -> pl.DataFrame:
        """
        Calculate Keltner Channels
        
        Args:
            df: DataFrame with OHLC data
            period: EMA period for middle line
            multiplier: ATR multiplier for bands
            
        Returns:
            DataFrame with Keltner Channel components
        """
        try:
            result_df = df.clone()
            
            # Middle line (EMA of close)
            alpha = 2.0 / (period + 1)
            middle_line = pl.col('close').ewm_mean(alpha=alpha)
            
            # Calculate ATR
            high_low = pl.col('high') - pl.col('low')
            high_close = (pl.col('high') - pl.col('close').shift(1)).abs()
            low_close = (pl.col('low') - pl.col('close').shift(1)).abs()
            
            true_range = pl.max_horizontal([high_low, high_close, low_close])
            atr = true_range.ewm_mean(alpha=alpha)
            
            # Upper and lower bands
            upper_band = middle_line + (multiplier * atr)
            lower_band = middle_line - (multiplier * atr)
            
            result_df = result_df.with_columns([
                upper_band.alias('keltner_upper'),
                middle_line.alias('keltner_middle'),
                lower_band.alias('keltner_lower')
            ])
            
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Keltner Channels calculation failed: {e}")
            return df
    
    @staticmethod
    def bollinger_band_derivatives(df: pl.DataFrame, 
                                  period: int = 20,
                                  std_dev: float = 2.0) -> pl.DataFrame:
        """
        Calculate Bollinger Band derivatives (Width, %B)
        
        Args:
            df: DataFrame with OHLC data
            period: Moving average period
            std_dev: Standard deviation multiplier
            
        Returns:
            DataFrame with Bollinger Band derivatives
        """
        try:
            result_df = df.clone()
            
            # Calculate basic Bollinger Bands
            sma = pl.col('close').rolling_mean(window_size=period)
            std = pl.col('close').rolling_std(window_size=period)
            
            upper_band = sma + (std_dev * std)
            lower_band = sma - (std_dev * std)
            
            # Bollinger Band Width
            bb_width = ((upper_band - lower_band) / sma) * 100
            
            # %B (Bollinger Band Percent)
            bb_percent = ((pl.col('close') - lower_band) / (upper_band - lower_band)) * 100
            
            result_df = result_df.with_columns([
                upper_band.alias('bb_upper'),
                sma.alias('bb_middle'),
                lower_band.alias('bb_lower'),
                bb_width.alias('bb_width'),
                bb_percent.alias('bb_percent')
            ])
            
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Bollinger Band derivatives calculation failed: {e}")
            return df
    
    @staticmethod
    def volume_weighted_indicators(df: pl.DataFrame) -> pl.DataFrame:
        """
        Calculate volume-weighted indicators
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with volume-weighted indicators
        """
        try:
            result_df = df.clone()
            
            # Volume Weighted Moving Average (VWMA)
            vwma_20 = (pl.col('close') * pl.col('volume')).rolling_sum(window_size=20) / \
                     pl.col('volume').rolling_sum(window_size=20)
            
            # Volume SMA
            volume_sma = pl.col('volume').rolling_mean(window_size=20)
            
            # Volume Ratio
            volume_ratio = pl.col('volume') / volume_sma
            
            # Ease of Movement
            distance_moved = ((pl.col('high') + pl.col('low')) / 2) - \
                           ((pl.col('high').shift(1) + pl.col('low').shift(1)) / 2)
            
            box_height = pl.col('volume') / (pl.col('high') - pl.col('low'))
            ease_of_movement = distance_moved / box_height
            ease_of_movement_ma = ease_of_movement.rolling_mean(window_size=14)
            
            result_df = result_df.with_columns([
                vwma_20.alias('vwma_20'),
                volume_sma.alias('volume_sma'),
                volume_ratio.alias('volume_ratio'),
                ease_of_movement_ma.alias('ease_of_movement')
            ])
            
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Volume-weighted indicators calculation failed: {e}")
            return df
    
    @staticmethod
    def custom_oscillators(df: pl.DataFrame) -> pl.DataFrame:
        """
        Calculate custom oscillators and momentum indicators
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            DataFrame with custom oscillators
        """
        try:
            result_df = df.clone()
            
            # Chaikin Oscillator
            ad_line = ((pl.col('close') - pl.col('low')) - (pl.col('high') - pl.col('close'))) / \
                     (pl.col('high') - pl.col('low')) * pl.col('volume')
            ad_line_cumsum = ad_line.cumsum()
            
            chaikin_osc = ad_line_cumsum.ewm_mean(alpha=2.0/4) - ad_line_cumsum.ewm_mean(alpha=2.0/11)
            
            # Fisher Transform
            hl2 = (pl.col('high') + pl.col('low')) / 2
            hl2_norm = (hl2 - hl2.rolling_min(window_size=10)) / \
                      (hl2.rolling_max(window_size=10) - hl2.rolling_min(window_size=10))
            
            # Clamp values to avoid infinite results
            hl2_clamped = pl.max_horizontal([pl.min_horizontal([hl2_norm, pl.lit(0.999)]), pl.lit(0.001)])
            fisher_transform = 0.5 * ((1 + hl2_clamped) / (1 - hl2_clamped)).log()
            
            # ConnorsRSI (simplified version)
            # RSI component
            price_change = pl.col('close') - pl.col('close').shift(1)
            gain = pl.when(price_change > 0).then(price_change).otherwise(0)
            loss = pl.when(price_change < 0).then(-price_change).otherwise(0)
            
            avg_gain = gain.rolling_mean(window_size=3)
            avg_loss = loss.rolling_mean(window_size=3)
            rs = avg_gain / avg_loss
            rsi_3 = 100 - (100 / (1 + rs))
            
            # Streak component (simplified)
            up_streak = pl.when(price_change > 0).then(1).otherwise(0)
            down_streak = pl.when(price_change < 0).then(1).otherwise(0)
            
            # ConnorsRSI (simplified combination)
            connors_rsi = (rsi_3 + 50) / 2  # Simplified version
            
            # Coppock Curve
            roc_14 = ((pl.col('close') / pl.col('close').shift(14)) - 1) * 100
            roc_11 = ((pl.col('close') / pl.col('close').shift(11)) - 1) * 100
            coppock_curve = (roc_14 + roc_11).rolling_mean(window_size=10)
            
            result_df = result_df.with_columns([
                chaikin_osc.alias('chaikin_oscillator'),
                fisher_transform.alias('fisher_transform'),
                connors_rsi.alias('connors_rsi'),
                coppock_curve.alias('coppock_curve')
            ])
            
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Custom oscillators calculation failed: {e}")
            return df
    
    @staticmethod
    def support_resistance_levels(df: pl.DataFrame, 
                                 pivot_period: int = 5) -> pl.DataFrame:
        """
        Calculate support and resistance levels using pivot points
        
        Args:
            df: DataFrame with OHLC data
            pivot_period: Period for pivot calculation
            
        Returns:
            DataFrame with support/resistance levels
        """
        try:
            result_df = df.clone()
            
            # Calculate pivot point (using previous day's data)
            prev_high = pl.col('high').shift(1)
            prev_low = pl.col('low').shift(1)
            prev_close = pl.col('close').shift(1)
            
            pivot_point = (prev_high + prev_low + prev_close) / 3
            
            # Support and Resistance levels
            resistance_1 = (2 * pivot_point) - prev_low
            support_1 = (2 * pivot_point) - prev_high
            
            resistance_2 = pivot_point + (prev_high - prev_low)
            support_2 = pivot_point - (prev_high - prev_low)
            
            # Donchian Channel (alternative support/resistance)
            donchian_high = pl.col('high').rolling_max(window_size=20)
            donchian_low = pl.col('low').rolling_min(window_size=20)
            donchian_middle = (donchian_high + donchian_low) / 2
            
            result_df = result_df.with_columns([
                pivot_point.alias('pivot_point'),
                resistance_1.alias('resistance_1'),
                resistance_2.alias('resistance_2'),
                support_1.alias('support_1'),
                support_2.alias('support_2'),
                donchian_high.alias('donchian_high'),
                donchian_low.alias('donchian_low'),
                donchian_middle.alias('donchian_middle')
            ])
            
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Support/Resistance calculation failed: {e}")
            return df
    
    @staticmethod
    def calculate_all_advanced_indicators(df: pl.DataFrame) -> pl.DataFrame:
        """
        Calculate all advanced indicators in one pass
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with all advanced indicators
        """
        try:
            result_df = df.clone()
            
            # Apply all indicator calculations
            result_df = AdvancedIndicators.ichimoku_cloud(result_df)
            result_df = AdvancedIndicators.keltner_channels(result_df)
            result_df = AdvancedIndicators.bollinger_band_derivatives(result_df)
            result_df = AdvancedIndicators.volume_weighted_indicators(result_df)
            result_df = AdvancedIndicators.custom_oscillators(result_df)
            result_df = AdvancedIndicators.support_resistance_levels(result_df)
            
            logger.info("[INDICATORS] All advanced indicators calculated successfully")
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Advanced indicators calculation failed: {e}")
            return df

#!/usr/bin/env python3
"""
Angel One API Utility Module
Comprehensive SmartAPI integration for risk management and trading operations

Features:
- SmartAPI authentication and session management
- Margin calculation and RMS limit checking
- Position and holding tracking
- Fund management and balance monitoring
- Real-time market data integration
- Error handling and retry mechanisms
"""

import os
import sys
import logging
import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Third-party imports
import pyotp
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# SmartAPI integration
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
except ImportError:
    print("[WARN]  SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None

# Setup logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG) # Explicitly set logger to DEBUG for detailed output

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA MODELS
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class MarginRequirement:
    """Margin requirement details for a trade"""
    symbol: str
    exchange: str
    quantity: int
    price: float
    product_type: str  # MIS, CNC, NRML
    transaction_type: str  # BUY, SELL
    margin_required: float
    available_margin: float
    is_allowed: bool
    limit_used_percent: float
    error_message: Optional[str] = None

@dataclass
class RMSLimits:
    """RMS (Risk Management System) limits from Angel One"""
    net_available_cash: float
    available_margin: float
    utilized_margin: float
    gross_collateral_value: float
    net_collateral_value: float
    margin_used_percent: float
    cash_margin_available: float
    category_limits: Dict[str, Any]
    timestamp: datetime

@dataclass
class PositionData:
    """Position information"""
    symbol: str
    exchange: str
    product_type: str
    quantity: int
    average_price: float
    last_price: float
    pnl: float
    pnl_percent: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime

@dataclass
class FundData:
    """Fund and balance information"""
    available_cash: float
    utilized_margin: float
    available_margin: float
    collateral_value: float
    m2m_unrealized: float
    m2m_realized: float
    opening_balance: float
    payin: float
    payout: float
    timestamp: datetime

# ═══════════════════════════════════════════════════════════════════════════════
# [CONNECT] ANGEL ONE API CLIENT
# ═══════════════════════════════════════════════════════════════════════════════

class AngelOneAPIClient:
    """
    Angel One SmartAPI client with comprehensive risk management features
    
    Features:
    - Authentication and session management
    - Margin calculation and validation
    - Position and fund monitoring
    - Real-time data integration
    - Error handling and retry logic
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Angel One API client"""
        
        self.config = config
        self.api_config = config.get('angel_one_api', {})
        
        # API credentials
        self.api_key = os.getenv('SMARTAPI_API_KEY')
        self.username = os.getenv('SMARTAPI_USERNAME')
        self.password = os.getenv('SMARTAPI_PASSWORD')
        self.totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
        
        # API settings
        self.timeout = self.api_config.get('timeout', 10)
        self.max_retries = self.api_config.get('max_retries', 3)
        self.retry_delay = self.api_config.get('retry_delay', 2)
        
        # Rate limiting - Very conservative settings to avoid rate limit errors
        self.requests_per_second = self.api_config.get('requests_per_second', 1)  # Reduced to 1 request per second
        self.last_request_time = 0
        self.request_count = 0
        self.min_request_interval = 2.0  # Minimum 2 seconds between requests
        
        # SmartAPI client
        self.smart_api = None
        self.auth_token = None
        self.refresh_token = None
        self.feed_token = None
        self.user_id = None
        
        # Session management
        self.session_active = False
        self.session_expiry = None
        
        # Cache for frequently accessed data
        self.cache = {}
        self.cache_duration = self.config.get('system', {}).get('performance', {}).get('cache_duration_seconds', 30)
        
        # Determine if in paper trading mode
        self.is_paper_mode = os.getenv('TRADING_MODE', 'paper').lower() == 'paper'

        # Initialize SmartAPI client only if not in paper mode
        self.smart_api = None
        if not self.is_paper_mode and SmartConnect:
            self._initialize_client()
        elif self.is_paper_mode:
            logger.info("[INFO] Angel One API client operating in simulated paper trading mode.")
    
    def _initialize_client(self):
        """Initialize SmartAPI client (only called if not in paper mode)"""
        try:
            if not self.api_key:
                raise ValueError("API key not provided in config for real trading mode.")
            
            self.smart_api = SmartConnect(api_key=self.api_key)
            logger.info("[SUCCESS] Angel One API client initialized for real trading.")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Angel One API client for real trading: {e}")
            raise
    
    async def authenticate(self) -> bool:
        """Authenticate with Angel One API or simulate in paper mode"""
        if self.is_paper_mode:
            logger.info("[INFO] Simulating Angel One API authentication in paper trading mode.")
            self.session_active = True
            self.user_id = "PAPER_TRADER"
            return True

        try:
            if not self.smart_api:
                logger.error("[ERROR] SmartAPI client not initialized for real trading.")
                return False
            
            # Generate TOTP
            if not self.totp_token:
                raise ValueError("TOTP token not provided for real trading.")

            # Debug: Check TOTP token value
            logger.debug(f"[DEBUG] Raw TOTP token: '{self.totp_token}'")
            logger.debug(f"[DEBUG] TOTP token length: {len(self.totp_token)}")
            logger.debug(f"[DEBUG] TOTP token starts with: {self.totp_token[:5]}...")

            # Clean TOTP token (remove any whitespace/newlines)
            clean_totp_token = self.totp_token.strip()
            logger.debug(f"[DEBUG] Cleaned TOTP token: '{clean_totp_token}'")
            logger.debug(f"[DEBUG] Cleaned TOTP token length: {len(clean_totp_token)}")

            try:
                totp = pyotp.TOTP(clean_totp_token).now()
                logger.debug(f"[DEBUG] Generated TOTP: {totp}")
            except Exception as totp_e:
                logger.error(f"[ERROR] Failed to generate TOTP: {totp_e}")
                raise ValueError(f"Invalid TOTP token format or generation issue: {totp_e}")

            # Generate session
            logger.info("[SECURE] Authenticating with Angel One API...")
            logger.debug(f"[DEBUG] Calling generateSession with username: {self.username}, password: [REDACTED], totp: {totp}")
            data = self.smart_api.generateSession(self.username, self.password, totp)
            logger.debug(f"[DEBUG] generateSession response: {data}")
            
            if not data.get('status'):
                error_message = data.get('message', 'Unknown error')
                error_code = data.get('errorcode', 'N/A')
                logger.error(f"[ERROR] Authentication failed. Message: {error_message}, Code: {error_code}")
                raise Exception(f"Authentication failed: {error_message} (Code: {error_code})")
            
            # Extract tokens
            session_data = data['data']
            self.auth_token = session_data['jwtToken']
            self.refresh_token = session_data['refreshToken']
            self.feed_token = session_data['feedToken']
            self.user_id = session_data.get('clientcode')
            
            # Set session as active
            self.session_active = True
            self.session_expiry = datetime.now() + timedelta(hours=8)  # Typical session duration
            
            logger.info(f"[SUCCESS] Authentication successful for user: {self.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Authentication failed: {e}")
            self.session_active = False
            return False
    
    async def _ensure_authenticated(self) -> bool:
        """Ensure API session is authenticated or simulated in paper mode"""
        if self.is_paper_mode:
            return True # Always authenticated in paper mode

        if not self.session_active or (self.session_expiry and datetime.now() > self.session_expiry):
            logger.info("[WORKFLOW] Session expired, re-authenticating...")
            return await self.authenticate()
        return True
    
    def _rate_limit(self):
        """Apply aggressive rate limiting to prevent API errors (only for real mode)"""
        if self.is_paper_mode:
            return # No rate limiting in paper mode

        current_time = time.time()

        # Ensure minimum interval between requests
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            logger.debug(f"[RATE_LIMIT] Sleeping {sleep_time:.2f}s to respect rate limits")
            time.sleep(sleep_time)
            current_time = time.time()

        # Reset counter every second
        if current_time - self.last_request_time >= 1.0:
            self.request_count = 0
            self.last_request_time = current_time

        # Check rate limit
        if self.request_count >= self.requests_per_second:
            sleep_time = 1.0 - (current_time - self.last_request_time)
            if sleep_time > 0:
                logger.debug(f"[RATE_LIMIT] Rate limit reached, sleeping {sleep_time:.2f}s")
                time.sleep(sleep_time)
                self.request_count = 0
                self.last_request_time = time.time()

        self.request_count += 1
        self.last_request_time = time.time()
    
    def _get_cached_data(self, key: str) -> Optional[Any]:
        """Get cached data if still valid"""
        if self.is_paper_mode:
            return None # No caching in paper mode for simplicity, or implement a separate paper cache
        
        if key in self.cache:
            data, timestamp = self.cache[key]
            if (datetime.now() - timestamp).total_seconds() < self.cache_duration:
                return data
        return None
    
    def _set_cached_data(self, key: str, data: Any):
        """Set data in cache"""
        if self.is_paper_mode:
            return # No caching in paper mode for simplicity
        self.cache[key] = (data, datetime.now())

    async def _get_symbol_token(self, symbol: str, exchange: str) -> Optional[str]:
        """
        Get symbol token for a given symbol and exchange, or simulate in paper mode

        Args:
            symbol: Trading symbol (e.g., 'RELIANCE-EQ')
            exchange: Exchange (NSE/BSE)

        Returns:
            Symbol token as string or None if not found
        """
        if self.is_paper_mode:
            logger.debug(f"[INFO] Simulating symbol token for {symbol} in paper mode.")
            # Return a dummy token for paper trading
            return f"PAPER_TOKEN_{symbol}"

        try:
            # Check cache first
            cache_key = f"token_{exchange}_{symbol}"
            cached_token = self._get_cached_data(cache_key)
            if cached_token:
                return cached_token

            if not await self._ensure_authenticated():
                return None

            self._rate_limit()

            # Extract base symbol name (remove -EQ suffix if present)
            base_symbol = symbol.replace('-EQ', '').replace('-BE', '')

            # Search for the symbol
            logger.debug(f"[DEBUG] Searching for symbol token: {base_symbol} on {exchange}")
            search_result = self.smart_api.searchScrip(exchange, base_symbol)

            if not search_result or not search_result.get('status'):
                logger.error(f"[ERROR] Symbol search failed for {symbol}")
                return None

            # Parse search results
            search_data = search_result.get('data', [])
            if not search_data:
                logger.error(f"[ERROR] No search results for {symbol}")
                return None

            # Find exact match
            for item in search_data:
                if item.get('tradingsymbol') == symbol:
                    token = item.get('symboltoken')
                    if token:
                        # Cache the result
                        self._set_cached_data(cache_key, token)
                        logger.debug(f"[SUCCESS] Found token for {symbol}: {token}")
                        return token

            # If exact match not found, try the first result
            first_result = search_data[0]
            token = first_result.get('symboltoken')
            if token:
                self._set_cached_data(cache_key, token)
                logger.warning(f"[WARN] Using approximate match for {symbol}: {first_result.get('tradingsymbol')} -> {token}")
                return token

            logger.error(f"[ERROR] No token found for {symbol}")
            return None

        except Exception as e:
            logger.error(f"[ERROR] Error getting symbol token for {symbol}: {e}")
            return None

    async def get_margin_requirement(self, symbol: str, exchange: str, quantity: int,
                                   price: float, transaction_type: str = "BUY", 
                                   product_type: str = "MIS") -> Optional[MarginRequirement]:
        """
        Calculate margin requirement for a trade, or simulate in paper mode
        
        Args:
            symbol: Trading symbol (e.g., "RELIANCE-EQ")
            exchange: Exchange (NSE, BSE)
            quantity: Number of shares
            price: Price per share
            transaction_type: BUY or SELL
            product_type: MIS, CNC, or NRML
        
        Returns:
            MarginRequirement object or None if failed
        """
        if self.is_paper_mode:
            logger.debug(f"[INFO] Simulating margin requirement for {symbol} in paper mode.")
            # Simulate a margin requirement for paper trading
            simulated_margin_required = quantity * price * 0.15 # Example: 15% margin
            simulated_available_margin = self.config.get('capital_allocation', {}).get('total_capital', 100000) # Use total capital as available margin
            
            return MarginRequirement(
                symbol=symbol,
                exchange=exchange,
                quantity=quantity,
                price=price,
                product_type=product_type,
                transaction_type=transaction_type,
                margin_required=simulated_margin_required,
                available_margin=simulated_available_margin,
                is_allowed=True, # Always allowed in paper mode for simplicity
                limit_used_percent=(simulated_margin_required / simulated_available_margin * 100) if simulated_available_margin > 0 else 0,
                error_message=None
            )

        try:
            if not await self._ensure_authenticated():
                return None
            
            self._rate_limit()
            
            # Get symbol token for the margin calculation
            symbol_token = await self._get_symbol_token(symbol, exchange)
            if not symbol_token:
                logger.error(f"[ERROR] Could not find token for {symbol}")
                return MarginRequirement(
                    symbol=symbol,
                    exchange=exchange,
                    quantity=quantity,
                    price=price,
                    product_type=product_type,
                    transaction_type=transaction_type,
                    margin_required=0,
                    available_margin=0,
                    is_allowed=False,
                    limit_used_percent=0,
                    error_message="Symbol token not found"
                )

            # Map product types to SmartAPI format
            product_type_map = {
                "MIS": "INTRADAY",
                "CNC": "DELIVERY",
                "NRML": "CARRYFORWARD"
            }
            smart_product_type = product_type_map.get(product_type, "INTRADAY")

            # Prepare margin calculation parameters (exact SmartAPI format)
            margin_params = {
                "positions": [{
                    "exchange": exchange,
                    "qty": quantity,
                    "price": price if price > 0 else 0,  # Use 0 for market orders
                    "productType": smart_product_type,
                    "token": str(symbol_token),
                    "tradeType": transaction_type
                }]
            }

            # Call margin API
            logger.debug(f"[STATUS] Calculating margin for {symbol}: {quantity} @ {price}")
            logger.debug(f"[STATUS] Margin params: {margin_params}")
            response = self.smart_api.getMarginApi(margin_params)
            
            if not response or not response.get('status'):
                error_msg = response.get('message', 'Unknown error') if response else 'No response'
                logger.error(f"[ERROR] Margin calculation failed: {error_msg}")
                return MarginRequirement(
                    symbol=symbol,
                    exchange=exchange,
                    quantity=quantity,
                    price=price,
                    product_type=product_type,
                    transaction_type=transaction_type,
                    margin_required=0,
                    available_margin=0,
                    is_allowed=False,
                    limit_used_percent=0,
                    error_message=error_msg
                )

            # Parse response - SmartAPI returns margin data in 'data' field
            margin_data = response.get('data', {})
            logger.debug(f"[STATUS] Margin response data: {margin_data}")

            # Extract margin information (field names from SmartAPI documentation)
            margin_required = float(margin_data.get('totalMarginRequired',
                                  margin_data.get('total',
                                  margin_data.get('marginRequired', 0))))

            # Get available margin from RMS limits
            rms_limits = await self.get_rms_limits()
            available_margin = rms_limits.available_margin if rms_limits else 0.0

            # Calculate if trade is allowed
            is_allowed = margin_required <= available_margin if available_margin > 0 else False
            limit_used_percent = (margin_required / available_margin * 100) if available_margin > 0 else 100

            logger.info(f"[SUCCESS] Margin calculation for {symbol}:")
            logger.info(f"   Quantity: {quantity}, Price: Rs.{price:.2f}")
            logger.info(f"   Margin Required: Rs.{margin_required:,.2f}")
            logger.info(f"   Available Margin: Rs.{available_margin:,.2f}")
            logger.info(f"   Trade Allowed: {is_allowed}")

            return MarginRequirement(
                symbol=symbol,
                exchange=exchange,
                quantity=quantity,
                price=price,
                product_type=product_type,
                transaction_type=transaction_type,
                margin_required=margin_required,
                available_margin=available_margin,
                is_allowed=is_allowed,
                limit_used_percent=limit_used_percent,
                error_message=None
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating margin requirement: {e}")
            return MarginRequirement(
                symbol=symbol,
                exchange=exchange,
                quantity=quantity,
                price=price,
                product_type=product_type,
                transaction_type=transaction_type,
                margin_required=0,
                available_margin=0,
                is_allowed=False,
                limit_used_percent=0,
                error_message=str(e)
            )
    
    async def get_rms_limits(self) -> Optional[RMSLimits]:
        """
        Get RMS (Risk Management System) limits, or simulate in paper mode
        
        Returns:
            RMSLimits object or None if failed
        """
        if self.is_paper_mode:
            logger.debug("[INFO] Simulating RMS limits in paper mode.")
            # Simulate RMS limits for paper trading
            total_capital = self.config.get('capital_allocation', {}).get('total_capital', 100000)
            return RMSLimits(
                net_available_cash=total_capital,
                available_margin=total_capital,
                utilized_margin=0.0,
                gross_collateral_value=total_capital,
                net_collateral_value=total_capital,
                margin_used_percent=0.0,
                cash_margin_available=total_capital,
                category_limits={},
                timestamp=datetime.now()
            )

        try:
            # Check cache first
            cached_data = self._get_cached_data('rms_limits')
            if cached_data:
                return cached_data
            
            if not await self._ensure_authenticated():
                return None
            
            self._rate_limit()
            
            logger.debug("[STATUS] Fetching RMS limits...")
            response = self.smart_api.rmsLimit()
            
            if not response.get('status'):
                logger.error(f"[ERROR] Failed to fetch RMS limits: {response.get('message')}")
                return None
            
            # Parse RMS data
            rms_data = response.get('data', {})
            
            rms_limits = RMSLimits(
                net_available_cash=float(rms_data.get('net', 0)),
                available_margin=float(rms_data.get('availablemargin', 0)),
                utilized_margin=float(rms_data.get('utilisedmargin', 0)),
                gross_collateral_value=float(rms_data.get('grosscollateralvalue', 0)),
                net_collateral_value=float(rms_data.get('netcollateralvalue', 0)),
                margin_used_percent=float(rms_data.get('marginused', 0)),
                cash_margin_available=float(rms_data.get('cashmarginavailable', 0)),
                category_limits=rms_data.get('category', {}),
                timestamp=datetime.now()
            )
            
            # Cache the data
            self._set_cached_data('rms_limits', rms_limits)
            
            logger.debug(f"[SUCCESS] RMS limits fetched: Available margin: Rs.{rms_limits.available_margin:,.2f}")
            return rms_limits
            
        except Exception as e:
            logger.error(f"[ERROR] Error fetching RMS limits: {e}")
            return None

    async def get_positions(self) -> List[PositionData]:
        """
        Get current positions, or simulate in paper mode

        Returns:
            List of PositionData objects
        """
        if self.is_paper_mode:
            logger.debug("[INFO] Simulating positions in paper mode.")
            # Return a dummy list of positions for paper trading
            return [] # Start with no positions in paper mode for simplicity

        try:
            if not await self._ensure_authenticated():
                return []

            self._rate_limit()

            logger.debug("[STATUS] Fetching positions...")

            # Retry mechanism for rate limiting
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.smart_api.position()

                    if response.get('status'):
                        break  # Success, exit retry loop

                    error_msg = response.get('message', 'Unknown error')
                    if 'access rate' in error_msg.lower():
                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 5  # Exponential backoff: 5s, 10s, 15s
                            logger.warning(f"[WARN] Rate limit exceeded, waiting {wait_time}s before retry {attempt + 1}/{max_retries}")
                            time.sleep(wait_time)
                            continue
                        else:
                            logger.error("[ERROR] Rate limit exceeded after all retries")
                            return []
                    else:
                        logger.error(f"[ERROR] Failed to fetch positions: {error_msg}")
                        return []

                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"[WARN] API call failed, retrying {attempt + 1}/{max_retries}: {e}")
                        time.sleep(2)
                        continue
                    else:
                        logger.error(f"[ERROR] API call failed after all retries: {e}")
                        return []

            positions = []
            position_data = response.get('data', [])

            # Handle case where position_data might be None
            if position_data is None:
                logger.warning("[WARN] Position data is None, returning empty list")
                return []

            for pos in position_data:
                position = PositionData(
                    symbol=pos.get('tradingsymbol', ''),
                    exchange=pos.get('exchange', ''),
                    product_type=pos.get('producttype', ''),
                    quantity=int(pos.get('netqty', 0)),
                    average_price=float(pos.get('avgnetprice', 0)),
                    last_price=float(pos.get('ltp', 0)),
                    pnl=float(pos.get('pnl', 0)),
                    pnl_percent=float(pos.get('pnlpercent', 0)),
                    market_value=float(pos.get('netvalue', 0)),
                    unrealized_pnl=float(pos.get('unrealised', 0)),
                    realized_pnl=float(pos.get('realised', 0)),
                    timestamp=datetime.now()
                )
                positions.append(position)

            logger.debug(f"[SUCCESS] Fetched {len(positions)} positions")
            return positions

        except Exception as e:
            logger.error(f"[ERROR] Error fetching positions: {e}")
            return []

    async def get_funds(self) -> Optional[FundData]:
        """
        Get fund and balance information, or simulate in paper mode

        Returns:
            FundData object or None if failed
        """
        if self.is_paper_mode:
            logger.debug("[INFO] Simulating fund data in paper mode.")
            # Simulate fund data for paper trading
            total_capital = self.config.get('capital_allocation', {}).get('total_capital', 100000)
            return FundData(
                available_cash=total_capital,
                utilized_margin=0.0,
                available_margin=total_capital,
                collateral_value=total_capital,
                m2m_unrealized=0.0,
                m2m_realized=0.0,
                opening_balance=total_capital,
                payin=0.0,
                payout=0.0,
                timestamp=datetime.now()
            )

        try:
            # Check cache first
            cached_data = self._get_cached_data('funds')
            if cached_data:
                return cached_data

            if not await self._ensure_authenticated():
                return None

            self._rate_limit()

            logger.debug("[STATUS] Fetching fund data...")
            response = self.smart_api.rmsLimit()  # RMS limit contains fund info

            if not response.get('status'):
                logger.error(f"[ERROR] Failed to fetch fund data: {response.get('message')}")
                return None

            # Parse fund data
            fund_info = response.get('data', {})

            fund_data = FundData(
                available_cash=float(fund_info.get('net', 0)),
                utilized_margin=float(fund_info.get('utilisedmargin', 0)),
                available_margin=float(fund_info.get('availablemargin', 0)),
                collateral_value=float(fund_info.get('netcollateralvalue', 0)),
                m2m_unrealized=float(fund_info.get('m2munrealized', 0)),
                m2m_realized=float(fund_info.get('m2mrealized', 0)),
                opening_balance=float(fund_info.get('openingbalance', 0)),
                payin=float(fund_info.get('payin', 0)),
                payout=float(fund_info.get('payout', 0)),
                timestamp=datetime.now()
            )

            # Cache the data
            self._set_cached_data('funds', fund_data)

            logger.debug(f"[SUCCESS] Fund data fetched: Available cash: Rs.{fund_data.available_cash:,.2f}")
            return fund_data

        except Exception as e:
            logger.error(f"[ERROR] Error fetching fund data: {e}")
            return None

    async def validate_trade_margin(self, symbol: str, exchange: str, quantity: int,
                                  price: float, transaction_type: str = "BUY",
                                  product_type: str = "MIS") -> Tuple[bool, str, Optional[MarginRequirement]]:
        """
        Validate if a trade can be executed based on margin requirements, or simulate in paper mode

        Args:
            symbol: Trading symbol
            exchange: Exchange
            quantity: Number of shares
            price: Price per share
            transaction_type: BUY or SELL
            product_type: MIS, CNC, or NRML

        Returns:
            Tuple of (is_valid, reason, margin_requirement)
        """
        if self.is_paper_mode:
            logger.debug(f"[INFO] Simulating trade margin validation for {symbol} in paper mode.")
            # Simulate a successful margin validation for paper trading
            simulated_margin_required = quantity * price * 0.15 # Example: 15% margin
            simulated_available_margin = self.config.get('capital_allocation', {}).get('total_capital', 100000)
            
            simulated_margin_req = MarginRequirement(
                symbol=symbol,
                exchange=exchange,
                quantity=quantity,
                price=price,
                product_type=product_type,
                transaction_type=transaction_type,
                margin_required=simulated_margin_required,
                available_margin=simulated_available_margin,
                is_allowed=True,
                limit_used_percent=(simulated_margin_required / simulated_available_margin * 100) if simulated_available_margin > 0 else 0,
                error_message=None
            )
            return True, "Simulated trade validation successful", simulated_margin_req

        try:
            # Get margin requirement
            margin_req = await self.get_margin_requirement(
                symbol, exchange, quantity, price, transaction_type, product_type
            )

            if not margin_req:
                return False, "Failed to calculate margin requirement", None

            if margin_req.error_message:
                return False, f"Margin calculation error: {margin_req.error_message}", margin_req

            if not margin_req.is_allowed:
                return False, "Trade not allowed by broker", margin_req

            # Check if sufficient margin is available
            if margin_req.margin_required > margin_req.available_margin:
                return False, f"Insufficient margin: Required Rs.{margin_req.margin_required:,.2f}, Available Rs.{margin_req.available_margin:,.2f}", margin_req

            # Check margin buffer (keep some buffer for safety)
            margin_buffer_percent = self.config.get('capital_allocation', {}).get('intraday_margin', {}).get('margin_buffer_percent', 10.0)
            required_with_buffer = margin_req.margin_required * (1 + margin_buffer_percent / 100)

            if required_with_buffer > margin_req.available_margin:
                return False, f"Insufficient margin with buffer: Required Rs.{required_with_buffer:,.2f}, Available Rs.{margin_req.available_margin:,.2f}", margin_req

            return True, "Trade validation successful", margin_req

        except Exception as e:
            logger.error(f"[ERROR] Error validating trade margin: {e}")
            return False, f"Validation error: {str(e)}", None

    async def get_ltp(self, symbol: str, exchange: str, symbol_token: str) -> Optional[float]:
        """
        Get Last Traded Price (LTP) for a symbol, or simulate in paper mode

        Args:
            symbol: Trading symbol
            exchange: Exchange
            symbol_token: Symbol token

        Returns:
            LTP or None if failed
        """
        if self.is_paper_mode:
            logger.debug(f"[INFO] Simulating LTP for {symbol} in paper mode.")
            import random
            # Simulate a random LTP within a reasonable range
            return random.uniform(1000, 5000) # Example price range

        try:
            if not await self._ensure_authenticated():
                return None

            self._rate_limit()

            response = self.smart_api.ltpData(exchange, symbol, symbol_token)

            if not response.get('status'):
                logger.error(f"[ERROR] Failed to fetch LTP for {symbol}: {response.get('message')}")
                return None

            ltp_data = response.get('data', {})
            ltp = float(ltp_data.get('ltp', 0))

            return ltp if ltp > 0 else None

        except Exception as e:
            logger.error(f"[ERROR] Error fetching LTP for {symbol}: {e}")
            return None

    async def search_symbol(self, exchange: str, search_term: str) -> List[Dict[str, Any]]:
        """
        Search for trading symbols, or simulate in paper mode

        Args:
            exchange: Exchange (NSE, BSE)
            search_term: Search term

        Returns:
            List of symbol information
        """
        if self.is_paper_mode:
            logger.debug(f"[INFO] Simulating symbol search for {search_term} in paper mode.")
            # Return a dummy search result for paper trading
            return [
                {"tradingsymbol": f"{search_term}-EQ", "exchange": exchange, "symboltoken": f"PAPER_TOKEN_{search_term}"}
            ]

        try:
            if not await self._ensure_authenticated():
                return []

            self._rate_limit()

            response = self.smart_api.searchScrip(exchange, search_term)

            if not response.get('status'):
                logger.error(f"[ERROR] Symbol search failed: {response.get('message')}")
                return []

            return response.get('data', [])

        except Exception as e:
            logger.error(f"[ERROR] Error searching symbols: {e}")
            return []

    async def close_session(self):
        """Close API session, or do nothing in paper mode"""
        if self.is_paper_mode:
            logger.info("[INFO] Skipping Angel One API session closure in paper trading mode.")
            self.session_active = False
            return

        try:
            if self.session_active and self.user_id:
                self.smart_api.terminateSession(self.user_id)
                logger.info("[SUCCESS] API session closed")

            self.session_active = False
            self.auth_token = None
            self.refresh_token = None
            self.feed_token = None

        except Exception as e:
            logger.error(f"[ERROR] Error closing session: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def calculate_position_size(capital: float, risk_percent: float, entry_price: float,
                          stop_loss: float, margin_multiplier: float = 1.0) -> int:
    """
    Calculate position size based on risk management parameters

    Args:
        capital: Total capital
        risk_percent: Risk percentage (1.0 for 1%)
        entry_price: Entry price
        stop_loss: Stop loss price
        margin_multiplier: Margin multiplier for intraday (e.g., 3.5)

    Returns:
        Quantity to trade
    """
    try:
        risk_amount = capital * (risk_percent / 100)
        price_diff = abs(entry_price - stop_loss)

        if price_diff == 0:
            return 0

        # Calculate base quantity
        base_quantity = int(risk_amount / price_diff)

        # Adjust for margin if applicable
        if margin_multiplier > 1:
            max_order_value = capital * margin_multiplier
            max_quantity_by_margin = int(max_order_value / entry_price)
            base_quantity = min(base_quantity, max_quantity_by_margin)

        return max(0, base_quantity)

    except Exception as e:
        logger.error(f"[ERROR] Error calculating position size: {e}")
        return 0

def format_currency(amount: float) -> str:
    """Format currency amount for display"""
    return f"Rs.{amount:,.2f}"

def calculate_margin_utilization(used_margin: float, available_margin: float) -> float:
    """Calculate margin utilization percentage"""
    total_margin = used_margin + available_margin
    if total_margin == 0:
        return 0
    return (used_margin / total_margin) * 100

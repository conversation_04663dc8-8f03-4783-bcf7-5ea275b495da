#!/usr/bin/env python3
"""
🚀 CUDA Performance Optimizer for RTX 3060Ti
Optimizes CUDA settings and memory management for maximum backtesting speed
"""

import os
import logging
import yaml
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import gc

logger = logging.getLogger(__name__)

class CUDAOptimizer:
    """CUDA Performance optimizer for RTX 3060Ti"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/cuda_optimization_config.yaml"
        self.config = self._load_config()
        self.cuda_available = False
        self.device_info = {}
        
        # Initialize CUDA if available
        self._initialize_cuda()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load CUDA optimization configuration"""
        try:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r') as f:
                    return yaml.safe_load(f)
            else:
                logger.warning(f"Config file not found: {self.config_path}, using defaults")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default CUDA configuration for RTX 3060Ti"""
        return {
            'cuda_settings': {
                'memory_fraction': 0.90,
                'benchmark_mode': True,
                'deterministic': False,
                'batch_size': 16384,
                'chunk_size': 100000,
                'cuda_threads_per_block': 1024,
                'max_blocks_per_grid': 4096,
                'use_mixed_precision': True,
                'default_dtype': 'float16'
            },
            'thresholds': {
                'min_rows_for_cuda': 500,
                'min_signals_for_cuda': 5,
                'min_trades_for_cuda': 2
            },
            'memory': {
                'aggressive_cleanup': True,
                'gc_frequency': 50,
                'clear_cache_frequency': 250
            }
        }
    
    def _initialize_cuda(self):
        """Initialize CUDA with optimal settings"""
        try:
            # Try PyTorch first
            import torch
            if torch.cuda.is_available():
                self.cuda_available = True
                
                # Set memory fraction
                memory_fraction = self.config['cuda_settings']['memory_fraction']
                torch.cuda.set_per_process_memory_fraction(memory_fraction)
                
                # Enable optimizations
                torch.backends.cudnn.benchmark = self.config['cuda_settings']['benchmark_mode']
                torch.backends.cudnn.deterministic = self.config['cuda_settings']['deterministic']
                
                # Enable TF32 for RTX 30 series if available
                if hasattr(torch.backends.cuda.matmul, 'allow_tf32'):
                    torch.backends.cuda.matmul.allow_tf32 = True
                    torch.backends.cudnn.allow_tf32 = True
                
                # Get device info
                device = torch.cuda.current_device()
                self.device_info = {
                    'name': torch.cuda.get_device_name(device),
                    'memory_total': torch.cuda.get_device_properties(device).total_memory,
                    'memory_allocated': torch.cuda.memory_allocated(device),
                    'memory_reserved': torch.cuda.memory_reserved(device)
                }
                
                logger.info(f"🚀 CUDA initialized: {self.device_info['name']}")
                logger.info(f"📊 Memory: {self.device_info['memory_total'] / 1024**3:.1f} GB total")
                
        except ImportError:
            logger.warning("PyTorch not available, trying Numba CUDA")
            
        # Try Numba CUDA as fallback
        if not self.cuda_available:
            try:
                from numba import cuda
                cuda.detect()
                self.cuda_available = True
                
                device = cuda.get_current_device()
                meminfo = cuda.current_context().get_memory_info()
                
                self.device_info = {
                    'name': device.name.decode(),
                    'memory_total': meminfo[1],
                    'memory_free': meminfo[0]
                }
                
                logger.info(f"⚡ Numba CUDA initialized: {self.device_info['name']}")
                
            except Exception as e:
                logger.warning(f"CUDA not available: {e}")
                self.cuda_available = False
    
    def optimize_for_backtesting(self) -> Dict[str, Any]:
        """Apply optimal settings for backtesting workload"""
        if not self.cuda_available:
            return {'cuda_enabled': False, 'message': 'CUDA not available'}
        
        optimizations = {
            'cuda_enabled': True,
            'optimizations_applied': []
        }
        
        try:
            # PyTorch optimizations
            import torch
            
            # Clear cache
            torch.cuda.empty_cache()
            optimizations['optimizations_applied'].append('cache_cleared')
            
            # Set optimal data types
            torch.set_default_dtype(torch.float32)
            optimizations['optimizations_applied'].append('float32_default')
            
            # Enable mixed precision if supported
            if self.config['cuda_settings']['use_mixed_precision']:
                # This would be used in actual training/inference
                optimizations['optimizations_applied'].append('mixed_precision_ready')
            
            logger.info("🚀 PyTorch CUDA optimizations applied")
            
        except ImportError:
            pass
        
        # Set environment variables for optimal performance
        os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # Async execution
        os.environ['CUDA_CACHE_DISABLE'] = '0'    # Enable caching
        
        # Numba optimizations
        try:
            os.environ['NUMBA_CUDA_DEBUGINFO'] = '0'
            os.environ['NUMBA_CUDA_FASTMATH'] = '1'
            optimizations['optimizations_applied'].append('numba_fastmath')
        except Exception:
            pass
        
        return optimizations
    
    def get_optimal_batch_size(self, data_size: int, base_batch_size: Optional[int] = None) -> int:
        """Calculate optimal batch size based on available memory and data size"""
        if not self.cuda_available:
            return min(data_size, 1000)  # CPU fallback
        
        base_batch = base_batch_size or self.config['cuda_settings']['batch_size']
        
        # Adjust based on available memory
        try:
            import torch
            if torch.cuda.is_available():
                free_memory = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()
                memory_gb = free_memory / 1024**3
                
                # Scale batch size based on available memory
                if memory_gb > 6:
                    multiplier = 2.0
                elif memory_gb > 4:
                    multiplier = 1.5
                elif memory_gb > 2:
                    multiplier = 1.0
                else:
                    multiplier = 0.5
                
                optimal_batch = int(base_batch * multiplier)
                return min(optimal_batch, data_size)
        except Exception:
            pass
        
        return min(base_batch, data_size)
    
    def should_use_cuda(self, data_size: int, signal_count: int = 0, trade_count: int = 0) -> bool:
        """Determine if CUDA should be used based on data characteristics"""
        if not self.cuda_available:
            return False
        
        thresholds = self.config['thresholds']
        
        # Check size thresholds
        if data_size < thresholds['min_rows_for_cuda']:
            return False
        
        if signal_count > 0 and signal_count < thresholds['min_signals_for_cuda']:
            return False
        
        if trade_count > 0 and trade_count < thresholds['min_trades_for_cuda']:
            return False
        
        return True
    
    def get_cuda_kernel_config(self, data_size: int) -> Tuple[int, int]:
        """Get optimal CUDA kernel configuration"""
        threads_per_block = self.config['cuda_settings']['cuda_threads_per_block']
        max_blocks = self.config['cuda_settings']['max_blocks_per_grid']
        
        blocks_per_grid = min((data_size + threads_per_block - 1) // threads_per_block, max_blocks)
        
        return blocks_per_grid, threads_per_block
    
    def cleanup_memory(self, force: bool = False):
        """Cleanup GPU memory"""
        if not self.cuda_available:
            return
        
        try:
            import torch
            if torch.cuda.is_available():
                if force:
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                else:
                    # Gentle cleanup
                    torch.cuda.empty_cache()
        except ImportError:
            pass
        
        # Python garbage collection
        gc.collect()
    
    def get_memory_info(self) -> Dict[str, Any]:
        """Get current GPU memory information"""
        if not self.cuda_available:
            return {'cuda_available': False}
        
        try:
            import torch
            if torch.cuda.is_available():
                device = torch.cuda.current_device()
                return {
                    'cuda_available': True,
                    'device_name': torch.cuda.get_device_name(device),
                    'memory_allocated': torch.cuda.memory_allocated(device) / 1024**3,
                    'memory_reserved': torch.cuda.memory_reserved(device) / 1024**3,
                    'memory_total': torch.cuda.get_device_properties(device).total_memory / 1024**3
                }
        except ImportError:
            pass
        
        return {'cuda_available': True, 'backend': 'numba'}
    
    def optimize_polars_for_gpu(self):
        """Configure Polars for GPU acceleration"""
        try:
            import polars as pl
            
            # Set larger chunk sizes for GPU processing
            chunk_size = self.config.get('polars', {}).get('streaming_chunk_size', 100000)
            pl.Config.set_streaming_chunk_size(chunk_size)
            
            logger.info(f"📊 Polars configured for GPU: chunk_size={chunk_size}")
            
        except Exception as e:
            logger.warning(f"Could not configure Polars for GPU: {e}")

# Global optimizer instance (initially None for lazy loading)
_global_cuda_optimizer: Optional[CUDAOptimizer] = None

def get_cuda_optimizer() -> CUDAOptimizer:
    """Get the global CUDA optimizer instance, initializing if necessary"""
    global _global_cuda_optimizer
    if _global_cuda_optimizer is None:
        logger.debug("Instantiating CUDAOptimizer for the first time...")
        _global_cuda_optimizer = CUDAOptimizer()
        logger.debug("CUDAOptimizer instantiated.")
    return _global_cuda_optimizer

def optimize_cuda_for_backtesting():
    """Quick function to optimize CUDA for backtesting"""
    # Ensure optimizer is initialized before calling its method
    optimizer = get_cuda_optimizer()
    return optimizer.optimize_for_backtesting()

#!/usr/bin/env python3
"""
Dynamic Stock Selection Workflow
Implements intelligent stock selection based on market conditions and real-time analysis
"""

import asyncio
import logging
import json
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import pytz

# Local imports
from utils.robust_websocket_manager import RobustWebSocketManager, ConnectionState
from utils.stock_universe import StockUniverse, StockInfo

logger = logging.getLogger(__name__)

class MarketPhase(Enum):
    PRE_MARKET = "pre_market"
    OPENING = "opening"
    ACTIVE_TRADING = "active_trading"
    CLOSING = "closing"
    POST_MARKET = "post_market"
    CLOSED = "closed"

@dataclass
class MarketCondition:
    phase: MarketPhase
    volatility_level: str  # low, medium, high
    trend_direction: str   # bullish, bearish, sideways
    volume_profile: str    # low, normal, high
    sector_rotation: List[str]  # active sectors
    market_breadth: float  # percentage of stocks advancing

@dataclass
class StockSelectionCriteria:
    max_stocks: int = 20
    min_liquidity: float = 1000000  # minimum daily volume
    max_volatility: float = 0.05    # maximum daily volatility
    min_market_cap: str = "Mid"     # Large, Mid, Small
    sector_diversification: bool = True
    momentum_threshold: float = 0.02  # minimum momentum score
    volume_spike_threshold: float = 2.0  # volume spike threshold

class DynamicStockSelectionWorkflow:
    """Intelligent workflow for dynamic stock selection and monitoring"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Components
        self.websocket_manager = RobustWebSocketManager(config.get('websocket', {}))
        self.stock_universe = StockUniverse()
        
        # Market timing
        self.ist_timezone = pytz.timezone('Asia/Kolkata')
        self.market_open_time = time(9, 15)  # 9:15 AM IST
        self.market_close_time = time(15, 30)  # 3:30 PM IST
        
        # Workflow state
        self.current_phase = MarketPhase.CLOSED
        self.market_condition = None
        self.all_stocks_data = {}
        self.selected_stocks = []
        self.monitoring_active = False
        
        # Configuration
        self.initial_monitoring_duration = config.get('initial_monitoring_minutes', 30)
        self.reselection_interval = config.get('reselection_interval_hours', 2)
        self.selection_criteria = StockSelectionCriteria(**config.get('selection_criteria', {}))
        
        # Callbacks
        self.on_stocks_selected_callback = None
        self.on_market_data_callback = None
        
        logger.info("[INIT] Dynamic Stock Selection Workflow initialized")
    
    def set_callbacks(self, 
                     on_stocks_selected: Optional[callable] = None,
                     on_market_data: Optional[callable] = None):
        """Set callback functions for workflow events"""
        self.on_stocks_selected_callback = on_stocks_selected
        self.on_market_data_callback = on_market_data
    
    async def start_workflow(self) -> bool:
        """Start the dynamic stock selection workflow"""
        try:
            logger.info("[WORKFLOW] Starting dynamic stock selection workflow...")
            
            # Step 1: Initialize stock universe
            if not await self._initialize_stock_universe():
                logger.error("[ERROR] Failed to initialize stock universe")
                return False
            
            # Step 2: Detect market phase and wait if necessary
            await self._detect_and_wait_for_market()
            
            # Step 3: Start initial broad monitoring
            if not await self._start_initial_monitoring():
                logger.error("[ERROR] Failed to start initial monitoring")
                return False
            
            # Step 4: Begin the selection and monitoring cycle
            await self._run_selection_cycle()
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Workflow startup failed: {e}")
            return False
    
    async def _initialize_stock_universe(self) -> bool:
        """Initialize the stock universe for selection"""
        try:
            logger.info("[INIT] Loading stock universe...")
            
            if not self.stock_universe.load_stock_universe():
                logger.error("[ERROR] Failed to load stock universe")
                return False
            
            all_stocks = self.stock_universe.get_all_stocks()
            logger.info(f"[INIT] Loaded {len(all_stocks)} stocks in universe")
            
            # Filter to get the top 500 most liquid stocks
            liquid_stocks = self._get_top_liquid_stocks(all_stocks, 500)
            logger.info(f"[INIT] Selected top {len(liquid_stocks)} liquid stocks for monitoring")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Stock universe initialization failed: {e}")
            return False
    
    def _get_top_liquid_stocks(self, stocks: List[StockInfo], count: int) -> List[StockInfo]:
        """Get top liquid stocks based on market cap and trading volume"""
        try:
            # Filter by market cap (Large and Mid cap preferred)
            filtered_stocks = [
                stock for stock in stocks
                if stock.market_cap in ["Large", "Mid"] and stock.is_active
            ]
            
            # Sort by market cap priority and liquidity indicators
            def liquidity_score(stock):
                cap_score = {"Large": 3, "Mid": 2, "Small": 1}.get(stock.market_cap, 0)
                nifty_bonus = 2 if stock.nifty_50 else (1 if stock.nifty_500 else 0)
                return cap_score + nifty_bonus
            
            filtered_stocks.sort(key=liquidity_score, reverse=True)
            
            return filtered_stocks[:count]
            
        except Exception as e:
            logger.error(f"[ERROR] Liquidity filtering failed: {e}")
            return stocks[:count]  # Fallback
    
    async def _detect_and_wait_for_market(self):
        """Detect current market phase and wait appropriately"""
        try:
            current_time = datetime.now(self.ist_timezone).time()
            current_phase = self._get_market_phase(current_time)
            
            logger.info(f"[MARKET] Current market phase: {current_phase.value}")
            self.current_phase = current_phase
            
            if current_phase == MarketPhase.CLOSED:
                # Wait for market to open
                next_open = self._get_next_market_open()
                wait_time = (next_open - datetime.now(self.ist_timezone)).total_seconds()
                
                if wait_time > 0:
                    logger.info(f"[MARKET] Market closed. Waiting {wait_time/3600:.1f} hours for market open")
                    await asyncio.sleep(min(wait_time, 3600))  # Wait max 1 hour at a time
                    return await self._detect_and_wait_for_market()  # Recursive check
            
            elif current_phase == MarketPhase.PRE_MARKET:
                # Wait for market opening
                wait_time = self._time_until_market_open()
                logger.info(f"[MARKET] Pre-market phase. Waiting {wait_time/60:.1f} minutes for market open")
                await asyncio.sleep(wait_time)
            
            elif current_phase in [MarketPhase.ACTIVE_TRADING, MarketPhase.OPENING]:
                # Market is open, proceed immediately
                logger.info("[MARKET] Market is active, proceeding with workflow")
            
            else:
                # Market closing or post-market
                logger.info(f"[MARKET] Market in {current_phase.value} phase, proceeding with limited monitoring")
            
        except Exception as e:
            logger.error(f"[ERROR] Market phase detection failed: {e}")
    
    def _get_market_phase(self, current_time: time) -> MarketPhase:
        """Determine current market phase based on time"""
        if current_time < time(9, 0):
            return MarketPhase.CLOSED
        elif current_time < time(9, 15):
            return MarketPhase.PRE_MARKET
        elif current_time < time(9, 30):
            return MarketPhase.OPENING
        elif current_time < time(15, 15):
            return MarketPhase.ACTIVE_TRADING
        elif current_time < time(15, 30):
            return MarketPhase.CLOSING
        elif current_time < time(16, 0):
            return MarketPhase.POST_MARKET
        else:
            return MarketPhase.CLOSED
    
    def _get_next_market_open(self) -> datetime:
        """Get the next market opening time"""
        now = datetime.now(self.ist_timezone)
        today_open = now.replace(hour=9, minute=15, second=0, microsecond=0)
        
        if now.time() < self.market_open_time:
            return today_open
        else:
            # Next trading day (skip weekends)
            next_day = now + timedelta(days=1)
            while next_day.weekday() >= 5:  # Skip Saturday (5) and Sunday (6)
                next_day += timedelta(days=1)
            return next_day.replace(hour=9, minute=15, second=0, microsecond=0)
    
    def _time_until_market_open(self) -> float:
        """Get seconds until market opens"""
        now = datetime.now(self.ist_timezone)
        next_open = self._get_next_market_open()
        return max(0, (next_open - now).total_seconds())
    
    async def _start_initial_monitoring(self) -> bool:
        """Start monitoring all 500 stocks initially"""
        try:
            logger.info("[MONITOR] Starting initial broad monitoring of 500 stocks...")
            
            # Set up WebSocket callbacks
            self.websocket_manager.set_callbacks(
                on_connected=self._on_websocket_connected,
                on_data=self._on_market_data_received,
                on_error=self._on_websocket_error,
                on_disconnected=self._on_websocket_disconnected
            )
            
            # Connect WebSocket
            if not await self.websocket_manager.connect():
                logger.error("[ERROR] Failed to establish WebSocket connection")
                return False
            
            # Subscribe to all 500 stocks
            all_stocks = self.stock_universe.get_all_stocks()[:500]
            stock_symbols = [stock.symbol for stock in all_stocks]
            
            if not await self.websocket_manager.subscribe_symbols(stock_symbols):
                logger.error("[ERROR] Failed to subscribe to initial stock symbols")
                return False
            
            logger.info(f"[SUCCESS] Started monitoring {len(stock_symbols)} stocks")
            self.monitoring_active = True
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Initial monitoring setup failed: {e}")
            return False
    
    async def _run_selection_cycle(self):
        """Run the main selection and monitoring cycle"""
        try:
            logger.info("[CYCLE] Starting selection and monitoring cycle...")
            
            while self.monitoring_active:
                # Phase 1: Collect data for initial period
                logger.info(f"[CYCLE] Collecting market data for {self.initial_monitoring_duration} minutes...")
                await asyncio.sleep(self.initial_monitoring_duration * 60)
                
                # Phase 2: Analyze market conditions
                market_condition = await self._analyze_market_conditions()
                self.market_condition = market_condition
                
                # Phase 3: Select optimal stocks
                selected_stocks = await self._select_optimal_stocks(market_condition)
                
                if selected_stocks:
                    self.selected_stocks = selected_stocks
                    logger.info(f"[SELECTION] Selected {len(selected_stocks)} optimal stocks")
                    
                    # Phase 4: Switch to monitoring selected stocks only
                    await self._switch_to_selected_monitoring(selected_stocks)
                    
                    # Notify callback
                    if self.on_stocks_selected_callback:
                        self.on_stocks_selected_callback(selected_stocks, market_condition)
                
                # Phase 5: Monitor selected stocks for reselection interval
                logger.info(f"[CYCLE] Monitoring selected stocks for {self.reselection_interval} hours...")
                await asyncio.sleep(self.reselection_interval * 3600)
                
                # Check if market is still active
                current_phase = self._get_market_phase(datetime.now(self.ist_timezone).time())
                if current_phase in [MarketPhase.CLOSED, MarketPhase.POST_MARKET]:
                    logger.info("[CYCLE] Market closed, ending monitoring cycle")
                    break
            
        except Exception as e:
            logger.error(f"[ERROR] Selection cycle failed: {e}")
    
    async def _analyze_market_conditions(self) -> MarketCondition:
        """Analyze current market conditions based on collected data"""
        try:
            logger.info("[ANALYSIS] Analyzing market conditions...")
            
            # Placeholder for market analysis logic
            # In a real implementation, this would analyze:
            # - Overall market volatility
            # - Sector performance
            # - Volume patterns
            # - Trend direction
            
            # For now, return a basic market condition
            return MarketCondition(
                phase=self.current_phase,
                volatility_level="medium",
                trend_direction="sideways",
                volume_profile="normal",
                sector_rotation=["Technology", "Banking", "Pharma"],
                market_breadth=0.55
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Market analysis failed: {e}")
            return MarketCondition(
                phase=self.current_phase,
                volatility_level="medium",
                trend_direction="sideways",
                volume_profile="normal",
                sector_rotation=[],
                market_breadth=0.5
            )
    
    async def _select_optimal_stocks(self, market_condition: MarketCondition) -> List[StockInfo]:
        """Select optimal stocks based on market conditions and criteria"""
        try:
            logger.info("[SELECTION] Selecting optimal stocks based on market conditions...")
            
            all_stocks = self.stock_universe.get_all_stocks()
            
            # Apply selection criteria based on market conditions
            filtered_stocks = []
            
            for stock in all_stocks:
                # Basic filters
                if not stock.is_active:
                    continue
                
                if stock.market_cap not in ["Large", "Mid"]:
                    continue
                
                # Market condition specific filters
                if market_condition.volatility_level == "high":
                    # In high volatility, prefer large cap stocks
                    if stock.market_cap != "Large":
                        continue
                
                # Sector rotation filter
                if market_condition.sector_rotation and stock.sector in market_condition.sector_rotation:
                    filtered_stocks.append(stock)
                elif not market_condition.sector_rotation:
                    filtered_stocks.append(stock)
            
            # Sort by selection criteria and return top stocks
            selected = filtered_stocks[:self.selection_criteria.max_stocks]
            
            logger.info(f"[SELECTION] Selected {len(selected)} stocks: {[s.symbol for s in selected[:5]]}...")
            return selected
            
        except Exception as e:
            logger.error(f"[ERROR] Stock selection failed: {e}")
            return []
    
    async def _switch_to_selected_monitoring(self, selected_stocks: List[StockInfo]):
        """Switch WebSocket monitoring to selected stocks only"""
        try:
            logger.info("[SWITCH] Switching to monitoring selected stocks only...")
            
            # Disconnect current WebSocket
            await self.websocket_manager.disconnect()
            
            # Wait a moment for clean disconnect
            await asyncio.sleep(2)
            
            # Reconnect with selected stocks
            if await self.websocket_manager.connect():
                selected_symbols = [stock.symbol for stock in selected_stocks]
                await self.websocket_manager.subscribe_symbols(selected_symbols)
                logger.info(f"[SUCCESS] Now monitoring {len(selected_symbols)} selected stocks")
            else:
                logger.error("[ERROR] Failed to reconnect for selected stock monitoring")
            
        except Exception as e:
            logger.error(f"[ERROR] Monitoring switch failed: {e}")
    
    def _on_websocket_connected(self):
        """Handle WebSocket connection established"""
        logger.info("[WEBSOCKET] Connected successfully")
    
    def _on_websocket_disconnected(self):
        """Handle WebSocket disconnection"""
        logger.warning("[WEBSOCKET] Disconnected")
    
    def _on_websocket_error(self, error):
        """Handle WebSocket errors"""
        logger.error(f"[WEBSOCKET] Error: {error}")
    
    def _on_market_data_received(self, data):
        """Handle incoming market data"""
        try:
            # Process and store market data
            # This would parse the SmartAPI data format
            
            if self.on_market_data_callback:
                self.on_market_data_callback(data)
                
        except Exception as e:
            logger.error(f"[ERROR] Market data processing failed: {e}")
    
    async def stop_workflow(self):
        """Stop the workflow gracefully"""
        logger.info("[STOP] Stopping dynamic stock selection workflow...")
        
        self.monitoring_active = False
        await self.websocket_manager.disconnect()
        
        logger.info("[STOP] Workflow stopped successfully")
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status"""
        return {
            'current_phase': self.current_phase.value,
            'monitoring_active': self.monitoring_active,
            'selected_stocks_count': len(self.selected_stocks),
            'selected_stocks': [stock.symbol for stock in self.selected_stocks[:10]],
            'market_condition': self.market_condition.__dict__ if self.market_condition else None,
            'websocket_status': self.websocket_manager.get_connection_status()
        }

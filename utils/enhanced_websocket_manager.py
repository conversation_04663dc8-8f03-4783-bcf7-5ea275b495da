#!/usr/bin/env python3
"""
[INIT] Enhanced WebSocket Manager for SmartAPI
Robust real-time data streaming with advanced error handling and performance optimization

Features:
- Intelligent connection management with exponential backoff
- Data validation and quality checks
- Performance monitoring and optimization
- Automatic reconnection with circuit breaker pattern
- Memory-efficient data buffering
- Real-time health monitoring
"""

import os
import sys
import asyncio
import logging
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import numpy as np
from enum import Enum
import websocket
import queue

# Import SmartAPI WebSocket
try:
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("[WARN] SmartAPI not available. Install with: pip install smartapi-python")

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """WebSocket connection states"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"
    CIRCUIT_OPEN = "circuit_open"

@dataclass
class ConnectionMetrics:
    """Connection performance metrics"""
    connection_time: float = 0.0
    total_messages: int = 0
    messages_per_second: float = 0.0
    last_message_time: Optional[datetime] = None
    connection_uptime: float = 0.0
    reconnection_count: int = 0
    error_count: int = 0
    data_quality_score: float = 1.0

@dataclass
class MarketDataTick:
    """Enhanced market data tick with validation"""
    symbol: str
    token: str
    exchange_type: int
    timestamp: datetime
    last_traded_price: float
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    volume: int = 0
    change_percent: float = 0.0
    
    # Data quality indicators
    is_valid: bool = True
    quality_score: float = 1.0
    validation_errors: List[str] = None
    
    def __post_init__(self):
        if self.validation_errors is None:
            self.validation_errors = []
        self.validate_data()
    
    def validate_data(self):
        """Validate tick data quality"""
        self.validation_errors = []
        
        # Price validation
        if self.last_traded_price <= 0:
            self.validation_errors.append("Invalid LTP")
            self.is_valid = False
        
        # OHLC consistency check
        if self.high_price > 0 and self.low_price > 0:
            if self.high_price < self.low_price:
                self.validation_errors.append("High < Low")
                self.quality_score *= 0.8
        
        # Volume validation
        if self.volume < 0:
            self.validation_errors.append("Negative volume")
            self.quality_score *= 0.9
        
        # Timestamp validation
        now = datetime.now()
        if self.timestamp > now + timedelta(minutes=1):
            self.validation_errors.append("Future timestamp")
            self.quality_score *= 0.7
        
        # Calculate overall quality score
        if self.validation_errors:
            self.quality_score = max(0.1, self.quality_score - len(self.validation_errors) * 0.1)

class CircuitBreaker:
    """Circuit breaker for connection management"""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open
    
    def call_failed(self):
        """Record a failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            logger.warning(f"[CIRCUIT] Circuit breaker opened after {self.failure_count} failures")
    
    def call_succeeded(self):
        """Record a successful call"""
        self.failure_count = 0
        self.state = "closed"
    
    def can_execute(self) -> bool:
        """Check if execution is allowed"""
        if self.state == "closed":
            return True
        
        if self.state == "open":
            if time.time() - self.last_failure_time >= self.timeout:
                self.state = "half_open"
                return True
            return False
        
        # half_open state
        return True

class EnhancedWebSocketManager:
    """
    Enhanced WebSocket Manager with robust error handling and performance optimization
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Enhanced WebSocket Manager"""
        
        self.config = config
        self.websocket_config = config.get('websocket', {})
        
        # Connection settings
        self.api_key = config.get('api_key', '')
        self.username = config.get('username', '')
        self.auth_token = None
        self.feed_token = None
        
        # WebSocket instance
        self.websocket = None
        self.connection_state = ConnectionState.DISCONNECTED
        self.circuit_breaker = CircuitBreaker()
        
        # Performance settings
        self.max_symbols_per_batch = self.websocket_config.get('max_symbols_per_batch', 50)
        self.max_reconnection_attempts = self.websocket_config.get('max_reconnection_attempts', 10)
        self.reconnection_delay = self.websocket_config.get('reconnection_delay', 5)
        self.heartbeat_interval = self.websocket_config.get('heartbeat_interval', 30)
        
        # Data management
        self.subscribed_symbols = {}
        self.subscription_batches = []
        self.data_buffer = deque(maxlen=10000)  # Memory-efficient circular buffer
        self.data_callbacks = []
        
        # Metrics and monitoring
        self.metrics = ConnectionMetrics()
        self.performance_monitor = {}
        self.last_heartbeat = None
        
        # Threading
        self.websocket_thread = None
        self.heartbeat_thread = None
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        
        # Data quality tracking
        self.quality_metrics = {
            'total_ticks': 0,
            'valid_ticks': 0,
            'invalid_ticks': 0,
            'quality_score_sum': 0.0
        }
        
        logger.info("[INIT] Enhanced WebSocket Manager initialized")
    
    async def authenticate(self, auth_token: str, feed_token: str) -> bool:
        """Authenticate with SmartAPI"""
        try:
            self.auth_token = auth_token
            self.feed_token = feed_token
            
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI not available")
                return False
            
            logger.info("[AUTH] WebSocket authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Authentication failed: {e}")
            return False
    
    def add_symbols(self, symbols: List[Dict[str, Any]]):
        """Add symbols for subscription with intelligent batching"""
        try:
            # Clear existing subscriptions
            self.subscribed_symbols.clear()
            self.subscription_batches.clear()
            
            # Process and validate symbols
            valid_symbols = []
            for symbol_data in symbols:
                if self._validate_symbol_data(symbol_data):
                    symbol_key = f"{symbol_data['symbol']}_{symbol_data['token']}"
                    self.subscribed_symbols[symbol_key] = symbol_data
                    valid_symbols.append(symbol_data)
            
            # Create intelligent batches
            self._create_intelligent_batches(valid_symbols)
            
            logger.info(f"[SYMBOLS] Added {len(valid_symbols)} symbols in {len(self.subscription_batches)} batches")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add symbols: {e}")
    
    def _validate_symbol_data(self, symbol_data: Dict[str, Any]) -> bool:
        """Validate symbol data"""
        required_fields = ['symbol', 'token', 'exchange']
        
        for field in required_fields:
            if field not in symbol_data or not symbol_data[field]:
                logger.warning(f"[VALIDATION] Missing {field} in symbol data")
                return False
        
        return True
    
    def _create_intelligent_batches(self, symbols: List[Dict[str, Any]]):
        """Create intelligent symbol batches for optimal performance"""
        try:
            # Group by exchange for better performance
            exchange_groups = defaultdict(list)
            for symbol in symbols:
                exchange = symbol.get('exchange', 'NSE')
                exchange_groups[exchange].append(symbol)
            
            # Create batches within each exchange group
            for exchange, exchange_symbols in exchange_groups.items():
                for i in range(0, len(exchange_symbols), self.max_symbols_per_batch):
                    batch = exchange_symbols[i:i + self.max_symbols_per_batch]
                    self.subscription_batches.append({
                        'exchange': exchange,
                        'symbols': batch,
                        'batch_id': len(self.subscription_batches)
                    })
            
            logger.info(f"[BATCHING] Created {len(self.subscription_batches)} intelligent batches")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create batches: {e}")
    
    async def start_streaming(self) -> bool:
        """Start enhanced real-time data streaming"""
        try:
            if not self.auth_token or not self.feed_token:
                logger.error("[ERROR] Not authenticated")
                return False
            
            if not self.circuit_breaker.can_execute():
                logger.error("[ERROR] Circuit breaker is open")
                return False
            
            # Initialize WebSocket with enhanced configuration
            self.websocket = SmartWebSocketV2(
                self.auth_token,
                self.api_key,
                self.username,
                self.feed_token,
                max_retry_attempt=self.max_reconnection_attempts
            )
            
            # Set up enhanced callbacks
            self.websocket.on_open = self._on_websocket_open
            self.websocket.on_data = self._on_websocket_data
            self.websocket.on_error = self._on_websocket_error
            self.websocket.on_close = self._on_websocket_close
            
            # Start connection and monitoring
            self.connection_state = ConnectionState.CONNECTING
            self.metrics.connection_time = time.time()
            
            # Start WebSocket in separate thread
            self.websocket_thread = threading.Thread(target=self._run_websocket_thread)
            self.websocket_thread.daemon = True
            self.websocket_thread.start()
            
            # Start monitoring threads
            self._start_monitoring_threads()
            
            logger.info("[STREAMING] Enhanced WebSocket streaming started")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start streaming: {e}")
            self.circuit_breaker.call_failed()
            return False
    
    def _run_websocket_thread(self):
        """Run WebSocket connection in dedicated thread"""
        try:
            self.websocket.connect()
        except Exception as e:
            logger.error(f"[ERROR] WebSocket thread error: {e}")
            self.connection_state = ConnectionState.FAILED
    
    def _start_monitoring_threads(self):
        """Start monitoring and heartbeat threads"""
        try:
            # Heartbeat thread
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_monitor)
            self.heartbeat_thread.daemon = True
            self.heartbeat_thread.start()
            
            # Performance monitoring thread
            self.monitoring_thread = threading.Thread(target=self._performance_monitor)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start monitoring threads: {e}")
    
    def _heartbeat_monitor(self):
        """Monitor connection health with heartbeat"""
        while not self.stop_event.is_set():
            try:
                if self.connection_state == ConnectionState.CONNECTED:
                    current_time = datetime.now()
                    
                    # Check if we've received data recently
                    if (self.last_heartbeat and 
                        (current_time - self.last_heartbeat).total_seconds() > self.heartbeat_interval * 2):
                        logger.warning("[HEARTBEAT] No data received, connection may be stale")
                        self._trigger_reconnection()
                    
                    self.last_heartbeat = current_time
                
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"[ERROR] Heartbeat monitor error: {e}")
                time.sleep(5)
    
    def _performance_monitor(self):
        """Monitor performance metrics"""
        while not self.stop_event.is_set():
            try:
                if self.connection_state == ConnectionState.CONNECTED:
                    # Calculate messages per second
                    current_time = time.time()
                    time_diff = current_time - self.metrics.connection_time
                    
                    if time_diff > 0:
                        self.metrics.messages_per_second = self.metrics.total_messages / time_diff
                        self.metrics.connection_uptime = time_diff
                    
                    # Calculate data quality score
                    if self.quality_metrics['total_ticks'] > 0:
                        self.metrics.data_quality_score = (
                            self.quality_metrics['quality_score_sum'] / 
                            self.quality_metrics['total_ticks']
                        )
                    
                    # Log performance metrics periodically
                    if self.metrics.total_messages % 1000 == 0 and self.metrics.total_messages > 0:
                        logger.info(f"[PERFORMANCE] Messages: {self.metrics.total_messages}, "
                                  f"Rate: {self.metrics.messages_per_second:.2f}/s, "
                                  f"Quality: {self.metrics.data_quality_score:.3f}")
                
                time.sleep(30)  # Monitor every 30 seconds
                
            except Exception as e:
                logger.error(f"[ERROR] Performance monitor error: {e}")
                time.sleep(10)
    
    def _on_websocket_open(self, wsapp):
        """Enhanced WebSocket open handler"""
        try:
            self.connection_state = ConnectionState.CONNECTED
            self.circuit_breaker.call_succeeded()
            self.metrics.connection_time = time.time()
            
            logger.info("[SUCCESS] Enhanced WebSocket connected")
            
            # Subscribe to all batches with error handling
            self._subscribe_all_batches_enhanced()
            
        except Exception as e:
            logger.error(f"[ERROR] Error in enhanced websocket open handler: {e}")
    
    def _subscribe_all_batches_enhanced(self):
        """Subscribe to all symbol batches with enhanced error handling"""
        try:
            successful_subscriptions = 0
            
            for batch in self.subscription_batches:
                try:
                    # Create subscription payload
                    token_list = []
                    
                    for symbol in batch['symbols']:
                        token_list.append({
                            "exchangeType": self._get_exchange_type(symbol['exchange']),
                            "tokens": [symbol['token']]
                        })
                    
                    # Subscribe to batch
                    if token_list:
                        self.websocket.subscribe(token_list, 1)  # Mode 1 for LTP
                        successful_subscriptions += 1
                        
                        # Small delay between batches to avoid overwhelming
                        time.sleep(0.1)
                
                except Exception as e:
                    logger.error(f"[ERROR] Failed to subscribe to batch {batch.get('batch_id', 'unknown')}: {e}")
            
            logger.info(f"[SUBSCRIPTION] Successfully subscribed to {successful_subscriptions}/{len(self.subscription_batches)} batches")
            
        except Exception as e:
            logger.error(f"[ERROR] Enhanced subscription failed: {e}")
    
    def _get_exchange_type(self, exchange: str) -> int:
        """Get exchange type code"""
        exchange_mapping = {
            'NSE': 1,
            'NFO': 2,
            'BSE': 3,
            'BFO': 4,
            'MCX': 5,
            'NCDS': 7,
            'CDS': 13
        }
        return exchange_mapping.get(exchange.upper(), 1)
    
    def _on_websocket_data(self, wsapp, message):
        """Enhanced data handler with validation and quality checks"""
        try:
            # Parse message
            data = json.loads(message) if isinstance(message, str) else message
            
            # Create enhanced tick data
            tick = self._create_enhanced_tick(data)
            
            if tick:
                # Update metrics
                self.metrics.total_messages += 1
                self.metrics.last_message_time = datetime.now()
                self.last_heartbeat = datetime.now()
                
                # Update quality metrics
                self.quality_metrics['total_ticks'] += 1
                self.quality_metrics['quality_score_sum'] += tick.quality_score
                
                if tick.is_valid:
                    self.quality_metrics['valid_ticks'] += 1
                else:
                    self.quality_metrics['invalid_ticks'] += 1
                
                # Store in buffer
                self.data_buffer.append(tick)
                
                # Call registered callbacks
                for callback in self.data_callbacks:
                    try:
                        callback(tick)
                    except Exception as e:
                        logger.error(f"[ERROR] Callback error: {e}")
            
        except Exception as e:
            logger.error(f"[ERROR] Enhanced data handler error: {e}")
    
    def _create_enhanced_tick(self, data: Dict[str, Any]) -> Optional[MarketDataTick]:
        """Create enhanced tick with validation"""
        try:
            # Extract basic fields
            symbol = data.get('symbol', '')
            token = data.get('token', '')
            
            if not symbol or not token:
                return None
            
            # Create tick with validation
            tick = MarketDataTick(
                symbol=symbol,
                token=str(token),
                exchange_type=data.get('exchange_type', 1),
                timestamp=datetime.now(),
                last_traded_price=float(data.get('ltp', 0)),
                open_price=float(data.get('open', 0)),
                high_price=float(data.get('high', 0)),
                low_price=float(data.get('low', 0)),
                close_price=float(data.get('close', 0)),
                volume=int(data.get('volume', 0)),
                change_percent=float(data.get('change_percent', 0))
            )
            
            return tick
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create enhanced tick: {e}")
            return None
    
    def _on_websocket_error(self, wsapp, error):
        """Enhanced error handler"""
        try:
            self.metrics.error_count += 1
            self.circuit_breaker.call_failed()
            
            logger.error(f"[ERROR] Enhanced WebSocket error: {error}")
            
            # Trigger reconnection if not too many failures
            if self.metrics.error_count < self.max_reconnection_attempts:
                self._trigger_reconnection()
            else:
                self.connection_state = ConnectionState.FAILED
                logger.error("[ERROR] Max reconnection attempts reached")
            
        except Exception as e:
            logger.error(f"[ERROR] Error handler error: {e}")
    
    def _on_websocket_close(self, wsapp, close_status_code, close_msg):
        """Enhanced close handler"""
        try:
            self.connection_state = ConnectionState.DISCONNECTED
            logger.warning(f"[CLOSE] WebSocket closed: {close_status_code} - {close_msg}")
            
            # Attempt reconnection if not intentionally closed
            if not self.stop_event.is_set():
                self._trigger_reconnection()
            
        except Exception as e:
            logger.error(f"[ERROR] Close handler error: {e}")
    
    def _trigger_reconnection(self):
        """Trigger intelligent reconnection"""
        try:
            if not self.circuit_breaker.can_execute():
                logger.warning("[RECONNECT] Circuit breaker prevents reconnection")
                return
            
            self.connection_state = ConnectionState.RECONNECTING
            self.metrics.reconnection_count += 1
            
            # Exponential backoff
            delay = min(self.reconnection_delay * (2 ** self.metrics.reconnection_count), 60)
            
            logger.info(f"[RECONNECT] Attempting reconnection #{self.metrics.reconnection_count} in {delay}s")
            
            # Schedule reconnection
            threading.Timer(delay, self._attempt_reconnection).start()
            
        except Exception as e:
            logger.error(f"[ERROR] Reconnection trigger error: {e}")
    
    def _attempt_reconnection(self):
        """Attempt to reconnect"""
        try:
            if self.stop_event.is_set():
                return
            
            logger.info("[RECONNECT] Attempting to reconnect...")
            
            # Close existing connection
            if self.websocket:
                try:
                    self.websocket.close()
                except:
                    pass
            
            # Start new connection
            asyncio.create_task(self.start_streaming())
            
        except Exception as e:
            logger.error(f"[ERROR] Reconnection attempt failed: {e}")
    
    def add_data_callback(self, callback: Callable[[MarketDataTick], None]):
        """Add data callback function"""
        self.data_callbacks.append(callback)
    
    def get_latest_data(self, symbol: str) -> Optional[MarketDataTick]:
        """Get latest data for a symbol"""
        for tick in reversed(self.data_buffer):
            if tick.symbol == symbol:
                return tick
        return None
    
    def get_connection_metrics(self) -> ConnectionMetrics:
        """Get connection performance metrics"""
        return self.metrics
    
    def get_quality_metrics(self) -> Dict[str, Any]:
        """Get data quality metrics"""
        total = self.quality_metrics['total_ticks']
        if total == 0:
            return self.quality_metrics
        
        return {
            **self.quality_metrics,
            'valid_percentage': (self.quality_metrics['valid_ticks'] / total) * 100,
            'invalid_percentage': (self.quality_metrics['invalid_ticks'] / total) * 100,
            'average_quality_score': self.quality_metrics['quality_score_sum'] / total
        }
    
    async def stop_streaming(self):
        """Stop streaming and cleanup"""
        try:
            self.stop_event.set()
            self.connection_state = ConnectionState.DISCONNECTED
            
            if self.websocket:
                self.websocket.close()
            
            # Wait for threads to finish
            if self.websocket_thread and self.websocket_thread.is_alive():
                self.websocket_thread.join(timeout=5)
            
            if self.heartbeat_thread and self.heartbeat_thread.is_alive():
                self.heartbeat_thread.join(timeout=5)
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)
            
            logger.info("[STOP] Enhanced WebSocket streaming stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping streaming: {e}")

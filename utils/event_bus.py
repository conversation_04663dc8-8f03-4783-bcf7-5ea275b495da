#!/usr/bin/env python3
"""
EVENT BUS
Event-driven communication system for agent coordination

Features:
- Publish-subscribe pattern for loose coupling
- Event filtering and routing
- Async event handling
- Event history and debugging
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict
import json

logger = logging.getLogger(__name__)

# Event type constants
class EventTypes:
    """Standard event types for the trading system"""

    # Market data events
    MARKET_DATA_RECEIVED = "market_data_received"
    MARKET_DATA_UPDATE = "market_data_update"
    HISTORICAL_DATA_LOADED = "historical_data_loaded"
    LIVE_PRICE_UPDATE = "live_price_update"
    LIVE_CANDLE_UPDATE = "live_candle_update"
    WEBSOCKET_CONNECTED = "websocket_connected"
    WEBSOCKET_DISCONNECTED = "websocket_disconnected"

    # Trading signal events
    SIGNAL_GENERATED = "signal_generated"
    SIGNAL_VALIDATED = "signal_validated"
    SIGNAL_REJECTED = "signal_rejected"
    SIGNAL_RISK_APPROVED = "signal_risk_approved"

    # Risk management events
    RISK_CHECK_PASSED = "risk_check_passed"
    RISK_CHECK_FAILED = "risk_check_failed"
    POSITION_LIMIT_EXCEEDED = "position_limit_exceeded"
    HIGH_RISK_WARNING = "high_risk_warning"
    HIGH_EXPOSURE_WARNING = "high_exposure_warning"
    RISK_METRICS_UPDATE = "risk_metrics_update"

    # Execution events
    ORDER_PLACED = "order_placed"
    ORDER_FILLED = "order_filled"
    ORDER_REJECTED = "order_rejected"
    ORDER_CANCELLED = "order_cancelled"
    TRADE_EXECUTED = "trade_executed"
    TRADE_EXECUTION_FAILED = "trade_execution_failed"
    POSITION_UPDATE = "position_update"
    TRADE_APPROVED = "trade_approved"
    TRADE_REQUEST = "trade_request"
    CANCEL_ORDER_REQUEST = "cancel_order_request"

    # System events
    AGENT_STARTED = "agent_started"
    AGENT_STOPPED = "agent_stopped"
    AGENT_ERROR = "agent_error"
    AGENT_HEARTBEAT = "agent_heartbeat"
    AGENT_RESTART_REQUEST = "agent_restart_request"
    SYSTEM_SHUTDOWN = "system_shutdown"
    SYSTEM_ERROR = "system_error"
    SYSTEM_ALERT = "system_alert"

    # Emergency events
    EMERGENCY_STOP = "emergency_stop"
    EMERGENCY_STOP_CONFIRMED = "emergency_stop_confirmed"
    CIRCUIT_BREAKER_TRIGGERED = "circuit_breaker_triggered"
    CIRCUIT_BREAKER_RESET = "circuit_breaker_reset"

@dataclass
class Event:
    """Event data structure"""
    type: str
    data: Dict[str, Any]
    source: str
    timestamp: datetime = None
    event_id: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.event_id is None:
            self.event_id = f"{self.type}_{self.timestamp.strftime('%Y%m%d_%H%M%S_%f')}"

class EventBus:
    """
    Event bus for agent communication
    
    Provides:
    - Event publishing and subscription
    - Event filtering and routing
    - Event history for debugging
    - Async event handling
    """
    
    def __init__(self, max_history: int = 1000):
        """Initialize event bus"""
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.event_history: List[Event] = []
        self.max_history = max_history
        self.event_count = 0
        self.running = True
        
        # Event processing queue
        self.event_queue = asyncio.Queue()
        self.processor_task = None
        
        logger.info("[INIT] Event bus initialized")
    
    def subscribe(self, event_type: str, callback: Callable):
        """Subscribe to events of a specific type"""
        try:
            self.subscribers[event_type].append(callback)
            logger.debug(f"[SUBSCRIBE] Subscribed to {event_type}")
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to {event_type}: {e}")
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """Unsubscribe from events of a specific type"""
        try:
            if callback in self.subscribers[event_type]:
                self.subscribers[event_type].remove(callback)
                logger.debug(f"[UNSUBSCRIBE] Unsubscribed from {event_type}")
        except Exception as e:
            logger.error(f"[ERROR] Failed to unsubscribe from {event_type}: {e}")
    
    def publish(self, event: Event):
        """Publish an event"""
        try:
            # Add to queue for async processing
            if self.running:
                asyncio.create_task(self._queue_event(event))
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to publish event {event.type}: {e}")
    
    async def _queue_event(self, event: Event):
        """Queue event for processing"""
        try:
            await self.event_queue.put(event)
        except Exception as e:
            logger.error(f"[ERROR] Failed to queue event: {e}")
    
    async def start_processor(self):
        """Start the event processor"""
        try:
            logger.info("[START] Starting event processor...")
            self.processor_task = asyncio.create_task(self._process_events())
        except Exception as e:
            logger.error(f"[ERROR] Failed to start event processor: {e}")
    
    async def stop_processor(self):
        """Stop the event processor"""
        try:
            logger.info("[STOP] Stopping event processor...")
            self.running = False
            
            if self.processor_task:
                self.processor_task.cancel()
                try:
                    await self.processor_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("[SUCCESS] Event processor stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop event processor: {e}")
    
    async def _process_events(self):
        """Process events from the queue"""
        try:
            while self.running:
                try:
                    # Get event from queue with timeout
                    event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                    
                    # Process the event
                    await self._handle_event(event)
                    
                except asyncio.TimeoutError:
                    # No events to process, continue
                    continue
                except Exception as e:
                    logger.error(f"[ERROR] Error processing event: {e}")
                    await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"[ERROR] Event processor error: {e}")
    
    async def _handle_event(self, event: Event):
        """Handle a single event"""
        try:
            # Add to history
            self._add_to_history(event)
            
            # Get subscribers for this event type
            callbacks = self.subscribers.get(event.type, [])
            
            if callbacks:
                logger.debug(f"[EVENT] Processing {event.type} for {len(callbacks)} subscribers")
                
                # Call all subscribers
                tasks = []
                for callback in callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            tasks.append(asyncio.create_task(callback(event)))
                        else:
                            # Run sync callback in thread pool
                            tasks.append(asyncio.create_task(
                                asyncio.get_event_loop().run_in_executor(None, callback, event)
                            ))
                    except Exception as e:
                        logger.error(f"[ERROR] Failed to create task for callback: {e}")
                
                # Wait for all callbacks to complete
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
            
            self.event_count += 1
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to handle event {event.type}: {e}")
    
    def _add_to_history(self, event: Event):
        """Add event to history"""
        try:
            self.event_history.append(event)
            
            # Trim history if too long
            if len(self.event_history) > self.max_history:
                self.event_history = self.event_history[-self.max_history:]
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add event to history: {e}")
    
    def get_event_history(self, event_type: Optional[str] = None, limit: int = 100) -> List[Event]:
        """Get event history"""
        try:
            if event_type:
                # Filter by event type
                filtered_events = [e for e in self.event_history if e.type == event_type]
                return filtered_events[-limit:]
            else:
                return self.event_history[-limit:]
        except Exception as e:
            logger.error(f"[ERROR] Failed to get event history: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get event bus statistics"""
        try:
            event_type_counts = defaultdict(int)
            for event in self.event_history:
                event_type_counts[event.type] += 1
            
            return {
                "total_events": self.event_count,
                "history_size": len(self.event_history),
                "subscriber_count": sum(len(callbacks) for callbacks in self.subscribers.values()),
                "event_types": dict(event_type_counts),
                "running": self.running,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"[ERROR] Failed to get statistics: {e}")
            return {}
    
    def export_history(self, filename: str, event_type: Optional[str] = None):
        """Export event history to file"""
        try:
            events = self.get_event_history(event_type)
            
            # Convert events to serializable format
            serializable_events = []
            for event in events:
                event_dict = asdict(event)
                event_dict['timestamp'] = event.timestamp.isoformat()
                serializable_events.append(event_dict)
            
            with open(filename, 'w') as f:
                json.dump(serializable_events, f, indent=2, default=str)
            
            logger.info(f"[EXPORT] Exported {len(events)} events to {filename}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to export history: {e}")
    
    def clear_history(self):
        """Clear event history"""
        try:
            self.event_history.clear()
            logger.info("[CLEAR] Event history cleared")
        except Exception as e:
            logger.error(f"[ERROR] Failed to clear history: {e}")
#!/usr/bin/env python3
"""
🚀 GPU-Optimized Data Loader
High-performance data loading with GPU memory management and async I/O
"""

import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import polars as pl
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class GPUDataLoader:
    """GPU-optimized data loader with memory management"""
    
    def __init__(self, cuda_optimizer):
        self.cuda_optimizer = cuda_optimizer
        self.cuda_available = cuda_optimizer.cuda_available
        self.cupy_available = cuda_optimizer.cupy_available
        
        # Configuration
        config = cuda_optimizer.config
        self.chunk_size = config.get('data_pipeline', {}).get('chunk_size', 100000)
        self.prefetch_factor = config.get('data_pipeline', {}).get('prefetch_factor', 4)
        self.use_pinned_memory = config.get('data_pipeline', {}).get('pinned_memory', True)
        
        # Thread pool for async I/O
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def load_data_async(self, file_path: str) -> Optional[pl.DataFrame]:
        """Asynchronously load data with GPU optimization"""
        try:
            # Load data in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            df = await loop.run_in_executor(self.executor, self._load_data_sync, file_path)
            
            if df is None:
                return None
            
            # Optimize for GPU processing
            if self.cuda_available:
                df = self._optimize_dataframe_for_gpu(df)
            
            return df
            
        except Exception as e:
            logger.error(f"Async data loading failed for {file_path}: {e}")
            return None
    
    def _load_data_sync(self, file_path: str) -> Optional[pl.DataFrame]:
        """Synchronous data loading with optimizations"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return None
            
            # Load with Polars optimizations
            if file_path.suffix == '.parquet':
                df = pl.read_parquet(
                    file_path,
                    use_pyarrow=True,
                    memory_map=True
                )
            elif file_path.suffix == '.csv':
                df = pl.read_csv(
                    file_path,
                    try_parse_dates=True,
                    chunk_size=self.chunk_size
                )
            else:
                logger.error(f"Unsupported file format: {file_path.suffix}")
                return None
            
            logger.debug(f"Loaded {len(df)} rows from {file_path.name}")
            return df
            
        except Exception as e:
            logger.error(f"Data loading failed for {file_path}: {e}")
            return None
    
    def _optimize_dataframe_for_gpu(self, df: pl.DataFrame) -> pl.DataFrame:
        """Optimize DataFrame for GPU processing"""
        try:
            # Convert to optimal data types for GPU
            dtype_mapping = {
                pl.Float64: pl.Float32,  # Use float32 for GPU efficiency
                pl.Int64: pl.Int32,      # Use int32 for GPU efficiency
            }
            
            # Apply dtype optimizations
            for old_dtype, new_dtype in dtype_mapping.items():
                for col in df.columns:
                    if df[col].dtype == old_dtype:
                        df = df.with_columns(df[col].cast(new_dtype))
            
            # Sort by datetime for better memory access patterns
            if 'datetime' in df.columns:
                df = df.sort('datetime')
            
            # Pre-calculate common technical indicators on GPU if possible
            if self.cupy_available and len(df) > 10000:
                df = self._precompute_gpu_indicators(df)
            
            return df
            
        except Exception as e:
            logger.warning(f"DataFrame GPU optimization failed: {e}")
            return df
    
    def _precompute_gpu_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Pre-compute technical indicators on GPU"""
        try:
            import cupy as cp
            
            # Extract price data
            if 'close' not in df.columns:
                return df
            
            close_prices = df['close'].to_numpy()
            gpu_close = self.cuda_optimizer.get_gpu_array(close_prices)
            
            # Calculate common indicators on GPU
            indicators = {}
            
            # Simple Moving Averages
            for period in [5, 10, 20, 50]:
                if len(close_prices) > period:
                    kernel = cp.ones(period) / period
                    sma = cp.convolve(gpu_close, kernel, mode='valid')
                    
                    # Pad with NaN
                    sma_full = cp.full(len(close_prices), cp.nan)
                    sma_full[period-1:] = sma
                    
                    indicators[f'sma_{period}'] = sma_full.get()
            
            # RSI calculation
            if len(close_prices) > 14:
                price_changes = cp.diff(gpu_close, prepend=gpu_close[0])
                gains = cp.where(price_changes > 0, price_changes, 0)
                losses = cp.where(price_changes < 0, -price_changes, 0)
                
                # Calculate RSI
                avg_gains = cp.convolve(gains, cp.ones(14)/14, mode='valid')
                avg_losses = cp.convolve(losses, cp.ones(14)/14, mode='valid')
                
                rs = avg_gains / (avg_losses + 1e-8)
                rsi = 100 - (100 / (1 + rs))
                
                rsi_full = cp.full(len(close_prices), 50.0)
                rsi_full[13:] = rsi
                
                indicators['rsi_14'] = rsi_full.get()
            
            # Add indicators to DataFrame
            for name, values in indicators.items():
                df = df.with_columns(pl.Series(name, values))
            
            logger.debug(f"Pre-computed {len(indicators)} GPU indicators")
            return df
            
        except Exception as e:
            logger.warning(f"GPU indicator pre-computation failed: {e}")
            return df
    
    async def load_multiple_files_async(self, file_paths: List[str]) -> Dict[str, pl.DataFrame]:
        """Load multiple files asynchronously with GPU optimization"""
        try:
            # Create tasks for concurrent loading
            tasks = [self.load_data_async(file_path) for file_path in file_paths]
            
            # Execute with controlled concurrency
            results = {}
            for i, task in enumerate(asyncio.as_completed(tasks)):
                df = await task
                if df is not None:
                    file_name = Path(file_paths[i]).stem
                    results[file_name] = df
            
            logger.info(f"Loaded {len(results)} files successfully")
            return results
            
        except Exception as e:
            logger.error(f"Multiple file loading failed: {e}")
            return {}
    
    def create_gpu_batch(self, dataframes: List[pl.DataFrame]) -> Optional[Any]:
        """Create GPU batch from multiple DataFrames"""
        if not self.cuda_available or not dataframes:
            return None
        
        try:
            # Concatenate DataFrames
            combined_df = pl.concat(dataframes)
            
            # Convert to GPU arrays
            numeric_cols = [col for col in combined_df.columns 
                          if combined_df[col].dtype in [pl.Float32, pl.Float64, pl.Int32, pl.Int64]]
            
            if not numeric_cols:
                return None
            
            # Create GPU batch
            batch_data = combined_df.select(numeric_cols).to_numpy()
            gpu_batch = self.cuda_optimizer.get_gpu_array(batch_data)
            
            return {
                'gpu_data': gpu_batch,
                'columns': numeric_cols,
                'shape': batch_data.shape,
                'original_dfs': dataframes
            }
            
        except Exception as e:
            logger.error(f"GPU batch creation failed: {e}")
            return None
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            self.executor.shutdown(wait=True)
            if self.cuda_available:
                self.cuda_optimizer.cleanup_memory(force=True)
        except Exception as e:
            logger.warning(f"Cleanup failed: {e}")

# Global GPU data loader instance
_global_gpu_data_loader: Optional[GPUDataLoader] = None

def get_gpu_data_loader():
    """Get global GPU data loader instance"""
    global _global_gpu_data_loader
    if _global_gpu_data_loader is None:
        from utils.enhanced_cuda_optimizer import get_enhanced_cuda_optimizer
        cuda_optimizer = get_enhanced_cuda_optimizer()
        _global_gpu_data_loader = GPUDataLoader(cuda_optimizer)
    return _global_gpu_data_loader
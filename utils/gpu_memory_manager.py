#!/usr/bin/env python3
"""
🚀 GPU Memory Manager for Optimized Backtesting
Manages GPU memory allocation and cleanup for maximum performance
"""

import logging
import gc
import time
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class GPUMemoryManager:
    """Manages GPU memory for optimal backtesting performance"""
    
    def __init__(self):
        self.torch_available = False
        self.cuda_available = False
        self.memory_stats = {}
        self.cleanup_threshold = 0.8  # Cleanup when 80% memory used
        
        self._initialize()
    
    def _initialize(self):
        """Initialize GPU memory management"""
        try:
            import torch
            self.torch_available = True
            self.cuda_available = torch.cuda.is_available()
            
            if self.cuda_available:
                # Set memory management settings
                torch.cuda.empty_cache()
                logger.info("🚀 GPU Memory Manager initialized")
            else:
                logger.info("⚠️ CUDA not available for memory management")
                
        except ImportError:
            logger.info("⚠️ PyTorch not available for GPU memory management")
    
    def get_memory_stats(self) -> Dict[str, float]:
        """Get current GPU memory statistics"""
        if not self.cuda_available:
            return {'cuda_available': False}
        
        try:
            import torch
            
            device = torch.cuda.current_device()
            props = torch.cuda.get_device_properties(device)
            
            allocated = torch.cuda.memory_allocated(device)
            reserved = torch.cuda.memory_reserved(device)
            total = props.total_memory
            
            stats = {
                'cuda_available': True,
                'device_name': props.name,
                'total_gb': total / 1024**3,
                'allocated_gb': allocated / 1024**3,
                'reserved_gb': reserved / 1024**3,
                'free_gb': (total - reserved) / 1024**3,
                'utilization_pct': (allocated / total) * 100,
                'reserved_pct': (reserved / total) * 100
            }
            
            self.memory_stats = stats
            return stats
            
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {'cuda_available': False, 'error': str(e)}
    
    def should_cleanup(self) -> bool:
        """Check if memory cleanup is needed"""
        if not self.cuda_available:
            return False
        
        stats = self.get_memory_stats()
        return stats.get('utilization_pct', 0) > (self.cleanup_threshold * 100)
    
    def cleanup_memory(self, force: bool = False) -> Dict[str, Any]:
        """Cleanup GPU memory"""
        if not self.cuda_available:
            return {'cleaned': False, 'reason': 'CUDA not available'}
        
        try:
            import torch
            
            # Get stats before cleanup
            stats_before = self.get_memory_stats()
            
            if not force and not self.should_cleanup():
                return {
                    'cleaned': False, 
                    'reason': 'Cleanup not needed',
                    'utilization_pct': stats_before.get('utilization_pct', 0)
                }
            
            # Perform cleanup
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
            
            # Python garbage collection
            gc.collect()
            
            # Get stats after cleanup
            stats_after = self.get_memory_stats()
            
            freed_gb = stats_before.get('allocated_gb', 0) - stats_after.get('allocated_gb', 0)
            
            result = {
                'cleaned': True,
                'freed_gb': freed_gb,
                'before_utilization': stats_before.get('utilization_pct', 0),
                'after_utilization': stats_after.get('utilization_pct', 0),
                'improvement_pct': stats_before.get('utilization_pct', 0) - stats_after.get('utilization_pct', 0)
            }
            
            if freed_gb > 0.1:  # Only log if significant memory was freed
                logger.info(f"🧹 GPU memory cleaned: {freed_gb:.2f} GB freed")
            
            return result
            
        except Exception as e:
            logger.error(f"Error during memory cleanup: {e}")
            return {'cleaned': False, 'error': str(e)}
    
    @contextmanager
    def memory_context(self, cleanup_after: bool = True):
        """Context manager for automatic memory cleanup"""
        try:
            # Get initial stats
            initial_stats = self.get_memory_stats()
            yield self
        finally:
            if cleanup_after and self.cuda_available:
                self.cleanup_memory()
    
    def optimize_for_batch_size(self, target_batch_size: int, data_size: int) -> int:
        """Optimize batch size based on available memory"""
        if not self.cuda_available:
            return min(target_batch_size, data_size)
        
        stats = self.get_memory_stats()
        free_gb = stats.get('free_gb', 0)
        
        # Estimate memory per sample (rough approximation)
        memory_per_sample_mb = 0.1  # 100KB per sample (conservative estimate)
        
        # Calculate max batch size based on available memory
        max_batch_size = int((free_gb * 1024) / memory_per_sample_mb)
        
        # Use 80% of available memory to leave buffer
        safe_batch_size = int(max_batch_size * 0.8)
        
        # Return the minimum of target, safe, and data size
        optimal_batch_size = min(target_batch_size, safe_batch_size, data_size)
        
        logger.debug(f"📊 Batch size optimization: target={target_batch_size}, "
                    f"safe={safe_batch_size}, optimal={optimal_batch_size}")
        
        return optimal_batch_size
    
    def monitor_memory_usage(self, operation_name: str = "operation") -> Dict[str, Any]:
        """Monitor memory usage for an operation"""
        if not self.cuda_available:
            return {'monitoring': False}
        
        stats = self.get_memory_stats()
        
        # Log warning if memory usage is high
        utilization = stats.get('utilization_pct', 0)
        if utilization > 85:
            logger.warning(f"⚠️ High GPU memory usage during {operation_name}: {utilization:.1f}%")
        elif utilization > 70:
            logger.info(f"📊 GPU memory usage during {operation_name}: {utilization:.1f}%")
        
        return {
            'monitoring': True,
            'operation': operation_name,
            'utilization_pct': utilization,
            'allocated_gb': stats.get('allocated_gb', 0),
            'free_gb': stats.get('free_gb', 0)
        }
    
    def get_optimal_chunk_size(self, total_size: int, base_chunk_size: int = 10000) -> int:
        """Get optimal chunk size based on available memory"""
        if not self.cuda_available:
            return base_chunk_size
        
        stats = self.get_memory_stats()
        free_gb = stats.get('free_gb', 0)
        
        # Scale chunk size based on available memory
        if free_gb > 4:
            multiplier = 2.0
        elif free_gb > 2:
            multiplier = 1.5
        elif free_gb > 1:
            multiplier = 1.0
        else:
            multiplier = 0.5
        
        optimal_chunk = int(base_chunk_size * multiplier)
        return min(optimal_chunk, total_size)

# Global memory manager instance
gpu_memory_manager = GPUMemoryManager()

def get_gpu_memory_manager() -> GPUMemoryManager:
    """Get the global GPU memory manager instance"""
    return gpu_memory_manager

def cleanup_gpu_memory(force: bool = False) -> Dict[str, Any]:
    """Quick function to cleanup GPU memory"""
    return gpu_memory_manager.cleanup_memory(force=force)

def get_gpu_memory_stats() -> Dict[str, float]:
    """Quick function to get GPU memory stats"""
    return gpu_memory_manager.get_memory_stats()

@contextmanager
def gpu_memory_context(cleanup_after: bool = True):
    """Context manager for GPU memory management"""
    with gpu_memory_manager.memory_context(cleanup_after=cleanup_after) as manager:
        yield manager
#!/usr/bin/env python3
"""
🚀 GPU Performance Monitor
Real-time GPU performance monitoring and optimization recommendations
"""

import time
import logging
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

@dataclass
class GPUMetrics:
    """GPU performance metrics"""
    timestamp: datetime = field(default_factory=datetime.now)
    memory_used: float = 0.0
    memory_total: float = 0.0
    memory_utilization: float = 0.0
    gpu_utilization: float = 0.0
    temperature: float = 0.0
    power_usage: float = 0.0
    compute_capability: tuple = field(default_factory=tuple)
    kernel_execution_time: float = 0.0
    memory_bandwidth: float = 0.0

@dataclass
class PerformanceStats:
    """Performance statistics"""
    operations_per_second: float = 0.0
    average_kernel_time: float = 0.0
    memory_throughput: float = 0.0
    cache_hit_rate: float = 0.0
    optimization_score: float = 0.0

class GPUPerformanceMonitor:
    """Real-time GPU performance monitoring"""
    
    def __init__(self, cuda_optimizer):
        self.cuda_optimizer = cuda_optimizer
        self.cuda_available = cuda_optimizer.cuda_available
        self.cupy_available = cuda_optimizer.cupy_available
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread = None
        self.metrics_history: List[GPUMetrics] = []
        self.max_history = 1000
        
        # Performance tracking
        self.operation_times = []
        self.kernel_times = []
        self.memory_transfers = []
        
        # Optimization recommendations
        self.recommendations = []
        
    def start_monitoring(self, interval: float = 1.0):
        """Start GPU performance monitoring"""
        if not self.cuda_available:
            logger.warning("CUDA not available, monitoring disabled")
            return
        
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"🔍 GPU monitoring started (interval: {interval}s)")
    
    def stop_monitoring(self):
        """Stop GPU performance monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("🛑 GPU monitoring stopped")
    
    def _monitor_loop(self, interval: float):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                if metrics:
                    self.metrics_history.append(metrics)
                    
                    # Keep history size manageable
                    if len(self.metrics_history) > self.max_history:
                        self.metrics_history = self.metrics_history[-self.max_history:]
                    
                    # Check for optimization opportunities
                    self._analyze_performance(metrics)
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                time.sleep(interval)
    
    def _collect_metrics(self) -> Optional[GPUMetrics]:
        """Collect current GPU metrics"""
        try:
            metrics = GPUMetrics()
            
            # Get memory info
            memory_info = self.cuda_optimizer.get_memory_info()
            if memory_info.get('cuda_available'):
                metrics.memory_used = memory_info.get('memory_allocated', 0.0)
                metrics.memory_total = memory_info.get('memory_total', 0.0)
                metrics.memory_utilization = (metrics.memory_used / metrics.memory_total * 100 
                                            if metrics.memory_total > 0 else 0.0)
            
            # Try to get additional metrics
            if self.cuda_available:
                try:
                    import torch
                    if torch.cuda.is_available():
                        # GPU utilization (approximated)
                        metrics.gpu_utilization = self._estimate_gpu_utilization()
                        
                        # Temperature (if available)
                        try:
                            import pynvml
                            pynvml.nvmlInit()
                            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                            metrics.temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                            metrics.power_usage = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # Convert to watts
                        except ImportError:
                            pass  # pynvml not available
                        
                except Exception as e:
                    logger.debug(f"Extended metrics collection failed: {e}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Metrics collection failed: {e}")
            return None
    
    def _estimate_gpu_utilization(self) -> float:
        """Estimate GPU utilization based on recent activity"""
        try:
            # Simple heuristic based on recent kernel execution times
            if not self.kernel_times:
                return 0.0
            
            recent_times = self.kernel_times[-10:]  # Last 10 operations
            if not recent_times:
                return 0.0
            
            # Estimate utilization based on kernel execution frequency
            avg_time = sum(recent_times) / len(recent_times)
            utilization = min(avg_time * 100, 100.0)  # Cap at 100%
            
            return utilization
            
        except Exception:
            return 0.0
    
    def _analyze_performance(self, metrics: GPUMetrics):
        """Analyze performance and generate recommendations"""
        try:
            # Clear old recommendations
            self.recommendations.clear()
            
            # Memory utilization analysis
            if metrics.memory_utilization > 90:
                self.recommendations.append({
                    'type': 'memory',
                    'severity': 'high',
                    'message': 'GPU memory usage is very high (>90%). Consider reducing batch sizes.',
                    'suggestion': 'Reduce batch_size in configuration or enable memory cleanup'
                })
            elif metrics.memory_utilization > 75:
                self.recommendations.append({
                    'type': 'memory',
                    'severity': 'medium',
                    'message': 'GPU memory usage is high (>75%). Monitor for potential issues.',
                    'suggestion': 'Consider enabling aggressive memory cleanup'
                })
            
            # Temperature analysis
            if metrics.temperature > 80:
                self.recommendations.append({
                    'type': 'thermal',
                    'severity': 'high',
                    'message': f'GPU temperature is high ({metrics.temperature}°C). Check cooling.',
                    'suggestion': 'Reduce workload intensity or improve cooling'
                })
            
            # Performance analysis
            if len(self.metrics_history) > 10:
                recent_metrics = self.metrics_history[-10:]
                avg_utilization = sum(m.gpu_utilization for m in recent_metrics) / len(recent_metrics)
                
                if avg_utilization < 30:
                    self.recommendations.append({
                        'type': 'performance',
                        'severity': 'low',
                        'message': f'GPU utilization is low ({avg_utilization:.1f}%). Consider increasing batch sizes.',
                        'suggestion': 'Increase batch_size or strategies_per_batch in configuration'
                    })
            
        except Exception as e:
            logger.error(f"Performance analysis failed: {e}")
    
    def record_operation(self, operation_name: str, duration: float, data_size: int = 0):
        """Record operation performance"""
        try:
            self.operation_times.append({
                'name': operation_name,
                'duration': duration,
                'data_size': data_size,
                'timestamp': datetime.now(),
                'throughput': data_size / duration if duration > 0 else 0
            })
            
            # Keep recent operations only
            if len(self.operation_times) > 100:
                self.operation_times = self.operation_times[-100:]
                
        except Exception as e:
            logger.error(f"Operation recording failed: {e}")
    
    def record_kernel_execution(self, kernel_name: str, duration: float):
        """Record CUDA kernel execution time"""
        try:
            self.kernel_times.append(duration)
            
            # Keep recent kernel times only
            if len(self.kernel_times) > 100:
                self.kernel_times = self.kernel_times[-100:]
                
            logger.debug(f"Kernel {kernel_name} executed in {duration:.4f}s")
            
        except Exception as e:
            logger.error(f"Kernel recording failed: {e}")
    
    def get_performance_stats(self) -> PerformanceStats:
        """Get current performance statistics"""
        try:
            stats = PerformanceStats()
            
            if self.operation_times:
                recent_ops = [op for op in self.operation_times 
                            if datetime.now() - op['timestamp'] < timedelta(minutes=5)]
                
                if recent_ops:
                    stats.operations_per_second = len(recent_ops) / 300.0  # 5 minutes
                    stats.memory_throughput = sum(op['throughput'] for op in recent_ops) / len(recent_ops)
            
            if self.kernel_times:
                stats.average_kernel_time = sum(self.kernel_times) / len(self.kernel_times)
            
            # Calculate optimization score
            stats.optimization_score = self._calculate_optimization_score()
            
            return stats
            
        except Exception as e:
            logger.error(f"Performance stats calculation failed: {e}")
            return PerformanceStats()
    
    def _calculate_optimization_score(self) -> float:
        """Calculate overall optimization score (0-100)"""
        try:
            score = 100.0
            
            if not self.metrics_history:
                return 50.0  # Neutral score
            
            latest_metrics = self.metrics_history[-1]
            
            # Penalize high memory usage
            if latest_metrics.memory_utilization > 90:
                score -= 30
            elif latest_metrics.memory_utilization > 75:
                score -= 15
            
            # Penalize high temperature
            if latest_metrics.temperature > 80:
                score -= 20
            elif latest_metrics.temperature > 70:
                score -= 10
            
            # Reward good utilization
            if 50 <= latest_metrics.gpu_utilization <= 85:
                score += 10
            elif latest_metrics.gpu_utilization < 30:
                score -= 15
            
            # Penalize for recommendations
            score -= len(self.recommendations) * 5
            
            return max(0.0, min(100.0, score))
            
        except Exception:
            return 50.0
    
    def get_recommendations(self) -> List[Dict[str, Any]]:
        """Get current optimization recommendations"""
        return self.recommendations.copy()
    
    def export_metrics(self, file_path: str):
        """Export metrics to JSON file"""
        try:
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'metrics_count': len(self.metrics_history),
                'performance_stats': self.get_performance_stats().__dict__,
                'recommendations': self.recommendations,
                'recent_metrics': [
                    {
                        'timestamp': m.timestamp.isoformat(),
                        'memory_utilization': m.memory_utilization,
                        'gpu_utilization': m.gpu_utilization,
                        'temperature': m.temperature
                    }
                    for m in self.metrics_history[-50:]  # Last 50 metrics
                ]
            }
            
            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            logger.info(f"Metrics exported to {file_path}")
            
        except Exception as e:
            logger.error(f"Metrics export failed: {e}")
    
    def print_status(self):
        """Print current GPU status"""
        try:
            if not self.cuda_available:
                print("🚫 CUDA not available")
                return
            
            memory_info = self.cuda_optimizer.get_memory_info()
            stats = self.get_performance_stats()
            
            print("\n" + "="*60)
            print("🚀 GPU PERFORMANCE STATUS")
            print("="*60)
            
            if memory_info.get('cuda_available'):
                print(f"📱 Device: {memory_info.get('device_name', 'Unknown')}")
                print(f"💾 Memory: {memory_info.get('memory_allocated', 0):.1f}GB / {memory_info.get('memory_total', 0):.1f}GB")
                print(f"📊 Utilization: {memory_info.get('memory_allocated', 0) / memory_info.get('memory_total', 1) * 100:.1f}%")
            
            print(f"⚡ Optimization Score: {stats.optimization_score:.1f}/100")
            print(f"🔄 Avg Kernel Time: {stats.average_kernel_time:.4f}s")
            print(f"📈 Operations/sec: {stats.operations_per_second:.2f}")
            
            if self.recommendations:
                print(f"\n⚠️  Recommendations ({len(self.recommendations)}):")
                for rec in self.recommendations[:3]:  # Show top 3
                    print(f"   • {rec['message']}")
            else:
                print("\n✅ No optimization recommendations")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"Status printing failed: {e}")

# Global performance monitor instance
_global_gpu_monitor: Optional[GPUPerformanceMonitor] = None

def get_gpu_performance_monitor():
    """Get global GPU performance monitor instance"""
    global _global_gpu_monitor
    if _global_gpu_monitor is None:
        from utils.enhanced_cuda_optimizer import get_enhanced_cuda_optimizer
        cuda_optimizer = get_enhanced_cuda_optimizer()
        _global_gpu_monitor = GPUPerformanceMonitor(cuda_optimizer)
    return _global_gpu_monitor
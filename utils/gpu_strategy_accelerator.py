"""
GPU-Accelerated Strategy Evolution Module

This module provides GPU acceleration for strategy evolution using:
- PyTorch CUDA for tensor operations
- CuPy for NumPy-like GPU operations  
- VectorBT for vectorized backtesting
- Numba CUDA for custom kernels

Author: Enhanced Strategy Evolution Agent
"""

import numpy as np
import pandas as pd
import polars as pl
import cupy as cp
import torch
import vectorbt as vbt
from numba import cuda, float32, int32
from typing import Dict, List, Tuple, Any, Optional
import logging
from dataclasses import dataclass
import time

logger = logging.getLogger(__name__)

@dataclass
class GPUBacktestResult:
    """GPU backtesting result structure"""
    strategy_name: str
    symbol: str
    timeframe: str
    total_trades: int
    winning_trades: int
    total_pnl: float
    roi: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    execution_time: float

class GPUStrategyAccelerator:
    """GPU-accelerated strategy evaluation and backtesting"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.cp_device = cp.cuda.Device(0)
        logger.info(f"🚀 GPU Strategy Accelerator initialized on {self.device}")
        
        # Pre-allocate GPU memory pools
        self._setup_memory_pools()
        
    def _setup_memory_pools(self):
        """Setup GPU memory pools for efficient allocation"""
        try:
            # CuPy memory pool
            mempool = cp.get_default_memory_pool()
            mempool.set_limit(size=2**30)  # 1GB limit
            
            # PyTorch memory management
            torch.cuda.empty_cache()
            
            logger.info("🚀 GPU memory pools configured")
        except Exception as e:
            logger.warning(f"Memory pool setup failed: {e}")

    def prepare_data_for_gpu(self, df: pl.DataFrame) -> Dict[str, cp.ndarray]:
        """Convert Polars DataFrame to GPU arrays"""
        try:
            gpu_data = {}
            
            # Essential OHLCV data
            gpu_data['open'] = cp.array(df['open'].to_numpy(), dtype=cp.float32)
            gpu_data['high'] = cp.array(df['high'].to_numpy(), dtype=cp.float32)
            gpu_data['low'] = cp.array(df['low'].to_numpy(), dtype=cp.float32)
            gpu_data['close'] = cp.array(df['close'].to_numpy(), dtype=cp.float32)
            gpu_data['volume'] = cp.array(df['volume'].to_numpy(), dtype=cp.float32)
            
            # Technical indicators if available
            if 'rsi_14' in df.columns:
                gpu_data['rsi'] = cp.array(df['rsi_14'].to_numpy(), dtype=cp.float32)
            if 'ema_10' in df.columns:
                gpu_data['ema_10'] = cp.array(df['ema_10'].to_numpy(), dtype=cp.float32)
            if 'ema_20' in df.columns:
                gpu_data['ema_20'] = cp.array(df['ema_20'].to_numpy(), dtype=cp.float32)
            if 'macd' in df.columns:
                gpu_data['macd'] = cp.array(df['macd'].to_numpy(), dtype=cp.float32)
            if 'bb_upper' in df.columns:
                gpu_data['bb_upper'] = cp.array(df['bb_upper'].to_numpy(), dtype=cp.float32)
            if 'bb_lower' in df.columns:
                gpu_data['bb_lower'] = cp.array(df['bb_lower'].to_numpy(), dtype=cp.float32)
                
            logger.debug(f"📊 Prepared {len(gpu_data)} GPU arrays with {len(gpu_data['close'])} data points")
            return gpu_data
            
        except Exception as e:
            logger.error(f"Error preparing GPU data: {e}")
            return {}

    def vectorized_backtest_gpu(self, gpu_data: Dict[str, cp.ndarray],
                               strategies: List[Dict[str, Any]],
                               symbol: str, timeframe: str,
                               use_walk_forward: bool = True) -> List[GPUBacktestResult]:
        """Vectorized backtesting using GPU acceleration with walk-forward analysis"""
        try:
            start_time = time.time()
            results = []

            # Get data length
            data_len = len(gpu_data['close'])

            for strategy in strategies:
                strategy_start = time.time()

                if use_walk_forward and data_len > 1000:
                    # Use walk-forward analysis for robust testing
                    performance = self._walk_forward_analysis_gpu(gpu_data, strategy)
                else:
                    # Standard backtesting for smaller datasets
                    signals = self._generate_signals_gpu(gpu_data, strategy)
                    performance = self._calculate_performance_vectorbt(gpu_data, signals, strategy)

                # Create result
                result = GPUBacktestResult(
                    strategy_name=strategy.get('name', 'Unknown'),
                    symbol=symbol,
                    timeframe=timeframe,
                    total_trades=int(performance['total_trades']),
                    winning_trades=int(performance['winning_trades']),
                    total_pnl=float(performance['total_pnl']),
                    roi=float(performance['roi']),
                    sharpe_ratio=float(performance['sharpe_ratio']),
                    max_drawdown=float(performance['max_drawdown']),
                    win_rate=float(performance['win_rate']),
                    execution_time=time.time() - strategy_start
                )

                results.append(result)
                logger.debug(f"✅ GPU backtest complete for {strategy.get('name')}: {result.execution_time:.3f}s")

            total_time = time.time() - start_time
            logger.info(f"🚀 GPU vectorized backtest completed: {len(strategies)} strategies in {total_time:.3f}s")

            return results

        except Exception as e:
            logger.error(f"GPU vectorized backtest failed: {e}")
            return []

    def _walk_forward_analysis_gpu(self, gpu_data: Dict[str, cp.ndarray],
                                  strategy: Dict[str, Any]) -> Dict[str, float]:
        """
        Perform walk-forward analysis to prevent overfitting

        Splits data into training/testing windows and validates strategy robustness
        """
        try:
            data_len = len(gpu_data['close'])

            # Walk-forward parameters
            train_window = min(2000, data_len // 3)  # Training window size
            test_window = min(500, data_len // 10)   # Testing window size
            step_size = test_window // 2             # Step size for rolling window

            if train_window + test_window > data_len:
                # Fallback to simple split if data is too small
                split_point = int(data_len * 0.7)
                train_data = {k: v[:split_point] for k, v in gpu_data.items()}
                test_data = {k: v[split_point:] for k, v in gpu_data.items()}

                # Generate signals on test data
                signals = self._generate_signals_gpu(test_data, strategy)
                return self._calculate_performance_vectorbt(test_data, signals, strategy)

            # Perform multiple walk-forward tests
            all_results = []

            for start_idx in range(0, data_len - train_window - test_window, step_size):
                train_end = start_idx + train_window
                test_start = train_end
                test_end = test_start + test_window

                if test_end > data_len:
                    break

                # Extract test data window
                test_data = {k: v[test_start:test_end] for k, v in gpu_data.items()}

                # Generate signals and calculate performance
                signals = self._generate_signals_gpu(test_data, strategy)
                performance = self._calculate_performance_vectorbt(test_data, signals, strategy)

                if performance['total_trades'] > 0:  # Only include windows with trades
                    all_results.append(performance)

            if not all_results:
                return self._get_default_performance_metrics()

            # Aggregate results across all walk-forward windows
            return self._aggregate_walk_forward_results(all_results)

        except Exception as e:
            logger.error(f"Walk-forward analysis failed: {e}")
            return self._get_default_performance_metrics()

    def _aggregate_walk_forward_results(self, results: List[Dict[str, float]]) -> Dict[str, float]:
        """Aggregate results from multiple walk-forward windows"""
        if not results:
            return self._get_default_performance_metrics()

        # Calculate weighted averages and other aggregated metrics
        total_trades = sum(r['total_trades'] for r in results)
        total_winning = sum(r['winning_trades'] for r in results)

        # Weight by number of trades for more reliable metrics
        weights = [r['total_trades'] for r in results]
        total_weight = sum(weights) if sum(weights) > 0 else 1

        weighted_roi = sum(r['roi'] * w for r, w in zip(results, weights)) / total_weight
        weighted_sharpe = sum(r['sharpe_ratio'] * w for r, w in zip(results, weights)) / total_weight
        max_drawdown = max(r['max_drawdown'] for r in results)  # Worst case

        return {
            'total_trades': total_trades,
            'winning_trades': total_winning,
            'total_pnl': weighted_roi,
            'roi': weighted_roi,
            'sharpe_ratio': weighted_sharpe,
            'max_drawdown': max_drawdown,
            'win_rate': total_winning / total_trades if total_trades > 0 else 0.0
        }

    def _generate_signals_gpu(self, gpu_data: Dict[str, cp.ndarray], 
                             strategy: Dict[str, Any]) -> cp.ndarray:
        """Generate trading signals using GPU acceleration"""
        try:
            data_len = len(gpu_data['close'])
            signals = cp.zeros(data_len, dtype=cp.int8)
            
            strategy_type = strategy.get('type', 'unknown')
            
            if strategy_type == 'RSI_Reversal':
                signals = self._rsi_reversal_signals_gpu(gpu_data, strategy)
            elif strategy_type == 'EMA_Crossover':
                signals = self._ema_crossover_signals_gpu(gpu_data, strategy)
            elif strategy_type == 'MACD_Signal':
                signals = self._macd_signals_gpu(gpu_data, strategy)
            elif strategy_type == 'Bollinger_Bands':
                signals = self._bollinger_signals_gpu(gpu_data, strategy)
            else:
                # Default momentum strategy
                signals = self._momentum_signals_gpu(gpu_data, strategy)
            
            return signals
            
        except Exception as e:
            logger.error(f"Signal generation failed: {e}")
            return cp.zeros(len(gpu_data['close']), dtype=cp.int8)

    def _rsi_reversal_signals_gpu(self, gpu_data: Dict[str, cp.ndarray],
                                 strategy: Dict[str, Any]) -> cp.ndarray:
        """Improved RSI reversal signals using GPU with better logic"""
        if 'rsi' not in gpu_data:
            # If no RSI data, calculate it from close prices
            close = gpu_data['close']
            rsi = self._calculate_rsi_gpu(close, period=14)
        else:
            rsi = gpu_data['rsi']

        signals = cp.zeros_like(rsi, dtype=cp.int8)

        # RSI parameters with better defaults
        oversold = strategy.get('oversold_threshold', 25)  # More extreme levels
        overbought = strategy.get('overbought_threshold', 75)

        # Improved signal logic
        # Buy signals: RSI was oversold and now recovering
        rsi_prev = cp.roll(rsi, 1)
        rsi_prev2 = cp.roll(rsi, 2)

        # Buy: RSI crosses above oversold level (reversal from oversold)
        buy_condition = (
            (rsi > oversold) &
            (rsi_prev <= oversold) &
            (rsi > rsi_prev)  # RSI is increasing
        )

        # Sell: RSI crosses below overbought level (reversal from overbought)
        sell_condition = (
            (rsi < overbought) &
            (rsi_prev >= overbought) &
            (rsi < rsi_prev)  # RSI is decreasing
        )

        # Apply signals with some filtering to avoid noise
        signals[buy_condition] = 1
        signals[sell_condition] = -1

        # Remove signals in first few periods where we don't have enough history
        signals[:3] = 0

        return signals

    def _calculate_rsi_gpu(self, close: cp.ndarray, period: int = 14) -> cp.ndarray:
        """Calculate RSI using GPU acceleration"""
        try:
            # Calculate price changes
            delta = cp.diff(close, prepend=close[0])

            # Separate gains and losses
            gains = cp.where(delta > 0, delta, 0)
            losses = cp.where(delta < 0, -delta, 0)

            # Calculate simple moving averages for the first RSI value
            avg_gain = cp.zeros_like(close)
            avg_loss = cp.zeros_like(close)

            # Initial averages
            if len(gains) >= period:
                avg_gain[period-1] = cp.mean(gains[:period])
                avg_loss[period-1] = cp.mean(losses[:period])

                # Calculate smoothed averages (Wilder's smoothing)
                for i in range(period, len(close)):
                    avg_gain[i] = (avg_gain[i-1] * (period-1) + gains[i]) / period
                    avg_loss[i] = (avg_loss[i-1] * (period-1) + losses[i]) / period

            # Calculate RSI
            rs = cp.where(avg_loss != 0, avg_gain / avg_loss, 0)
            rsi = 100 - (100 / (1 + rs))

            # Handle edge cases
            rsi = cp.where(cp.isnan(rsi), 50, rsi)  # Default to neutral
            rsi = cp.where(cp.isinf(rsi), 100, rsi)  # Handle infinity

            return rsi

        except Exception as e:
            logger.error(f"RSI calculation failed: {e}")
            return cp.full_like(close, 50)  # Return neutral RSI

    def _ema_crossover_signals_gpu(self, gpu_data: Dict[str, cp.ndarray],
                                  strategy: Dict[str, Any]) -> cp.ndarray:
        """EMA crossover signals using GPU"""
        if 'ema_10' not in gpu_data or 'ema_20' not in gpu_data:
            return cp.zeros(len(gpu_data['close']), dtype=cp.int8)

        ema_fast = gpu_data['ema_10']
        ema_slow = gpu_data['ema_20']
        signals = cp.zeros_like(ema_fast, dtype=cp.int8)

        # Buy: fast EMA crosses above slow EMA
        buy_condition = (ema_fast > ema_slow) & (cp.roll(ema_fast, 1) <= cp.roll(ema_slow, 1))
        signals[buy_condition] = 1

        # Sell: fast EMA crosses below slow EMA
        sell_condition = (ema_fast < ema_slow) & (cp.roll(ema_fast, 1) >= cp.roll(ema_slow, 1))
        signals[sell_condition] = -1

        return signals

    def _momentum_signals_gpu(self, gpu_data: Dict[str, cp.ndarray],
                             strategy: Dict[str, Any]) -> cp.ndarray:
        """Momentum signals using GPU"""
        close = gpu_data['close']
        lookback = strategy.get('lookback', 10)
        threshold = strategy.get('threshold', 0.02)

        signals = cp.zeros_like(close, dtype=cp.int8)

        # Calculate momentum
        momentum = (close - cp.roll(close, lookback)) / cp.roll(close, lookback)

        # Buy signals: positive momentum above threshold
        buy_condition = momentum > threshold
        signals[buy_condition] = 1

        # Sell signals: negative momentum below threshold
        sell_condition = momentum < -threshold
        signals[sell_condition] = -1

        return signals

    def _macd_signals_gpu(self, gpu_data: Dict[str, cp.ndarray],
                         strategy: Dict[str, Any]) -> cp.ndarray:
        """MACD signals using GPU"""
        if 'macd' not in gpu_data:
            return cp.zeros(len(gpu_data['close']), dtype=cp.int8)

        macd = gpu_data['macd']
        signals = cp.zeros_like(macd, dtype=cp.int8)

        # Buy: MACD crosses above zero
        buy_condition = (macd > 0) & (cp.roll(macd, 1) <= 0)
        signals[buy_condition] = 1

        # Sell: MACD crosses below zero
        sell_condition = (macd < 0) & (cp.roll(macd, 1) >= 0)
        signals[sell_condition] = -1

        return signals

    def _bollinger_signals_gpu(self, gpu_data: Dict[str, cp.ndarray],
                              strategy: Dict[str, Any]) -> cp.ndarray:
        """Bollinger Bands signals using GPU"""
        if 'bb_upper' not in gpu_data or 'bb_lower' not in gpu_data:
            return cp.zeros(len(gpu_data['close']), dtype=cp.int8)

        close = gpu_data['close']
        bb_upper = gpu_data['bb_upper']
        bb_lower = gpu_data['bb_lower']
        signals = cp.zeros_like(close, dtype=cp.int8)

        # Buy: price touches lower band
        buy_condition = close <= bb_lower
        signals[buy_condition] = 1

        # Sell: price touches upper band
        sell_condition = close >= bb_upper
        signals[sell_condition] = -1

        return signals

    def _calculate_performance_vectorbt(self, gpu_data: Dict[str, cp.ndarray],
                                       signals: cp.ndarray,
                                       strategy: Dict[str, Any]) -> Dict[str, float]:
        """Calculate performance metrics using VectorBT with improved logic"""
        try:
            # Convert GPU arrays back to CPU for VectorBT
            close_prices = cp.asnumpy(gpu_data['close'])
            signals_cpu = cp.asnumpy(signals)

            # Improved signal processing
            entries = signals_cpu == 1
            exits = signals_cpu == -1

            # Ensure we have some signals
            if not np.any(entries) and not np.any(exits):
                logger.warning("No trading signals generated")
                return self._get_default_performance_metrics()

            # Run VectorBT simulation with better parameters
            try:
                pf = vbt.Portfolio.from_signals(
                    close_prices,
                    entries,
                    exits,
                    init_cash=100000,
                    fees=0.0005,  # 0.05% fees (more realistic)
                    freq='1min',
                    sl_stop=0.02,  # 2% stop loss
                    tp_stop=0.04,  # 4% take profit (2:1 risk-reward)
                    upon_stop_exit='Close'
                )

                # Check if portfolio has trades
                if len(pf.trades.records) == 0:
                    logger.warning("No trades executed in portfolio")
                    return self._get_default_performance_metrics()

                # Extract metrics safely
                stats = pf.stats()
                trades = pf.trades.records

                # Calculate metrics with proper error handling
                total_trades = len(trades)
                winning_trades = len(trades[trades['pnl'] > 0]) if total_trades > 0 else 0
                win_rate = winning_trades / total_trades if total_trades > 0 else 0.0

                # Get returns and calculate Sharpe ratio manually if needed
                returns = pf.returns()
                sharpe_ratio = 0.0
                if len(returns) > 0 and returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252)  # Annualized

                # Total return calculation
                total_return = float(stats.get('Total Return [%]', 0.0))
                if np.isnan(total_return) or np.isinf(total_return):
                    total_return = 0.0

                # Max drawdown calculation
                max_drawdown = float(stats.get('Max Drawdown [%]', 0.0))
                if np.isnan(max_drawdown) or np.isinf(max_drawdown):
                    max_drawdown = 0.0

                return {
                    'total_trades': total_trades,
                    'winning_trades': winning_trades,
                    'total_pnl': total_return,
                    'roi': total_return,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': abs(max_drawdown),
                    'win_rate': win_rate
                }

            except Exception as vbt_error:
                logger.error(f"VectorBT simulation failed: {vbt_error}")
                return self._get_default_performance_metrics()

        except Exception as e:
            logger.error(f"Performance calculation failed: {e}")
            return self._get_default_performance_metrics()

    def _get_default_performance_metrics(self) -> Dict[str, float]:
        """Return default performance metrics for failed calculations"""
        return {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'roi': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0
        }

    def cleanup_gpu_memory(self):
        """Clean up GPU memory"""
        try:
            cp.get_default_memory_pool().free_all_blocks()
            torch.cuda.empty_cache()
            logger.debug("🧹 GPU memory cleaned up")
        except Exception as e:
            logger.warning(f"GPU cleanup failed: {e}")

# Numba CUDA Kernels for ultra-fast processing
@cuda.jit
def rsi_reversal_kernel(rsi, signals, oversold, overbought, n):
    """CUDA kernel for RSI reversal signal generation"""
    idx = cuda.grid(1)
    if idx < n and idx > 0:
        # Buy signal: RSI crosses above oversold
        if rsi[idx] > oversold and rsi[idx-1] <= oversold:
            signals[idx] = 1
        # Sell signal: RSI crosses below overbought
        elif rsi[idx] < overbought and rsi[idx-1] >= overbought:
            signals[idx] = -1
        else:
            signals[idx] = 0

@cuda.jit
def ema_crossover_kernel(ema_fast, ema_slow, signals, n):
    """CUDA kernel for EMA crossover signal generation"""
    idx = cuda.grid(1)
    if idx < n and idx > 0:
        # Buy: fast EMA crosses above slow EMA
        if ema_fast[idx] > ema_slow[idx] and ema_fast[idx-1] <= ema_slow[idx-1]:
            signals[idx] = 1
        # Sell: fast EMA crosses below slow EMA
        elif ema_fast[idx] < ema_slow[idx] and ema_fast[idx-1] >= ema_slow[idx-1]:
            signals[idx] = -1
        else:
            signals[idx] = 0

@cuda.jit
def momentum_kernel(close, signals, lookback, threshold, n):
    """CUDA kernel for momentum-based signals"""
    idx = cuda.grid(1)
    if idx >= lookback and idx < n:
        momentum = (close[idx] - close[idx - lookback]) / close[idx - lookback]
        if momentum > threshold:
            signals[idx] = 1
        elif momentum < -threshold:
            signals[idx] = -1
        else:
            signals[idx] = 0

@cuda.jit
def bollinger_bands_kernel(close, bb_upper, bb_lower, signals, n):
    """CUDA kernel for Bollinger Bands signals"""
    idx = cuda.grid(1)
    if idx < n:
        # Buy when price touches lower band
        if close[idx] <= bb_lower[idx]:
            signals[idx] = 1
        # Sell when price touches upper band
        elif close[idx] >= bb_upper[idx]:
            signals[idx] = -1
        else:
            signals[idx] = 0

class NumbaGPUAccelerator:
    """Numba CUDA kernel accelerator for strategy signals"""

    def __init__(self):
        self.threads_per_block = 256
        logger.info("🚀 Numba CUDA accelerator initialized")

    def generate_rsi_signals_cuda(self, rsi: cp.ndarray, oversold: float = 30,
                                 overbought: float = 70) -> cp.ndarray:
        """Generate RSI signals using CUDA kernel"""
        n = len(rsi)
        signals = cp.zeros(n, dtype=cp.int8)

        # Calculate grid dimensions
        blocks_per_grid = (n + self.threads_per_block - 1) // self.threads_per_block

        # Launch CUDA kernel
        rsi_reversal_kernel[blocks_per_grid, self.threads_per_block](
            rsi, signals, oversold, overbought, n
        )

        return signals

    def generate_ema_signals_cuda(self, ema_fast: cp.ndarray,
                                 ema_slow: cp.ndarray) -> cp.ndarray:
        """Generate EMA crossover signals using CUDA kernel"""
        n = len(ema_fast)
        signals = cp.zeros(n, dtype=cp.int8)

        blocks_per_grid = (n + self.threads_per_block - 1) // self.threads_per_block

        ema_crossover_kernel[blocks_per_grid, self.threads_per_block](
            ema_fast, ema_slow, signals, n
        )

        return signals

    def generate_momentum_signals_cuda(self, close: cp.ndarray, lookback: int = 10,
                                     threshold: float = 0.02) -> cp.ndarray:
        """Generate momentum signals using CUDA kernel"""
        n = len(close)
        signals = cp.zeros(n, dtype=cp.int8)

        blocks_per_grid = (n + self.threads_per_block - 1) // self.threads_per_block

        momentum_kernel[blocks_per_grid, self.threads_per_block](
            close, signals, lookback, threshold, n
        )

        return signals

# Enhanced GPU accelerator with Numba CUDA support
class EnhancedGPUAccelerator(GPUStrategyAccelerator):
    """Enhanced GPU accelerator with Numba CUDA kernels"""

    def __init__(self):
        super().__init__()
        self.numba_accelerator = NumbaGPUAccelerator()
        logger.info("🚀 Enhanced GPU accelerator with Numba CUDA initialized")

    def _generate_signals_gpu(self, gpu_data: Dict[str, cp.ndarray],
                             strategy: Dict[str, Any]) -> cp.ndarray:
        """Enhanced signal generation using Numba CUDA kernels"""
        try:
            strategy_type = strategy.get('type', 'unknown')

            if strategy_type == 'RSI_Reversal' and 'rsi' in gpu_data:
                return self.numba_accelerator.generate_rsi_signals_cuda(
                    gpu_data['rsi'],
                    strategy.get('oversold_threshold', 30),
                    strategy.get('overbought_threshold', 70)
                )
            elif strategy_type == 'EMA_Crossover' and 'ema_10' in gpu_data and 'ema_20' in gpu_data:
                return self.numba_accelerator.generate_ema_signals_cuda(
                    gpu_data['ema_10'], gpu_data['ema_20']
                )
            elif strategy_type == 'Momentum':
                return self.numba_accelerator.generate_momentum_signals_cuda(
                    gpu_data['close'],
                    strategy.get('lookback', 10),
                    strategy.get('threshold', 0.02)
                )
            else:
                # Fallback to parent implementation
                return super()._generate_signals_gpu(gpu_data, strategy)

        except Exception as e:
            logger.error(f"Enhanced signal generation failed: {e}")
            return cp.zeros(len(gpu_data['close']), dtype=cp.int8)

# Global enhanced GPU accelerator instance
gpu_accelerator = EnhancedGPUAccelerator()

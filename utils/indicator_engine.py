#!/usr/bin/env python3
"""
High-Performance Indicator Calculation Engine
Optimized for 500+ stocks across multiple timeframes using polars and polars-talib
"""

import os
import sys
import asyncio
import logging
import polars as pl
import pyarrow as pa
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Import advanced indicators
from utils.advanced_indicators import AdvancedIndicators

# Try to import polars-talib for optimized technical indicators
try:
    import polars_talib as ta
    POLARS_TALIB_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("[SUCCESS] polars-talib available for optimized indicator calculations")
except ImportError:
    POLARS_TALIB_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("[WARN] polars-talib not available. Install with: pip install polars-talib")

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] HIGH-PERFORMANCE INDICATOR ENGINE
# ═══════════════════════════════════════════════════════════════════════════════

class IndicatorEngine:
    """
    High-Performance Indicator Calculation Engine
    
    Features:
    - Vectorized calculations using polars
    - Support for 500+ stocks simultaneously
    - Multiple timeframe processing
    - Async processing for better performance
    - Memory-efficient chunked processing
    - Real-time indicator updates
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Indicator Engine"""
        
        self.config = config
        self.performance_config = config.get('performance', {})
        
        # Performance settings
        self.chunk_size = self.performance_config.get('chunk_size', 100)  # Symbols per chunk
        self.parallel_workers = self.performance_config.get('parallel_workers', 4)
        self.memory_limit_mb = self.performance_config.get('memory_limit_mb', 2048)
        
        # Indicator settings
        self.indicator_config = config.get('indicators', {})
        self.enabled_indicators = self._get_enabled_indicators()
        
        # Cache for computed indicators
        self.indicator_cache = {}
        self.cache_duration = self.performance_config.get('cache_duration_seconds', 60)
        
        logger.info(f"[INIT] Indicator Engine initialized with {len(self.enabled_indicators)} indicators")
        logger.info(f"   Chunk size: {self.chunk_size}, Workers: {self.parallel_workers}")
    
    def _get_enabled_indicators(self) -> List[str]:
        """Get list of enabled indicators from config"""
        default_indicators = [
            # Moving Averages
            'sma_20', 'sma_50', 'sma_200', 'ema_5', 'ema_10', 'ema_13', 'ema_20', 'ema_21', 'ema_30', 'ema_50', 'ema_100', 'ema_200',
            'wma_20', 'hma_20', 'tema_20', 'dema_20',

            # Momentum Indicators
            'rsi_5', 'rsi_14', 'rsi_21', 'stoch_k', 'stoch_d', 'stoch_rsi_k', 'stoch_rsi_d',
            'williams_r', 'roc_10', 'roc_20', 'momentum_10', 'momentum_20',

            # Trend Indicators
            'macd', 'macd_signal', 'macd_histogram', 'adx', 'adx_plus_di', 'adx_minus_di',
            'aroon_up', 'aroon_down', 'aroon_oscillator', 'psar', 'supertrend',

            # Volatility Indicators
            'bb_upper', 'bb_lower', 'bb_middle', 'bb_width', 'bb_percent',
            'keltner_upper', 'keltner_lower', 'keltner_middle',
            'atr', 'atr_percent', 'true_range', 'natr',

            # Volume Indicators
            'mfi', 'obv', 'ad_line', 'cmf', 'vwap', 'vwma_20',
            'volume_sma', 'volume_ratio', 'ease_of_movement',

            # Support/Resistance
            'donchian_high', 'donchian_low', 'donchian_middle',
            'pivot_point', 'resistance_1', 'resistance_2', 'support_1', 'support_2',

            # Ichimoku Cloud
            'ichimoku_tenkan', 'ichimoku_kijun', 'ichimoku_senkou_a', 'ichimoku_senkou_b', 'ichimoku_chikou',

            # Custom Indicators
            'cci', 'trix', 'ultimate_oscillator', 'chaikin_oscillator',
            'fisher_transform', 'connors_rsi', 'coppock_curve'
        ]

        return self.indicator_config.get('enabled', default_indicators)
    
    async def calculate_indicators_batch(self, df: pl.DataFrame, 
                                       symbols: Optional[List[str]] = None) -> pl.DataFrame:
        """
        Calculate indicators for a batch of symbols using async processing
        
        Args:
            df: Input DataFrame with OHLCV data
            symbols: Optional list of symbols to process (None = all)
        
        Returns:
            DataFrame with calculated indicators
        """
        try:
            if df.is_empty():
                logger.warning("[WARN] Empty DataFrame provided")
                return df
            
            # Filter symbols if specified
            if symbols:
                df = df.filter(pl.col('symbol').is_in(symbols))
            
            unique_symbols = df['symbol'].unique().to_list()
            logger.info(f"[STATUS] Calculating indicators for {len(unique_symbols)} symbols")
            
            # Process in chunks for memory efficiency
            chunks = [unique_symbols[i:i + self.chunk_size] 
                     for i in range(0, len(unique_symbols), self.chunk_size)]
            
            # Process chunks concurrently
            tasks = []
            for chunk_symbols in chunks:
                task = self._process_symbol_chunk(df, chunk_symbols)
                tasks.append(task)
            
            # Wait for all chunks to complete
            chunk_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine results
            valid_results = []
            for i, result in enumerate(chunk_results):
                if isinstance(result, Exception):
                    logger.error(f"[ERROR] Chunk {i} failed: {result}")
                elif result is not None:
                    valid_results.append(result)
            
            if valid_results:
                combined_df = pl.concat(valid_results)
                logger.info(f"[SUCCESS] Indicators calculated for {len(combined_df)} rows")
                return combined_df.sort(['symbol', 'timestamp'])
            else:
                logger.error("[ERROR] No valid results from indicator calculation")
                return df
                
        except Exception as e:
            logger.error(f"[ERROR] Error in batch indicator calculation: {e}")
            return df
    
    async def _process_symbol_chunk(self, df: pl.DataFrame, 
                                  symbols: List[str]) -> Optional[pl.DataFrame]:
        """Process a chunk of symbols"""
        try:
            # Filter data for this chunk
            chunk_df = df.filter(pl.col('symbol').is_in(symbols))
            
            if chunk_df.is_empty():
                return None
            
            # Calculate indicators for each symbol in the chunk
            symbol_results = []
            
            for symbol in symbols:
                symbol_df = chunk_df.filter(pl.col('symbol') == symbol)
                
                if len(symbol_df) < 50:  # Need minimum data for indicators
                    logger.warning(f"[WARN] Insufficient data for {symbol}: {len(symbol_df)} rows")
                    continue
                
                # Calculate indicators for this symbol
                symbol_with_indicators = self._calculate_symbol_indicators(symbol_df)
                symbol_results.append(symbol_with_indicators)
            
            if symbol_results:
                return pl.concat(symbol_results)
            else:
                return None
                
        except Exception as e:
            logger.error(f"[ERROR] Error processing symbol chunk: {e}")
            return None
    
    def _calculate_symbol_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate all indicators for a single symbol"""
        try:
            if POLARS_TALIB_AVAILABLE:
                return self._calculate_with_polars_talib(df)
            else:
                return self._calculate_with_polars_native(df)
                
        except Exception as e:
            symbol = df['symbol'][0] if len(df) > 0 else 'unknown'
            logger.error(f"[ERROR] Error calculating indicators for {symbol}: {e}")
            return df
    
    def _calculate_with_polars_talib(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate indicators using polars-talib (optimized)"""
        try:
            result_df = df.clone()
            
            # Moving Averages
            if 'sma_20' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.sma(pl.col('close'), 20).alias('sma_20')
                )
            
            if 'ema_5' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ema(pl.col('close'), 5).alias('ema_5')
                )
            
            if 'ema_10' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ema(pl.col('close'), 10).alias('ema_10')
                )
            
            if 'ema_13' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ema(pl.col('close'), 13).alias('ema_13')
                )
            
            if 'ema_20' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ema(pl.col('close'), 20).alias('ema_20')
                )
            
            if 'ema_21' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ema(pl.col('close'), 21).alias('ema_21')
                )
            
            if 'ema_30' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ema(pl.col('close'), 30).alias('ema_30')
                )
            
            if 'ema_50' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ema(pl.col('close'), 50).alias('ema_50')
                )
            
            if 'ema_100' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ema(pl.col('close'), 100).alias('ema_100')
                )
            
            # RSI
            if 'rsi_5' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.rsi(pl.col('close'), 5).alias('rsi_5')
                )
            
            if 'rsi_14' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.rsi(pl.col('close'), 14).alias('rsi_14')
                )
            
            # MACD
            if 'macd' in self.enabled_indicators or 'macd_signal' in self.enabled_indicators:
                macd_result = ta.macd(pl.col('close'), 12, 26, 9)
                if 'macd' in self.enabled_indicators:
                    result_df = result_df.with_columns(macd_result.alias('macd'))
                if 'macd_signal' in self.enabled_indicators:
                    result_df = result_df.with_columns(
                        ta.ema(macd_result, 9).alias('macd_signal')
                    )
            
            # Stochastic
            if 'stoch_k' in self.enabled_indicators or 'stoch_d' in self.enabled_indicators:
                stoch_k = ta.stoch(pl.col('high'), pl.col('low'), pl.col('close'), 14, 3, 3)
                if 'stoch_k' in self.enabled_indicators:
                    result_df = result_df.with_columns(stoch_k.alias('stoch_k'))
                if 'stoch_d' in self.enabled_indicators:
                    result_df = result_df.with_columns(
                        ta.sma(stoch_k, 3).alias('stoch_d')
                    )
            
            # CCI
            if 'cci' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.cci(pl.col('high'), pl.col('low'), pl.col('close'), 20).alias('cci')
                )
            
            # ADX
            if 'adx' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.adx(pl.col('high'), pl.col('low'), pl.col('close'), 14).alias('adx')
                )
            
            # MFI
            if 'mfi' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.mfi(pl.col('high'), pl.col('low'), pl.col('close'), pl.col('volume'), 14).alias('mfi')
                )
            
            # Bollinger Bands
            if 'bb_upper' in self.enabled_indicators or 'bb_lower' in self.enabled_indicators:
                bb_upper, bb_middle, bb_lower = ta.bbands(pl.col('close'), 20, 2, 2)
                if 'bb_upper' in self.enabled_indicators:
                    result_df = result_df.with_columns(bb_upper.alias('bb_upper'))
                if 'bb_lower' in self.enabled_indicators:
                    result_df = result_df.with_columns(bb_lower.alias('bb_lower'))
            
            # ATR and related volatility indicators
            if 'atr' in self.enabled_indicators:
                atr_14 = ta.atr(pl.col('high'), pl.col('low'), pl.col('close'), 14)
                result_df = result_df.with_columns(atr_14.alias('atr'))

                # ATR Percent
                if 'atr_percent' in self.enabled_indicators:
                    result_df = result_df.with_columns(
                        (atr_14 / pl.col('close') * 100).alias('atr_percent')
                    )

                # Normalized ATR
                if 'natr' in self.enabled_indicators:
                    result_df = result_df.with_columns(
                        ta.natr(pl.col('high'), pl.col('low'), pl.col('close'), 14).alias('natr')
                    )

            # True Range
            if 'true_range' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.trange(pl.col('high'), pl.col('low'), pl.col('close')).alias('true_range')
                )

            # Williams %R
            if 'williams_r' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.willr(pl.col('high'), pl.col('low'), pl.col('close'), 14).alias('williams_r')
                )

            # Rate of Change (ROC)
            if 'roc_10' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.roc(pl.col('close'), 10).alias('roc_10')
                )

            if 'roc_20' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.roc(pl.col('close'), 20).alias('roc_20')
                )

            # Momentum
            if 'momentum_10' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.mom(pl.col('close'), 10).alias('momentum_10')
                )

            if 'momentum_20' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.mom(pl.col('close'), 20).alias('momentum_20')
                )

            # Stochastic RSI
            if 'stoch_rsi_k' in self.enabled_indicators or 'stoch_rsi_d' in self.enabled_indicators:
                rsi_for_stoch = ta.rsi(pl.col('close'), 14)
                stoch_rsi_k = ta.stoch(rsi_for_stoch, rsi_for_stoch, rsi_for_stoch, 14, 3, 3)

                if 'stoch_rsi_k' in self.enabled_indicators:
                    result_df = result_df.with_columns(stoch_rsi_k.alias('stoch_rsi_k'))

                if 'stoch_rsi_d' in self.enabled_indicators:
                    result_df = result_df.with_columns(
                        ta.sma(stoch_rsi_k, 3).alias('stoch_rsi_d')
                    )

            # MACD Histogram
            if 'macd_histogram' in self.enabled_indicators:
                macd_line = ta.macd(pl.col('close'), 12, 26, 9)
                macd_signal = ta.ema(macd_line, 9)
                result_df = result_df.with_columns(
                    (macd_line - macd_signal).alias('macd_histogram')
                )

            # ADX with Directional Indicators
            if 'adx_plus_di' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.plus_di(pl.col('high'), pl.col('low'), pl.col('close'), 14).alias('adx_plus_di')
                )

            if 'adx_minus_di' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.minus_di(pl.col('high'), pl.col('low'), pl.col('close'), 14).alias('adx_minus_di')
                )

            # Aroon Indicators
            if 'aroon_up' in self.enabled_indicators or 'aroon_down' in self.enabled_indicators:
                aroon_up, aroon_down = ta.aroon(pl.col('high'), pl.col('low'), 14)

                if 'aroon_up' in self.enabled_indicators:
                    result_df = result_df.with_columns(aroon_up.alias('aroon_up'))

                if 'aroon_down' in self.enabled_indicators:
                    result_df = result_df.with_columns(aroon_down.alias('aroon_down'))

                if 'aroon_oscillator' in self.enabled_indicators:
                    result_df = result_df.with_columns(
                        (aroon_up - aroon_down).alias('aroon_oscillator')
                    )

            # Parabolic SAR
            if 'psar' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.sar(pl.col('high'), pl.col('low'), 0.02, 0.2).alias('psar')
                )

            # On-Balance Volume (OBV)
            if 'obv' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.obv(pl.col('close'), pl.col('volume')).alias('obv')
                )

            # Accumulation/Distribution Line
            if 'ad_line' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ad(pl.col('high'), pl.col('low'), pl.col('close'), pl.col('volume')).alias('ad_line')
                )

            # Chaikin Money Flow
            if 'cmf' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.cmf(pl.col('high'), pl.col('low'), pl.col('close'), pl.col('volume'), 20).alias('cmf')
                )

            # TRIX
            if 'trix' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.trix(pl.col('close'), 14).alias('trix')
                )

            # Ultimate Oscillator
            if 'ultimate_oscillator' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    ta.ultosc(pl.col('high'), pl.col('low'), pl.col('close'), 7, 14, 28).alias('ultimate_oscillator')
                )

            # Apply advanced indicators using custom implementations
            result_df = self._apply_advanced_indicators(result_df)

            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Error with polars-talib calculations: {e}")
            return df

    def _apply_advanced_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Apply advanced custom indicators"""
        try:
            result_df = df.clone()

            # Check which advanced indicators are enabled and apply them
            advanced_indicators_needed = [
                'ichimoku_tenkan', 'ichimoku_kijun', 'ichimoku_senkou_a', 'ichimoku_senkou_b', 'ichimoku_chikou',
                'keltner_upper', 'keltner_lower', 'keltner_middle',
                'bb_width', 'bb_percent',
                'vwma_20', 'volume_sma', 'volume_ratio', 'ease_of_movement',
                'chaikin_oscillator', 'fisher_transform', 'connors_rsi', 'coppock_curve',
                'pivot_point', 'resistance_1', 'resistance_2', 'support_1', 'support_2',
                'donchian_high', 'donchian_low', 'donchian_middle'
            ]

            # Check if any advanced indicators are enabled
            if any(indicator in self.enabled_indicators for indicator in advanced_indicators_needed):
                # Apply Ichimoku Cloud if any component is enabled
                ichimoku_indicators = ['ichimoku_tenkan', 'ichimoku_kijun', 'ichimoku_senkou_a', 'ichimoku_senkou_b', 'ichimoku_chikou']
                if any(indicator in self.enabled_indicators for indicator in ichimoku_indicators):
                    result_df = AdvancedIndicators.ichimoku_cloud(result_df)

                # Apply Keltner Channels if any component is enabled
                keltner_indicators = ['keltner_upper', 'keltner_lower', 'keltner_middle']
                if any(indicator in self.enabled_indicators for indicator in keltner_indicators):
                    result_df = AdvancedIndicators.keltner_channels(result_df)

                # Apply Bollinger Band derivatives if enabled
                bb_derivatives = ['bb_width', 'bb_percent']
                if any(indicator in self.enabled_indicators for indicator in bb_derivatives):
                    result_df = AdvancedIndicators.bollinger_band_derivatives(result_df)

                # Apply volume-weighted indicators if enabled
                volume_indicators = ['vwma_20', 'volume_sma', 'volume_ratio', 'ease_of_movement']
                if any(indicator in self.enabled_indicators for indicator in volume_indicators):
                    result_df = AdvancedIndicators.volume_weighted_indicators(result_df)

                # Apply custom oscillators if enabled
                oscillator_indicators = ['chaikin_oscillator', 'fisher_transform', 'connors_rsi', 'coppock_curve']
                if any(indicator in self.enabled_indicators for indicator in oscillator_indicators):
                    result_df = AdvancedIndicators.custom_oscillators(result_df)

                # Apply support/resistance levels if enabled
                sr_indicators = ['pivot_point', 'resistance_1', 'resistance_2', 'support_1', 'support_2', 'donchian_high', 'donchian_low', 'donchian_middle']
                if any(indicator in self.enabled_indicators for indicator in sr_indicators):
                    result_df = AdvancedIndicators.support_resistance_levels(result_df)

            return result_df

        except Exception as e:
            logger.error(f"[ERROR] Advanced indicators calculation failed: {e}")
            return df
    
    def _calculate_with_polars_native(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate indicators using native polars operations (fallback)"""
        try:
            result_df = df.clone()
            
            # Simple Moving Average
            if 'sma_20' in self.enabled_indicators:
                result_df = result_df.with_columns(
                    pl.col('close').rolling_mean(window_size=20).alias('sma_20')
                )
            
            # Exponential Moving Averages (approximation)
            ema_periods = [5, 10, 13, 20, 21, 30, 50, 100]
            for period in ema_periods:
                indicator_name = f'ema_{period}'
                if indicator_name in self.enabled_indicators:
                    alpha = 2.0 / (period + 1)
                    result_df = result_df.with_columns(
                        pl.col('close').ewm_mean(alpha=alpha).alias(indicator_name)
                    )
            
            # RSI (simplified calculation)
            if 'rsi_14' in self.enabled_indicators:
                result_df = result_df.with_columns([
                    (pl.col('close') - pl.col('close').shift(1)).alias('price_change')
                ])
                
                result_df = result_df.with_columns([
                    pl.when(pl.col('price_change') > 0)
                    .then(pl.col('price_change'))
                    .otherwise(0).alias('gain'),
                    pl.when(pl.col('price_change') < 0)
                    .then(-pl.col('price_change'))
                    .otherwise(0).alias('loss')
                ])
                
                result_df = result_df.with_columns([
                    pl.col('gain').rolling_mean(window_size=14).alias('avg_gain'),
                    pl.col('loss').rolling_mean(window_size=14).alias('avg_loss')
                ])
                
                result_df = result_df.with_columns([
                    (100 - (100 / (1 + (pl.col('avg_gain') / pl.col('avg_loss'))))).alias('rsi_14')
                ])
                
                # Clean up temporary columns
                result_df = result_df.drop(['price_change', 'gain', 'loss', 'avg_gain', 'avg_loss'])
            
            # VWAP (Volume Weighted Average Price)
            if 'vwap' in self.enabled_indicators:
                result_df = result_df.with_columns([
                    (pl.col('close') * pl.col('volume')).alias('price_volume')
                ])
                
                result_df = result_df.with_columns([
                    (pl.col('price_volume').cumsum() / pl.col('volume').cumsum()).alias('vwap')
                ])
                
                result_df = result_df.drop(['price_volume'])
            
            # Bollinger Bands (simplified)
            if 'bb_upper' in self.enabled_indicators or 'bb_lower' in self.enabled_indicators:
                result_df = result_df.with_columns([
                    pl.col('close').rolling_mean(window_size=20).alias('bb_middle'),
                    pl.col('close').rolling_std(window_size=20).alias('bb_std')
                ])
                
                if 'bb_upper' in self.enabled_indicators:
                    result_df = result_df.with_columns([
                        (pl.col('bb_middle') + (pl.col('bb_std') * 2)).alias('bb_upper')
                    ])
                
                if 'bb_lower' in self.enabled_indicators:
                    result_df = result_df.with_columns([
                        (pl.col('bb_middle') - (pl.col('bb_std') * 2)).alias('bb_lower')
                    ])
                
                result_df = result_df.drop(['bb_middle', 'bb_std'])
            
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] Error with native polars calculations: {e}")
            return df
    
    async def calculate_realtime_indicators(self, live_data: Dict[str, Any], 
                                          historical_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Calculate indicators for real-time data
        
        Args:
            live_data: Current live price data
            historical_df: Historical data for context
        
        Returns:
            Dictionary with calculated indicators
        """
        try:
            symbol = live_data.get('symbol')
            if not symbol:
                return {}
            
            # Get historical data for this symbol
            symbol_df = historical_df.filter(pl.col('symbol') == symbol)
            
            if symbol_df.is_empty():
                logger.warning(f"[WARN] No historical data for {symbol}")
                return {}
            
            # Add current live data as latest row
            current_row = {
                'timestamp': datetime.now(),
                'open': live_data.get('open', symbol_df['close'][-1]),
                'high': live_data.get('high', live_data.get('ltp', symbol_df['close'][-1])),
                'low': live_data.get('low', live_data.get('ltp', symbol_df['close'][-1])),
                'close': live_data.get('ltp', symbol_df['close'][-1]),
                'volume': live_data.get('volume', 0),
                'symbol': symbol,
                'exchange': symbol_df['exchange'][0],
                'token': symbol_df['token'][0]
            }
            
            # Append to historical data
            updated_df = pl.concat([
                symbol_df,
                pl.DataFrame([current_row])
            ])
            
            # Calculate indicators
            with_indicators = self._calculate_symbol_indicators(updated_df)
            
            # Return latest indicator values
            if len(with_indicators) > 0:
                latest_row = with_indicators.tail(1).to_dicts()[0]
                
                # Extract only indicator values
                indicators = {}
                for col in with_indicators.columns:
                    if col not in ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'symbol', 'exchange', 'token']:
                        indicators[col] = latest_row.get(col)
                
                return indicators
            
            return {}
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating real-time indicators: {e}")
            return {}
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            'chunk_size': self.chunk_size,
            'parallel_workers': self.parallel_workers,
            'memory_limit_mb': self.memory_limit_mb,
            'enabled_indicators': len(self.enabled_indicators),
            'polars_talib_available': POLARS_TALIB_AVAILABLE,
            'cache_size': len(self.indicator_cache)
        }

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

async def calculate_indicators_for_file(input_file: str, output_file: str, 
                                      config: Dict[str, Any]) -> bool:
    """Calculate indicators for a parquet file"""
    try:
        logger.info(f"[STATUS] Calculating indicators for {input_file}")
        
        # Load data
        df = pl.read_parquet(input_file)
        
        # Initialize engine
        engine = IndicatorEngine(config)
        
        # Calculate indicators
        result_df = await engine.calculate_indicators_batch(df)
        
        # Save result
        result_df.write_parquet(output_file)
        
        logger.info(f"[SUCCESS] Indicators saved to {output_file}")
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] Error calculating indicators for file: {e}")
        return False

#!/usr/bin/env python3
"""
LOGGING MANAGER
Centralized logging configuration for the trading system

Features:
- Structured logging with proper formatting
- File rotation and size management
- Console and file output
- Agent-specific loggers
- Performance logging
"""

import logging
import logging.handlers
import os
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any
import json

class LoggingManager:
    """
    Centralized logging manager for the trading system
    
    Provides:
    - Structured logging configuration
    - File rotation and management
    - Agent-specific loggers
    - Performance metrics logging
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize logging manager"""
        self.config = config or self._get_default_config()
        self.loggers = {}
        
        # Setup logging
        self._setup_logging()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default logging configuration"""
        return {
            'log_level': 'INFO',
            'log_file': 'logs/trading_system.log',
            'max_log_files': 10,
            'max_log_size_mb': 100,
            'console_logging': True,
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S'
        }
    
    def _setup_logging(self):
        """Setup logging configuration"""
        try:
            # Create logs directory
            log_file = Path(self.config['log_file'])
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Configure root logger
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, self.config['log_level'].upper()))
            
            # Clear existing handlers
            root_logger.handlers.clear()
            
            # Create formatter
            formatter = logging.Formatter(
                self.config['format'],
                datefmt=self.config['date_format']
            )
            
            # File handler with rotation
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.config['max_log_size_mb'] * 1024 * 1024,
                backupCount=self.config['max_log_files']
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
            # Console handler
            if self.config['console_logging']:
                console_handler = logging.StreamHandler()
                console_handler.setFormatter(formatter)
                root_logger.addHandler(console_handler)
            
            # Create session-specific log file
            session_log = log_file.parent / f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            session_handler = logging.FileHandler(session_log)
            session_handler.setFormatter(formatter)
            root_logger.addHandler(session_handler)
            
            logging.info("[LOGGING] Logging manager initialized")
            logging.info(f"[LOGGING] Log file: {log_file}")
            logging.info(f"[LOGGING] Session log: {session_log}")
            
        except Exception as e:
            print(f"[ERROR] Failed to setup logging: {e}")
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get logger for specific component"""
        try:
            if name not in self.loggers:
                logger = logging.getLogger(name)
                self.loggers[name] = logger
            
            return self.loggers[name]
            
        except Exception as e:
            print(f"[ERROR] Failed to get logger for {name}: {e}")
            return logging.getLogger(name)
    
    def log_performance(self, component: str, operation: str, duration: float, **kwargs):
        """Log performance metrics"""
        try:
            perf_logger = self.get_logger(f"performance.{component}")
            
            perf_data = {
                'component': component,
                'operation': operation,
                'duration_ms': round(duration * 1000, 2),
                'timestamp': datetime.now().isoformat(),
                **kwargs
            }
            
            perf_logger.info(f"[PERF] {json.dumps(perf_data)}")
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to log performance: {e}")
    
    def log_trade(self, trade_data: Dict[str, Any]):
        """Log trade information"""
        try:
            trade_logger = self.get_logger("trades")
            trade_logger.info(f"[TRADE] {json.dumps(trade_data, default=str)}")
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to log trade: {e}")
    
    def log_signal(self, signal_data: Dict[str, Any]):
        """Log signal information"""
        try:
            signal_logger = self.get_logger("signals")
            signal_logger.info(f"[SIGNAL] {json.dumps(signal_data, default=str)}")
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to log signal: {e}")
    
    def log_market_data(self, market_data: Dict[str, Any]):
        """Log market data information"""
        try:
            market_logger = self.get_logger("market_data")
            market_logger.debug(f"[MARKET] {json.dumps(market_data, default=str)}")
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to log market data: {e}")
    
    def log_error_with_context(self, component: str, error: Exception, context: Dict[str, Any] = None):
        """Log error with additional context"""
        try:
            error_logger = self.get_logger(f"errors.{component}")
            
            error_data = {
                'component': component,
                'error_type': type(error).__name__,
                'error_message': str(error),
                'timestamp': datetime.now().isoformat(),
                'context': context or {}
            }
            
            error_logger.error(f"[ERROR] {json.dumps(error_data, default=str)}")
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to log error with context: {e}")
    
    def create_agent_logger(self, agent_name: str) -> logging.Logger:
        """Create logger specifically for an agent"""
        try:
            logger_name = f"agents.{agent_name}"
            return self.get_logger(logger_name)
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to create agent logger for {agent_name}: {e}")
            return logging.getLogger(logger_name)
    
    def set_log_level(self, level: str):
        """Set logging level"""
        try:
            numeric_level = getattr(logging, level.upper())
            logging.getLogger().setLevel(numeric_level)
            logging.info(f"[LOGGING] Log level set to {level}")
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to set log level: {e}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """Get logging statistics"""
        try:
            log_file = Path(self.config['log_file'])
            
            stats = {
                'log_file': str(log_file),
                'log_file_exists': log_file.exists(),
                'log_file_size_mb': 0,
                'active_loggers': len(self.loggers),
                'logger_names': list(self.loggers.keys()),
                'log_level': logging.getLogger().level,
                'timestamp': datetime.now().isoformat()
            }
            
            if log_file.exists():
                stats['log_file_size_mb'] = round(log_file.stat().st_size / (1024 * 1024), 2)
            
            return stats
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to get log stats: {e}")
            return {}
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """Clean up old log files"""
        try:
            log_dir = Path(self.config['log_file']).parent
            cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
            
            cleaned_count = 0
            for log_file in log_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    cleaned_count += 1
            
            logging.info(f"[CLEANUP] Cleaned up {cleaned_count} old log files")
            
        except Exception as e:
            logging.error(f"[ERROR] Failed to cleanup old logs: {e}")
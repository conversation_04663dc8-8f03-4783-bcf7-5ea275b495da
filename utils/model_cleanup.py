#!/usr/bin/env python3
"""
Model Cleanup Utility
Simple tool to find and remove old model data before creating new models
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict

def find_old_model_locations() -> Dict[str, List[str]]:
    """Find all locations where old model data is stored"""
    
    base_path = Path("/media/jmk/BKP/Documents/Equity")
    
    model_locations = {
        "Main Models Directory": [],
        "Data Models Directory": [],
        "Test Models Directory": [],
        "Enhanced Models": [],
        "Backup Models": [],
        "Registry Models": [],
        "Checkpoints": [],
        "Individual Files": []
    }
    
    # Main models directory
    main_models = base_path / "models"
    if main_models.exists():
        for item in main_models.iterdir():
            model_locations["Main Models Directory"].append(str(item))
    
    # Data models directory
    data_models = base_path / "data" / "models"
    if data_models.exists():
        # AI training ensemble
        ai_ensemble = data_models / "ai_training_ensemble"
        if ai_ensemble.exists():
            model_locations["Data Models Directory"].append(str(ai_ensemble))
        
        # Enhanced models
        enhanced = data_models / "enhanced"
        if enhanced.exists():
            model_locations["Enhanced Models"].append(str(enhanced))
        
        # Backup models
        bkp = data_models / "bkp"
        if bkp.exists():
            model_locations["Backup Models"].append(str(bkp))
        
        # Registry
        registry = data_models / "registry"
        if registry.exists():
            model_locations["Registry Models"].append(str(registry))
        
        # Checkpoints
        checkpoints = data_models / "online_checkpoints"
        if checkpoints.exists():
            for item in checkpoints.iterdir():
                model_locations["Checkpoints"].append(str(item))
    
    # Test models
    test_models = base_path / "test_models"
    if test_models.exists():
        for item in test_models.iterdir():
            model_locations["Test Models Directory"].append(str(item))
    
    # Individual model files
    for pattern in ["*.joblib", "*.pkl", "*.zip", "*.txt"]:
        for file in base_path.rglob(pattern):
            if any(keyword in str(file).lower() for keyword in ["model", "scaler", "encoder", "lightgbm", "tabnet"]):
                model_locations["Individual Files"].append(str(file))
    
    return model_locations

def print_model_locations():
    """Print all found model locations"""
    locations = find_old_model_locations()
    
    print("=" * 60)
    print("OLD MODEL DATA LOCATIONS FOUND:")
    print("=" * 60)
    
    total_items = 0
    for category, items in locations.items():
        if items:
            print(f"\n📁 {category}:")
            for item in items:
                print(f"   • {item}")
                total_items += 1
    
    print(f"\n📊 Total items found: {total_items}")
    return locations

def remove_model_data(locations: Dict[str, List[str]], categories_to_remove: List[str] = None):
    """Remove specified model data categories"""
    
    if categories_to_remove is None:
        categories_to_remove = list(locations.keys())
    
    removed_count = 0
    
    for category in categories_to_remove:
        if category in locations:
            print(f"\n🗑️  Removing {category}...")
            
            for item_path in locations[category]:
                try:
                    path = Path(item_path)
                    if path.exists():
                        if path.is_dir():
                            shutil.rmtree(path)
                            print(f"   ✅ Removed directory: {path}")
                        else:
                            path.unlink()
                            print(f"   ✅ Removed file: {path}")
                        removed_count += 1
                    else:
                        print(f"   ⚠️  Not found: {path}")
                except Exception as e:
                    print(f"   ❌ Error removing {item_path}: {e}")
    
    print(f"\n✅ Removed {removed_count} items")

def interactive_cleanup():
    """Interactive cleanup with user confirmation"""
    
    print("🧹 AI Training Agent - Model Cleanup Utility")
    print("=" * 50)
    
    locations = print_model_locations()
    
    if not any(locations.values()):
        print("\n✅ No old model data found!")
        return
    
    print("\n" + "=" * 50)
    print("CLEANUP OPTIONS:")
    print("=" * 50)
    
    categories = [cat for cat, items in locations.items() if items]
    
    for i, category in enumerate(categories, 1):
        item_count = len(locations[category])
        print(f"{i}. {category} ({item_count} items)")
    
    print(f"{len(categories) + 1}. Remove ALL model data")
    print(f"{len(categories) + 2}. Cancel")
    
    while True:
        try:
            choice = input(f"\nSelect option (1-{len(categories) + 2}): ").strip()
            
            if choice == str(len(categories) + 2):  # Cancel
                print("❌ Cleanup cancelled")
                return
            
            elif choice == str(len(categories) + 1):  # Remove all
                confirm = input("⚠️  Remove ALL model data? (yes/no): ").strip().lower()
                if confirm == 'yes':
                    remove_model_data(locations)
                    print("✅ All model data removed!")
                else:
                    print("❌ Cleanup cancelled")
                return
            
            elif choice.isdigit() and 1 <= int(choice) <= len(categories):
                selected_category = categories[int(choice) - 1]
                item_count = len(locations[selected_category])
                
                confirm = input(f"⚠️  Remove {selected_category} ({item_count} items)? (yes/no): ").strip().lower()
                if confirm == 'yes':
                    remove_model_data(locations, [selected_category])
                    print(f"✅ {selected_category} removed!")
                else:
                    print("❌ Cleanup cancelled")
                return
            
            else:
                print("❌ Invalid choice. Please try again.")
        
        except KeyboardInterrupt:
            print("\n❌ Cleanup cancelled")
            return
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    interactive_cleanup()
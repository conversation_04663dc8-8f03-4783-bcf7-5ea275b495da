#!/usr/bin/env python3
"""
MODERN WEBSOCKET MANAGER
Clean WebSocket implementation for SmartAPI v2 with proper async handling

Features:
- Modern SmartAPI WebSocket v2 integration
- Proper async/await patterns
- Robust error handling and reconnection
- Real-time market data streaming
"""

import asyncio
import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass

try:
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class WebSocketConfig:
    """WebSocket configuration"""
    auth_token: str
    api_key: str
    client_code: str
    feed_token: str
    max_reconnect_attempts: int = 5
    reconnect_delay: int = 5

class ModernWebSocketManager:
    """
    Modern WebSocket manager for SmartAPI v2
    
    Features:
    - Async-friendly WebSocket handling
    - Automatic reconnection with exponential backoff
    - Proper callback management
    - Thread-safe operations
    """
    
    def __init__(self, config: WebSocketConfig):
        self.config = config
        self.websocket_client = None
        self.connected = False
        self.reconnect_attempts = 0
        
        # Callbacks
        self.on_connect_callback = None
        self.on_data_callback = None
        self.on_error_callback = None
        self.on_close_callback = None
        
        # Connection management
        self.connection_thread = None
        self.should_reconnect = True
        
        logger.info("[INIT] Modern WebSocket Manager initialized")
    
    def set_callbacks(self, 
                     on_connect: Optional[Callable] = None,
                     on_data: Optional[Callable] = None,
                     on_error: Optional[Callable] = None,
                     on_close: Optional[Callable] = None):
        """Set callback functions"""
        self.on_connect_callback = on_connect
        self.on_data_callback = on_data
        self.on_error_callback = on_error
        self.on_close_callback = on_close
    
    async def connect(self) -> bool:
        """Connect to WebSocket"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI not available")
                return False
            
            logger.info("[WEBSOCKET] Connecting to SmartAPI WebSocket v2...")
            
            # Create WebSocket client
            self.websocket_client = SmartWebSocketV2(
                auth_token=self.config.auth_token,
                api_key=self.config.api_key,
                client_code=self.config.client_code,
                feed_token=self.config.feed_token
            )
            
            # Set up callbacks
            self.websocket_client.on_open = self._on_open
            self.websocket_client.on_data = self._on_data
            self.websocket_client.on_error = self._on_error
            self.websocket_client.on_close = self._on_close
            
            # Connect in separate thread
            success = await self._connect_in_thread()
            
            if success:
                logger.info("[SUCCESS] WebSocket connected successfully")
                self.reconnect_attempts = 0
                return True
            else:
                logger.error("[ERROR] WebSocket connection failed")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to connect WebSocket: {e}")
            return False
    
    async def _connect_in_thread(self) -> bool:
        """Connect WebSocket in separate thread"""
        try:
            # Create connection event
            connection_event = threading.Event()
            connection_result = {'success': False}
            
            def connect_worker():
                try:
                    self.websocket_client.connect()
                    connection_result['success'] = True
                except Exception as e:
                    logger.error(f"[ERROR] WebSocket connection error: {e}")
                    connection_result['success'] = False
                finally:
                    connection_event.set()
            
            # Start connection thread
            self.connection_thread = threading.Thread(target=connect_worker, daemon=True)
            self.connection_thread.start()
            
            # Wait for connection with timeout
            await asyncio.sleep(5)  # Give 5 seconds for connection
            
            return self.connected
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to connect in thread: {e}")
            return False
    
    def subscribe(self, symbols: List[Dict[str, Any]], mode: int = 1) -> bool:
        """Subscribe to symbols"""
        try:
            if not self.connected or not self.websocket_client:
                logger.error("[ERROR] WebSocket not connected")
                return False
            
            # Create correlation ID
            correlation_id = f"sub_{int(time.time())}"
            
            # Subscribe to symbols
            self.websocket_client.subscribe(correlation_id, mode, symbols)
            
            logger.info(f"[WEBSOCKET] Subscribed to {len(symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe: {e}")
            return False
    
    def unsubscribe(self, symbols: List[Dict[str, Any]], mode: int = 1) -> bool:
        """Unsubscribe from symbols"""
        try:
            if not self.connected or not self.websocket_client:
                logger.error("[ERROR] WebSocket not connected")
                return False
            
            # Create correlation ID
            correlation_id = f"unsub_{int(time.time())}"
            
            # Unsubscribe from symbols
            self.websocket_client.unsubscribe(correlation_id, mode, symbols)
            
            logger.info(f"[WEBSOCKET] Unsubscribed from {len(symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to unsubscribe: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect WebSocket"""
        try:
            logger.info("[WEBSOCKET] Disconnecting...")
            
            self.should_reconnect = False
            self.connected = False
            
            if self.websocket_client:
                self.websocket_client.close_connection()
            
            logger.info("[SUCCESS] WebSocket disconnected")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to disconnect: {e}")
    
    async def reconnect(self) -> bool:
        """Reconnect WebSocket with exponential backoff"""
        try:
            if self.reconnect_attempts >= self.config.max_reconnect_attempts:
                logger.error(f"[ERROR] Max reconnection attempts ({self.config.max_reconnect_attempts}) reached")
                return False
            
            self.reconnect_attempts += 1
            
            # Calculate backoff delay
            delay = min(self.config.reconnect_delay * (2 ** (self.reconnect_attempts - 1)), 60)
            
            logger.info(f"[RECONNECT] Attempt {self.reconnect_attempts}/{self.config.max_reconnect_attempts} in {delay}s")
            
            await asyncio.sleep(delay)
            
            # Attempt reconnection
            return await self.connect()
            
        except Exception as e:
            logger.error(f"[ERROR] Reconnection failed: {e}")
            return False
    
    def _on_open(self, ws):
        """WebSocket open callback"""
        try:
            logger.info("[WEBSOCKET] Connection opened")
            self.connected = True
            
            if self.on_connect_callback:
                self.on_connect_callback(ws)
                
        except Exception as e:
            logger.error(f"[ERROR] Error in on_open callback: {e}")
    
    def _on_data(self, ws, data):
        """WebSocket data callback"""
        try:
            if self.on_data_callback:
                self.on_data_callback(ws, data)
                
        except Exception as e:
            logger.error(f"[ERROR] Error in on_data callback: {e}")
    
    def _on_error(self, ws, error):
        """WebSocket error callback"""
        try:
            logger.error(f"[WEBSOCKET] Error: {error}")
            self.connected = False
            
            if self.on_error_callback:
                self.on_error_callback(ws, error)
            
            # Schedule reconnection if enabled
            if self.should_reconnect:
                asyncio.create_task(self.reconnect())
                
        except Exception as e:
            logger.error(f"[ERROR] Error in on_error callback: {e}")
    
    def _on_close(self, ws, code=None, reason=None):
        """WebSocket close callback"""
        try:
            logger.info(f"[WEBSOCKET] Connection closed: {code} - {reason}")
            self.connected = False
            
            if self.on_close_callback:
                self.on_close_callback(ws, code, reason)
                
        except Exception as e:
            logger.error(f"[ERROR] Error in on_close callback: {e}")
    
    def is_connected(self) -> bool:
        """Check if WebSocket is connected"""
        return self.connected
    
    def get_status(self) -> Dict[str, Any]:
        """Get WebSocket status"""
        return {
            'connected': self.connected,
            'reconnect_attempts': self.reconnect_attempts,
            'max_reconnect_attempts': self.config.max_reconnect_attempts,
            'should_reconnect': self.should_reconnect,
            'timestamp': datetime.now().isoformat()
        }
#!/usr/bin/env python3
"""
Robust WebSocket Manager for SmartAPI
Addresses common connection issues with enhanced error handling and retry logic
"""

import os
import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
import pyotp

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# SmartAPI imports
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    SMARTAPI_AVAILABLE = True
except ImportError:
    print("[ERROR] SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None
    SMARTAPI_AVAILABLE = False

# Local imports
try:
    from utils.instrument_master import InstrumentMaster
except ImportError:
    InstrumentMaster = None

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"

@dataclass
class ConnectionMetrics:
    connection_attempts: int = 0
    successful_connections: int = 0
    failed_connections: int = 0
    last_connection_time: Optional[datetime] = None
    last_error: Optional[str] = None
    total_messages_received: int = 0
    connection_uptime: timedelta = timedelta()

class RobustWebSocketManager:
    """Enhanced WebSocket manager with robust error handling"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Authentication
        self.api_key = os.getenv('SMARTAPI_API_KEY')
        self.username = os.getenv('SMARTAPI_USERNAME')
        self.password = os.getenv('SMARTAPI_PASSWORD')
        self.totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
        
        # SmartAPI clients
        self.smartapi_client = None
        self.websocket_client = None
        self.auth_token = None
        self.feed_token = None
        
        # Connection management
        self.state = ConnectionState.DISCONNECTED
        self.metrics = ConnectionMetrics()
        self.connection_lock = threading.Lock()
        self.stop_event = threading.Event()
        
        # Configuration
        self.max_retry_attempts = config.get('max_retry_attempts', 5)
        self.retry_delay_base = config.get('retry_delay_base', 2)
        self.connection_timeout = config.get('connection_timeout', 15)
        self.heartbeat_interval = config.get('heartbeat_interval', 30)
        
        # Callbacks
        self.on_connected_callback = None
        self.on_disconnected_callback = None
        self.on_data_callback = None
        self.on_error_callback = None
        
        # Subscription management
        self.subscribed_symbols = set()
        self.pending_subscriptions = []

        # Instrument master for token mapping
        self.instrument_master = None
        if InstrumentMaster:
            self.instrument_master = InstrumentMaster()
            # Load instruments immediately
            try:
                self.instrument_master.load_instruments()
                logger.info("[INIT] Instrument master loaded successfully")
            except Exception as e:
                logger.warning(f"[WARN] Failed to load instrument master: {e}")
                self.instrument_master = None

        logger.info("[INIT] Robust WebSocket Manager initialized")
    
    def set_callbacks(self, 
                     on_connected: Optional[Callable] = None,
                     on_disconnected: Optional[Callable] = None,
                     on_data: Optional[Callable] = None,
                     on_error: Optional[Callable] = None):
        """Set callback functions for WebSocket events"""
        self.on_connected_callback = on_connected
        self.on_disconnected_callback = on_disconnected
        self.on_data_callback = on_data
        self.on_error_callback = on_error
    
    async def authenticate(self) -> bool:
        """Authenticate with SmartAPI with enhanced error handling"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI library not available")
                return False
            
            if not all([self.api_key, self.username, self.password, self.totp_token]):
                logger.error("[ERROR] Missing authentication credentials")
                return False
            
            logger.info("[AUTH] Starting authentication process...")
            
            # Initialize SmartConnect
            self.smartapi_client = SmartConnect(api_key=self.api_key)
            
            # Generate TOTP with validation
            try:
                totp = pyotp.TOTP(self.totp_token)
                totp_code = totp.now()
                logger.info(f"[AUTH] Generated TOTP: {totp_code}")
            except Exception as e:
                logger.error(f"[ERROR] TOTP generation failed: {e}")
                return False
            
            # Attempt login with retry logic
            for attempt in range(3):
                try:
                    data = self.smartapi_client.generateSession(
                        clientCode=self.username,
                        password=self.password,
                        totp=totp_code
                    )
                    
                    if data.get('status'):
                        self.auth_token = data['data']['jwtToken']
                        self.feed_token = self.smartapi_client.getfeedToken()
                        logger.info("[SUCCESS] Authentication successful")
                        return True
                    else:
                        error_msg = data.get('message', 'Unknown authentication error')
                        logger.error(f"[ERROR] Authentication failed: {error_msg}")
                        
                        # If TOTP error, regenerate for next attempt
                        if 'totp' in error_msg.lower() and attempt < 2:
                            await asyncio.sleep(30)  # Wait for new TOTP
                            totp_code = totp.now()
                            logger.info(f"[AUTH] Regenerated TOTP for retry: {totp_code}")
                        else:
                            return False
                            
                except Exception as e:
                    logger.error(f"[ERROR] Authentication attempt {attempt + 1} failed: {e}")
                    if attempt < 2:
                        await asyncio.sleep(5)
            
            return False
            
        except Exception as e:
            logger.error(f"[ERROR] Authentication process failed: {e}")
            return False
    
    async def connect(self) -> bool:
        """Establish WebSocket connection with robust error handling"""
        with self.connection_lock:
            if self.state in [ConnectionState.CONNECTED, ConnectionState.CONNECTING]:
                logger.warning("[WARN] Connection already in progress or established")
                return self.state == ConnectionState.CONNECTED
            
            self.state = ConnectionState.CONNECTING
            self.metrics.connection_attempts += 1
        
        try:
            # Ensure authentication
            if not self.auth_token or not self.feed_token:
                logger.info("[AUTH] Authenticating before connection...")
                if not await self.authenticate():
                    self.state = ConnectionState.FAILED
                    return False
            
            # Initialize WebSocket with enhanced configuration
            self.websocket_client = SmartWebSocketV2(
                self.auth_token,
                self.api_key,
                self.username,
                self.feed_token,
                max_retry_attempt=self.max_retry_attempts
            )
            
            # Set up event handlers
            self.websocket_client.on_open = self._on_websocket_open
            self.websocket_client.on_data = self._on_websocket_data
            self.websocket_client.on_error = self._on_websocket_error
            self.websocket_client.on_close = self._on_websocket_close
            
            # Attempt connection with timeout
            logger.info("[CONNECT] Attempting WebSocket connection...")
            
            connection_successful = await self._connect_with_timeout()
            
            if connection_successful:
                self.state = ConnectionState.CONNECTED
                self.metrics.successful_connections += 1
                self.metrics.last_connection_time = datetime.now()
                logger.info("[SUCCESS] WebSocket connected successfully")
                
                # Process pending subscriptions
                if self.pending_subscriptions:
                    await self._process_pending_subscriptions()
                
                return True
            else:
                self.state = ConnectionState.FAILED
                self.metrics.failed_connections += 1
                logger.error("[ERROR] WebSocket connection failed")
                return False
                
        except Exception as e:
            self.state = ConnectionState.FAILED
            self.metrics.failed_connections += 1
            self.metrics.last_error = str(e)
            logger.error(f"[ERROR] Connection process failed: {e}")
            return False
    
    async def _connect_with_timeout(self) -> bool:
        """Connect with timeout using threading approach (like working example)"""
        try:
            logger.info("[CONNECT] Starting WebSocket connection in thread...")

            # Load instrument master if not already loaded
            if self.instrument_master and not hasattr(self.instrument_master, 'symbol_to_token'):
                logger.info("[CONNECT] Loading instrument master...")
                self.instrument_master.load_instruments()

            # Start connection in separate thread (proven working approach)
            connection_thread = threading.Thread(target=self.websocket_client.connect, daemon=True)
            connection_thread.start()

            # Wait for connection to establish
            max_wait = self.connection_timeout
            wait_interval = 0.5
            waited = 0

            while waited < max_wait:
                await asyncio.sleep(wait_interval)
                waited += wait_interval

                # Check if connection is established
                if self.state == ConnectionState.CONNECTED:
                    logger.info(f"[SUCCESS] Connection established after {waited:.1f}s")
                    return True

            # If we get here, connection timed out
            logger.warning(f"[TIMEOUT] Connection not established after {max_wait}s")

            # But check if the thread succeeded anyway (sometimes state update is delayed)
            if connection_thread.is_alive():
                logger.info("[INFO] Connection thread still running, giving more time...")
                await asyncio.sleep(2)  # Give a bit more time
                if self.state == ConnectionState.CONNECTED:
                    return True

            return False

        except Exception as e:
            logger.error(f"[ERROR] Connection process failed: {e}")
            return False
    
    def _on_websocket_open(self, wsapp):
        """Handle WebSocket connection opened"""
        logger.info("[CONNECT] WebSocket connection opened")
        self.state = ConnectionState.CONNECTED
        
        if self.on_connected_callback:
            try:
                self.on_connected_callback()
            except Exception as e:
                logger.error(f"[ERROR] Connected callback error: {e}")
    
    def _on_websocket_data(self, wsapp, message):
        """Handle incoming WebSocket data"""
        try:
            self.metrics.total_messages_received += 1
            
            if self.on_data_callback:
                self.on_data_callback(message)
                
        except Exception as e:
            logger.error(f"[ERROR] Data processing error: {e}")
    
    def _on_websocket_error(self, wsapp, error):
        """Handle WebSocket errors"""
        logger.error(f"[ERROR] WebSocket error: {error}")
        self.metrics.last_error = str(error)
        
        if self.on_error_callback:
            try:
                self.on_error_callback(error)
            except Exception as e:
                logger.error(f"[ERROR] Error callback failed: {e}")
    
    def _on_websocket_close(self, wsapp):
        """Handle WebSocket connection closed"""
        logger.warning("[CONNECT] WebSocket connection closed")
        
        if self.state == ConnectionState.CONNECTED:
            self.state = ConnectionState.DISCONNECTED
            
            if self.on_disconnected_callback:
                try:
                    self.on_disconnected_callback()
                except Exception as e:
                    logger.error(f"[ERROR] Disconnected callback error: {e}")
            
            # Attempt reconnection if not intentionally stopped
            if not self.stop_event.is_set():
                asyncio.create_task(self._attempt_reconnection())
    
    async def _attempt_reconnection(self):
        """Attempt to reconnect with exponential backoff"""
        if self.state == ConnectionState.RECONNECTING:
            return  # Already reconnecting
        
        self.state = ConnectionState.RECONNECTING
        logger.info("[RECONNECT] Starting reconnection process...")
        
        for attempt in range(self.max_retry_attempts):
            if self.stop_event.is_set():
                break
            
            delay = self.retry_delay_base * (2 ** attempt)
            logger.info(f"[RECONNECT] Attempt {attempt + 1}/{self.max_retry_attempts} in {delay}s")
            
            await asyncio.sleep(delay)
            
            if await self.connect():
                logger.info("[SUCCESS] Reconnection successful")
                return
        
        logger.error("[ERROR] All reconnection attempts failed")
        self.state = ConnectionState.FAILED
    
    async def subscribe_symbols(self, symbols: List[str]) -> bool:
        """Subscribe to symbols using proper token-based subscription"""
        try:
            if self.state != ConnectionState.CONNECTED:
                logger.warning("[WARN] Not connected, storing symbols for later subscription")
                self.pending_subscriptions.extend(symbols)
                return False

            if not self.instrument_master:
                logger.error("[ERROR] Instrument master not available for token mapping")
                return False

            logger.info(f"[SUBSCRIBE] Converting {len(symbols)} symbols to tokens...")

            # Convert symbols to tokens
            tokens = []
            for symbol in symbols:
                token = self.instrument_master.get_token(symbol)
                if token:
                    tokens.append(token)
                    logger.debug(f"[TOKEN] {symbol}: {token}")
                else:
                    logger.warning(f"[WARN] Token not found for symbol: {symbol}")

            if not tokens:
                logger.error("[ERROR] No valid tokens found for subscription")
                return False

            # Create subscription data in correct format
            correlation_id = "robust_ws_subscription"
            mode = 1  # LTP mode
            token_list = [{
                "exchangeType": 1,  # NSE
                "tokens": tokens
            }]

            logger.info(f"[SUBSCRIBE] Subscribing to {len(tokens)} tokens: {tokens[:5]}{'...' if len(tokens) > 5 else ''}")

            # Subscribe using the correct method
            self.websocket_client.subscribe(correlation_id, mode, token_list)

            # Add to subscribed symbols
            self.subscribed_symbols.update(symbols)

            logger.info(f"[SUCCESS] Subscription request sent for {len(symbols)} symbols")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Symbol subscription failed: {e}")
            return False
    
    async def _process_pending_subscriptions(self):
        """Process any pending symbol subscriptions"""
        if self.pending_subscriptions:
            logger.info(f"[SUBSCRIBE] Processing {len(self.pending_subscriptions)} pending subscriptions")
            await self.subscribe_symbols(self.pending_subscriptions)
            self.pending_subscriptions.clear()
    
    async def disconnect(self):
        """Gracefully disconnect WebSocket"""
        logger.info("[DISCONNECT] Initiating graceful disconnect...")
        
        self.stop_event.set()
        
        if self.websocket_client:
            try:
                # Use the correct method name for SmartWebSocketV2
                if hasattr(self.websocket_client, 'close_connection'):
                    self.websocket_client.close_connection()
                elif hasattr(self.websocket_client, 'close'):
                    self.websocket_client.close()
                else:
                    logger.warning("[WARN] No close method found on WebSocket client")
            except Exception as e:
                logger.error(f"[ERROR] Error during disconnect: {e}")
        
        self.state = ConnectionState.DISCONNECTED
        logger.info("[DISCONNECT] Disconnected successfully")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status and metrics"""
        return {
            'state': self.state.value,
            'metrics': {
                'connection_attempts': self.metrics.connection_attempts,
                'successful_connections': self.metrics.successful_connections,
                'failed_connections': self.metrics.failed_connections,
                'last_connection_time': self.metrics.last_connection_time,
                'last_error': self.metrics.last_error,
                'total_messages_received': self.metrics.total_messages_received,
                'subscribed_symbols_count': len(self.subscribed_symbols)
            },
            'is_connected': self.state == ConnectionState.CONNECTED
        }

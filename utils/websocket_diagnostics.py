#!/usr/bin/env python3
"""
WebSocket Connection Diagnostics and Troubleshooting Tool
Comprehensive analysis and fixes for SmartAPI WebSocket connection issues
"""

import os
import sys
import asyncio
import logging
import json
import time
import socket
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
import pyotp

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# SmartAPI imports
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    SMARTAPI_AVAILABLE = True
except ImportError:
    print("[ERROR] SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None
    SMARTAPI_AVAILABLE = False

logger = logging.getLogger(__name__)

class WebSocketDiagnostics:
    """Comprehensive WebSocket diagnostics and troubleshooting"""
    
    def __init__(self):
        self.api_key = os.getenv('SMARTAPI_API_KEY')
        self.username = os.getenv('SMARTAPI_USERNAME')
        self.password = os.getenv('SMARTAPI_PASSWORD')
        self.totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
        
        self.smartapi_client = None
        self.auth_token = None
        self.feed_token = None
        self.websocket_client = None
        
        self.diagnostics_results = {}
        
    async def run_full_diagnostics(self) -> Dict[str, Any]:
        """Run comprehensive WebSocket diagnostics"""
        print("\n🔍 Starting WebSocket Connection Diagnostics...")
        print("=" * 60)
        
        # Step 1: Environment validation
        await self._check_environment()
        
        # Step 2: Network connectivity
        await self._check_network_connectivity()
        
        # Step 3: SmartAPI authentication
        await self._check_smartapi_authentication()
        
        # Step 4: WebSocket connection test
        await self._test_websocket_connection()
        
        # Step 5: Generate recommendations
        await self._generate_recommendations()
        
        return self.diagnostics_results
    
    async def _check_environment(self):
        """Check environment variables and dependencies"""
        print("\n📋 Checking Environment Configuration...")
        
        env_checks = {
            'smartapi_available': SMARTAPI_AVAILABLE,
            'api_key_set': bool(self.api_key),
            'username_set': bool(self.username),
            'password_set': bool(self.password),
            'totp_token_set': bool(self.totp_token),
            'api_key_length': len(self.api_key) if self.api_key else 0,
            'username_format': self.username.isalnum() if self.username else False,
            'totp_token_length': len(self.totp_token) if self.totp_token else 0
        }
        
        self.diagnostics_results['environment'] = env_checks
        
        # Print results
        for check, result in env_checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check}: {result}")
        
        # Validate TOTP token format
        if self.totp_token:
            try:
                totp = pyotp.TOTP(self.totp_token)
                current_otp = totp.now()
                print(f"   ✅ TOTP generation successful: {current_otp}")
                env_checks['totp_generation'] = True
            except Exception as e:
                print(f"   ❌ TOTP generation failed: {e}")
                env_checks['totp_generation'] = False
    
    async def _check_network_connectivity(self):
        """Check network connectivity to SmartAPI servers"""
        print("\n🌐 Checking Network Connectivity...")
        
        network_checks = {}
        
        # Test basic internet connectivity
        try:
            response = requests.get("https://www.google.com", timeout=5)
            network_checks['internet_connectivity'] = response.status_code == 200
            print(f"   ✅ Internet connectivity: OK")
        except Exception as e:
            network_checks['internet_connectivity'] = False
            print(f"   ❌ Internet connectivity: {e}")
        
        # Test SmartAPI server connectivity
        smartapi_endpoints = [
            "https://apiconnect.angelbroking.com",
            "https://smartapi.angelbroking.com"
        ]
        
        for endpoint in smartapi_endpoints:
            try:
                response = requests.get(f"{endpoint}/rest/auth/angelbroking/user/v1/loginByPassword", 
                                      timeout=10, allow_redirects=False)
                network_checks[f'smartapi_server_{endpoint.split("//")[1]}'] = True
                print(f"   ✅ {endpoint}: Reachable")
            except Exception as e:
                network_checks[f'smartapi_server_{endpoint.split("//")[1]}'] = False
                print(f"   ❌ {endpoint}: {e}")
        
        # Test WebSocket port connectivity
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('apiconnect.angelbroking.com', 443))
            network_checks['websocket_port_443'] = result == 0
            sock.close()
            print(f"   ✅ WebSocket port 443: {'Open' if result == 0 else 'Closed'}")
        except Exception as e:
            network_checks['websocket_port_443'] = False
            print(f"   ❌ WebSocket port test: {e}")
        
        self.diagnostics_results['network'] = network_checks
    
    async def _check_smartapi_authentication(self):
        """Test SmartAPI authentication flow"""
        print("\n🔐 Testing SmartAPI Authentication...")
        
        auth_checks = {}
        
        if not SMARTAPI_AVAILABLE:
            auth_checks['smartapi_import'] = False
            print("   ❌ SmartAPI library not available")
            self.diagnostics_results['authentication'] = auth_checks
            return
        
        try:
            # Initialize SmartConnect
            self.smartapi_client = SmartConnect(api_key=self.api_key)
            auth_checks['smartconnect_init'] = True
            print("   ✅ SmartConnect initialized")
            
            # Generate TOTP
            totp = pyotp.TOTP(self.totp_token)
            totp_code = totp.now()
            auth_checks['totp_generated'] = True
            print(f"   ✅ TOTP generated: {totp_code}")
            
            # Attempt login
            data = self.smartapi_client.generateSession(
                clientCode=self.username,
                password=self.password,
                totp=totp_code
            )
            
            if data['status']:
                self.auth_token = data['data']['jwtToken']
                self.feed_token = self.smartapi_client.getfeedToken()
                auth_checks['login_successful'] = True
                auth_checks['jwt_token_received'] = bool(self.auth_token)
                auth_checks['feed_token_received'] = bool(self.feed_token)
                print("   ✅ Authentication successful")
                print(f"   ✅ JWT Token: {self.auth_token[:20]}...")
                print(f"   ✅ Feed Token: {self.feed_token}")
            else:
                auth_checks['login_successful'] = False
                print(f"   ❌ Authentication failed: {data.get('message', 'Unknown error')}")
                
        except Exception as e:
            auth_checks['authentication_error'] = str(e)
            print(f"   ❌ Authentication error: {e}")
        
        self.diagnostics_results['authentication'] = auth_checks
    
    async def _test_websocket_connection(self):
        """Test WebSocket connection establishment"""
        print("\n📡 Testing WebSocket Connection...")
        
        ws_checks = {}
        
        if not self.auth_token or not self.feed_token:
            ws_checks['prerequisites_met'] = False
            print("   ❌ Missing authentication tokens for WebSocket test")
            self.diagnostics_results['websocket'] = ws_checks
            return
        
        try:
            # Initialize WebSocket
            self.websocket_client = SmartWebSocketV2(
                self.auth_token,
                self.api_key,
                self.username,
                self.feed_token
            )
            ws_checks['websocket_init'] = True
            print("   ✅ WebSocket client initialized")
            
            # Set up connection tracking
            connection_established = asyncio.Event()
            connection_error = None
            
            def on_open(wsapp):
                print("   ✅ WebSocket connection opened")
                ws_checks['connection_opened'] = True
                connection_established.set()
            
            def on_error(wsapp, error):
                nonlocal connection_error
                connection_error = error
                print(f"   ❌ WebSocket error: {error}")
                ws_checks['connection_error'] = str(error)
                connection_established.set()
            
            def on_close(wsapp):
                print("   ⚠️ WebSocket connection closed")
                ws_checks['connection_closed'] = True
            
            # Set callbacks
            self.websocket_client.on_open = on_open
            self.websocket_client.on_error = on_error
            self.websocket_client.on_close = on_close
            
            # Attempt connection with timeout
            print("   🔄 Attempting WebSocket connection...")
            
            # Start connection in background
            import threading
            connection_thread = threading.Thread(target=self.websocket_client.connect)
            connection_thread.daemon = True
            connection_thread.start()
            
            # Wait for connection result with timeout
            try:
                await asyncio.wait_for(connection_established.wait(), timeout=15.0)
                if connection_error:
                    ws_checks['connection_successful'] = False
                    ws_checks['error_details'] = str(connection_error)
                else:
                    ws_checks['connection_successful'] = True
                    print("   ✅ WebSocket connection test successful")
            except asyncio.TimeoutError:
                ws_checks['connection_successful'] = False
                ws_checks['timeout_occurred'] = True
                print("   ❌ WebSocket connection timeout (15 seconds)")
            
        except Exception as e:
            ws_checks['websocket_test_error'] = str(e)
            print(f"   ❌ WebSocket test error: {e}")
        
        self.diagnostics_results['websocket'] = ws_checks
    
    async def _generate_recommendations(self):
        """Generate troubleshooting recommendations"""
        print("\n💡 Generating Recommendations...")
        
        recommendations = []
        
        # Environment issues
        env = self.diagnostics_results.get('environment', {})
        if not env.get('smartapi_available'):
            recommendations.append("Install SmartAPI: pip install smartapi-python")
        
        if not env.get('api_key_set'):
            recommendations.append("Set SMARTAPI_API_KEY environment variable")
        
        if not env.get('totp_generation', True):
            recommendations.append("Verify TOTP token format in SMARTAPI_TOTP_TOKEN")
        
        # Network issues
        network = self.diagnostics_results.get('network', {})
        if not network.get('internet_connectivity'):
            recommendations.append("Check internet connection")
        
        if not network.get('websocket_port_443'):
            recommendations.append("Check firewall settings for port 443")
        
        # Authentication issues
        auth = self.diagnostics_results.get('authentication', {})
        if not auth.get('login_successful'):
            recommendations.append("Verify SmartAPI credentials (API key, username, password)")
            recommendations.append("Check if TOTP token is correct and not expired")
        
        # WebSocket issues
        ws = self.diagnostics_results.get('websocket', {})
        if not ws.get('connection_successful'):
            if ws.get('timeout_occurred'):
                recommendations.append("Check network stability and firewall settings")
                recommendations.append("Try connecting during market hours")
            recommendations.append("Verify you have less than 3 active WebSocket connections")
        
        self.diagnostics_results['recommendations'] = recommendations
        
        print("\n📋 Recommendations:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
        
        if not recommendations:
            print("   ✅ No issues detected! WebSocket should be working properly.")

async def main():
    """Run WebSocket diagnostics"""
    diagnostics = WebSocketDiagnostics()
    results = await diagnostics.run_full_diagnostics()
    
    print("\n" + "=" * 60)
    print("🔍 DIAGNOSTICS COMPLETE")
    print("=" * 60)
    
    # Save results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"websocket_diagnostics_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())

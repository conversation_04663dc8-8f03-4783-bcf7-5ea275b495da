#!/usr/bin/env python3
"""
Real-time WebSocket Manager for Angel One SmartAPI
Handles real-time market data streaming for 500+ stocks
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from collections import defaultdict, deque

import pyotp
import pytz

# SmartAPI imports
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    SMARTAPI_AVAILABLE = True
except ImportError:
    print("[WARN] SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None
    SMARTAPI_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class MarketTick:
    """Real-time market tick data"""
    symbol: str
    token: str
    exchange_type: int
    timestamp: datetime
    last_traded_price: float
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    volume: int = 0
    change_percent: float = 0.0

@dataclass
class SymbolSubscription:
    """Symbol subscription configuration"""
    symbol: str
    token: str
    exchange_type: int
    mode: int = 1  # 1=LTP, 2=Quote, 3=SnapQuote

class WebSocketManager:
    """
    Real-time WebSocket Manager for Angel One SmartAPI
    
    Features:
    - Real-time price streaming for 500+ stocks
    - Automatic reconnection and error handling
    - Rate limiting and subscription management
    - Callback system for real-time data processing
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_config = config.get('angel_one_api', {})
        
        # Authentication details
        self.api_key = self.api_config.get('api_key')
        self.username = self.api_config.get('username')
        self.password = self.api_config.get('password')
        self.totp_token = self.api_config.get('totp_token')
        
        # SmartAPI instances
        self.smart_api = None
        self.websocket = None
        
        # Authentication tokens
        self.auth_token = None
        self.feed_token = None
        
        # Subscription management
        self.subscribed_symbols: Dict[str, SymbolSubscription] = {}
        self.subscription_batches: List[List[SymbolSubscription]] = []
        self.max_symbols_per_batch = 1000  # Angel One limit
        
        # Data callbacks
        self.data_callbacks: List[Callable[[MarketTick], None]] = []
        
        # Connection state
        self.is_connected = False
        self.connection_attempts = 0
        self.max_connection_attempts = 5
        
        # Performance metrics
        self.ticks_received = 0
        self.last_tick_time = None
        self.connection_start_time = None
        
        # Rate limiting
        self.tick_buffer = deque(maxlen=1000)
        self.last_buffer_clear = time.time()
        
    async def initialize(self) -> bool:
        """Initialize WebSocket manager"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI not available")
                return False
                
            # Authenticate with Angel One
            if not await self._authenticate():
                logger.error("[ERROR] Authentication failed")
                return False
                
            logger.info("[SUCCESS] WebSocket Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize WebSocket Manager: {e}")
            return False
    
    async def _authenticate(self) -> bool:
        """Authenticate with Angel One SmartAPI"""
        try:
            self.smart_api = SmartConnect(api_key=self.api_key)
            
            # Generate TOTP
            totp = pyotp.TOTP(self.totp_token).now()
            
            # Login
            data = self.smart_api.generateSession(self.username, self.password, totp)
            
            if not data.get('status'):
                logger.error(f"[ERROR] Authentication failed: {data.get('message')}")
                return False
                
            # Extract tokens
            self.auth_token = data['data']['jwtToken']
            self.feed_token = self.smart_api.getfeedToken()
            
            logger.info("[SUCCESS] Authenticated with Angel One SmartAPI")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Authentication error: {e}")
            return False
    
    def add_data_callback(self, callback: Callable[[MarketTick], None]):
        """Add callback for real-time data processing"""
        self.data_callbacks.append(callback)
        logger.info(f"[INFO] Added data callback: {callback.__name__}")
    
    async def subscribe_symbols(self, symbols: List[SymbolSubscription]) -> bool:
        """Subscribe to real-time data for multiple symbols"""
        try:
            # Add symbols to subscription list
            for symbol in symbols:
                self.subscribed_symbols[symbol.token] = symbol
            
            # Create subscription batches (Angel One limit: 1000 symbols per connection)
            self._create_subscription_batches()
            
            logger.info(f"[INFO] Prepared {len(symbols)} symbols for subscription in {len(self.subscription_batches)} batches")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to prepare symbol subscriptions: {e}")
            return False
    
    def _create_subscription_batches(self):
        """Create subscription batches based on Angel One limits"""
        self.subscription_batches = []
        symbols_list = list(self.subscribed_symbols.values())
        
        for i in range(0, len(symbols_list), self.max_symbols_per_batch):
            batch = symbols_list[i:i + self.max_symbols_per_batch]
            self.subscription_batches.append(batch)
    
    async def start_streaming(self) -> bool:
        """Start real-time data streaming"""
        try:
            if not self.auth_token or not self.feed_token:
                logger.error("[ERROR] Not authenticated")
                return False
            
            # Initialize WebSocket
            self.websocket = SmartWebSocketV2(
                self.auth_token, 
                self.api_key, 
                self.username, 
                self.feed_token,
                max_retry_attempt=self.max_connection_attempts
            )
            
            # Set up callbacks
            self.websocket.on_open = self._on_websocket_open
            self.websocket.on_data = self._on_websocket_data
            self.websocket.on_error = self._on_websocket_error
            self.websocket.on_close = self._on_websocket_close
            
            # Start connection in background
            self.connection_start_time = datetime.now()
            asyncio.create_task(self._run_websocket())
            
            logger.info("[INFO] WebSocket streaming started")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start streaming: {e}")
            return False
    
    async def _run_websocket(self):
        """Run WebSocket connection in background"""
        try:
            await asyncio.to_thread(self.websocket.connect)
        except Exception as e:
            logger.error(f"[ERROR] WebSocket connection error: {e}")
    
    def _on_websocket_open(self, wsapp):
        """Handle WebSocket connection open"""
        try:
            self.is_connected = True
            self.connection_attempts = 0
            logger.info("[SUCCESS] WebSocket connected")
            
            # Subscribe to symbols in batches
            self._subscribe_all_batches()
            
        except Exception as e:
            logger.error(f"[ERROR] Error in websocket open handler: {e}")
    
    def _subscribe_all_batches(self):
        """Subscribe to all symbol batches"""
        try:
            for batch_idx, batch in enumerate(self.subscription_batches):
                # Create token list for this batch
                token_list = []
                
                # Group by exchange type
                exchange_groups = defaultdict(list)
                for symbol in batch:
                    exchange_groups[symbol.exchange_type].append(symbol.token)
                
                # Create subscription format
                for exchange_type, tokens in exchange_groups.items():
                    token_list.append({
                        "exchangeType": exchange_type,
                        "tokens": tokens
                    })
                
                # Subscribe to this batch
                correlation_id = f"batch_{batch_idx}"
                mode = 2  # Quote mode for OHLC data
                
                self.websocket.subscribe(correlation_id, mode, token_list)
                logger.info(f"[INFO] Subscribed to batch {batch_idx + 1}/{len(self.subscription_batches)} with {len(batch)} symbols")
                
        except Exception as e:
            logger.error(f"[ERROR] Error subscribing to symbols: {e}")
    
    def _on_websocket_data(self, wsapp, message):
        """Handle incoming WebSocket data"""
        try:
            self.ticks_received += 1
            self.last_tick_time = datetime.now()
            
            # Parse market tick
            tick = self._parse_market_tick(message)
            if tick:
                # Add to buffer for rate limiting
                self.tick_buffer.append(tick)
                
                # Process callbacks
                for callback in self.data_callbacks:
                    try:
                        callback(tick)
                    except Exception as e:
                        logger.error(f"[ERROR] Error in data callback {callback.__name__}: {e}")
            
            # Clear buffer periodically
            current_time = time.time()
            if current_time - self.last_buffer_clear > 60:  # Clear every minute
                self.tick_buffer.clear()
                self.last_buffer_clear = current_time
                
        except Exception as e:
            logger.error(f"[ERROR] Error processing websocket data: {e}")
    
    def _parse_market_tick(self, message: Dict[str, Any]) -> Optional[MarketTick]:
        """Parse raw websocket message into MarketTick"""
        try:
            # Convert timestamp
            timestamp = datetime.fromtimestamp(
                message['exchange_timestamp'] / 1000, 
                pytz.timezone('Asia/Kolkata')
            )
            
            # Get symbol info
            token = str(message['token'])
            symbol_info = self.subscribed_symbols.get(token)
            
            if not symbol_info:
                return None
            
            # Create market tick
            tick = MarketTick(
                symbol=symbol_info.symbol,
                token=token,
                exchange_type=message['exchange_type'],
                timestamp=timestamp,
                last_traded_price=message['last_traded_price'] / 100,
                open_price=message.get('open_price_of_the_day', 0) / 100,
                high_price=message.get('high_price_of_the_day', 0) / 100,
                low_price=message.get('low_price_of_the_day', 0) / 100,
                close_price=message.get('closed_price', 0) / 100,
                volume=message.get('volume_trade_for_the_day', 0)
            )
            
            # Calculate change percent
            if tick.close_price > 0:
                tick.change_percent = ((tick.last_traded_price - tick.close_price) / tick.close_price) * 100
            
            return tick
            
        except Exception as e:
            logger.error(f"[ERROR] Error parsing market tick: {e}")
            return None
    
    def _on_websocket_error(self, wsapp, error):
        """Handle WebSocket errors"""
        logger.error(f"[ERROR] WebSocket error: {error}")
        self.is_connected = False
    
    def _on_websocket_close(self, wsapp):
        """Handle WebSocket connection close"""
        logger.warning("[WARN] WebSocket connection closed")
        self.is_connected = False
        
        # Attempt reconnection
        if self.connection_attempts < self.max_connection_attempts:
            self.connection_attempts += 1
            logger.info(f"[INFO] Attempting reconnection {self.connection_attempts}/{self.max_connection_attempts}")
            asyncio.create_task(self._reconnect())
    
    async def _reconnect(self):
        """Attempt to reconnect WebSocket"""
        try:
            await asyncio.sleep(5)  # Wait before reconnecting
            await self.start_streaming()
        except Exception as e:
            logger.error(f"[ERROR] Reconnection failed: {e}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get WebSocket performance metrics"""
        uptime = (datetime.now() - self.connection_start_time).total_seconds() if self.connection_start_time else 0
        
        return {
            'is_connected': self.is_connected,
            'ticks_received': self.ticks_received,
            'subscribed_symbols': len(self.subscribed_symbols),
            'subscription_batches': len(self.subscription_batches),
            'uptime_seconds': uptime,
            'ticks_per_second': self.ticks_received / uptime if uptime > 0 else 0,
            'last_tick_time': self.last_tick_time.isoformat() if self.last_tick_time else None,
            'buffer_size': len(self.tick_buffer)
        }
    
    async def stop(self):
        """Stop WebSocket streaming"""
        try:
            if self.websocket:
                self.websocket.close_connection()
            
            self.is_connected = False
            logger.info("[INFO] WebSocket streaming stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping WebSocket: {e}")
